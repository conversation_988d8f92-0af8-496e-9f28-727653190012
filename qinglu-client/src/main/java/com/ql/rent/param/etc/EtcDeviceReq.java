package com.ql.rent.param.etc;

import com.ql.rent.param.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("etc设备管理")
public class EtcDeviceReq extends BaseQuery implements Serializable {
    private static final long serialVersionUID = -751059561046673121L;


    private Long id;
    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID", name = "merchantId")
    private Long merchantId;
    /**
     * 门店
     */
    @ApiModelProperty(value = "门店", name = "storeId")
    private Long storeId;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型", name = "vehicleModelId")
    private Long vehicleModelId;

    /**
     * 车型
     */
    @ApiModelProperty(value = "车型集合", name = "vehicleModelIdList")
    private List<Long> vehicleModelIdList;

    /**
     * 车辆id
     */
    @ApiModelProperty(value = "车辆id", name = "vehicleIdList")
    private List<Long> vehicleIdList;

    /**
     * 车辆id
     */
    @ApiModelProperty(value = "车牌号", name = "license")
    private String license;

    /**
     * 订单
     */
    @ApiModelProperty(value = "订单", name = "orderId")
    private String orderId;
    /**
     * etc订单
     */
    @ApiModelProperty(value = "etc订单", name = "etcOrderId")
    private String etcOrderId;

    /**
     * 提现状态
     */
    @ApiModelProperty(value = "提现状态 0 -未提现 1-提现审核中 2-提现完成", name = "orderLock")
    private Byte orderLock;

    /**
     * 提现状态
     */
    @ApiModelProperty(value = "产品状态   1-上架 0-下架", name = "service")
    private Byte service;

    @ApiModelProperty(value = "订单状态 0:已收单，待签约;1:待支付;2:已取消;3:已开始;4:待结算;5:已结束", name = "orderStatus")
    private Byte orderStatus;

    @ApiModelProperty(value = "车辆id", name = "vehicleId")
    private Long vehicleId;

    @ApiModelProperty(value = "订单预计开始时间",name = "orderStartTime")
    private Long orderStartTime;

    @ApiModelProperty(value = "订单预计结束时间",name = "orderEndTime")
    private Long orderEndTime;


    @ApiModelProperty(value = "订单实际开始时间",name = "realStartTime")
    private Long realStartTime;

    @ApiModelProperty(value = "订单实际结束时间",name = "realEndTime")
    private Long realEndTime;

    @ApiModelProperty(value = "发行方 1好人好车 2支付宝代发行",name = "publisher")
    private Integer publisher;

    @ApiModelProperty(value = "租赁状态 0空闲中 1租赁中",name = "etcRentStatus")
    private Integer etcRentStatus;

    @ApiModelProperty(value = "激活状态 0未激活 1已激活",name = "etcDeviceStatus")
    private Integer etcDeviceStatus;

    @ApiModelProperty(value = "0 已拉黑 1正常",name = "blacklistStatus")
    private Integer blacklistStatus;

    @ApiModelProperty(value = "车架号",name = "franeNum")
    private String frameNum;

    @ApiModelProperty(value = "1:待上线;2:未租赁;3:租赁中;4:维修中;5:保养中;6:调车中;7:事故中'",name = "vehicleStatus")
    private Integer vehicleStatus;

    @ApiModelProperty(value = "ETC设备号",name = "etcNo")
    private String etcNo;

    @ApiModelProperty(value = "工作状态 1正常 2可租赁 3异常",name = "etcWorkStatus")
    private Integer etcWorkStatus;

    @ApiModelProperty(value = "etc设备激活状态 0未激活 1已激活",name = "etcActiveStatus")
    private Integer etcActiveStatus;
}



