package com.ql.rent.param.etc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 第三方车辆库Id关联查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/16 10:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "三分映射字段")
public class RelationParam implements Serializable {

    private static final long serialVersionUID = -26022265629566211L;

    @ApiModelProperty(value = "1 etc_商家 2 etc车辆")
    private Byte type;

    @ApiModelProperty(value = "1 好人好车 2 SAAS自营")
    private Long source;

    @ApiModelProperty(value = "内部SaasId")
    private List<Long> saasIdList;

    @ApiModelProperty(value = "第三方ID")
    private List<String> thirdIdList;

    @ApiModelProperty(value = "saas商家id")
    private Long merchantId;
}
