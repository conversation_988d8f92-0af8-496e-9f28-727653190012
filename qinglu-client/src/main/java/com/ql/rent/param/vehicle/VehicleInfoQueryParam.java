package com.ql.rent.param.vehicle;

import com.ql.rent.param.StoreBaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 门店车辆列表查询接口
 *
 * <AUTHOR>
 */
@ApiModel("车辆列表查询入参")
@Data
public class VehicleInfoQueryParam extends StoreBaseQuery {

    private static final long serialVersionUID = 7838150479638568973L;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", required = false)
    private String license;

    /**
     * 车牌号列表
     */
    @ApiModelProperty(value = "车牌号列表", required = false)
    private List<String> licenseList;

    /**
     * 所属门店id
     */
    @ApiModelProperty(value = "所属门店id", required = false)
    private Long storeId;

    /**
     * 车辆来源
     */
    @ApiModelProperty(value = "车辆来源", notes = "1:自有;2:长期借调;3:临时借调;4:挂靠;5:托管;6:融资租赁", required = false)
    private Byte vehicleSource;

    /**
     * 车架号列表
     */
    @ApiModelProperty("车架号列表")
    private List<String> frameNumList;

    /**
     * 车型id列表
     */
    @ApiModelProperty("车型id列表")
    private List<Long> vehicleModelIdList;

    /**
     * 车型id
     */
    @ApiModelProperty("车型id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型状态", notes = "1:待上线;2:未租赁;3:租赁中;4:维修中;5:保养中;6:调车中;7:事故中'")
    private Byte vehicleStatus;

    @ApiModelProperty("排除车辆状态")
    private List<Byte> notInStatusList;

    @ApiModelProperty(hidden = true)
    private List<Long> idList;

    @ApiModelProperty(hidden = true)
    private List<Long> storeIdList;

    @ApiModelProperty(value = "携程车辆审核状态 0:审核中 1:审核通过 2:审核失败 3无需审核")
    private Byte ctripVehicleAuditStatus;

    @ApiModelProperty(value = "携程车辆状态 0:系统下线 10:已上线 20:永久下线 30:无需审核")
    private Byte ctripVehicleStatus;

    /**
     * 登陆名(三亚交管用)
     */
    @ApiModelProperty(hidden = true)
    private String LoginName;

    @ApiModelProperty(value = "车辆状态，true查询包括已删除的，false，null，查询未删除")
    private Boolean includeDel = false;

    @ApiModelProperty(value = "2024.10.15新增字段，车辆标签id")
    private List<Long> tagIdList;

    @ApiModelProperty(value = "是否只查询保养过期的车辆 1:是 其他:否")
    private Byte maintenanceExpired;

    @ApiModelProperty(value = "年检是否过期 0:否 1:是")
    private Byte yearlyInspectionExpired;

    @ApiModelProperty(value = "保险是否过期 0:否 1:是")
    private Byte insuranceExpired;

    @ApiModelProperty(value = "是否有待处理违章 0:否 1:是")
    private Byte illegalUnhand;

    @ApiModelProperty(value = "是否租赁中 1:是")
    private Byte leased;

    @ApiModelProperty(value = "是否是ETC发行车辆查询 1:是")
    private Byte etcPublishSearch;

}
