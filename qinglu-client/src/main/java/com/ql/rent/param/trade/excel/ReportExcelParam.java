package com.ql.rent.param.trade.excel;

import com.ql.rent.param.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @auther musi
 * @date 2023/3/24 09:33
 */
@Data
@ApiModel("报表excel导出参数")
public class ReportExcelParam extends BaseQuery implements Serializable {

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("城市id")
    private List<Long> cityIdList;

    @ApiModelProperty("门店id")
    private List<Long> storeIdList;

    @ApiModelProperty("渠道id")
    private List<Long> channelIdList;

    @ApiModelProperty("开始日期")
    private Integer startYmd;

    @ApiModelProperty("结束日期")
    private Integer endYmd;

}
