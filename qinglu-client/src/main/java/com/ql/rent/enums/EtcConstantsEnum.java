package com.ql.rent.enums;

import com.ql.rent.share.exception.BizException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * etc相关枚举管理
 */
public interface EtcConstantsEnum {

    //提现方式 1 - 微信 2 支付宝 3 -对公
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum WithdrawalMethodEnum {
        //是否在线 3-未知 1-在线 0-不在线 todo  别骂我  - 为了给前端展示
        wechat((byte) 1, "微信"),
        alipay((byte) 2, "支付宝"),
        corporate_remittance((byte) 3, "对公汇款"),
        ;
        private byte code;
        private String desc;

        public static WithdrawalMethodEnum forByCode(byte code) {
            return Arrays.stream(WithdrawalMethodEnum.values())
                    .filter(f -> f.code == code).findFirst()
                    .orElseThrow(() -> new BizException("Unknown code"));
        }

    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum OnlineEnum {
       //是否在线 3-未知 1-在线 0-不在线 todo  别骂我  - 为了给前端展示
       unknown((byte) 3, "-",null),
        on_line((byte) 1, "在线",Boolean.TRUE),
        not_online((byte) 0, "不在线",Boolean.FALSE),
        ;
        private byte code;
        private String desc;
        private Boolean enumeration;

        public static OnlineEnum forByCode(byte code){
            return Arrays.stream(OnlineEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static OnlineEnum forByDesc(String desc){
            if (desc==null){
                return unknown;
            }
            return Arrays.stream(OnlineEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }
        public static OnlineEnum forByEnumeration(Boolean enumeration){
            if (enumeration==null){
                return unknown;
            }
            return Arrays.stream(OnlineEnum.values())
                    .filter(f->enumeration.equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }

        public static OnlineEnum forByEnumeration(String enumeration){
            if (enumeration==null){
                return unknown;
            }
            return Arrays.stream(OnlineEnum.values())
                    .filter(f->Boolean.valueOf(enumeration).equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum HardNinkStatusEnum {
        //硬件连接状态 3-未知 1-连接 0-断开 todo  别骂我  - 为了给前端展示
        unknown((byte) 3, "-",null),
        connect((byte) 1, "连接",Boolean.TRUE),
        break_link((byte) 0, "断开",Boolean.FALSE),
        ;
        private byte code;
        private String desc;
        private Boolean enumeration;

        public static HardNinkStatusEnum forByCode(byte code){
            return Arrays.stream(HardNinkStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static HardNinkStatusEnum forByCode(String codeStr){
            if(StringUtils.isBlank(codeStr)){
                return unknown;
            }
            Byte code = Byte.valueOf(codeStr);
            return Arrays.stream(HardNinkStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static HardNinkStatusEnum forByDesc(String desc){
            if (desc==null){
                return unknown;
            }
            return Arrays.stream(HardNinkStatusEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }
        public static HardNinkStatusEnum forByEnumeration(String enumeration){
            if(enumeration==null){
                return unknown;
            }
            return Arrays.stream(HardNinkStatusEnum.values())
                    .filter(f-> Boolean.valueOf(enumeration).equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }

        public static HardNinkStatusEnum forByEnumeration(Boolean enumeration){
            if(enumeration==null){
                return unknown;
            }
            return Arrays.stream(HardNinkStatusEnum.values())
                    .filter(f-> enumeration.equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum WorkStatusEnum {
        //工作状态 0-正常 1-拆卸 2-故障 3-低电
        normal((byte) 0, "正常",Boolean.TRUE),
//        disassemble((byte) 1, "拆卸"),
        fault((byte) 2, "故障",Boolean.FALSE),
//        low_electricity((byte) 3, "低电"),
        ;
        private byte code;
        private String desc;
        private Boolean enumeration;

        public static WorkStatusEnum forByCode(byte code){
            return Arrays.stream(WorkStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }

        public static WorkStatusEnum forByDesc(String desc){
            if (desc==null){
                return fault;
            }
            return Arrays.stream(WorkStatusEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }

        public static WorkStatusEnum forByEnumeration(Boolean enumeration){
            if(enumeration== null){
                return fault;
            }
            return Arrays.stream(WorkStatusEnum.values())
                    .filter(f->enumeration.equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
        public static WorkStatusEnum forByEnumeration(String enumeration){
            if(enumeration== null){
                return fault;
            }
            return Arrays.stream(WorkStatusEnum.values())
                    .filter(f->Boolean.valueOf(enumeration).equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
        public static WorkStatusEnum forByCode(String codeStr){
            Byte code = Byte.valueOf(codeStr);
            return Arrays.stream(WorkStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum ServiceEnum {
        //产品状态   1-上架 0-下架
        grounding((byte) 1, "上架",Boolean.TRUE),
        Off_shelf((byte) 0, "下架",Boolean.FALSE),
        ;
        private byte code;
        private String desc;
        private Boolean enumeration;

        public static ServiceEnum forByCode(byte code){
            return Arrays.stream(ServiceEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }

        public static ServiceEnum forByDesc(String desc){
            if (desc==null){
                return Off_shelf;
            }
            return Arrays.stream(ServiceEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }
        public static ServiceEnum forByCode(String codeStr){
            if(StringUtils.isBlank(codeStr)){
                return Off_shelf;
            }
            Byte code = Byte.valueOf(codeStr);
            return Arrays.stream(ServiceEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static ServiceEnum forByEnumeration(Boolean enumeration){
            return Arrays.stream(ServiceEnum.values())
                    .filter(f->f.enumeration.equals(enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum EtcOrderStatusEnum {
//        YTO_BE_SIGNED : 已收单，待签约 TO_BE_PAID：待支付 CANCELLED：已取消 STARTED：已开始【待结算】：开发中 COMPLETED：已结束
        //订单状态 0:已收单，待签约;1:待支付;2:已取消;3:已开始;4:待结算;5:已结束
        to_be_signed_order((byte) 0, "已收单,待签约","TO_BE_SIGNED"),
        to_be_paid_order((byte) 1, "待支付","TO_BE_PAID"),
        canceled_order((byte) 2, "已取消","CANCELLED"),
        started_order((byte) 3, "已开始","STARTED"),
        to_be_settled_order((byte) 4, "待结算","SETTLE_ACCOUNTS"),
        ended_oder((byte) 5, "已结束","COMPLETED"),
        ;
        private byte code;
        private String desc;
        private String enumeration;

        public static EtcOrderStatusEnum forByCode(byte code){
            return Arrays.stream(EtcOrderStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static EtcOrderStatusEnum forByEnumeration(String enumeration){
            return Arrays.stream(EtcOrderStatusEnum.values())
                    .filter(f->f.enumeration.equals(enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum WithdrawalStatusEnum {
        //提现状态 状态 1 已提交 2 已到款 3 已取消
        submitted((byte) 1, "已提交"),
        received_payment((byte) 2, "已到款"),
        canceled((byte) 3, "已取消"),
        ;
        private byte code;
        private String desc;

        public static WithdrawalStatusEnum forByCode(byte code){
            return Arrays.stream(WithdrawalStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum PlateColorEnum {
        //车牌颜色，0-蓝1-黄2-黑3-白4-绿白5-绿黄 6-绿
        unknown((byte) -1, "未知"),
        blue((byte) 0, "蓝"),
        yellow((byte) 1, "黄"),
        black((byte) 2, "黑"),
        white((byte) 3, "白"),
        green_white((byte) 4, "绿白"),
        green_yellow((byte) 5, "绿黄"),
        green((byte) 6, "绿"),
        ;
        private byte code;
        private String desc;

        public static PlateColorEnum forByCode(byte code){
            return Arrays.stream(PlateColorEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }

        public static PlateColorEnum forByDesc(String desc){

            return Arrays.stream(PlateColorEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }
        public static PlateColorEnum forByCode(String codeStr){
            Byte code = Byte.valueOf(codeStr);
            return Arrays.stream(PlateColorEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum EtcSubSceneEnum {
        //           1 *ETC_HIGHWAY：ETC封闭式高速公路；
        //     2* ETC_HIGHWAY_OPEN：ETC开放式高速公路；
        //     3* ETC_PARKING：ETC停车场；
        //     4* ETC_GAS：ETC加油站；
        //     5 ETC_SERVICE_AREA：ETC服务区；
        //     6 * ETC_MUNICIPAL_SERVICE：ETC市政服务


        ETC_HIGHWAY((byte) 1, "ETC封闭式高速公路","ETC_HIGHWAY"),
        ETC_HIGHWAY_OPEN((byte) 2, "ETC开放式高速公路","ETC_HIGHWAY_OPEN"),
        ETC_PARKING((byte) 3, "ETC停车场","ETC_PARKING"),
        ETC_GAS((byte) 4, "ETC加油站","ETC_GAS"),
        ETC_SERVICE_AREA((byte) 5, "ETC服务区","ETC_SERVICE_AREA"),
        ETC_MUNICIPAL_SERVICE((byte) 6, "ETC市政服务","ETC_MUNICIPAL_SERVICE"),
        ;
        private byte code;
        private String desc;
        private String enumeration;

        public static EtcSubSceneEnum forByCode(byte code){
            return Arrays.stream(EtcSubSceneEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static EtcSubSceneEnum forByEnumeration(String enumeration){
            return Arrays.stream(EtcSubSceneEnum.values())
                    .filter(f->f.enumeration.equals(enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum EtcSubTypeEnum {
//     1* 高速交易场景类型: HIGHWAY_TYPE
//     2 * 拓展消费交易类型: EXPAND_TYPE


        HIGHWAY_TYPE((byte) 1, "高速交易场景类型","HIGHWAY_TYPE"),
        EXPAND_TYPE((byte) 2, "拓展消费交易类型","EXPAND_TYPE"),
        ;
        private byte code;
        private String desc;
        private String enumeration;

        public static EtcSubTypeEnum forByCode(byte code){
            return Arrays.stream(EtcSubTypeEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static EtcSubTypeEnum forByEnumeration(String enumeration){
            return Arrays.stream(EtcSubTypeEnum.values())
                    .filter(f->f.enumeration.equals(enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum ActivateStatusEnum {
        //激活态 3 未知 null  1-已激活 true 0 -未激活 false  todo  别骂我  - 为了给前端展示
        unknown((byte) 3, "-","null"),
        active((byte) 1, "已激活","true"),
        not_active((byte) 0, "未激活","false"),
        ;
        private byte code;
        private String desc;
        private String enumeration;

        public static ActivateStatusEnum forByCode(byte code){
            return Arrays.stream(ActivateStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }

        public static ActivateStatusEnum forByDesc(String desc){
            if (desc==null){
                return unknown;
            }
            return Arrays.stream(ActivateStatusEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }

        public static ActivateStatusEnum forByEnumeration(String enumeration){
            if(enumeration== null){
                return unknown;
            }
            return Arrays.stream(ActivateStatusEnum.values())
                    .filter(f->enumeration.equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
        public static ActivateStatusEnum forByEnumeration(Boolean enumeration){
            if(enumeration== null){
                return unknown;
            }
            return Arrays.stream(ActivateStatusEnum.values())
                    .filter(f->enumeration.toString().equals(f.enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown enumeration"));
        }
    }

    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum OrderLockEnum {
        //提现状态 0 -未提现 1-提现审核中 2-提现完成
        undelivered((byte) 0, "未提现"),
        approve((byte) 1, "提现审核中"),
        complete((byte) 2, "提现完成"),
        ;
        private byte code;
        private String desc;


        public static OrderLockEnum forByCode(byte code){
            return Arrays.stream(OrderLockEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }

    }


    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum AvailabilityStatusEnum {
        //  0-  未提交  1 -submit：已提交审核-审核中 2- auditFail：审核失败  3- toActivate：可激活 4- activated：已激活
        undelivered((byte) 0, "未提交",null),
        submit((byte) 1, "已提交审核-审核中","submit"),
        auditFail((byte) 2, "审核失败","auditFail"),
        toActivate((byte) 3, "可激活","toActivate"),
        activated((byte) 4, "已激活","activated"),
        ;
        private byte code;
        private String desc;
        private String enumeration;


        public static AvailabilityStatusEnum forByCode(byte code){
            return Arrays.stream(AvailabilityStatusEnum.values())
                    .filter(f->f.code==code).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
        public static AvailabilityStatusEnum forByDesc(String desc){
            if (desc==null){
                return undelivered;
            }
            return Arrays.stream(AvailabilityStatusEnum.values())
                    .filter(f->f.desc.equals(desc)).findFirst()
                    .orElseThrow(()->new BizException("Unknown desc"));
        }
        public static AvailabilityStatusEnum forByEnumeration(String enumeration){
            if(enumeration==null){
                return undelivered;
            }
            return Arrays.stream(AvailabilityStatusEnum.values())
                    .filter(f-> ObjectUtils.equals(f.enumeration,enumeration)).findFirst()
                    .orElseThrow(()->new BizException("Unknown code"));
        }
    }
    // 三方关联类型

    @AllArgsConstructor
    @Getter
    enum ThirdRelationTypeEnum {
//        1 etc_商家 2 etc车辆 ,3 厂商车牌 和厂商 车辆id绑定关系
        ETC_MERCHANT((byte)1, "etc_商家"),

        ETC_VEHICLE((byte)2, "etc车辆"),

        ETC_VEHICLE_LICENCE((byte)6, "etc车牌"),
        ;
        private final Byte type;
        private final String name;
    }

    @AllArgsConstructor
    @Getter
    enum ThirdSourceEnum {
        //   来源     1 好人好车
        ETC_MERCHANT((byte)1, "好人好车"),
        SAAS_SELF((byte)2, "擎路自营"),
        UNKNOWN((byte)-1, "未知"),
        ;
        private final Byte type;
        private final String name;

        public static ThirdSourceEnum forByCode(byte type) {
            for (ThirdSourceEnum color : values()) {
                if (color.getType() == type) {
                    return color;
                }
            }
            return UNKNOWN;
        }
    }

    @AllArgsConstructor
    @Getter
    enum BlacklistStatusEnum {
        BLACKLIST((byte)0, "已拉黑"),
        NORAL((byte)1, "正常"),
        UNKNOWN((byte)-1, "未知"),
        ;
        private final Byte type;
        private final String desc;

        public static BlacklistStatusEnum forByCode(byte type) {
            for (BlacklistStatusEnum blacklistStatusEnum : values()) {
                if (blacklistStatusEnum.getType() == type) {
                    return blacklistStatusEnum;
                }
            }
            return UNKNOWN;
        }
    }


    @AllArgsConstructor
    @Getter
    enum EtcOrderChargeSource {
        //   来源     1 好人好车
        ETC_MERCHANT((byte)1, "擎路自营"),
        SAAS_SELF((byte)2, "好人好车"),
        UNKNOWN((byte)-1, "未知"),
        ;
        private final Byte type;
        private final String name;

        public static EtcOrderChargeSource forByCode(byte type) {
            for (EtcOrderChargeSource color : values()) {
                if (color.getType() == type) {
                    return color;
                }
            }
            return UNKNOWN;
        }
    }

    // 三方关联类型

//    @AllArgsConstructor
//    @Getter
//    enum EtcMallOrderSource {
//        //   来源     1 好人好车
//        SAAS_SELF((byte)1, "擎路自营"),
//
//        ETC_MERCHANT((byte)2, "好人好车"),
//        ;
//        private final Byte code;
//        private final String name;
//
//        public static EtcMallOrderSource forByCode(byte code){
//            return Arrays.stream(EtcMallOrderSource.values())
//                    .filter(f->f.code==code).findFirst()
//                    .orElseThrow(()->new BizException("Unknown code"));
//        }
//    }


    @AllArgsConstructor
    @Getter
    public enum SaasSelftPlateColorEnum {
        // 未知颜色
        UNKNOWN("UNKNOWN", -1, "未知"),
        // 蓝色
        BLUE("BLUE", 0, "蓝色"),
        // 黄色
        YELLOW("YELLOW", 1, "黄色"),
        // 黑色
        BLACK("BLACK", 2, "黑色"),
        // 白色
        WHITE("WHITE", 3, "白色"),
        // 绿白
        GREEN_WHITE("GREEN_WHITE", 4, "绿白"),
        // 绿黄
        GREEN_YELLOW("GREEN_YELLOW", 5, "绿黄"),
        // 绿色
        GREEN("GREEN", 6, "绿色");

        private final String platformCode;
        private final int code;
        private final String desc;

        public static SaasSelftPlateColorEnum forByPlatformCode(String platformCode) {
            if (platformCode == null) {
                return UNKNOWN;
            }
            for (SaasSelftPlateColorEnum color : values()) {
                if (color.getPlatformCode().equals(platformCode)) {
                    return color;
                }
            }
            return UNKNOWN;
        }

        public static SaasSelftPlateColorEnum forByCode(int code) {
            for (SaasSelftPlateColorEnum color : values()) {
                if (color.getCode() == code) {
                    return color;
                }
            }
            return UNKNOWN;
        }
    }
    @AllArgsConstructor
    @Getter
    enum SaasSelfEtcOrderStatusEnum {
        // 未知状态
        UNKNOWN(0, 0, "未知状态"),
        // 待签约
        TO_BE_SIGNED(1, 30, "待签约"),
        // 待支付
        TO_BE_PAID(2, 5, "待支付"),
        // 进行中
        IN_PROGRESS(3, 0, "进行中"),
        // 已结束
        FINISHED(4, 0, "已结束"),
        // 结算中
        SETTLING(5, 0, "结算中"),
        // 已取消
        CANCELED(6, 0, "已取消"),
        //
        TO_BE_START(7, 0, "未开始");

        private final int code;
        private final int timeoutMinutes;
        private final String description;

        // 根据状态码查找对应的枚举实例
        public static SaasSelfEtcOrderStatusEnum getByCode(int code) {
            for (SaasSelfEtcOrderStatusEnum status : values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return UNKNOWN;
        }

        public static List<Long> getPayedList() {
            return Arrays.stream(SaasSelfEtcOrderStatusEnum.values())
                    .filter(f -> f.getCode() == 3 || f.getCode() == 4 || f.getCode() == 5)
                    .map(it->(long)it.getCode())
                    .collect(Collectors.toList());
        }

    }

    /**
     * 租赁状态枚举
     */
    @AllArgsConstructor
    @Getter
    public enum EtcRentStatusEnum {
        OTHER(0, "未知"),
        UN_USED(1, "空闲中"),
        USED(2, "租赁中");

        private final Integer code;
        private final String desc;
        public static EtcRentStatusEnum forByCode(Integer code) {
            if (code == null) {
                return OTHER;
            }
            for (EtcRentStatusEnum etcRentStatusEnum : values()) {
                if (etcRentStatusEnum.getCode().equals(code)) {
                    return etcRentStatusEnum;
                }
            }
            return OTHER;
        }
    }

    /**
     * 发行方枚举
     */
    @AllArgsConstructor
    @Getter
    public enum PublisherEnum {
        OTHER(0, "未知"),
        HAOREN_HAOCHE(1, "好人好车"),
        ZFB_PROXY(2, "支付宝代发行");

        private final Integer code;
        private final String desc;
        public static PublisherEnum forByCode(Integer code) {
            if (code == null) {
                return OTHER;
            }
            for (PublisherEnum publisherEnum : values()) {
                if (publisherEnum.getCode().equals(code)) {
                    return publisherEnum;
                }
            }
            return OTHER;
        }
    }

    /**
     * 发行方枚举
     */
    @AllArgsConstructor
    @Getter
    public enum PublishStatusEnum {
        OTHER(-1, "未知"),
        UN_PUBLISH(0, "未发行"),
        PUBLISHED(1, "已发行");

        private final Integer code;
        private final String desc;
        public static PublishStatusEnum forByCode(Integer code) {
            if (code == null) {
                return OTHER;
            }
            for (PublishStatusEnum publisherEnum : values()) {
                if (publisherEnum.getCode().equals(code)) {
                    return publisherEnum;
                }
            }
            return OTHER;
        }
    }


    
    /**
     * 自动扣款协议状态枚举
     */
    @AllArgsConstructor
    @Getter
    public enum AutoDeductionAgreementEnum {
        UNKNOWN("UNKNOW", "未知"),
        NOT_SIGNED("WAIT_SIGN", "待签约"),
        SIGNED("SIGNED", "已签约"),
        EXPIRED("UNSIGN", "已解约");

        private final String code;
        private final String desc;

        public static AutoDeductionAgreementEnum getByCode(String code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (AutoDeductionAgreementEnum agreementEnum : values()) {
                if (agreementEnum.getCode().equals(code)) {
                    return agreementEnum;
                }
            }
            return UNKNOWN;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum EtcOrderEndTypeEnum {
        UNKNOWN(-1, ""),
        RENTAL_PERIOD_END(1, "租期结束"),
        USER_END(2, "用户结束"),
        EXCEPTION_END(3, "异常结束"),
        MANUAL_END(4, "人工结束"),
        FORCED_END(5, "强制结束");

        private final Integer code;
        private final String desc;

        public static EtcOrderEndTypeEnum getByCode(Integer code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (EtcOrderEndTypeEnum endTypeEnum : values()) {
                if (endTypeEnum.getCode().equals(code)) {
                    return endTypeEnum;
                }
            }
            return UNKNOWN;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum EtcOrderCancelTypeEnum {
        UNKNOWN(-1, ""),
        SIGN_TIMEOUT(1, "签约超时"),
        PAY_TIMEOUT(2, "支付超时"),
        USER_CANCEL(3, "用户取消"),
        FORCE_CANCEL(4, "强制取消");

        private final Integer code;
        private final String desc;

        public static EtcOrderCancelTypeEnum getByCode(Integer code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (EtcOrderCancelTypeEnum cancelTypeEnum : values()) {
                if (cancelTypeEnum.getCode().equals(code)) {
                    return cancelTypeEnum;
                }
            }
            return UNKNOWN;
        }
    }


    /**
     * etc订单类型枚举
     */
    @AllArgsConstructor
    @Getter
    public enum EtcOrderTypeEnum {
        UNKNOWN(-1, "未知"),
        NORMAL_PERSON(1, "普通消费者");

        private final Integer code;
        private final String desc;

        public static EtcOrderTypeEnum getByCode(Integer code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (EtcOrderTypeEnum etcOrderTypeEnum : values()) {
                if (etcOrderTypeEnum.getCode().equals(code)) {
                    return etcOrderTypeEnum;
                }
            }
            return UNKNOWN;
        }
    }


    /**
     * etc工作状态
     */
    @AllArgsConstructor
    @Getter
    public enum EtcDeviceWorkStatusEnum {
        UNKNOWN(3, "异常"),
        ACTIVE(0, "可租赁"),
        UN_ACTIVE(1, "正常");

        private final Integer code;
        private final String desc;

        public static EtcDeviceWorkStatusEnum getByCode(Integer code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (EtcDeviceWorkStatusEnum etcDeviceWorkStatusEnum : values()) {
                if (etcDeviceWorkStatusEnum.getCode().equals(code)) {
                    return etcDeviceWorkStatusEnum;
                }
            }
            return UNKNOWN;
        }
    }

    @AllArgsConstructor
    @Getter
    enum PayStatusEnum {
        UNKNOWN(-1, "未知"),
        UN_PAY(0, "未支付"),
        CANCEL_PAY(8, "已取消"),
        FAILED_PAY(3, "支付失败"),
        PART_PAID(1, "部分支付"),
        ALL_PAID(2, "全额支付");

        private final int code;
        private final String description;

        public static PayStatusEnum getByCode(int code) {
            for (PayStatusEnum source : values()) {
                if (source.getCode() == code) {
                    return source;
                }
            }
            return UNKNOWN;
        }
        public static List<Long> getPayedList() {
            return Arrays.asList((long)PayStatusEnum.ALL_PAID.getCode(),(long)PayStatusEnum.PART_PAID.getCode());
        }
    }

    @AllArgsConstructor
    @Getter
    public enum SelfEtcSubTypeEnum {
        UNKNOWN("UN_KNOW", "未知"),
        /**
         * 高速交易场景类型
         */
        HIGHWAY_TYPE("HIGHWAY_TYPE", "高速交易场景类型"),

        /**
         * 拓展消费交易类型
         */
        EXPAND_TYPE("EXPAND_TYPE", "拓展消费交易类型");

        private final String code;
        private final String description;

        public static SelfEtcSubTypeEnum getByCode(String code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (SelfEtcSubTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return UNKNOWN;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SelfEtcSubSceneEnum {

        UN_KNOW("UN_KNOW", "未知", SelfEtcSubTypeEnum.UNKNOWN),
        /**
         * ETC封闭式高速公路
         */
        ETC_HIGHWAY("ETC_HIGHWAY", "ETC封闭式高速公路", SelfEtcSubTypeEnum.HIGHWAY_TYPE),

        /**
         * ETC开放式高速公路
         */
        ETC_HIGHWAY_OPEN("ETC_HIGHWAY_OPEN", "ETC开放式高速公路", SelfEtcSubTypeEnum.HIGHWAY_TYPE),

        /**
         * ETC停车场
         */
        ETC_PARKING("ETC_PARKING", "ETC停车场", SelfEtcSubTypeEnum.EXPAND_TYPE),

        /**
         * ETC加油站
         */
        ETC_GAS("ETC_GAS", "ETC加油站", SelfEtcSubTypeEnum.EXPAND_TYPE),

        /**
         * ETC服务区
         */
        ETC_SERVICE_AREA("ETC_SERVICE_AREA", "ETC服务区", SelfEtcSubTypeEnum.EXPAND_TYPE),

        /**
         * ETC市政服务
         */
        ETC_MUNICIPAL_SERVICE("ETC_MUNICIPAL_SERVICE", "ETC市政服务", SelfEtcSubTypeEnum.EXPAND_TYPE);

        private final String code;
        private final String description;
        private final SelfEtcSubTypeEnum subType;
        public static SelfEtcSubSceneEnum getByCode(String code) {
            if (code == null) {
                return UN_KNOW;
            }
            for (SelfEtcSubSceneEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return UN_KNOW;
        }

        /**
         * 判断是否属于某个交易类型
         */
        public static boolean isSubType(String code, SelfEtcSubTypeEnum subType) {
            if (code == null || subType == null) {
                return false;
            }
            SelfEtcSubSceneEnum scene = getByCode(code);
            return scene != null && scene.getSubType() == subType;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum RefundStatusEnum {
        UN_KNOW(-1, "未知"),
        /**
         * 取消退款状态
         */
        CANCEL_REFUND(0, "取消退款"),
        /**
         * 退款成功状态
         */
        REFUND_SUCCESS(1, "退款成功"),
        /**
         * 退款失败状态
         */
        REFUND_FAIL(2, "退款失败"),
        /**
         * 退款异常状态
         */
        REFUND_EXCEPTION(3, "退款异常"),

        REFUNDING((byte)4, "退款中");

        /**
         * 状态码
         */
        private final int code;
        /**
         * 状态描述
         */
        private final String description;


        /**
         * 根据状态码查找对应的枚举值
         * @param code 状态码
         * @return 对应的枚举值，如果未找到则返回 null
         */
        public static RefundStatusEnum getByCode(int code) {
            for (RefundStatusEnum status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return UN_KNOW;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum SignStatusEnum {
        /**
         * 待签约状态
         */
        WAIT_SIGN("WAIT_SIGN", "待签约"),

        /**
         * 已签约状态
         */
        SIGNED("SIGNED", "已签约"),

        /**
         * 已解约状态
         */
        UNSIGN("UNSIGN", "已解约");

        /**
         * 状态码
         */
        private final String code;

        /**
         * 状态描述
         */
        private final String description;

        /**
         * 根据状态码查找对应的枚举值
         * @param code 状态码
         * @return 对应的枚举值，如果未找到则返回 null
         */
        public static SignStatusEnum fromCode(String code) {
            for (SignStatusEnum status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
