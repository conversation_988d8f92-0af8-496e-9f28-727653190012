package com.ql.rent.vo.etc;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EtcOrderChargeVo implements Serializable {
    private static final long serialVersionUID = -260226781515656611L;

    @ApiModelProperty(value = "Id", name = "id")
    private Long id;

    @ApiModelProperty(value = "商家Id", name = "merchantId")
    private Long merchantId;

    @ApiModelProperty(value = "门店Id", name = "storeId")
    private Long storeId;

    @ApiModelProperty(value = "门店名称", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "车型Id", name = "vehicleModelId")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车系名称", name = "vehicleSeryName")
    private String vehicleSeryName;

    @ApiModelProperty(value = "车辆Id", name = "vehicleId")
    private Long vehicleId;

    @ApiModelProperty(value = "车辆名称", name = "vehicleName")
    private String vehicleName;

    @ApiModelProperty(value = "etc租赁费 （单位分）", name = "tenancyFee")
    private Long tenancyFee;

    @ApiModelProperty(value = "商家分润（单位分）", name = "merchantProfit")
    private Long merchantProfit;

    @ApiModelProperty(value = "高速通行费用（单位分）", name = "expresswayTolls")
    private Long expresswayTolls;


    @ApiModelProperty(value = "saas订单号", name = "oederId")
    private String orderId;

    @ApiModelProperty(value = "etc订单号", name = "etcOrder")
    private String etcOrder;

    @ApiModelProperty(value = "订单来源 0-未知 1-saas 2-好车", name = "source")
    private Byte source;

    @ApiModelProperty(value = "订单状态 0:已收单，待签约;1:待支付;2:已取消;3:已开始;4:待结算;5:已结束", name = "orderStatus")
    private String orderStatus;

    @ApiModelProperty(value = "提现状态 0 -未提现 1-提现审核中 2-提现完成", name = "orderLock")
    private String orderLock;

    @ApiModelProperty(value = "三方推送租赁费 （单位分）", name = "amt")
    private Long amt;

    @ApiModelProperty(value = "版本", name = "lastVer")
    private Integer lastVer;

    @ApiModelProperty(value = "操作人", name = "opUserId")
    private Long opUserId;

    @ApiModelProperty(value = "创建实际", name = "createTime")
    private Long createTime;

    @ApiModelProperty(value = "操作时间", name = "vehicleId")
    private Long opTime;

    @ApiModelProperty(value = "结算时间",name = "endPaymentTime")
    private Long endPaymentTime;

    @ApiModelProperty(value = "订单预计开始时间",name = "orderStartTime")
    private Long orderStartTime;

    @ApiModelProperty(value = "订单预计结束时间",name = "orderEndTime")
    private Long orderEndTime;

    @ApiModelProperty(value = "订单实际开始时间",name = "realStartTime")
    private Long realStartTime;

    @ApiModelProperty(value = "订单实际结束时间",name = "realEndTime")
    private Long realEndTime;

    @ApiModelProperty(value = "来源 1：好人好车  2：擎路自营", name = "etcSource")
    private Integer etcSource;

    @ApiModelProperty(value = "来源 1：好人好车  2：擎路自营", name = "etcSourceStr")
    private String etcSourceStr;
}
