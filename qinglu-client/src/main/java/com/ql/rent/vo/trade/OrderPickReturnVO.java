package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2022-10-26 23:47
 */
@Data
@ToString
@ApiModel(value = "取还车信息VO")
public class OrderPickReturnVO implements Serializable {

    private static final long serialVersionUID = 3572240160489203511L;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "车辆名称")
    private String vehicleName;

    @ApiModelProperty(value = "车牌")
    private String vehicleNo;

    @ApiModelProperty(value = "订单状态")
    private Byte orderStatus;

    @ApiModelProperty(value = "车辆特性")
    private String vehicleUnionName;

    // --------同程暂时不支持还车时候的免押扣款，给个标识给前端单独处理
    private Byte orderSource;

    private String sourceOrderId;
    // --------同程暂时不支持还车时候的免押扣款，给个标识给前端单独处理

    @ApiModelProperty(value = "取车信息")
    private VehiclePickReturnVO pickupVehicleInfo;

    @ApiModelProperty(value = "还车信息")
    private VehiclePickReturnVO returnVehicleInfo;

    @ApiModelProperty(value = "还车收费明细")
    private List<VehicleReturnExpenseItemVO> deductionExpenseItemList;

    @ApiModelProperty(value = "还车退费明细")
    private List<VehicleReturnExpenseItemVO> refundExpenseItemList;

    @ApiModelProperty(value = "已经保存的服务项")
    private List<ServiceItemAmountVo> addedServiceItemList;

    @ApiModelProperty(value = "押金数据")
    private DepositVO depositVO;

    @ApiModelProperty(value = "是否支持 线上渠道(为false的情况， 押金只能线下支付")
    private Boolean supportUnderlineExempt;

    @ApiModelProperty(value = "车辆当前里程数, 取车时显示默认值")
    private Integer currentMileage;

    @ApiModelProperty(value = "订单来源名称")
    private String sourceName;

    @ApiModelProperty(value = "是否允许线上代扣 1-是，0-否")
    private Byte allowedOnlinePay;

    @ApiModelProperty(value = "是否允许线上退款 1-是，0-否")
    private Byte allowOnlineRefund;

    @ApiModelProperty(value = "取车验车单")
    private VehicleInspectionVO pickInspection;

    @ApiModelProperty(value = "还车验车单")
    private VehicleInspectionVO returnInspection;
}
