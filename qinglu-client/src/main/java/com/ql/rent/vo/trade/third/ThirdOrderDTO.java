package com.ql.rent.vo.trade.third;

import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.vo.store.LongLatVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 创建订单
 * @Author: musi
 * @Date: 2022/10/30 14:15
 */
@Data
public class ThirdOrderDTO implements Serializable {

    private Long channelId;

    private Long vehicleModelId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 车辆名称，没有就不传
     */
    private String vehicleName;

    private Long vehicleId;

    /**
     * 车牌，没有就不传
     */
    private String vehicleNo;

    /**
     * 取车时间
     */
    private String pickupDate;

    /**
     * 还车时间
     */
    private String returnDate;

    /**
     * 取车门店id
     */
    private Long pickupStoreId;

    /**
     * 还车门店id
     */
    private Long returnStoreId;

    /**
     * 取车城市code
     */
    private Integer pickupCityCode;

    /**
     * 还车城市code
     */
    private Integer returnCityCode;

    /**
     * 取车地址
     */
    private String pickupAddr;

    /**
     * 还车地址
     */
    private String returnAddr;

    /**
     * 取车方式：0：门店自取（自己去门店）；1：送车上门（门店上门）； 2：免费接送
     * {@link com.ql.rent.enums.trade.PickupTypeEnum}
     */
    private Byte pickupAddrType;

    /**
     * 还车方式：0：门店自还（自己去门店）；1：还车上门（门店上门）； 2：免费接送
     * {@link com.ql.rent.enums.trade.ReturnTypeEnum}
     */
    private Byte returnAddrType;

    /**
     * 是否是取车租车中心。1：是租车中心；0：不是。如未对接相关功能，可以忽略。
     * 必传 是
     */
    private Integer isPickUpRentalCenter;

    /**
     * 是否是还车租车中心。1：是租车中心；0：不是。如未对接相关功能，可以忽略。
     * 必传 是
     */
    private Integer isPickOffRentalCenter;

    /**
     * 取车经纬度
     */
    private LongLatVo pickupLongLatVo;

    /**
     * 还车经纬度
     */
    private LongLatVo returnLongLatVo;

    /**
     * 支付状态，没传则默认为全额支付
     */
    private Integer payStatus;

    /**
     * 实收金额,已减去活动金额，精确到分
     */
    private Integer payAmount;

    /**
     * 应收金额（未减去活动金额）,精确到分
     */
    private Integer receivableAmount;

    /**
     * 免押金;0：不支持；1：免租车押；2：全免押金；3：免违章押金
     * {@link com.ql.rent.enums.trade.FreeDepositTypeEnum}
     */
    private Integer freeDepositDegree;

    private Integer rentalDeposit;

    private Integer illegalDeposit;

    /**
     * 携程
     * 当字段freeDepositDegree等于10、20、30 时必传
     * 枚举值如下：1：信用租-程信分免押；2：信用租-新芝麻免押；3：芝麻免押 4：微信分免押
     */
    private String freeDepositWay;


    /**
     * 三方订单id
     */
    private String sourceOrderId;

    /**
     * 订单来源
     * {@link com.ql.rent.enums.trade.OrderSourceEnum}
     */
    private Byte orderSource;

    /**
     * 订单状态 0:未提交;1:已提交;2:确认中;3:已确认;4:已排车;5:已取车;6:已还车;7:取消中;8:已取消
     * {@link com.ql.rent.enums.trade.OrderStatusEnum}
     */
    private Byte orderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单下单时间
     */
    private Long orderTime;

    /**
     * 价格渠道
     */
    private Integer priceChannel;

    /**
     * 下订单用户信息
     * TODO 携程有什么字段就传什么
     */
    private ThirdOrderUserDTO thirdOrderUserDTO;

    /**
     * 服务列表
     */
    private List<ServiceItemAmountDTO> serviceItemDTOList;

    /**
     * 附加服务code
     */
    private List<String> addedServiceItemCode;

    /**
     * 标签
     */
    private List<String> tagList;

    /**
     * 优惠券code
     */
    private String couponCode;

    /**
     * 套餐代码，与供应商从列表页接口返回的车型的packageId对应。用于对接无忧租场景，非必传。如果为空则代表普通租的价格数据。
     * 必传 否
     */
    private String packageId;

    private List<ActivityAndCouponDTO> activityAndCouponList;

    private VehicleModelPriceAbbrDTO vehicleModelPriceAbbrDTO;

    private CardOrderExtDTO cardOrderExtDTO;

    private Byte selfPrOrder = 0;

    private Integer isExcel;

    private Long pickupCircleId;

    private Long returnCircleId;

    private boolean noWorriedOrder = false;


    /**
     * 标准费用code list。非必传。传递该list时，租车费的code 固定为999，其他费用的code 为供应商code。
     * 必传 否
     */
    private List<StandardFeeDTO> standardFeeList;

    /**
     * 价格日历。非必传。
     * 必传 否
     */
    private List<PriceDailyDTO> priceDailyList;

    private Integer childrenSeatNum = 0;

    /**
     * 是否跳过验价（上货三期）
     */
    private boolean skipCheckPrice = false;

    /**
     * 是否跳过替换价格费用
     */
    private boolean replacePrice = false;

    /**
     * 是否是一键跟价订单 1是 0否
     */
    private Integer followLimitDailyPrice;

    /**
     * 是否飞猪安心租
     */
    private boolean anxin = false;

    /**
     * 零散规则
     */
    private List<HourlyChargeSnapShotDTO> hourlyChargeSnapShot;

    /**
     * 违约金规则快照
     */
    private List<PenaltySnapShotDTO> penaltyInfoSnapShot;

    /**
     * 费用标准化：是否平台费用标准化订单。若是，且该订单带有零散租期，则供应商侧应跳过验价, 注意下单时请保存该字段，后续加购、续租请求根据下单落的字段判断是否也要跳过验价
     */
    private boolean platformCal = false;

    /**
     * 车龄
     */
    private Long levelCorrEarliestRegisterTime;
}
