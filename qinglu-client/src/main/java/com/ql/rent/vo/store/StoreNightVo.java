package com.ql.rent.vo.store;

import com.ql.rent.vo.BaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 门店夜间服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/16 10:43
 */
@Data
@ApiModel(description = "门店夜间服务")
public class StoreNightVo extends BaseVo {

    private static final long serialVersionUID = -260226781629566211L;

    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店ID", name = "storeId")
    private Long storeId;

    /**
     * 营业周期 周一-周日: 1111111;周一:1000000;周日:0000001;周六:0000011
     */
    @ApiModelProperty(value = "营业周期 周一-周日: 1111111;周一:1000000;周日:0000001;周六:0000011", name = "businessPeriod")
    private String businessPeriod;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", name = "businessFrom")
    private Integer businessFrom;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", name = "businessTo")
    private Integer businessTo;

    /**
     * 收费金额,精确到分
     */
    @ApiModelProperty(value = "收费金额,精确到分", name = "fee")
    private Integer fee;

    /**
     * 收费类型 目前默认每次 1：每次
     */
    @ApiModelProperty(value = "收费类型 目前默认每次 1：每次", name = "feeType")
    private Byte feeType = 1;
}
