package com.ql.rent.vo.etc;

import com.ql.rent.vo.store.LongLatVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EtcDeviceVo implements Serializable {
    private static final long serialVersionUID = -260226781625656611L;

    @ApiModelProperty(value = "Id", name = "id")
    private Long id;

    @ApiModelProperty(value = "商家Id", name = "merchantId")
    private Long merchantId;

    @ApiModelProperty(value = "门店Id", name = "storeId")
    private Long storeId;

    @ApiModelProperty(value = "车型Id", name = "vehicleModelId")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车辆Id", name = "vehicleId")
    private Long vehicleId;

    @ApiModelProperty("车牌")
    private String license;

    @ApiModelProperty(value = "etc标签号", name = "etcNo")
    private String etcNo;

    @ApiModelProperty(value = "etc标签号集合", name = "etcNos")
    private String etcNos;

    @ApiModelProperty(value = "etc卡号", name = "icNo")
    private String icNo;//todo i 开头变量可能有问题

    @ApiModelProperty(value = "是否在线 3-未知 1-在线 0-不在线", name = "online")
    private String online;

    @ApiModelProperty(value = "硬件连接状态 3-未知 1-连接 0-断开", name = "hardNinkStatus")
    private String hardLinkStatus;

    @ApiModelProperty(value = "工作状态 0-正常 1-拆卸 2-故障 3-低电", name = "workStatus")
    private String workStatus;

    @ApiModelProperty(value = "产品状态   1-上架 0-下架", name = "service")
    private String service;

    @ApiModelProperty(value = " 激活态 3 未知 1-已激活 0 -未激活", name = "activateStatus")
    private String activateStatus;

    @ApiModelProperty(value = "发行单类型 0-  未提交  1 -submit：已提交审核-审核中 2- auditFail：审核失败  3- toActivate：可激活 4- activated：已激活", name = "availabilityStatus")
    private String availabilityStatus;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "etc坐标信息", name = "gis")
    private LongLatVo gis;

    @ApiModelProperty(value = "车牌颜色，0-蓝1-黄2-黑3-白4-绿白5-绿黄 6-绿", name = "plateColor")
    private String plateColor;

    @ApiModelProperty(value = "车轴数", name = "axles")
    private String axles;

    @ApiModelProperty(value = "车身长mm", name = "length")
    private String length;

    @ApiModelProperty(value = "车身宽mm", name = "width")
    private String width;

    @ApiModelProperty(value = "车身高mm", name = "height")
    private String height;

    @ApiModelProperty(value = "总质量kg", name = "totalWeight")
    private String totalWeight;

    @ApiModelProperty(value = "车身重量(整备质量)kg", name = "grossWass")
    private String grossWass;

    @ApiModelProperty(value = "车辆注册日期(格式“yyyy-MM-dd HH:mm:ss”)", name = "registerDate")
    private Date registerDate;

    @ApiModelProperty(value = "行驶证发证日期(格式“yyyy-MM-dd HH:mm:ss”)", name = "grantDate")
    private Date grantDate;

    @ApiModelProperty(value = "车辆所有人", name = "ownerName")
    private String ownerName;

    @ApiModelProperty(value = "【自营用】etc申请单id", name = "etcApplyOrderId")
    private String etcApplyOrderId;

    @ApiModelProperty(value = "【自营用】ETC平台协议号", name = "bizAgreementNo")
    private String bizAgreementNo;

    @ApiModelProperty(value = "【自营用】设备状态明细，能清楚说明etc设备此时状态（/卡签注销/卡签挂失/ 已过户/维修中/⿊名单/卡过期/⽋费/标签脱落/设备报警/正常/ETC 停⽤等）", name = "deviceStatusDetail")
    private String deviceStatusDetail;

    @ApiModelProperty(value = "发行方", name = "publisher")
    private Integer publisher;

    @ApiModelProperty(value = "发行方名称", name = "publisherName")
    private Integer publisherName;

    @ApiModelProperty(value = "来源 1：好人好车  2：擎路自营", name = "etcSource")
    private Integer etcSource;

    @ApiModelProperty(value = "来源 1：好人好车  2：擎路自营", name = "etcSourceStr")
    private String etcSourceStr;

}
