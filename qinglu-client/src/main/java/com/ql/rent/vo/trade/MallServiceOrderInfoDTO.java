package com.ql.rent.vo.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
@ApiModel("商城订单model")
public class MallServiceOrderInfoDTO {

    @ApiModelProperty("数据id")
    private Long id;

    @ApiModelProperty("订单编号")
    private String orderNo;

    private Integer tempVersion;

    @ApiModelProperty("套餐规格类型")
    private Integer itemType;

    @ApiModelProperty("套餐规格类型")
    private String itemCode;

    @ApiModelProperty("套餐规格名称")
    private String itemName;

    @ApiModelProperty("订单时长（单位：月）")
    private Integer duration;

    @ApiModelProperty("套餐次数")
    private Integer itemCount;

    @ApiModelProperty("剩余次数")
    private Integer remainingCount;

    @ApiModelProperty("套餐到期时间")
    private Date expirationDate;

    @ApiModelProperty("订单金额")
    private Integer orderPrice;

    @ApiModelProperty("订单押金")
    private Integer orderDeposit;

    @ApiModelProperty("是否过期")
    private Boolean isExpire;

    @ApiModelProperty("订单状态 0:未支付;1:已全额支付;2:支付失败;3:部分退款;4:全额退款;5:退款失败;6:支付异常;7:已取消;")
    private Byte orderStatus;

    @ApiModelProperty("订单创建时间")
    private Long createTime;

    @ApiModelProperty("订单创建人")
    private Long createUserId;

    @ApiModelProperty("最后操作时间")
    private Long opTime;

    @ApiModelProperty("实际操作人")
    private Long opUserId;

    @ApiModelProperty("套餐金额")
    private Long itemPrice;

    @ApiModelProperty("套餐押金")
    private Integer deposit;

    @ApiModelProperty("子套餐名称")
    private String itemSubPackage;

    @ApiModelProperty("支付方式")
    private Byte paymentMethod;

    @ApiModelProperty("【ETC自营项目新增】来源code  1、 好人好车 2、擎路自营")
    private Byte deviceSource;

    @ApiModelProperty("【ETC自营项目新增】来源name  1、 好人好车 2、擎路自营")
    private String deviceSourceStr;
}
