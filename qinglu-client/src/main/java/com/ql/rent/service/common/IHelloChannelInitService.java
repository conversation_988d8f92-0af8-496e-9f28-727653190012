package com.ql.rent.service.common;

import com.ql.rent.share.result.Result;
import com.ql.rent.vo.common.TaskRecordVo;
import com.ql.rent.vo.common.ThirdBatchAddVo;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface IHelloChannelInitService {


    /**
     * 触发初始化任务
     */
    Result<Boolean> addGdsThirdMatchInfo(ThirdBatchAddVo req);


    Result<Integer> statusCallBack(TaskRecordVo callback);

    /**
     * 继续初始化
     */
    Result<Boolean> continueGdsInit(Long merchantId);

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    enum TaskStatusEnum {
        PROCESSING(0),
        SUCCESS(1),
        FAILED(2);
        private final int status;
    }
}
