package com.ql.rent.service.trade;

import com.ql.rent.api.aggregate.model.dto.DailyPriceListDTO;
import com.ql.rent.dto.trade.OrderDetailMsgDTO;
import com.ql.rent.dto.trade.OrderPayUrlDTO;
import com.ql.rent.dto.trade.UpdateOrderVehicleDTO;
import com.ql.rent.dto.trade.UpdateOrderVehicleForthirdDTO;
import com.ql.rent.enums.trade.ServiceItemListV2Vo;
import com.ql.rent.param.trade.OrderInfoParam;
import com.ql.rent.param.trade.VehicleModelPriceCalQueryParam;
import com.ql.rent.param.trade.VehicleModelPriceCalQueryV2Param;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.bill.QueryPayResultVo;
import com.ql.rent.vo.price.InsuranceServicePriceVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.StockVehicleBusyVO;

import java.util.List;
import java.util.Map;

/**
 * 订单相关接口
 *
 * @auther musi
 * @date 2022/10/20 17:18
 */
public interface IOrderService {

    /**
     * 算价
     *
     * @param priceCalVo
     * @return
     */
    Result<List<VehiclePriceAbbrVo>> priceCalForSaas(VehicleModelPriceCalQueryParam priceCalVo, Map<String, DailyPriceListDTO> servicePriceMap);

    /**
     * 新建订单
     *
     * @param createOrderVo
     * @return
     */
    Result<Long> createOrder(CreateOrderVo createOrderVo, LoginVo loginVo);

    /**
     * 根据订单查询参数获取订单信息列表
     *
     * @param param
     * @return
     */
    Result<OrderInfoListVo> getOrderList(OrderInfoParam param, LoginVo loginVo);

    /**
     * 获取RPA订单信息列表
     * @param orderInfoVos SAAS订单列表
     * @param loginVo 登录信息
     * @return
     */
    List<OrderInfoVo> getRPAOrderList(List<OrderInfoVo> orderInfoVos, LoginVo loginVo);

    /**
     * 获取RPA订单信息
     * @param orderDetailOrderInfoVo SAAS订单信息
     * @param loginVo 登录信息
     * @return
     */
    RPAOrderInfoVo getRPAOrder(OrderDetailOrderInfoVo orderDetailOrderInfoVo, LoginVo loginVo);


    Result<OrderInfoListVo> getOrderListV2(OrderInfoParam param, LoginVo loginVo);


    /**
     * 获取订单信息
     *
     * @param orderId
     * @return
     */
    Result<OrderInfoVo> getOrderInfo(Long orderId);


    Result<OrderInfoVo> getOrderInfo(String sourceOrderId, Long merchantId);


    Result<List<OrderInfoVo>> getOrderInfoList(List<String> sourceOrderId, Long merchantId);
    Result<List<OrderInfoVo>> getOrderInfoByIdList(List<Long> ids, Long merchantId);

    /**
     * 根据订单号获取订单信息
     *
     * @param param
     * @return
     */
    Result<List<OrderInfoVo>> getOrderBaseList(OrderInfoParam param, LoginVo loginVo);

    /**
     * 根据订单Id获取费用详细
     *
     * @param orderId
     * @return
     */
    Result<OrderDetailVo> getOrderDetail(Long orderId);


    /**
     * 长订单详细
     * @param orderId
     * @return
     */
    Result<LongOrderDetailVo> getLongOrderDetail(Long orderId);

    /**
     * 订单账单明细
     *
     * @param orderId
     * @param source
     * @return
     */
    Result<OrderBillDetailVo> getOrderBillDetail(Long orderId, Byte source);

    /**
     * 订单车辆违章信息
     *
     * @param orderId
     * @return
     */
    Result<List<VehicleIllegalOrderVO>> getIllegalOrder(Long orderId);

    /**
     * 订单车辆车损信息
     *
     * @param orderId
     * @return
     */
    Result<List<VehicleDamageOrderVO>> getDamageOrder(Long orderId);

    /**
     * 获取押金政策
     *
     * @param orderId 订单id
     * @return
     */
    Result<OrderDepositPolicyVo> getDepositPolicy(Long orderId);

    /**
     * 获取取车押金
     *
     * @param orderId 订单id
     * @return
     */
    Result<PickupOrderDepositVo> getPickupDeposit(Long orderId);

    /**
     * 获取订单备注
     *
     * @param orderId 订单id
     * @return
     */
    Result<List<OrderRemarkVo>> getOrderRemarkList(Long orderId);

    /**
     * 保存订单备注
     *
     * @param orderId  订单id
     * @param remark   备注
     * @param remarkId 备注id
     * @param urlList 图片
     * @param opUserId 操作人id
     * @return
     */
    Result<Boolean> saveOrderRemark(Long orderId, Long remarkId, String remark, List<String> urlList, Long opUserId);

    /**
     * 获取取消政策
     *
     * @param orderId       订单id
     * @param pickupDate    原取车时间
     * @param amount        订单金额，不传的话取订单金额
     * @param rerentOrderId 续租单id，可以为空
     * @param isReal        是否取实时的取消政策，如果是false(快照)，那么
     * @return
     */
    Result<List<OrderCancelRuleVo>> getCancelRule(Long orderId, Long pickupDate, Integer amount, Long rerentOrderId,
                                                  boolean isReal);

    /**
     * 取消订单
     *
     * @param orderId      订单id
     * @param reduceAmount 减免违约金
     * @param opUserId     用户id
     * @return
     */
    Result<Boolean> cancelOrder(Long orderId, Integer reduceAmount, Long opUserId);

    /**
     * 获取服务套餐
     *
     * @param orderId   订单id
     * @param serviceId 服务项id
     * @return
     */
    Result<InsuranceServicePriceVo> getServiceDetail(Long orderId, Long serviceId);

    /**
     * 增加订单的附加服务项
     *
     * @return
     */
    Result<Boolean> addOrderAddedServiceItem(Long orderId, List<Long> addIdList, List<Long> insuranceIdList,
                                             Long opUserId, Byte payKind);

    /**
     * 订单相关的枚举类
     *
     * @return
     */
    Result<OrderBaseEnumVo> getOrderBaseEmun();

    /**
     * 变更订单状态
     *
     * @param orderId     订单id
     * @param orderStatus 订单状态
     * @param opUserId    操作人id
     */
    Result<Boolean> updateOrderStatus(Long orderId, Byte orderStatus, Long opUserId);

    /**
     * 续租算价
     *
     * @param orderId
     * @param rerentTime
     * @return
     */
    Result<RerentPrecalculateVo> rerentPrecalculateForSaas(Long orderId, Long rerentTime);

    /**
     * 续租算价
     *
     * @param orderId
     * @param rerentTime
     * @return
     */
    Result<VehicleModelPriceAbbrVo> rerentPrecalculateForThird(Long orderId, Long rerentTime, Long source);

    /**
     * 创建续租订单
     *
     * @param orderId
     * @param rerentTime
     * @param rerentTime
     * @return
     */
    Result<Long> createRerentOrder(Long orderId, Long rerentTime, boolean forcibly, Byte reduceType,
                                   Boolean isDelayReturn, Integer reduceAmount, Long opUserId, Integer payKind);

    /**
     * 改排车
     *
     * @param orderId
     * @param vehicleId
     * @param changeSelfPrOrder
     * @return
     */
    Result<Boolean> updatePlanVehicle(Long orderId, Long vehicleId, Long vehicleModelId, LoginVo opUser, Integer changeSelfPrOrder);

    /**
     * 强制改排车
     *
     * @param orderId
     * @param vehicleId
     * @param changeSelfPrOrder
     * @return
     */
    Result<Boolean> forceUpdatePlanVehicle(Long orderId, Long vehicleId, LoginVo loginVo, Integer changeSelfPrOrder);

    /**
     * 续租记录
     *
     * @param orderId
     * @return
     */
    Result<List<RerentOrderVo>> getRerentOrderList(Long orderId);

    Result<RerentOrderVo> getRerentOrder(Long rerentOrderId);

    /**
     * 租车订单服务项
     *
     * @param orderId
     * @return
     */
    Result<List<ServiceItemAmountVo>> getRerentServiceList(Long orderId);

    /**
     * 获取附加服务列表，将订单中已选的服务项打标
     *
     * @param orderId
     * @return
     */
    Result<List<ServiceItemAmountVo>> getAddedServiceList(Long orderId);

    /**
     * 取消续租单
     *
     * @param rerentOrderId
     * @param rerentOrderId
     * @return
     */
    Result<Boolean> cancelRerentOrder(Long rerentOrderId, Long opUserId, boolean isThird);

    /**
     * 排车
     *
     * @param orderId        订单id
     * @param vehicleInfoId  车辆id
     * @param vehicleModelId 车型id
     * @param opUserId       操作人id
     */
    Result<Integer> savePlanVehicle(Long orderId, Long vehicleInfoId, Long vehicleModelId, Long opUserId);

//    /**
//     * 查询订单车辆的冲突情况
//     */
//    Result<List<StockVehicleBusyVO>> listConflictBusyVehicle(Long orderId, Long vehicleInfoId);

    /**
     * 查询商家所有的服务项
     *
     * @param merchantId
     * @return
     */
    Result<ServiceItemListV2Vo> getServiceAll(Long merchantId);

    /**
     * 新的算价接口
     *
     * @param priceCalVo
     * @return
     */
    Result<List<VehiclePriceAbbrVo>> newPriceCalForSaas(VehicleModelPriceCalQueryV2Param priceCalVo);

    /**
     * 修复order和ordermember姓名不同数据
     *
     * @return
     */
    Result<Boolean> fixOrderMemberDate();

    /**
     * 初始化分期信息
     *
     * @param orderInstalmentConfigVo
     * @return
     */
    Result<List<OrderInstalmentVo>> initInstalment(OrderInstalmentConfigVo orderInstalmentConfigVo);

    /**
     * 长订单分期计划
     *
     * @param orderId
     * @return
     */
    Result<List<OrderInstalmentVo>> instalmentList(Long orderId);

    /**
     * 获取分期付款页面数据
     *
     * @param orderId
     * @return
     */
    Result<OrderInstalmentVo> getInstalmentPay(Long orderId);

    /**
     * 保存分期付款支付
     *
     * @param orderId
     * @param instalmentId
     * @param payAmount
     * @param remark
     * @param userId
     * @return
     */
    Result<Boolean> saveInstalmentPay(Long orderId, Long instalmentId, Integer payAmount, String remark, Long userId);

    /**
     * 更新长订单押金金额
     * @param orderId
     * @param type 0：租车押金；1：违章押金
     * @param amount
     * @param opUserId
     * @return
     */
    Result<Boolean> updateLongOrderDeposit(Long orderId, Byte type, Integer amount, Long opUserId);

    /**
     * 违约金退款
     * @param orderId 订单号
     * @param amount 退款金额
     * @return
     */
    Result<Boolean> penaltyRefund(Long orderId, Integer amount, Long opUserId);

    /**
     * 更新订单车辆
     * @param list
     * @return
     */
    Result<Boolean> updateOrderVehicleId(List<UpdateOrderVehicleDTO> list);


    /**
     * 直连商家写入订单车辆
     * 背景描述：上汽项目下单后，未明确订单使用的车辆。 需要在后续库存同步的时候，把车辆信息写入订单
     * 步骤:
     * 1.saas订单生成
     * 2.推送订单消息到上汽，等待上汽排除
     * 3.上汽排车，上汽同步库存给saas
     * 4.saas感知到库存后，调用此方法将车辆信息写入订单
     */
    Result<Boolean> updateOrderVehicleIdForThird(List<UpdateOrderVehicleForthirdDTO> list);

    /**
     * 校验订单是否取车
     * 取车之后的状态都算取车
     * @param list
     * @return
     */
    Result<Map<String, Boolean>> checkOrderPickup(List<UpdateOrderVehicleDTO> list);


    /**
     * 批量获取订单费用明细
     * @param orderInfoVos
     * @return
     */
    Result<Map<Long, OrderFeeVo>> getOrderExcelList(List<OrderInfoVo> orderInfoVos);



    /**
     * 批量获取订单费用明细
     * @param orderIdList
     * @return
     */
    Result<Map<Long, OrderFeeVo>> getOrderExcelListById(List<Long> orderIdList);


    /**
     * 订单调价
     * @param orderId
     * @param discountType
     * @param discountAmount
     * @param userId
     * @return
     */
    Result<Boolean> adjustOrderAmount(Long orderId, Byte discountType, Integer discountAmount, Long userId);


    /**
     * 根据车辆ID更新订单的车型信息
     * @param vehicleId
     * @param userId
     * @return
     */
    Result<Boolean> updateOrderVehicleModelByVehicleId(Long merchantId, Long vehicleId, Long userId);


    /**
     * 是否改排订单
     * @param orderId
     * @return
     */
    Result<Boolean> isUpdatePlanVehicle(Long orderId);


    OrderDetailMsgDTO getOrderDetailMsg(Long orderId);

    /**
     * 更新订单支付状态
     */
    Result<Boolean> updateOrderPayStatus(Long orderId, Integer payStatus, Integer amount, String payNo);


    Result<Boolean> updateRerentOrderPayStatus(Long rerentOrderId, Integer payStatus, Integer amount, String payNo);

    /**
     * 解冻订单押金
     */
    Result<Boolean> unfreezeOrderDeposit(Long orderId, LoginVo loginVo);

    /**
     * 退款押金
     */
    Result<Boolean> depositRefund(Long orderId, Long refundAmount, LoginVo loginVo, String payNo);

    /**
     * 订单退款
     */
    Result<Boolean> refundOrder(Long orderId, LoginVo loginVo, Long amount, Long deductionAmount);


    void changeSelfPrOrder(Long orderId, Long merchantId, Byte selfPrOrder);

    /**
     * 获取支付url
     * @param orderId 订单id
     * @param type 付款码场景类型：1=创建订单，2=取车加购，3=续租，4=还车
     * @return
     */
    Result<OrderPayUrlDTO> getPayUrl(Long orderId, Byte type, PayFeeItemsVo payFeeItemsVo);

    /**
     * 查询支付结果
     *
     * @param orderId
     * @param payNo
     * @return
     */
    Result<QueryPayResultVo> queryPayResult(Long orderId, String payNo);

    /**
     * 取消服务并退款
     * @param orderId
     * @param type
     * @param itemId
     * @param userId
     * @return
     */
    Result<Boolean> cancelService(Long orderId, Byte type, Long itemId, Long userId);

    /**
     * 取消订单支付
     * @param orderId
     * @param payNo
     * @return
     */
    Result<Boolean> cancelOrderPay(Long orderId, String payNo);

    /**
     * 关闭订单
     * @param
     * @return
     */
    Result<Boolean> closeOrder(Long orderId);


    /**
     * 关闭续租订单
     * @param
     * @return
     */
    Result<Boolean> closeRerentOrder(Long rerentOrderId);
}
