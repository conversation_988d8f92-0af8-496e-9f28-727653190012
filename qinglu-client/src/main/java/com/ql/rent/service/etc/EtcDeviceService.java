package com.ql.rent.service.etc;

import com.ql.rent.param.etc.EtcDeviceReq;
import com.ql.rent.param.etc.EtcOrderChargeReq;
import com.ql.rent.param.etc.EtcOrderChargeResp;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.etc.*;

import java.util.List;
import java.util.Map;

public interface EtcDeviceService {

    /**
     * 车辆etc设备列表
     * @param req
     * @return
     */
    Result<PageListVo<EtcDeviceVo>> obtainDeviceList(EtcDeviceReq req);

    /**
     * 【自营新增】车辆ETC管理列表页
     * @param req
     * @return
     */
    Result<PageListVo<EtcVehicleVo>> obtainEtcVehicleList(EtcDeviceReq req);

    /**
     * 【自营新增】根据id查询etc车辆详情信息
     * @param req
     * @return
     */
    Result<EtcVehicleVo> getEtcVehicleById(Long etcVehicleId, Long merchantId);

        /**
         * 查询etc设备列表
         */
    Result<List<EtcDeviceVo>> listDevice(EtcDeviceReq req);

    /**
     * 订单列表
     * @param req
     *
     * @return
     */
    Result<PageListVo<EtcOrderChargeVo>> obtainRevenueDetails(EtcDeviceReq req);

    /**
     * 获取可提现金额
     * @param req
     * @return
     */
    Result<EtcOrderChargeResp> obtainWithdrawalAmount(EtcOrderChargeReq req);

    /**
     * 提现按钮
     * @param req
     * @return
     */
    Result<Boolean> withdrawal(EtcOrderChargeReq req);
    /**
     * 新增设备列表
     */
    Result<Boolean> saveDevice(EtcDeviceVo req);

    /**
     * 记录通行记录 并计算商家分润
     * @param db
     */
    Result<Boolean> saveEtcOrderTraffic(EtcOrderTollFeeVo db);

    /**
     * 处理订单消息  更新 订单状态 租赁费 or 新增好人端etc订单
     * @param vo
     */
    void saveOrderNotify(EtcOrderChargeVo vo);

    /**
     * 获取提现明细
     * @param req
     * @return
     */
    Result<PageListVo<WithdrawalRecordInfoVo>> obtainWithdrawal(EtcOrderChargeReq req);
    /**
     * 订单列表 查询 默认 查询 500条
     * @param req
     * @return
     */
    Result<List<String>> searchEtcOrderList(EtcOrderChargeReq req);

    /**
     * 厂商查询 订单还车时间和订单渠道
     * @param merchantId
     * @param query
     * @return
     */
    EtcOrderChargeVo orderQuery(Long merchantId,Long vehicle, String etcOrderId);

    /**
     * 根据提现单 导出 提现明细
     * @param merchantId
     * @param id
     * @return
     */
    List<EtcOrderChargeVo> exportWithdrawalIncome(Long merchantId,  Long id);

    /**
     * 根据saas订单 orderId 查询 etcOrderId 明细
     * @param merchantId
     * @param saasOrderId
     * @return
     */
    List<OrderCostDetailsVo> etcOrderDetails(Long merchantId, Long saasOrderId);

    /**
     * 取消saas订单
     * @param merchantId
     * @param id
     * @return
     */
    boolean cancelEtcOrder(Long merchantId, Long id);

    /**
     * 推送 商家信息
     * @param merchantId
     */
    void pushStore(Long merchantId);

    /**
     * 车辆上线架
     * @param vehicleId
     * @param merchantId
     */
    void pushVehicleUnmount(Long vehicleId, Boolean upFlag, Long merchantId);

    /**
     * 取消etc订单
     * @param id
     * @return
     */
    EtcOrderChargeVo obtainOrderChargeInfo(Long id);

    /**
     * 异步取消 etc订单
     */
    void asynchronousCancelEtcOrderTask( Long merchantId,Long saasOrder,Long vehicleId );

    /**
     * 获取用户已支付方式
     * @param merchantId
     * @return
     */
    Map<Byte,String> obtainPaymentMethod(Long merchantId);

    /**
     * 新增样本数据
     * @param samplesVo
     * @return
     */
    EtcDeviceVo addSamples(EtcDeviceVo samplesVo);

    /**
     * 根据车牌号 检索
     * @param samplesVo
     * @return
     */
    Result<List<EtcDeviceVo>> retrieveSampleEquipment(EtcDeviceVo samplesVo);

    /**
     * 根据样本 拷贝数据
     * @param samplesVo
     * @return
     */
    Result<Boolean> addCopySample(EtcDeviceVo samplesVo,Long opUserId);

    /**
     * 异步推送 etc 订单 信息 给携程
     * @param etcOrder
     */
    void aSynPushETCInfoToTrip(String etcOrder,Long vehicleId);

    /**
     * 异步结束 etc订单（擎路自营）
     */
    void asyncEndSaasEtcOrder(Long saasOrder);

    /**
     * saas自营处理订单消息  更新 订单状态 租赁费 or 新增好人端etc订单
     * @param vo
     */
    void saveOrderNotifySaasEtc(EtcOrderChargeVo vo);
}
