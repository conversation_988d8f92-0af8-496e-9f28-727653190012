package com.ql.rent.service.trade;

import com.ql.rent.share.result.Result;
import lombok.Data;

import java.util.List;

/**
 * 修复数据方法
 *
 * @auther musi
 * @date 2023/5/7 20:42
 */
public interface IOrderFixDataService {


    /**
     * 修复数据方法
     * 订单改排后，订单快照的车辆数据修复
     * @param orderId
     * @return
     */
    Result<Boolean> fixOrderSnapshot(Long orderId, Long vehicleId);


    /**
     * 清除订单快照同步多余数据
     * @return
     */
    Result<Boolean> delOrderSnapshot();

    /**
     * 添加订单快照
     * @param orderId
     * @return
     */
    Result<Boolean> addOrderSnapshot(Long orderId);


    /**
     * 订单消息推送
     * @param orderId
     * @return
     */
    Result<Boolean> orderMsgPush(Long orderId);


    /**
     * 临时需求：统计一下特殊的订单数量
     */
    Result<String> countOverTimeOriginOrder();

    /**
     * 补偿历史数据
     */
    Result<Integer> fixHistoryFinanceReport(FixHistoryParam param);





    Result<List<Long>> findWukongErrorAmountOrderIds(Long merchantId, Long start, Long end);


    Result<Boolean> syncChannelOrder(Long merchantId, Long channelId, String sourceOrderId);




    void fixTcOrderAmount(List<String> orderIds);


    void fixXcOrderDetail(Long merchantId);



    @Data
    class FixHistoryParam {
        private List<Integer> months;
        private List<Long> merchantIds;
    }
}
