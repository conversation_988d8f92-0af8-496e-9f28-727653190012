package com.ql.rent.service.vehicle;

import com.ql.dto.open.request.vehicle.VehicleStatusNotifyRequest;
import com.ql.dto.vehicle.VehicleInfoDTO;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.dto.vehicle.request.GetlVehicleRequest;
import com.ql.rent.api.aggregate.model.request.VehicleStatusNotifyReq;
import com.ql.rent.enums.trade.ServiceItemListVo;
import com.ql.rent.param.vehicle.VehicleMediaQueryParam;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.price.RentMainVo;
import com.ql.rent.vo.vehicle.VehicleMediaVO;
import com.ql.rent.vo.vehicle.VehicleModelVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @desc: 第三方渠道对接接口（车型&车辆）内部调用
 * @author: pijiu
 * @time: 2022-10-25 21:09
 * @Version: 1.0
 */
public interface IThirdVehicleService {

    /**
     * 查询车型价格日历
     *
     * @param storeAndModelIds
     * @param pickUpTime
     * @param returnTime
     * @param platformCal
     * @return
     */
    List<VehicleModelUniDTO<List<DailyPriceDTO>>> selectModelCalendarPrice(Long merchantId, Long channelId, List<VehicleModelUniDTO> storeAndModelIds, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId, String debugInfo, List<String> outMsgMap, boolean platformCal);
    void selectModelCalendarPrice(Map<Integer, List<VehicleModelUniDTO<List<DailyPriceDTO>>>> modelPriceMap, Long merchantId, Long channelId, List<VehicleModelUniDTO> storeAndModelIds, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId);
    /**
     * 查询车型价格日历
     *
     * @param storeAndModelId
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    List<DailyPriceDTO> selectModelCalendarPrice(Long merchantId, Long channelId, VehicleModelUniDTO storeAndModelId, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId, boolean platformCal);

    /**
     * 查询车型服务项
     *
     * @param storeAndModelIds
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    List<VehicleModelUniDTO<List<ServiceItemDTO>>> selectModelService(Long merchantId, List<VehicleModelUniDTO> storeAndModelIds, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas,  String debugInfo, List<String> outMsgMap);
    void selectModelService(Map<Integer, List<VehicleModelUniDTO<List<ServiceItemDTO>>>> modelServiceMap, Long merchantId, List<VehicleModelUniDTO> storeAndModelIds, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas);

    /**
     * 获取门店保险&附加零散规则
     *
     * @param merchantId
     * @param channelId
     * @param storeIds
     * @param pickUpTime
     * @param returnTime
     * @param debugInfo
     * @param platformCal
     * @return
     */
    List<StoreChargeDTO<List<DailyPriceListDTO>>> getStoreCharge(Long merchantId, Long channelId, List<Long> storeIds, Date pickUpTime, Date returnTime, Long orderId, String debugInfo, boolean platformCal);

    /**
     * 查询车型服务项
     *
     * @param storeAndModelId
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    List<ServiceItemDTO> selectModelService(Long merchantId, VehicleModelUniDTO storeAndModelId, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas);

    /**
     * 查询车型其他信息
     * <p>
     * *******说明*******
     * mileage 里程限制，仅飞猪返回 -1:不限制里程
     *
     * @param storeAndModelIds
     * @return
     */
    List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> selectModelOther(List<VehicleModelUniDTO> storeAndModelIds, Long channelId, List<RentMainVo> rentSellList, Long merchantId);

    /**
     * 查询车型其他信息
     *
     * @param storeAndModelId
     * @return
     */
    VehicleModelUniDTO<VehicleModelSubAbbrDTO> selectModelOther(VehicleModelUniDTO storeAndModelId, Long channelId, List<RentMainVo> rentSellList, Long merchantId);

    /**
     * 保险规则取得
     *
     * @param merchantId
     * @return
     */
    List<InsuranceServicePolicyDTO> getModelInsuranceServicePolicy(Long merchantId);

    /**
     * 车型服务项（给SaaS提供）
     *
     * @param merchantId
     * @param vehicleModelId
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    ServiceItemListVo selectModelServiceItemListVo(Long merchantId, Long storeId, Long channelId, Long vehicleModelId, Date pickUpTime, Date returnTime);

    /**
     * 计算零散小时费规则
     *
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    //List<SplitBusiDateVO> splitBusiDate(Long merchantId, Long storeId, Date pickUpTime, Date returnTime);

    /**
     * 查询车型数据
     * @param vehicleId
     * @return
     */
    VehicleModelVO selectModelBase(long vehicleId);
    /**
     * 查询门店渠道下的免押车型
     */
    List<Long> storeUnDeposits(Long channelId, Long storeId);



    /**
     * 车辆状态变更回调
     * @param notifyReq
     */
    void vehicleStatusNotify(VehicleStatusNotifyReq notifyReq, Long merchantId, boolean isRealCar);

    /**
     * 上货二期携程拉取车辆基本信息
     * @param getlVehicleRequest
     * @param merchantId 商家id
     * @return
     */
    Result<List<VehicleInfoDTO>> listVehicleInfo(Long merchantId, Long channeId, GetlVehicleRequest getlVehicleRequest);

    /**
     * 保存车型限价更新消息
     * @param content
     * @return
     */
    void saveVehicleModelLimitPriceUpdate(String content);

    /**
     * 查看车型图片
     * @param queryParam
     * @return
     */
    Result<List<VehicleMediaVO>> listByParam(VehicleMediaQueryParam queryParam);

    /**
     * 新增车辆数据
     * @param merchantId
     * @param vehicleInfo
     * @return
     */
    Result<VehicleInfoDTO> addVehicle(Long merchantId, VehicleInfoDTO vehicleInfo);

    /**
     * 保存哈啰车辆审核通知回调
     */
    Result<Integer> saveHelloVehicleAudit(Long vehicleId, VehicleStatusNotifyRequest request);
}