package com.ql.rent.api.aggregate.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 价格日历.
 *
 * <AUTHOR>
 * @date 2022/9/25 12:12
 */
@Data
public class DailyPriceListDTO {

    private String code;

    private String name;

    List<DailyPriceDTO> list;

    /**
     * 7天封顶比例合计
     */
    private Double high7Per;

    /**
     * 合计比例合计
     */
    private Double totalPer;

    /**
     * 最后一天零散
     */
    private Double loosePer;
}
