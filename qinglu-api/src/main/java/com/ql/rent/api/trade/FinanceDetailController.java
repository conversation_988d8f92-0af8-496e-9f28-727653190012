package com.ql.rent.api.trade;

import com.ql.enums.ThirdPayEnum;
import com.ql.rent.api.excel.FinanceDetailExcelBean;
import com.ql.rent.api.excel.OrderReconciliationExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.trade.FinanceEnum;
import com.ql.rent.param.trade.FinanceDashBoardParam;
import com.ql.rent.param.trade.FinanceQueryParam;
import com.ql.rent.param.trade.OrderReconciliationQueryParam;
import com.ql.rent.service.trade.IFinanceDetailService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2024-07-09 22:21
 */
@Slf4j
@Api(tags = "财务收支明细-相关接口")
@RestController
@RequestMapping("/finance_detail")
public class FinanceDetailController {

    @Resource
    private ITokenService tokenService;
    @Resource
    private IFinanceDetailService financeDetailService;

    @ApiOperation(value = "查询收支明细列表")
    @PostMapping("/v1/list")
    public ResultMap<PageListVo<FinanceDetailPageVO>> listPage(@RequestBody FinanceQueryParam param,
                                                               HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<PageListVo<FinanceDetailPageVO>> result = financeDetailService.listPage(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("FinanceIncomeExpenseDetailController listPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "统计收支明细情况")
    @PostMapping("/v1/statistics")
    public ResultMap<FinanceStatisticsVO> statisticsFinance(@RequestBody FinanceQueryParam param,
                                                            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<FinanceStatisticsVO> result = financeDetailService.statisticsFinance(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("FinanceIncomeExpenseDetailController statisticsFinance", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "财务概览视图")
    @PostMapping("/v1/dashboard")
    public ResultMap<FinanceDashBoardVO> getFinanceDashBoard(@RequestBody FinanceDashBoardParam param,
                                                             HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<FinanceDashBoardVO> result = financeDetailService.getFinanceDashBoard(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("FinanceIncomeExpenseDetailController getFinanceDashBoard", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "短租对账-导出明细", notes = "短租对账-导出明细")
    @PostMapping("/v1/excel")
    public void downOrderReconciliation(@RequestBody FinanceQueryParam queryParam,
                                        HttpServletRequest request, HttpServletResponse response) {

        try {
            LoginVo opUser = tokenService.getUserByRequest(request);

            // 使用流式下载，避免一次性加载大量数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "财务收支明细",
                    "财务收支明细",
                    FinanceDetailExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<List<FinanceDetailPageVO>> result = financeDetailService.listExcel(queryParam, opUser);
                        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getModel())) {
                            return new ArrayList<>();
                        }

                        return result.getModel();
                    },
                    // 明确指定参数类型为FinanceDetailPageVO
                    (FinanceDetailPageVO vo) -> {
                        FinanceDetailExcelBean bean = new FinanceDetailExcelBean();

                        // 直接赋值，避免使用反射
                        bean.setTypeStr(FinanceEnum.FinanceInOutComeEnum.getName(vo.getType()));
                        bean.setFinanceTypeStr(FinanceEnum.FinanceTypeEnum.getName(vo.getFinanceType()));
                        bean.setFinanceDetailTypeStr(FinanceEnum.FinanceDetailTypeEnum.getName(vo.getFinanceDetailType()));
                        bean.setTotalAmountStr(amountToStr(vo.getTotalAmount()));
                        bean.setSettledAmountStr(amountToStr(vo.getSettledAmount()));
                        bean.setRemark(vo.getRemark());
                        bean.setSettlementStatus(FinanceEnum.ReconciliationSettlementStatusEnum.getName(vo.getSettlementStatus()));
                        bean.setPaySourceStr(ThirdPayEnum.ThirdPaySource.getNameBySource(vo.getThirdPaySource()));

                        // 处理订单信息
                        OrderInfoVo orderInfo = vo.getOrderInfo();
                        if (orderInfo != null) {
                            bean.setVehicleNo(orderInfo.getVehicleNo());
                            bean.setOrderUserName(orderInfo.getUserName());
                            bean.setOrderSourceId(orderInfo.getSourceOrderId());
                            bean.setOrderId(orderInfo.getId());
                            bean.setStoreName(vo.getStoreName());
                        }

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("listReconciliation downOrderReconciliation {}", e.getMessage());
        }
    }

    /**
     * 金额转字符串辅助方法
     */
    private static String amountToStr(Long amount) {
        if (amount == null) {
            return "--";
        }
        return amount / 100 + "." + amount % 100;
    }

    @ApiOperation(value = "财务相关枚举")
    @PostMapping("/v1/enums")
    public ResultMap<FinanceBaseEnumVO> getFinanceEnums(HttpServletRequest httpServletRequest) {
        try {
            return ApiResultUtil.successResult(FinanceBaseEnumVO.getInstance());
        } catch (Exception e) {
            log.error("FinanceIncomeExpenseDetailController getFinanceEnums", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
