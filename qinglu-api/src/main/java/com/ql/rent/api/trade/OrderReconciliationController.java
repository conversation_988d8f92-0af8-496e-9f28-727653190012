package com.ql.rent.api.trade;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Lists;
import com.ql.rent.api.excel.*;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.IRedisService;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.trade.FinanceEnum;
import com.ql.rent.enums.trade.FreeDepositTypeEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.enums.trade.OrderStatusEnum;
import com.ql.rent.param.BaseQuery;
import com.ql.rent.param.trade.OrderInfoParam;
import com.ql.rent.param.trade.OrderReconciliationQueryParam;
import com.ql.rent.param.trade.OrderSettlementParam;
import com.ql.rent.service.trade.IOrderReconciliationService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.share.result.*;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleTagVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.ql.rent.api.trade.OrderController.buildOrderBaseExcelBean;

/**
 * 订单对账相关接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/order/reconciliation")
@Api(tags = "订单对账相关接口")
public class OrderReconciliationController {

    @Resource
    private IOrderReconciliationService orderReconciliationService;
    @Resource
    private ITokenService tokenService;
    @Resource
    private IOrderService orderService;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private IRedisService redisService;

    @ApiOperation(value = "查询渠道抽佣比例")
    @PostMapping("/commission_rates")
    public ResultMap<PageListVo<OrderCommissionSettingVO>> listStoreCommissionSetting(@RequestBody BaseQuery param,
                                                                                   HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<PageListVo<OrderCommissionSettingVO>> result =
                orderReconciliationService.listStoreCommissionSetting(param, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("listStoreReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "修改渠道抽佣比例")
    @PostMapping("/save_commission_rates")
    public ResultMap<Integer> saveCommissionSetting(@RequestBody List<OrderCommissionSettingVO> settings,
                                                      HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<Integer> result =
                orderReconciliationService.saveCommissionSetting(settings, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("saveCommissionSetting error", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "查询对账明细列表")
    @PostMapping("/list")
    public ResultMap<PageListVo<OrderReconciliationVO>> listReconciliation(
        @RequestBody OrderReconciliationQueryParam queryParam,
        HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<PageListVo<OrderReconciliationVO>> result = orderReconciliationService.listReconciliationPage(queryParam, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("listReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "统计对账状态数量")
    @PostMapping("/statistics")
    public ResultMap<OrderReconciliationStatisticsVO> countOrderReconciliation(@RequestBody OrderReconciliationQueryParam queryParam,
                                                                         HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<OrderReconciliationStatisticsVO> result =
                orderReconciliationService.countOrderReconciliation(queryParam, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("countOrderReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "结算单笔对账")
    @PostMapping("/settlement")
    public ResultMap<Integer> settleReconciliation(@RequestBody OrderSettlementParam param,
                                                   @RequestParam(name = "partSettlement", defaultValue = "0")
                                                   @ApiParam("是否部分结算" ) Byte partSettlement,
                                            HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<Integer> result = orderReconciliationService.settleReconciliation(param, partSettlement, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("settleReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "批量结算对账")
    @PostMapping("/batch_settlement")
    public ResultMap<Integer> batchSettleReconciliation(@RequestBody OrderSettlementParam param,
                                                        @RequestParam(name = "partSettlement", defaultValue = "0")
                                                        @ApiParam("是否部分结算") Byte partSettlement,
                                                        HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<Integer> result = orderReconciliationService.batchSettleReconciliation(param, partSettlement, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("batchSettleReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * todo 现将订单导出的逻辑复制到这里。
     */
    @ApiOperation(value = "短租对账-导出明细", notes = "短租对账-导出明细")
    @PostMapping("/excel")
    public void downOrderReconciliation(@RequestBody OrderReconciliationQueryParam queryParam,
                                        HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);

            // 设置标题和文件名
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("短租对账", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            try (ServletOutputStream outputStream = response.getOutputStream();
                 ExcelWriter excelWriter = EasyExcel.write(outputStream)
                         .registerWriteHandler(EasyExcelUtils.defaultStyles())
                         .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                         .build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(fileName).head(OrderExcelBean.class).build();

                // 使用分页方式获取对账数据，避免一次性加载全部数据
                int pageSize = 500;
                int pageIndex = 1;
                int totalCount = 0;
                int maxRecords = 10000; // 最多处理10000条记录

                queryParam.setPageSize(pageSize);

                while (true) {
                    queryParam.setPageIndex(pageIndex);

                    // 分页获取对账数据
                    Result<List<OrderReconciliationVO>> pageResult =
                            orderReconciliationService.listReconciliationExcel(queryParam, loginVo);

                    if (!ResultUtil.isModelNotNull(pageResult) ||
                            CollectionUtils.isEmpty(pageResult.getModel())) {
                        // 没有更多数据
                        break;
                    }

                    List<OrderReconciliationVO> reconciliationVOS = pageResult.getModel();

                    // 从当前页对账记录中提取订单ID
                    List<Long> orderIds = reconciliationVOS.stream()
                            .map(OrderReconciliationVO::getOrderId)
                            .distinct()
                            .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(orderIds)) {
                        pageIndex++;
                        continue;
                    }

                    // 获取订单详情
                    OrderInfoParam orderInfoParam = new OrderInfoParam();
                    orderInfoParam.setMerchantId(loginVo.getMerchantId());
                    orderInfoParam.setPageSize(orderIds.size());
                    orderInfoParam.setPageIndex(1);
                    orderInfoParam.setIdList(orderIds);
                    orderInfoParam.setNeedCount(false);

                    Result<OrderInfoListVo> orderResult = orderService.getOrderList(orderInfoParam, loginVo);
                    if (!orderResult.isSuccess() ||
                            CollectionUtils.isEmpty(orderResult.getModel().getCurrentList())) {
                        pageIndex++;
                        continue;
                    }

                    // 处理当前批次的订单数据
                    List<OrderInfoVo> orderList = orderResult.getModel().getCurrentList();
                    List<OrderBaseExcelBean> excelData = processOrderBatch(
                            orderList, orderInfoParam.getOrderType());

                    // 写入Excel
                    excelWriter.write(excelData, writeSheet);

                    totalCount += orderList.size();
                    log.info("短租对账-导出明细, 已处理订单数量: {}", totalCount);

                    // 达到最大处理记录数或没有更多数据时结束
                    if (totalCount >= maxRecords || reconciliationVOS.size() < pageSize) {
                        break;
                    }

                    pageIndex++;
                }

                log.info("短租对账-导出明细, 导出完成，总数: {}", totalCount);
            }
        } catch (Exception e) {
            log.error("downOrderReconciliation error {}", e.getMessage());
        }
    }

    /**
     * 处理订单批次数据
     */
    private List<OrderBaseExcelBean> processOrderBatch(List<OrderInfoVo> orderList, Byte orderType) {
        List<OrderBaseExcelBean> result = new ArrayList<>();

        // 处理普通订单
        if (orderType == null || orderType == 0) {
            Result<Map<Long, OrderFeeVo>> feeResult = orderService.getOrderExcelList(orderList);

            for (OrderInfoVo vo : orderList) {
                OrderExcelBean bean = new OrderExcelBean();
                buildOrderBaseExcelBean(vo, bean);
                processOrderExcelBean(vo, bean, feeResult.getModel().get(vo.getId()));
                processCardOrderInfo(vo, bean);
                result.add(bean);
            }
        }
        // 处理长租订单
        else {
            for (OrderInfoVo vo : orderList) {
                LongOrderExcelBean bean = new LongOrderExcelBean();
                buildOrderBaseExcelBean(vo, bean);
                bean.setPayAmount(bean.getPayAmount() / 100);
                bean.setReceivableAmount(bean.getReceivableAmount() / 100);
                result.add(bean);
            }
        }

        return result;
    }

    /**
     * 构建订单基础Excel数据（不使用反射）
     */
    private void buildOrderBaseExcelBean(OrderInfoVo vo, OrderBaseExcelBean bean) {
        // 直接赋值，避免使用反射
        bean.setId(vo.getId());
        bean.setOrderNo(vo.getOrderNo());
        bean.setSourceOrderId(vo.getSourceOrderId());
        bean.setVehicleId(vo.getVehicleId());
        bean.setUserName(vo.getUserName());
        bean.setMobile(vo.getMobile());
        bean.setVehicleNo(vo.getVehicleNo());

        // 设置门店和城市信息
        bean.setPickupStoreName(vo.getPickupStoreName());
        bean.setPickupCity(vo.getPickupCity());
        bean.setReturnStoreName(vo.getReturnStoreName());
        bean.setReturnCity(vo.getReturnCity());

        // 设置地址信息
        bean.setPickupAddr(vo.getPickupAddr());
        bean.setReturnAddr(vo.getReturnAddr());

        // 设置取车方式和还车方式
        bean.setPickupAddrTypeStr(vo.getPickupAddrTypeStr());
        bean.setReturnAddrTypeStr(vo.getReturnAddrTypeStr());

        // 设置司机信息
        bean.setPickupDriver(vo.getPickupDriver());
        bean.setReturnDriver(vo.getReturnDriver());

        bean.setOrderTime(DateUtil.getFormatDateStr(new Date(vo.getOrderTime()), DateUtil.yyyyMMddHHmmss));
        bean.setOrderSource(OrderSourceEnum.getNameByStatus(vo.getOrderSource()));
        bean.setOrderStatus(OrderStatusEnum.getByStatus(vo.getOrderStatus()).getName());
        bean.setPickupDate(DateUtil.getFormatDateStr(vo.getPickupDate(), DateUtil.yyyyMMddHHmmss));
        bean.setReturnDate(DateUtil.getFormatDateStr(vo.getReturnDate(), DateUtil.yyyyMMddHHmmss));
        bean.setFreeDepositDegree(
                FreeDepositTypeEnum.getNameByType(Byte.valueOf(String.valueOf(vo.getFreeDepositDegree()))));
        bean.setTagList(CollectionUtils.isEmpty(vo.getTagList()) ? ""
                : StringUtils.join(
                vo.getTagList().stream().map(VehicleTagVO::getTagName).collect(Collectors.toList()).toArray(),
                ","));
        bean.setOrderTagList(
                CollectionUtils.isEmpty(vo.getOrderTagList()) ? "" : StringUtils.join(vo.getOrderTagList(), ","));

        if (vo.getVehicleModel() != null && StringUtils.isNotBlank(
                vo.getVehicleModel().getVehicleUnionName())) {
            bean.setVehicleName(vo.getVehicleModel().getVehicleUnionName());
        }
        if (vo.getPickupDate() != null && vo.getReturnDate() != null) {
            bean.setRentalPeriod(DateUtil.getDatePoorHours(vo.getPickupDate().getTime(), vo.getReturnDate().getTime()));
        }
    }

    /**
     * 处理订单Excel数据（不使用反射）
     */
    private void processOrderExcelBean(OrderInfoVo vo, OrderExcelBean bean, OrderFeeVo orderFeeVo) {
        // 处理实际取车时间
        if (vo.getActualPickupDate() != null) {
            bean.setActualPickupDate(DateUtil.getFormatDateStr(
                    new Date(vo.getActualPickupDate()), DateUtil.yyyyMMddHHmmss));
        }

        // 处理实际还车时间
        if (vo.getActualReturnDate() != null) {
            bean.setActualReturnDate(DateUtil.getFormatDateStr(
                    new Date(vo.getActualReturnDate()), DateUtil.yyyyMMddHHmmss));
        }

        // 处理费用信息
        if (orderFeeVo != null) {
            // 直接赋值，避免使用反射
            // 租车费
            bean.setOnlineRentAmount(orderFeeVo.getOnlineRentAmount());
            bean.setOfflineRentAmount(orderFeeVo.getOfflineRentAmount());

            // 续租车费
            bean.setOnlineRerentAmount(orderFeeVo.getOnlineRerentAmount());
            bean.setOfflineRerentAmount(orderFeeVo.getOfflineRerentAmount());

            // 手续费
            bean.setOnlineTransactionAmount(orderFeeVo.getOnlineTransactionAmount());
            bean.setOfflineTransactionAmount(orderFeeVo.getOfflineTransactionAmount());

            // 儿童座椅费
            bean.setOnlineChildSeatAmount(orderFeeVo.getOnlineChildSeatAmount());
            bean.setOfflineChildSeatAmount(orderFeeVo.getOfflineChildSeatAmount());

            // 附加服务费
            bean.setOfflineSelfAddedAmount(orderFeeVo.getOfflineSelfAddedAmount());

            // 保险服务费
            bean.setOnlineBaseInsuranceAmount(orderFeeVo.getOnlineBaseInsuranceAmount());
            bean.setOfflineBaseInsuranceAmount(orderFeeVo.getOfflineBaseInsuranceAmount());
            bean.setOnlinePremiumInsuranceAmount(orderFeeVo.getOnlinePremiumInsuranceAmount());
            bean.setOfflinePremiumInsuranceAmount(orderFeeVo.getOfflinePremiumInsuranceAmount());
            bean.setOnlineExclusiveInsuranceAmount(orderFeeVo.getOnlineExclusiveInsuranceAmount());
            bean.setOfflineExclusiveInsuranceAmount(orderFeeVo.getOfflineExclusiveInsuranceAmount());
            bean.setOfflineSelfInsuranceAmount(orderFeeVo.getOfflineSelfInsuranceAmount());

            // 送车上门服务费
            bean.setOnlineDtdPickupAmount(orderFeeVo.getOnlineDtdPickupAmount());
            bean.setOfflineDtdPickupAmount(orderFeeVo.getOfflineDtdPickupAmount());

            // 上门取车服务费
            bean.setOnlineDtdReturnAmount(orderFeeVo.getOnlineDtdReturnAmount());
            bean.setOfflineDtdReturnAmount(orderFeeVo.getOfflineDtdReturnAmount());

            // 异地取车服务费
            bean.setOnlineDiffCityPickAmount(orderFeeVo.getOnlineDiffCityPickAmount());
            bean.setOfflineDiffCityPickAmount(orderFeeVo.getOfflineDiffCityPickAmount());

            // 异地还车服务费
            bean.setOnlineDiffCityReturnAmount(orderFeeVo.getOnlineDiffCityReturnAmount());
            bean.setOfflineDiffCityRetuenAmount(orderFeeVo.getOfflineDiffCityReturnAmount());

            // 夜间服务费
            bean.setOnlineNightAmount(orderFeeVo.getOnlineNightAmount());
            bean.setOfflineNightAmount(orderFeeVo.getOfflineNightAmount());

            // 优惠金额
            bean.setOnlineDiscountAmount(orderFeeVo.getOnlineDiscountAmount());
            bean.setOfflineDiscountAmount(orderFeeVo.getOfflineDiscountAmount());

            // 违约金
            bean.setOnlinePenaltyAmount(orderFeeVo.getOnlinePenaltyAmount());
            bean.setOfflinePenaltyAmount(orderFeeVo.getOfflinePenaltyAmount());

            // 还车收费
            bean.setOnlineChargeAmount(orderFeeVo.getOnlineChargeAmount());
            bean.setOfflineChargeAmount(orderFeeVo.getOfflineChargeAmount());

            // 还车退费
            bean.setOnlineRefundAmount(orderFeeVo.getOnlineRefundAmount());
            bean.setOfflineRefundAmount(orderFeeVo.getOfflineRefundAmount());

            // 补充金额
            bean.setSupplementaryAmount(orderFeeVo.getSupplementaryAmount());

            // 一口价
            bean.setOncePriceAmount(orderFeeVo.getOncePriceAmount());

            // 总金额
            bean.setOnlineTotalAmount(orderFeeVo.getOnlineTotalAmount());
            bean.setOfflineTotalAmount(orderFeeVo.getOfflineTotalAmount());
            bean.setTotalAmount(orderFeeVo.getTotalAmount());

            // 车损金额
            bean.setOnlineDamageAmount(orderFeeVo.getOnlineDamageAmount());
            bean.setOfflineDamageAmount(orderFeeVo.getOfflineDamageAmount());

            // 违章金额
            bean.setOnlineIllegalAmount(orderFeeVo.getOnlineIllegalAmount());
            bean.setOfflineIllegalAmount(orderFeeVo.getOfflineIllegalAmount());

            // 退提前还车费等
            bean.setRefundEarlyReturnAmount(orderFeeVo.getRefundEarlyReturnAmount());
            bean.setRefundFuelAmount(orderFeeVo.getRefundFuelAmount());
            bean.setRefundElectricAmount(orderFeeVo.getRefundElectricAmount());
            bean.setRefundOtherAmount(orderFeeVo.getRefundOtherAmount());

            // 扣费相关字段
            bean.setDeductFuelAndElectricityAmount(orderFeeVo.getDeductFuelAndElectricityAmount());
            bean.setDeductElectricityAmount(orderFeeVo.getDeductElectricityAmount());
            bean.setDeductFuelAmount(orderFeeVo.getDeductFuelAmount());
            bean.setDeductFuelAndElectricityServiceAmount(orderFeeVo.getDeductFuelAndElectricityServiceAmount());
            bean.setDeductRepairAmount(orderFeeVo.getDeductRepairAmount());
            bean.setDeductOtherAmount(orderFeeVo.getDeductOtherAmount());

            // 结算相关字段
            bean.setSettlementStatus(FinanceEnum.ReconciliationSettlementStatusEnum.getName(orderFeeVo.getSettlementStatus()));
            bean.setSettlementUserNames(orderFeeVo.getSettlementUserNames());
            bean.setTotalSettlementAmount(orderFeeVo.getTotalSettlementAmount());
            bean.setSettledAmount(orderFeeVo.getSettledAmount());
            // 备注
            bean.setRemark(orderFeeVo.getRemark());
        }

        // 设置油电量和里程
        bean.setPickupOilLiter(vo.getPickupOilLiter());
        bean.setReturnOilLiter(vo.getReturnOilLiter());
        bean.setPickupMileage(vo.getPickupMileage());
        bean.setReturnMileage(vo.getReturnMileage());

        // 设置续租次数
        bean.setRerentCount(vo.getRerentCount());

        // 处理爬虫订单标记
        if (vo.getIsReptile() != null && vo.getIsReptile().intValue() == 1
                && StringUtils.isNotBlank(bean.getOrderSource())) {
            bean.setOrderSource(bean.getOrderSource() + "同步");
        }

        // 计算实际租期
        if (vo.getActualPickupDate() != null && vo.getActualReturnDate() != null) {
            bean.setActualRentalPeriod(
                    DateUtil.getDatePoorHours(vo.getActualPickupDate(), vo.getActualReturnDate()));
        }

        // 处理爬虫订单标记
        if (vo.getIsReptile() != null && vo.getIsReptile().intValue() == 1
                && StringUtils.isNotBlank(bean.getOrderSource())) {
            bean.setOrderSource(bean.getOrderSource() + "同步");
        }
    }

    /**
     * 处理卡订单信息
     */
    private void processCardOrderInfo(OrderInfoVo vo, OrderExcelBean bean) {
        if (vo.getCardOrderExt() != null) {
            // 设置卡标签
            if (Objects.equals("1", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("次卡");
            } else if (Objects.equals("2", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("天卡");
            } else if (Objects.equals("3", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("随心租卡");
            }

            // 复制卡订单信息
            bean.setCardOrderId(vo.getCardOrderExt().getCardOrderId());
            bean.setCardId(vo.getCardOrderExt().getCardId());
            bean.setCardTradeNo(vo.getCardOrderExt().getCardTradeNo());
            bean.setBasicRentFee(vo.getCardOrderExt().getBasicRentFee());
            bean.setBasicServiceFee(vo.getCardOrderExt().getBasicServiceFee());
            bean.setPoundageFee(vo.getCardOrderExt().getPoundageFee());
        }
    }

    @ApiOperation(value = "批量导入携程账单")
    @PostMapping("/v1/import")
    public ResultMap importVehicleInfo(MultipartFile file, HttpServletRequest request,  HttpServletResponse response) {
        LoginVo user = tokenService.getUserByRequest(request);
        String lockKey = "order:excelSettlement_" + user.getMerchantId();
        try {
            if (redisService.setnx(lockKey, 30L) > 1) {
                return ApiResultUtil.failResult("导入结算执行中，请您稍后再试");
            }
            CtripOrderReconciliationImportListener listener =
                CtripOrderReconciliationImportListener.getInstance(orderReconciliationService, asyncPromiseExecutor, user);
            EasyExcel.read(file.getInputStream(), CtripOrderReconciliationImportExcelBean.class, listener)
                .sheet("分账单明细").headRowNumber(3).doRead();
            EasyExcelUtils.downLoad(response, listener.getErrorResults(), CtripOrderReconciliationImportExcelBean.class, "短租对账");
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("downOrderReconciliation error", e);
            return ApiResultUtil.successResult(1);
        } finally {
            redisService.remove(lockKey);
        }
    }

    /**
     * 手动补偿的口子，用于手动创建对账单
     */
    @PostMapping("/create")
    public ResultMap<Integer> createReconciliation(@RequestParam(name = "orderId") Long orderId,
                                                        HttpServletRequest request) {
        try {
            tokenService.getUserByRequest(request);
            Result<Integer> result = orderReconciliationService.createReconciliation(orderId);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("batchSettleReconciliation error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修改备注")
    @PostMapping("/upd_remark")
    public ResultMap<Integer> updReconciliationRemark(@RequestBody OrderReconciliationVO param,
                                                   HttpServletRequest request) {
        try {
            LoginVo opUser = tokenService.getUserByRequest(request);
            Result<Integer> result = orderReconciliationService.updReconciliationRemark(param, opUser);
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("updReconciliationRemark error", e);
            return ApiResultUtil.failResult(e);
        }
    }


}
