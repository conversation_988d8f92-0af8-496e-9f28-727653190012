package com.ql.rent.api.common;

import com.ql.rent.common.ITokenService;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IChannelService;
import com.ql.rent.service.common.IHelloChannelInitService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @desc: 渠道信息相关控制器
 * @author: zhengminjie
 * @time: 2022-09-21 22:15
 * @Version: 1.0
 */
@RestController
@Api(tags = "渠道信息相关控制器")
@Slf4j
@RequestMapping("/channel")
public class ChannelController {

    @Resource
    private IChannelService channelService;
    @Resource
    private ITokenService tokenService;
    @Resource
    private IHelloChannelInitService helloChannelInitService;
    @Resource
    private IApiConnService apiConnService;

    /**
     * 查询所有的渠道数据
     *
     * @return 所有渠道信息
     * <AUTHOR>
     * @date 2022/9/21 22:18
     */
    @ApiOperation(value = "查询所有的渠道数据", notes = "查询所有的渠道数据 不分页(用于展示 车型管理列表的表头上的第三平台数量)")
    @PostMapping("/v1/list")
    public ResultMap<PageListVo<VehicleBrandVO>> listAllChannel(
                    @RequestParam(value = "includeSub", required = false) @ApiParam(("需要刹车农户的车型组id")) Byte includeSub,
                    HttpServletRequest httpServletRequest) {
        try {
            tokenService.getUserByRequest(httpServletRequest);
            Result<List<ChannelVO>> result = channelService.listAllChannel(includeSub);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ChannelController listAllChannel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("gds-渠道授权申请")
    @PostMapping("/v1/third/gds/accredit/apply")
    public ResultMap<Boolean> gdsAccreditApplyFor(@RequestBody @ApiParam("渠道申请参数") ThirdAccreditApplyVO req,
                                                  HttpServletRequest request) {

        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            req.setMerchantId(loginVo.getMerchantId());
            Result<Boolean> result = apiConnService.gdsAccreditApplyFor(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("StoreInfoController gdsAccreditApplyFor error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("gds-适配批量入库")
    @PostMapping("/v1/third/gds/match/add")
    public ResultMap<Boolean> addGdsThirdMatchInfo(@RequestBody @ApiParam("三方数据批量新增") ThirdBatchAddVo req,
                                                HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            req.setMerchantId(loginVo.getMerchantId());
            Result<Boolean> result = helloChannelInitService.addGdsThirdMatchInfo(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
//            Result<Boolean> result = helloChannelInitService.addGdsThirdMatchInfo(req);
//            if (ResultUtil.isResultNotSuccess(result)) {
//                return ApiResultUtil.failResult(result.getMessage());
//            }
            return ApiResultUtil.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("StoreInfoController addThirdStoreInfo error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("gds-适配批量入库")
    @PostMapping("/v1/third/gds/match/continue")
    public ResultMap<Boolean> continueGdsThirdMatchInfo(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = helloChannelInitService.continueGdsInit(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("StoreInfoController continueGdsThirdMatchInfo error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("获取最近一次渠道开通任务id")
    @PostMapping("/v1/third/apply/record")
    public ResultMap<TaskStatusVO> selectApplyRecord(
        @RequestParam(value = "channelId") Long channelId, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<TaskStatusVO> result = apiConnService.getApplyResult(loginVo.getMerchantId(), channelId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("StoreInfoController selectApplyRecord error", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
