package com.ql.rent.api.trade;
import java.math.BigDecimal;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.ql.rent.api.excel.DailyOverviewExcelBean;
import com.ql.rent.api.excel.OrderReportExcelBean;
import com.ql.rent.api.excel.ReportDataExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.param.merchant.MerchantInfoQuery;
import com.ql.rent.param.trade.*;
import com.ql.rent.param.trade.excel.ReportExcelParam;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.trade.IReportNewService;
import com.ql.rent.service.trade.IReportService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.trade.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-01-08  11:50
 * Description
 */

@RestController
@Slf4j
@RequestMapping("/report")
@Api(tags = "数据看板")
public class ReportController {

    @Resource
    private ITokenService tokenService;

    @Resource
    private IReportService reportService;

    @Resource
    private IReportNewService reportNewService;

    @Resource
    private MerchantInfoService merchantInfoService;

    @ApiOperation(value = "更新数据汇总统计")
    @PostMapping("/v1/updReportQuota")
    public ResultMap<Integer> updReportQuota(HttpServletRequest request,
                                             @RequestParam("ymd") @ApiParam("年月日") String ymd) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> result = reportService.reportQuota(ymd);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "更新数据汇总统计")
    @PostMapping("/v1/updReportQuotaNew")
    public ResultMap<Integer> updReportQuotaNew(HttpServletRequest request,
                                             @RequestParam(value = "merchantId", required = false) @ApiParam("商家ID") Long merchantId,
                                             @RequestParam(value = "storeId", required = false) @ApiParam("门店ID") Long storeId,
                                             @RequestParam(value = "channelId", required = false) @ApiParam("渠道ID") Long channelId,
                                             @RequestParam(value = "ymd", required = false) @ApiParam("年月日") String ymd) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Date pickup = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            if (StringUtils.isNotEmpty(ymd)) {
                try {
                    pickup = sdf.parse(ymd);
                } catch (Exception e) {
                    return ApiResultUtil.failResult("日期错误");
                }
            }
            reportService.orderChangeRecount(merchantId, storeId, channelId, pickup);
            return ApiResultUtil.successResult(true);
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "数据看板汇总统计")
    @PostMapping("/v1/getReportQuota")
    public ResultMap<ReportQuotaAllVO> getReportQuota(@RequestBody ReportQuotaQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<ReportQuotaAllVO> result = reportService.getReportQuota(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询车辆统计")
    @PostMapping("/v1/getReportVehicleList")
    public ResultMap<Long> getReportVehicleList(@RequestBody ReportVehicleQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<ReportVehicleVO>> result = reportService.getReportVehicleList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "数据看板汇总统计")
    @PostMapping("/v1/getSumReportQuota")
    public ResultMap<Long> getSumReportQuota(@RequestBody ReportQuotaQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<SumReportQuotaVO> result = reportService.getSumReportQuota(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "数据看板折线图")
    @PostMapping("/v1/getReportQuotaList")
    public ResultMap<Long> getReportQuotaList(@RequestBody ReportQuotaQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<List<ReportQuotaVO>> result = reportService.getReportQuotaList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "数据看板饼图")
    @PostMapping("/v1/getReportQuotaPieList")
    public ResultMap<Long> getReportQuotaPieList(@RequestBody ReportQuotaQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<List<ReportQuotaVO>> result = reportService.getReportQuotaPieList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单Excel下载", notes = "订单Excel下载")
    @PostMapping("/v1/downExcel")
    public void downFile(HttpServletRequest request,
                         HttpServletResponse response,
                         @RequestBody ReportExcelParam param) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载，避免一次性加载大量数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "订单报表",
                    "订单报表",
                    OrderReportExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        param.setPageIndex(pageIndex);
                        param.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<List<OrderReportExcelVO>> result = reportService.getReportExcel(param);
                        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getModel())) {
                            return new ArrayList<>();
                        }

                        return result.getModel();
                    },
                    // 转换函数，直接映射字段而不使用反射
                    vo -> {
                        OrderReportExcelBean bean = new OrderReportExcelBean();

                        // 直接赋值，避免使用反射
                        bean.setId(vo.getId());
                        bean.setOrderNo(vo.getOrderNo());
                        bean.setMerchantName(vo.getMerchantName());
                        bean.setPickupStoreName(vo.getPickupStoreName());
                        bean.setReturnStoreName(vo.getReturnStoreName());
                        bean.setPickupDate(vo.getPickupDate());
                        bean.setReturnDate(vo.getReturnDate());
                        bean.setPickupCity(vo.getPickupCity());
                        bean.setReturnCity(vo.getReturnCity());
                        bean.setVehicleName(vo.getVehicleName());
                        bean.setOrderSource(vo.getOrderSource());
                        bean.setOnlineOrOffline(vo.getOnlineOrOffline());
                        bean.setIsCancel(vo.getIsCancel());
                        bean.setIsFinished(vo.getIsFinished());
                        bean.setSellAmount(vo.getSellAmount());
                        bean.setReceivableAmount(vo.getReceivableAmount());
                        bean.setPayAmount(vo.getPayAmount());
                        bean.setOnlineGmv(vo.getOnlineGmv());
                        bean.setOfflineGmv(vo.getOfflineGmv());
                        bean.setRentAmount(vo.getRentAmount());
                        bean.setInsuranceAmount(vo.getInsuranceAmount());
                        bean.setAddAmount(vo.getAddAmount());
                        bean.setInsuranceName(vo.getInsuranceName());
                        bean.setThirdCommissionAmount(vo.getThirdCommissionAmount());
                        bean.setThirdSubsidyAmount(vo.getThirdSubsidyAmount());
                        bean.setPlatformCommissionAmount(vo.getPlatformCommissionAmount());
                        bean.setSettlementAmount(vo.getSettlementAmount());

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("ReportController downExcel {}", e.getMessage());
        }
    }

    @ApiOperation(value = "每日概况Excel下载", notes = "每日概况Excel下载")
    @PostMapping("/v1/getDailyOverview/downExcel")
    public void downExcel(HttpServletRequest request,
                          HttpServletResponse response,
                          @RequestBody DailyOverviewQuery query) {

        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            query.setMerchantId(loginVo.getMerchantId());

            // 文件名
            String fileName = "每日概况报表" + DateUtil.getFormatDateStr(query.getYmd(), DateUtil.yyyyMMdd);

            // 使用流式下载，避免一次性加载大量数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    fileName,
                    "每日概况",
                    DailyOverviewExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        query.setPageIndex(pageIndex);
                        query.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<List<DailyOverviewExcelVO>> result = reportService.getDailyOverviewExcel(query);
                        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getModel())) {
                            return new ArrayList<>();
                        }

                        return result.getModel();
                    },
                    // 转换函数，直接映射字段而不使用反射
                    vo -> {
                        DailyOverviewExcelBean bean = new DailyOverviewExcelBean();

                        // 直接赋值，避免使用反射
                        bean.setStoreId(BigDecimal.valueOf(vo.getStoreId()));
                        bean.setStoreName(vo.getStoreName());
                        bean.setVehicleModelId(BigDecimal.valueOf(vo.getVehicleModelId()));
                        bean.setVehicleModelName(vo.getVehicleModelName());
                        bean.setUsableAllCount(BigDecimal.valueOf(vo.getUsableAllCount()));
                        bean.setRentalCount(BigDecimal.valueOf(vo.getRentalCount()));
                        bean.setRentalRate(vo.getRentalRate());
                        bean.setOrderCount(BigDecimal.valueOf(vo.getOrderCount()));
                        bean.setNewGmv(BigDecimal.valueOf(vo.getNewGmv()));
                        bean.setReturnGmv(BigDecimal.valueOf(vo.getReturnGmv()));
                        bean.setOfflineOrderCount(BigDecimal.valueOf(vo.getOfflineOrderCount()));
                        bean.setOfflineNewGmv(BigDecimal.valueOf(vo.getOfflineNewGmv()));
                        bean.setOfflineReturnGmv(BigDecimal.valueOf(vo.getOfflineReturnGmv()));
                        bean.setCtripOrderCount(BigDecimal.valueOf(vo.getCtripOrderCount()));
                        bean.setCtripNewGmv(BigDecimal.valueOf(vo.getCtripNewGmv()));
                        bean.setCtripReturnGmv(BigDecimal.valueOf(vo.getCtripReturnGmv()));
                        bean.setFeizhuOrderCount(BigDecimal.valueOf(vo.getFeizhuOrderCount()));
                        bean.setFeizhuNewGmv(BigDecimal.valueOf(vo.getFeizhuNewGmv()));
                        bean.setFeizhuReturnGmv(BigDecimal.valueOf(vo.getFeizhuReturnGmv()));
                        bean.setHelloOrderCount(BigDecimal.valueOf(vo.getHelloOrderCount()));
                        bean.setHelloNewGmv(BigDecimal.valueOf(vo.getHelloNewGmv()));
                        bean.setHelloReturnGmv(BigDecimal.valueOf(vo.getHelloReturnGmv()));
                        bean.setZuzucheOrderCount(BigDecimal.valueOf(vo.getZuzucheOrderCount()));
                        bean.setZuzucheNewGmv(BigDecimal.valueOf(vo.getZuzucheNewGmv()));
                        bean.setZuzucheReturnGmv(BigDecimal.valueOf(vo.getZuzucheReturnGmv()));
                        bean.setWukongOrderCount(BigDecimal.valueOf(vo.getWukongOrderCount()));
                        bean.setWukongNewGmv(BigDecimal.valueOf(vo.getWukongNewGmv()));
                        bean.setWukongReturnGmv(BigDecimal.valueOf(vo.getWukongReturnGmv()));
                        bean.setCtripResaleOrderCount(BigDecimal.valueOf(vo.getCtripResaleOrderCount()));
                        bean.setCtripResaleNewGmv(BigDecimal.valueOf(vo.getCtripResaleNewGmv()));
                        bean.setCtripResaleReturnGmv(BigDecimal.valueOf(vo.getCtripResaleReturnGmv()));

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("ReportController getDailyOverview downExcel {}", e.getMessage());
        }
    }

    @ApiOperation(value = "每日概况")
    @PostMapping("/v1/getDailyOverview")
    public ResultMap<DailyOverviewVO> getDailyOverview(@RequestBody DailyOverviewQuery query, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            query.setMerchantId(loginVo.getMerchantId());
            Result<DailyOverviewVO> result = reportService.getDailyOverview(query);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("getDailyOverview error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "历史数据更新数据看板汇总统计V2")
    @PostMapping("/v2/updateReportData")
    public ResultMap<Integer> updateReportData(HttpServletRequest request,
                                               @RequestParam(value = "type", required = false) @ApiParam("类型") Integer type,
                                               @RequestParam(value = "merchantId", required = false) @ApiParam("商户号") Long merchantId) {
        try {
            List<MerchantInfoVo> merchantInfoVos = new ArrayList<>();
            if (Objects.isNull(merchantId)){
                // 查出所有商家
                MerchantInfoQuery merchantInfoQuery = new MerchantInfoQuery();
                Result<List<MerchantInfoVo>> listResult = merchantInfoService.listMerchantInfoVo(merchantInfoQuery);
                if (ResultUtil.isResultNotSuccess(listResult)) {
                    return ApiResultUtil.failResult(listResult.getResultCode(), listResult.getMessage());
                }
                merchantInfoVos = listResult.getModel();
            }else {
                Result<MerchantInfoVo> merchantInfoVoResult = merchantInfoService.findById(merchantId);
                if (ResultUtil.isResultNotSuccess(merchantInfoVoResult)) {
                    return ApiResultUtil.failResult(merchantInfoVoResult.getResultCode(), merchantInfoVoResult.getMessage());
                }
                merchantInfoVos.add(merchantInfoVoResult.getModel());
            }

            for (MerchantInfoVo merchantInfoVo : merchantInfoVos) {
                try {
                    log.info("updateReportData start!");
                    Long merchantInfoVoId =  merchantInfoVo.getId();
                    SaveReportParam saveReportParam = new SaveReportParam();
                    saveReportParam.setMerchantId(merchantInfoVoId);
                    for (int i = 1; i <= 365; i++) {
                        Date date = DateUtils.addDays(new Date(), -i);
                        saveReportParam.setYmd(DateUtil.getFormatDateStr(date, DateUtil.yyyyMMdd1));
                        if (Objects.isNull(type)){
                            reportNewService.saveReportOrder(saveReportParam);
                            reportNewService.saveReportInsurance(saveReportParam);
                            reportNewService.saveReportRentRate(saveReportParam);
                            reportNewService.saveReportRentStockTime(saveReportParam);
                        }else if (type == 1){
                            reportNewService.saveReportOrder(saveReportParam);
                        }else if (type == 2){
                            reportNewService.saveReportInsurance(saveReportParam);
                        }else if (type == 3){
                            reportNewService.saveReportRentRate(saveReportParam);
                        }else if (type == 4){
                            reportNewService.saveReportRentStockTime(saveReportParam);
                        }else {
                            log.error("updateReportData error type="+ type);
                        }
                        log.info("updateReportData time:{}",i);
                    }
                    log.info("updateReportData end!");
                } catch (Exception e) {
                    log.error("updateReportData error merchantInfoVo:{}", JSON.toJSONString(merchantInfoVo), e);
                }
                //每跑完一个商家，休眠10秒
                Thread.sleep(10000);
            }
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("updateReportData error ", e);
            return ApiResultUtil.failResult(e);
        }
    }



    @ApiOperation(value = "数据看板汇总统计V2")
    @PostMapping("/v2/getReportData")
    public ResultMap<ReportDataAllVO> getReportData(@RequestBody ReportDataQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<ReportDataAllVO> result = reportNewService.getReportData(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("getReportData error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "手动更新数据看板汇总统计V2")
    @PostMapping("/v2/syncReportData")
    public ResultMap<Integer> syncReportData(HttpServletRequest request,
                                            @RequestParam("ymd") @ApiParam("年月日") String ymd,
                                            @RequestParam(value = "merchantId",required = false) @ApiParam("商户号") Long merchantId,
                                            @RequestParam(value = "type", required = false) @ApiParam("类型") Integer type) {
        try {
            List<MerchantInfoVo> merchantInfoVos = new ArrayList<>();
            if (Objects.isNull(merchantId)){
                // 查出所有商家
                MerchantInfoQuery merchantInfoQuery = new MerchantInfoQuery();
                Result<List<MerchantInfoVo>> listResult = merchantInfoService.listMerchantInfoVo(merchantInfoQuery);
                if (ResultUtil.isResultNotSuccess(listResult)) {
                    return ApiResultUtil.failResult(listResult.getResultCode(), listResult.getMessage());
                }
                merchantInfoVos = listResult.getModel();
            }else {
                Result<MerchantInfoVo> merchantInfoVoResult = merchantInfoService.findById(merchantId);
                if (ResultUtil.isResultNotSuccess(merchantInfoVoResult)) {
                    return ApiResultUtil.failResult(merchantInfoVoResult.getResultCode(), merchantInfoVoResult.getMessage());
                }
                merchantInfoVos.add(merchantInfoVoResult.getModel());
            }

            log.info("syncReportData start!");
            for (MerchantInfoVo merchantInfoVo : merchantInfoVos) {
                SaveReportParam saveReportParam = new SaveReportParam();
                saveReportParam.setMerchantId(merchantInfoVo.getId());
                saveReportParam.setYmd(ymd);
                try{
                    if (Objects.isNull(type)){
                        reportNewService.saveReportOrder(saveReportParam);
                        reportNewService.saveReportRentRate(saveReportParam);
                        reportNewService.saveReportInsurance(saveReportParam);
                        reportNewService.saveReportRentStockTime(saveReportParam);
                    }else if (type == 1){
                        reportNewService.saveReportOrder(saveReportParam);
                    }else if (type == 2){
                        reportNewService.saveReportInsurance(saveReportParam);
                    }else if (type == 3){
                        reportNewService.saveReportRentRate(saveReportParam);
                    }else if (type == 4){
                        reportNewService.saveReportRentStockTime(saveReportParam);
                    }else {
                        log.error("syncReportData error type="+ type);
                    }
                }catch (Exception e) {
                    log.error("syncReportData error merchantInfoVo:{}", JSON.toJSONString(merchantInfoVo), e);
                }
                //每跑完一个商家，休眠5秒
                Thread.sleep(5000);
            }
            log.info("syncReportData end!");
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("syncReportData error ", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "数据总览Excel下载", notes = "数据总览Excel下载")
    @PostMapping("/v2/getReportData/downReportDataExcel")
    public void downReportDataExcel(HttpServletRequest request,
                          HttpServletResponse response,
                          @RequestBody ReportDataQuery query) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            query.setMerchantId(loginVo.getMerchantId());

            // 文件名
            String fileName = "数据总览统计报表" + query.getStartYmd() + "-" + query.getEndYmd();

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            // 使用try-with-resources自动关闭资源
            try (ServletOutputStream out = response.getOutputStream();
                 ExcelWriter excelWriter = EasyExcel.write(out)
                         .registerWriteHandler(EasyExcelUtils.defaultStyles())
                         .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                         .build()) {

                // 创建订单报表Sheet
                WriteSheet orderSheet = EasyExcel.writerSheet(1, "订单报表")
                        .head(OrderReportExcelBean.class)
                        .build();

                // 创建出租率报表Sheet
                WriteSheet rentRateSheet = EasyExcel.writerSheet(2, "出租率报表")
                        .head(ReportDataExcelBean.class)
                        .build();

                // 准备订单报表参数
                ReportExcelParam orderParam = new ReportExcelParam();
                orderParam.setMerchantId(loginVo.getMerchantId());
                orderParam.setStartYmd(query.getStartYmd());
                orderParam.setEndYmd(query.getEndYmd());
                orderParam.setStoreIdList(query.getStoreIdList());
                orderParam.setCityIdList(query.getCityIdList());
                orderParam.setChannelIdList(query.getChannelIdList());

                // 分页处理订单报表数据
                int pageIndex = 1;
                int pageSize = 500;
                int totalOrderCount = 0;

                while (true) {
                    // 设置分页参数
                    orderParam.setPageIndex(pageIndex);
                    orderParam.setPageSize(pageSize);

                    // 查询当前页订单数据
                    Result<List<OrderReportExcelVO>> orderResult = reportService.getReportExcel(orderParam);
                    if (!orderResult.isSuccess() || CollectionUtils.isEmpty(orderResult.getModel())) {
                        break;
                    }

                    List<OrderReportExcelVO> orderVOs = orderResult.getModel();
                    List<OrderReportExcelBean> orderBeans = new ArrayList<>(orderVOs.size());

                    // 直接赋值转换数据，避免使用反射
                    for (OrderReportExcelVO vo : orderVOs) {
                        OrderReportExcelBean bean = new OrderReportExcelBean();

                        bean.setId(vo.getId());
                        bean.setOrderNo(vo.getOrderNo());
                        bean.setMerchantName(vo.getMerchantName());
                        bean.setPickupStoreName(vo.getPickupStoreName());
                        bean.setReturnStoreName(vo.getReturnStoreName());
                        bean.setPickupDate(vo.getPickupDate());
                        bean.setReturnDate(vo.getReturnDate());
                        bean.setPickupCity(vo.getPickupCity());
                        bean.setReturnCity(vo.getReturnCity());
                        bean.setVehicleName(vo.getVehicleName());
                        bean.setOrderSource(vo.getOrderSource());
                        bean.setOnlineOrOffline(vo.getOnlineOrOffline());
                        bean.setIsCancel(vo.getIsCancel());
                        bean.setIsFinished(vo.getIsFinished());
                        bean.setSellAmount(vo.getSellAmount());
                        bean.setReceivableAmount(vo.getReceivableAmount());
                        bean.setPayAmount(vo.getPayAmount());
                        bean.setOnlineGmv(vo.getOnlineGmv());
                        bean.setOfflineGmv(vo.getOfflineGmv());
                        bean.setRentAmount(vo.getRentAmount());
                        bean.setInsuranceAmount(vo.getInsuranceAmount());
                        bean.setAddAmount(vo.getAddAmount());
                        bean.setInsuranceName(vo.getInsuranceName());
                        bean.setThirdCommissionAmount(vo.getThirdCommissionAmount());
                        bean.setThirdSubsidyAmount(vo.getThirdSubsidyAmount());
                        bean.setPlatformCommissionAmount(vo.getPlatformCommissionAmount());
                        bean.setSettlementAmount(vo.getSettlementAmount());

                        orderBeans.add(bean);
                    }

                    // 写入当前页订单数据
                    excelWriter.write(orderBeans, orderSheet);

                    totalOrderCount += orderVOs.size();

                    // 如果数据量小于一页，说明已经是最后一页
                    if (orderVOs.size() < pageSize || totalOrderCount >= 10000) {
                        break;
                    }

                    pageIndex++;
                }

                // 分页处理出租率报表数据
                pageIndex = 1;
                int totalRentRateCount = 0;

                while (true) {
                    // 设置分页参数
                    query.setPageIndex(pageIndex);
                    query.setPageSize(pageSize);

                    // 查询当前页出租率数据
                    Result<List<ReportDataExcelVO>> rentRateResult = reportNewService.getReportDataExcel(query);
                    if (!rentRateResult.isSuccess() || CollectionUtils.isEmpty(rentRateResult.getModel())) {
                        break;
                    }

                    List<ReportDataExcelVO> rentRateVOs = rentRateResult.getModel();
                    List<ReportDataExcelBean> rentRateBeans = new ArrayList<>(rentRateVOs.size());

                    // 直接赋值转换数据，避免使用反射
                    for (ReportDataExcelVO vo : rentRateVOs) {
                        ReportDataExcelBean bean = new ReportDataExcelBean();

                        bean.setStoreId(BigDecimal.valueOf(vo.getStoreId()));
                        bean.setStoreName(vo.getStoreName());
                        bean.setVehicleModelId(BigDecimal.valueOf(vo.getVehicleModelId()));
                        bean.setVehicleModelName(vo.getVehicleModelName());
                        bean.setYmd(vo.getYmd());
                        bean.setRentTime(BigDecimal.valueOf(vo.getRentTime()));
                        bean.setAllTime(BigDecimal.valueOf(vo.getAllTime()));
                        bean.setRentalRate(vo.getRentalRate());

                        rentRateBeans.add(bean);
                    }

                    // 写入当前页出租率数据
                    excelWriter.write(rentRateBeans, rentRateSheet);

                    totalRentRateCount += rentRateVOs.size();

                    // 如果数据量小于一页，说明已经是最后一页
                    if (rentRateVOs.size() < pageSize || totalRentRateCount >= 10000) {
                        break;
                    }

                    pageIndex++;
                }
            }
        } catch (Exception e) {
            log.error("ReportController getReportData downReportDataExcel errorv {}", e.getMessage());
        }
    }

    @NotNull
    private static ReportDataExcelBean convertReportDataExcelVO2Bean(ReportDataExcelVO reportDataExcelVO) {
        ReportDataExcelBean reportDataExcelBean = new ReportDataExcelBean();
        reportDataExcelBean.setStoreId(BigDecimal.valueOf(reportDataExcelVO.getStoreId()));
        reportDataExcelBean.setStoreName(reportDataExcelVO.getStoreName());
        reportDataExcelBean.setVehicleModelId(BigDecimal.valueOf(reportDataExcelVO.getVehicleModelId()));
        reportDataExcelBean.setVehicleModelName(reportDataExcelVO.getVehicleModelName());
        reportDataExcelBean.setYmd(reportDataExcelVO.getYmd());
        reportDataExcelBean.setRentTime(BigDecimal.valueOf(reportDataExcelVO.getRentTime()));
        reportDataExcelBean.setAllTime(BigDecimal.valueOf(reportDataExcelVO.getAllTime()));
        reportDataExcelBean.setRentalRate(reportDataExcelVO.getRentalRate());
        return reportDataExcelBean;
    }

}
