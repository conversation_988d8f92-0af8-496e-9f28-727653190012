package com.ql.rent.api.trade;

import com.ql.rent.api.excel.VehicleDriverExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.trade.PickupTypeEnum;
import com.ql.rent.enums.trade.ReturnTypeEnum;
import com.ql.rent.enums.trade.VehiclePickReturnEnum;
import com.ql.rent.param.trade.*;
import com.ql.rent.service.trade.IOrderAddrDisplayConfigService;
import com.ql.rent.service.trade.IVehiclePickDriverService;
import com.ql.rent.service.trade.IVehiclePickDriverTaskTypeService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleSourceGroupVO;
import io.swagger.annotations.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2022-10-31 22:12
 */
@RestController
@Slf4j
@Api(tags = "排司机、任务管理相关")
@RequestMapping("/pick_diver")
public class VehiclePickDriverController {

    @Resource
    private IVehiclePickDriverService vehiclePickDriverService;
    @Resource
    private IVehiclePickDriverTaskTypeService vehiclePickDriverTaskService;
    @Resource
    private IOrderAddrDisplayConfigService orderAddrDisplayConfigService;
    @Resource
    private ITokenService tokenService;

    @Resource
    private IVehicleInfoService vehicleInfoService;

    /**
     * 分页查询 司机任务数列表
     */
    @ApiOperation(value = "查询司机任务类型")
    @PostMapping("/task_types/v1")
    public ResultMap<PageListVo<DriverTaskListVO>> listVehiclePickDriverTask(HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            Result<List<VehiclePickDriverTaskTypeVO>> result =
                vehiclePickDriverTaskService.listVehiclePickDriverTask(userLogin.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listVehiclePickDriverTask", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增/修改 司机任务类型")
    @PostMapping("/save/v1")
    public ResultMap<Integer> saveOrUpdate(@RequestBody VehiclePickDriverTaskTypeVO param,
                                           HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            param.setMerchantId(userLogin.getMerchantId());
            Result<Integer> result = vehiclePickDriverTaskService.saveOrUpdate(param, userLogin.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController saveOrUpdateForOpen", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "司机看板")
    @PostMapping("/dashboard/v1")
    public ResultMap<List<DriverDashboardVO>> listDriverDashboard(@RequestBody DriverDashboardParam param,
                                                                  HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            param.setMerchantId(userLogin.getMerchantId());
            Result<List<DriverDashboardVO>> result = vehiclePickDriverService.listDriverDashboard(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listDriverDashboard", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "司机看板(未排司机的订单列表)")
    @PostMapping("/dashboard/un_arrange/v1")
    public ResultMap<List<DriverDashboardVO>> listUnArrangeOrder(@RequestBody UnArrangeOrderQueryParam param,
                                                                 HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            param.setMerchantId(userLogin.getMerchantId());
            Result<List<UnArrangeOrderTaskVO>> result = vehiclePickDriverService.listUnArrangeOrder(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            List<UnArrangeOrderTaskVO> resultModel = result.getModel();
            List<VehicleSourceGroupVO> vehicleSourceGroupVOS = vehicleInfoService.findAuthVehicleIds(userLogin.getLoginName(), userLogin.getMerchantId(), param.getStoreIdList());
            if (vehicleSourceGroupVOS != null) {
                List<Long> vehicleIds = vehicleSourceGroupVOS.stream().map(VehicleSourceGroupVO::getVehicleId).collect(Collectors.toList());
                List<UnArrangeOrderTaskVO> lastModel = resultModel.stream().filter(r -> vehicleIds.contains(r.getVehicleId()))
                        .collect(Collectors.toList());
                result.setModel(lastModel);
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listUnArrangeOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @PostMapping("/dashboard/arrange/v1")
    @ApiOperation(value = "司机看板-排司机接口")
    public ResultMap<DriverDashboardVO.DriverTaskVO> dashboardArrange(
        @RequestBody @ApiParam("实体") DashBoardArrangeParam param,
        HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginUser = tokenService.getUserByRequest(httpServletRequest);
            Result<DriverDashboardVO.DriverTaskVO> result =
                vehiclePickDriverService.dashboardArrange(param, loginUser);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController dashboardArrange", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修改订单地址展示方式")
    @PostMapping("/dashboard/order_display_addr_type/v1/upd")
    public ResultMap<Integer> updateDisplayAddrType(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
                                                    @RequestParam("displayType")
                                                    @ApiParam("展示类型 1-市/区, 2-市-区-街道,3-详细地址")
                                                    Byte displayType,
                                                    HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = orderAddrDisplayConfigService.updateByOrderId(orderId, userLogin, displayType);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController updateDisplayAddrType", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修改商家全局地址展示配置")
    @PostMapping("/dashboard/merchant_display_addr_type/v1/upd")
    public ResultMap<OrderAddrDisplayConfigVO> updateMerchantDisplayAddrType(@ApiParam("展示类型 1-市/区, 2-市-区-街道,3-详细地址")
                                                                             @RequestParam("displayType")
                                                                             Byte displayType,
                                                                             HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            Result<OrderAddrDisplayConfigVO> result = orderAddrDisplayConfigService
                .updMerchantDisplay(userLogin.getMerchantId(),
                    IOrderAddrDisplayConfigService.BusiTypeEnum.DRIVER_DASHBOARD.getType(), displayType);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("更新失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "获取 商家全局地址展示方式")
    @PostMapping("/dashboard/merchant_display_addr_type/v1/get")
    public ResultMap<MerchantAddrDisplayConfigVO> getMerchantAddrDisplayConfig(HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            Result<MerchantAddrDisplayConfigVO> result =
                orderAddrDisplayConfigService.getMerchantDisplayType(userLogin.getMerchantId(),
                    IOrderAddrDisplayConfigService.BusiTypeEnum.DRIVER_DASHBOARD.getType());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController getMerchantAddrDisplayConfig", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除 司机任务类型")
    @PostMapping("/delete/v1")
    public ResultMap<Integer> deleteById(@RequestParam("id") @ApiParam("需要删除的id") Long id,
                                         HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehiclePickDriverTaskService.deleteById(id, userLogin.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController deleteById", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 分页查询 司机任务数列表
     */
    @ApiOperation(value = "分页查询 司机任务数列表")
    @PostMapping("/list/driver_num/v1")
    public ResultMap<PageListVo<DriverTaskListVO>> listDriverTaskStatistics(@RequestBody DriverTaskQuery query,
                                                                            HttpServletRequest httpServletRequest) {
        try {
            LoginVo userLogin = tokenService.getUserByRequest(httpServletRequest);
            query.setMerchantId(userLogin.getMerchantId());
            Result<PageListVo<DriverTaskListVO>> result =
                vehiclePickDriverService.listDriverTaskStatistics(query);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listDriverTaskStatistics", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 分页查询司机任务列表
     */
    @ApiOperation(value = "分页查询 司机任务列表")
    @PostMapping("/list/v1")
    public ResultMap<List<DriverTaskListVO>> listTaskPage(@RequestBody PickDriverOrderQuery query,
                                                          HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginUser = tokenService.getUserByRequest(httpServletRequest);
            if (YesOrNoEnum.isYes(query.getQueryCurrentUser())) {
                query.setDriverUserId(loginUser.getUserId());
            }
            query.setMerchantId(loginUser.getMerchantId());
            Result<List<PickDriverOrderVO>> result = vehiclePickDriverService.listDiverTask(query);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listTaskPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 排司机接口
     *
     * @param param              表单参数
     * @param httpServletRequest 请求对象
     * @return
     */
    @PostMapping("/arrange/v1")
    @ApiOperation(value = "排司机接口")
    public ResultMap<Integer> arrangeDriver(@RequestBody @ApiParam("实体") VehiclePickDriverParam param,
                                            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginUser = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result =
                vehiclePickDriverService.saveOrderPickReturnDriver(param, loginUser.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController arrangeDriver", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @PostMapping("/upd_task/v1")
    @ApiOperation(value = "修改送/收车任务")
    public ResultMap<Integer> updateTask(@RequestBody @ApiParam("实体") UpdTaskParam param,
                                         HttpServletRequest httpServletRequest) {

        try {
            LoginVo loginUser = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result =
                vehiclePickDriverService.updTask(param, loginUser);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController updateTask", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 查订单的排司机情况
     */
    @PostMapping("/list_by_order/v1")
    @ApiOperation(value = "查询排司机情况")
    public ResultMap<PageListVo<DriverTaskListVO>> listByOrderId(@RequestParam("order_id") @ApiParam("订单id")
                                                                 Long orderId,
                                                                 HttpServletRequest httpServletRequest) {
        try {
            tokenService.getUserByRequest(httpServletRequest);
            Result<List<PickDriverVO>> result = vehiclePickDriverService.listByOrderId(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listByOrderId", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 查询司机 各门店下订单
     */
    @PostMapping("/list_store_order/v1")
    @ApiOperation(value = "查询司机 各门店下订单")
    public ResultMap<PageListVo<DriverTaskListVO>> listDriverStoreOrder(
        @RequestParam(value = "driver_user_id", required = false) @ApiParam("司机用户ID")
        Long driverUserId,
        HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            if (driverUserId == null) {
                driverUserId = loginVo.getUserId();
            }
            Result<List<DriverStoreOrderVO>> result =
                vehiclePickDriverService.listStorePickerByDriverId(driverUserId, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController listDriverStoreOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * @param request
     * @param response
     * @param queryParam
     * @return
     * <AUTHOR>
     * @date 2023/1/16 21:07
     */
    @ApiOperation(value = "司机任务明细导出", notes = "司机任务明细导出")
    @PostMapping("/downExcel/v1")
    public void downFile(HttpServletRequest request, HttpServletResponse response,
                         @RequestBody DriverTaskQuery queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            queryParam.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载带转换，避免一次性加载所有数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "司机任务列表",
                    "司机任务列表",
                    VehicleDriverExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<List<DriverTaskExcelVO>> result = vehiclePickDriverService.listDriverTask(queryParam);
                        if (!result.isSuccess() || result.getModel() == null) {
                            return new ArrayList<>();
                        }

                        return result.getModel();
                    },
                    // 使用非反射方式直接处理，避免使用BeanUtils等反射工具
                    vo -> {
                        // 直接赋值，不使用反射
                        VehicleDriverExcelBean excelBean = new VehicleDriverExcelBean();
                        excelBean.setOrderNo(vo.getOrderNo());
                        excelBean.setStaffName(vo.getStaffName());
                        excelBean.setPrTypeName(VehiclePickReturnEnum.getNameByType(vo.getPrType()));
                        excelBean.setTaskTimeStr(DateUtil.getFormatDateStr(vo.getTaskTime(), DateUtil.yyyyMMddHHmmss));
                        excelBean.setStoreName(vo.getStoreName());

                        // 根据类型设置不同字段
                        if (VehiclePickReturnEnum.isPick(vo.getPrType())) {
                            String orderType = Optional.ofNullable(PickupTypeEnum.getByType(vo.getPickupAddrType()))
                                    .map(PickupTypeEnum::getName)
                                    .orElse("");
                            excelBean.setOrderPickReturnType(orderType);
                            excelBean.setPickReturnAddress(vo.getPickupAddr());
                        } else {
                            String orderType = Optional.ofNullable(ReturnTypeEnum.getByType(vo.getReturnAddrType()))
                                    .map(ReturnTypeEnum::getName)
                                    .orElse("");
                            excelBean.setOrderPickReturnType(orderType);
                            excelBean.setPickReturnAddress(vo.getReturnAddr());
                        }

                        return excelBean;
                    }
            );
        } catch (Exception e) {
            log.error("VehiclePickDriverController downExcel {}", e.getMessage());
        }
    }

    @PostMapping("/v1/cancel")
    @ApiOperation(value = "取消司机任务")
    public ResultMap<Integer> cancel(@RequestParam("id") @ApiParam("司机任务id") Long id,
                                     HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehiclePickDriverService.cancelTask(id, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehiclePickDriverController cancel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiModel("取还司机idVO")
    @Data
    public static class VehiclePickReturnTaskVO implements Serializable {

        private static final long serialVersionUID = -1197375632363488889L;

        @ApiModelProperty("订单id")
        private Long orderId;

        @ApiModelProperty("取车（送车任务id）")
        private Long pickTaskId;
        @ApiModelProperty("还车（收车任务id）")
        private Long returnTaskId;
    }
}
