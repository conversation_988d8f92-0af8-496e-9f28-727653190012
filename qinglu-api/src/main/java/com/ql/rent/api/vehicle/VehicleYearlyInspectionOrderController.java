package com.ql.rent.api.vehicle;

import com.ql.rent.api.excel.VehicleYearlyInspectionExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.vehicle.YearlyInspectionOrderStatusEnum;
import com.ql.rent.param.vehicle.VehicleWorkOrderQueryParam;
import com.ql.rent.param.vehicle.YearlyInspectionOrderParam;
import com.ql.rent.service.vehicle.IVehicleYearlyInspectionOrderService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.MerchantAccountVO;
import com.ql.rent.vo.vehicle.VehicleRepairMaintenanceDetailVO;
import com.ql.rent.vo.vehicle.VehicleYearlyInspectionDetailVO;
import com.ql.rent.vo.vehicle.VehicleYearlyInspectionVO;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 年检单接口
 */
@Slf4j
@Api(tags = "年检单接口")
@RestController
@RequestMapping("/vehicle/yearly_inspection")
public class VehicleYearlyInspectionOrderController {

    @Resource
    private IVehicleYearlyInspectionOrderService vehicleYearlyInspectionOrderService;
    @Resource
    private ITokenService tokenService;

    @ApiOperation(value = "查询年检单列表")
    @PostMapping("/list/v1")
    public ResultMap<PageListVo<VehicleYearlyInspectionVO>> listInspectionOrderPage(
            HttpServletRequest httpServletRequest,
            @RequestBody VehicleWorkOrderQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            // 权限
            // if (CollectionUtils.isNotEmpty(loginVo.getStoreIdList()) &&
            // loginVo.getStoreIdList().contains(0L)) {
            // } else {
            // queryParam.setStoreIdList(loginVo.getStoreIdList());
            // }
            Result<PageListVo<VehicleYearlyInspectionVO>> result = vehicleYearlyInspectionOrderService
                    .listYearlyInspectionOrderPage(queryParam, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController listInspectionOrderPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增年检单")
    @PostMapping("/save/v1")
    @ApiResponse(code = 200, message = "占用库存时回抛出异常时，抛出错误码:VEHICLE_002")
    @WithSpan("创建年检单")
    public ResultMap<Integer> saveInspectionOrder(HttpServletRequest httpServletRequest,
            @RequestBody YearlyInspectionOrderParam param) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withString("param", param.toString()).withLong("车辆ID",
                    null != param ? param.getVehicleInfoId() : 0L);
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleYearlyInspectionOrderService.saveYearlyInspectionOrder(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController saveInspectionOrder", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR, e.getMessage());
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询年检单详情")
    @PostMapping("/detail/v1")
    public ResultMap<VehicleRepairMaintenanceDetailVO> getDetail(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("维保单id") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<VehicleYearlyInspectionDetailVO> result = vehicleYearlyInspectionOrderService.getDetailById(id);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            if (!loginVo.getMerchantId().equals(result.getModel().getMerchantId())) {
                return ApiResultUtil.failResult("无权限查询");
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController getDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "处理年检单")
    @PostMapping("/handle/v1")
    public ResultMap<VehicleRepairMaintenanceDetailVO> handlerOrder(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("年检单id") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleYearlyInspectionOrderService.handlerOrder(id, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController handlerOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "作废年检单")
    @PostMapping("/cancel/v1")
    public ResultMap<VehicleRepairMaintenanceDetailVO> cancel(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("年检单id") Long id,
            @RequestParam("cancelReason") @ApiParam("作废原因") String cancelReason) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleYearlyInspectionOrderService.cancel(id, cancelReason, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController cancel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "车辆年检单Excel下载", notes = "车辆年检单Excel下载")
    @PostMapping("/v1/downExcel")
    public void downFile(HttpServletRequest request, HttpServletResponse response,
            @RequestBody VehicleWorkOrderQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            queryParam.setMerchantId(loginVo.getMerchantId());

            // 处理门店权限
            List<Long> storeIdList = loginVo.getStoreIdList();
            if (storeIdList != null && !storeIdList.contains(0L)) {
                queryParam.setStoreIdList(storeIdList);
            }

            // 使用流式下载，避免一次性加载大量数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "年检单列表",
                    "年检单列表",
                    VehicleYearlyInspectionExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<VehicleYearlyInspectionVO>> result = vehicleYearlyInspectionOrderService
                                .listYearlyInspectionOrderPage(queryParam, loginVo);

                        if (!result.isSuccess() || result.getModel() == null || CollectionUtils.isEmpty(result.getModel().getList())) {
                            return new ArrayList<>();
                        }

                        return result.getModel().getList();
                    },
                    // 转换函数，直接映射字段而不使用反射
                    vo -> {
                        VehicleYearlyInspectionExcelBean excelBean = new VehicleYearlyInspectionExcelBean();

                        // 直接赋值，避免使用反射
                        excelBean.setWorkOrderNo(vo.getWorkOrderNo());
                        excelBean.setLicenseNo(vo.getLicenseNo());
                        excelBean.setVehicleModelName(vo.getVehicleModelName());
                        excelBean.setStoreNameUnion(vo.getStoreNameUnion());

                        // 格式化时间
                        excelBean.setInspectionTime(
                                DateUtil.getFormatDateStr(new Date(vo.getInspectionTime()), DateUtil.yyyyMMddHHmmss));

                        if (vo.getInspectionEndTime() != null) {
                            excelBean.setInspectionEndTime(
                                    DateUtil.getFormatDateStr(new Date(vo.getInspectionEndTime()), DateUtil.yyyyMMddHHmmss));
                        } else {
                            excelBean.setInspectionEndTime("");
                        }

                        if (vo.getNextInspectionTime() != null) {
                            excelBean.setNextInspectionTime(
                                    DateUtil.getFormatDateStr(new Date(vo.getNextInspectionTime()), DateUtil.yyyyMMddHHmmss));
                        } else {
                            excelBean.setNextInspectionTime("");
                        }

                        excelBean.setHandlerUserName(vo.getHandlerUserName());
                        excelBean.setStatus(YearlyInspectionOrderStatusEnum.getNameByStatus(vo.getStatus()));

                        return excelBean;
                    }
            );
        } catch (Exception e) {
            log.error("VehicleYearlyInspectionOrderController downExcel {}", e.getMessage());
        }
    }

    @Value("${profile.env}")
    private String profileEnv;

    @ApiOperation(value = "触发年检单状态job接口")
    @PostMapping("/v1/status_job")
    public ResultMap<MerchantAccountVO> triggerStatus(HttpServletRequest request) {
        try {
            if (StringUtils.equalsAny(profileEnv, "dev")) {
                vehicleYearlyInspectionOrderService.yearlyInspectionStatusTask();
            }
            return ApiResultUtil.successResult(null);
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "触发年检单 通知提醒")
    @PostMapping("/v1/notice_job")
    public ResultMap<MerchantAccountVO> triggerNotice(HttpServletRequest request) {
        try {
            if (StringUtils.equalsAny(profileEnv, "dev")) {
                vehicleYearlyInspectionOrderService.yearlyInspectionNoticeTask();
            }
            return ApiResultUtil.successResult(null);
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
