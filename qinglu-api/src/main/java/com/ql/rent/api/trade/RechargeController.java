package com.ql.rent.api.trade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ql.rent.api.excel.RechargeExcelBean;
import com.ql.rent.api.excel.RefundExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.trade.RechargeTypeEnum;
import com.ql.rent.param.trade.OfflineRechargeQuery;
import com.ql.rent.param.trade.RechargeQuery;
import com.ql.rent.param.trade.RechargeSubmitParam;
import com.ql.rent.param.trade.RefundQuery;
import com.ql.rent.service.trade.IRechargeService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.CurrencyUtils;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.RechargeVO;
import com.ql.rent.vo.trade.RefundVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023-01-08  11:50
 * Description
 */

@RestController
@Slf4j
@RequestMapping("/recharge")
@Api(tags = "商户账户")
public class RechargeController {

    @Resource
    private ITokenService tokenService;

    @Resource
    private IRechargeService rechargeService;

    @ApiOperation(value = "充值下单")
    @PostMapping("/v1/rechargeSubmit")
    public ResultMap<Long> rechargeSubmit(@RequestBody RechargeSubmitParam param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setOpUserId(loginVo.getUserId());
            param.setMerchantId(loginVo.getMerchantId());
            Result<Long> result = rechargeService.rechargeSubmit(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询充值")
    @PostMapping("/v1/getRechargeList")
    public ResultMap<PageListVo<RechargeVO>> getRechargeList(@RequestBody RechargeQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<RechargeVO>> result = rechargeService.getRechargeList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "查询线下充值记录（Sass-Admin）")
    @PostMapping("/v1/getRechargeList4SassAdmin")
    public ResultMap<PageListVo<RechargeVO>> getRechargeList4SassAdmin(@RequestBody OfflineRechargeQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);

            if (!loginVo.isSaasAdmin()) {
                return ApiResultUtil.failResult("无权限操作");
            }
            Result<PageListVo<RechargeVO>> result = rechargeService.getOfflineRechargeList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询可退款充值")
    @PostMapping("/v1/getRefundRechargeList")
    public ResultMap<PageListVo<RechargeVO>> getRefundRechargeList(@RequestBody RechargeQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<RechargeVO>> result = rechargeService.getRefundRechargeList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "充值导出")
    @PostMapping("/v1/recharge/export")
    public void exportRechargeData(@RequestBody RechargeQuery param, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "充值导出",
                    "充值导出",
                    RechargeExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        param.setPageIndex(pageIndex);
                        param.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<RechargeVO>> pageResult = rechargeService.getRechargeList(param);
                        if (!pageResult.isSuccess() || pageResult.getModel() == null) {
                            return new ArrayList<>();
                        }

                        return pageResult.getModel().getList();
                    },
                    infoVO -> {
                        // 直接创建并设置属性，避免使用反射
                        RechargeExcelBean bean = new RechargeExcelBean();

                        bean.setRechargeNo(infoVO.getId().toString());
                        bean.setCreateTime(DateUtil.format(new Date(infoVO.getCreateTime()), DatePattern.NORM_DATETIME_PATTERN));
                        bean.setRechargePrice(CurrencyUtils.cent2Yuan(BigDecimal.valueOf(infoVO.getRechargePrice())));
                        bean.setInvoicingPrice(CurrencyUtils.cent2Yuan(BigDecimal.valueOf(infoVO.getRechargePrice() - infoVO.getInvoicingPrice())));

                        // 充值方式
                        if (Objects.equals(infoVO.getSource(), RechargeTypeEnum.WECHAT.getItemType())) {
                            bean.setSource(RechargeTypeEnum.WECHAT.getItemName());
                        } else if (Objects.equals(infoVO.getSource(), RechargeTypeEnum.ALIPAY.getItemType())) {
                            bean.setSource(RechargeTypeEnum.ALIPAY.getItemName());
                        } else if (Objects.equals(infoVO.getSource(), RechargeTypeEnum.BANK_CARD.getItemType())) {
                            bean.setSource(RechargeTypeEnum.BANK_CARD.getItemName());
                        } else if (Objects.equals(infoVO.getSource(), RechargeTypeEnum.GIFTED.getItemType())) {
                            bean.setSource(RechargeTypeEnum.GIFTED.getItemName());
                        } else if (Objects.equals(infoVO.getSource(), RechargeTypeEnum.OFFLINE.getItemType())) {
                            bean.setSource(RechargeTypeEnum.OFFLINE.getItemName());
                        } else {
                            bean.setSource(RechargeTypeEnum.OTHER.getItemName());
                        }

                        // 充值状态
                        if (infoVO.getStatus() == 1) {
                            bean.setRechargeStatus("已支付");
                        } else if (infoVO.getStatus() == 0) {
                            bean.setRechargeStatus("未支付");
                        } else {
                            bean.setRechargeStatus("作废");
                        }

                        // 提现信息
                        if (infoVO.getWithdrawalPrice() > 0) {
                            BigDecimal p = CurrencyUtils.cent2Yuan(BigDecimal.valueOf(infoVO.getWithdrawalPrice()));
                            if (infoVO.getWithdrawalPrice().intValue() == infoVO.getRechargePrice()) {
                                bean.setWithdrawal("全部提现，" + p + "元");
                            } else {
                                bean.setWithdrawal("部分提现，" + p + "元");
                            }
                        } else {
                            bean.setWithdrawal("-");
                        }

                        // 开票状态
                        if (infoVO.getStatus() == 1) {
                            if (infoVO.getInvoicingStatus() == 0) {
                                bean.setInvoicingStatus("可开票");
                            } else if (infoVO.getInvoicingStatus() == 1) {
                                bean.setInvoicingStatus("不可开票");
                            } else if (infoVO.getInvoicingStatus() == 2) {
                                bean.setInvoicingStatus("退票中");
                            } else if (infoVO.getInvoicingStatus() == 3) {
                                bean.setInvoicingStatus("已红冲");
                            } else if (infoVO.getInvoicingStatus() == 4) {
                                bean.setInvoicingStatus("开票中");
                            }
                        } else {
                            bean.setInvoicingStatus("-");
                        }
                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("获取充值详情失败 {}", e.getMessage());
        }
    }

    @ApiOperation(value = "查询退款")
    @PostMapping("/v1/getRefundList")
    public ResultMap<Long> getRefundList(@RequestBody RefundQuery param, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<RefundVO>> result = rechargeService.getRefundList(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("操作失败", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "退款导出")
    @PostMapping("/v1/refund/export")
    public void exportRefundData(@RequestBody RefundQuery param, HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "退款导出",
                    "退款导出",
                    RefundExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        param.setPageIndex(pageIndex);
                        param.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<RefundVO>> pageResult = rechargeService.getRefundList(param);
                        if (!pageResult.isSuccess() || pageResult.getModel() == null) {
                            return new ArrayList<>();
                        }

                        return pageResult.getModel().getList();
                    },
                    infoVO -> {
                        // 直接创建并设置属性，避免使用反射
                        RefundExcelBean bean = new RefundExcelBean();
                        bean.setCreateTime(DateUtil.format(new Date(infoVO.getCreateTime()), DatePattern.NORM_DATETIME_PATTERN));
                        bean.setRechargeNo(infoVO.getId().toString());

                        // 类型
                        if (infoVO.getOperation() == 2) {
                            bean.setType("提现");
                        } else if (infoVO.getOperation() == 3) {
                            bean.setType("退款");
                        }

                        bean.setPrice(CurrencyUtils.cent2Yuan(BigDecimal.valueOf(infoVO.getPrice())));

                        if (infoVO.getOpTime() > 0) {
                            bean.setOpTime(DateUtil.format(new Date(infoVO.getOpTime()), DatePattern.NORM_DATETIME_PATTERN));
                        } else {
                            bean.setOpTime(DateUtil.format(new Date(infoVO.getCreateTime()), DatePattern.NORM_DATETIME_PATTERN));
                        }
                        if (StringUtils.isNotEmpty(infoVO.getMemo())) {
                            bean.setMemo(infoVO.getMemo());
                        } else {
                            bean.setMemo("-");
                        }
                        if (infoVO.getOperation() == 2) {
                            if (infoVO.getStatus() == 1) {
                                bean.setStatus("进行中");
                            } else if (infoVO.getStatus() == 2) {
                                bean.setStatus("提现成功");
                            } else if (infoVO.getStatus() == 3) {
                                bean.setStatus("提现失败");
                            }
                        } else {
                            if (infoVO.getStatus() == 1) {
                                bean.setStatus("进行中");
                            } else if (infoVO.getStatus() == 2) {
                                bean.setStatus("退款成功");
                            } else if (infoVO.getStatus() == 3) {
                                bean.setStatus("退款失败");
                            }
                        }

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("获取退款详情失败 {}", e.getMessage());
        }
    }
}
