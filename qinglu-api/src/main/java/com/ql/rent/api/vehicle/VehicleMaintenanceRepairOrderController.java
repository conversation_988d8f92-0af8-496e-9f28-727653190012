package com.ql.rent.api.vehicle;

import com.ql.rent.api.excel.VehicleRepairMaintenanceExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.vehicle.RepairMaintenanceStatusEnum;
import com.ql.rent.enums.vehicle.VehicleWorkOrderEnum;
import com.ql.rent.param.vehicle.VehicleRepairMaintenanceHandelParam;
import com.ql.rent.param.vehicle.VehicleRepairMaintenanceOrderParam;
import com.ql.rent.param.vehicle.VehicleWorkOrderQueryParam;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.vehicle.*;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/vehicle/repair_maintenance")
@Slf4j
@Api(tags = "维/保单相关")
public class VehicleMaintenanceRepairOrderController {

    @Resource
    private IRepairDepotService repairDepotService;
    @Resource
    private IVehicleRepairMaintenanceOrderService vehicleRepairMaintenanceOrderService;
    @Resource
    private IRepairMaintenanceItemService repairMaintenanceItemService;
    @Resource
    private IVehicleWorkOrderExpensePropService vehicleWorkOrderExpensePropService;
    @Resource
    private ITokenService tokenService;

    @ApiOperation(value = "查询维/保单列表")
    @PostMapping("/list/v1")
    public ResultMap<PageListVo<VehicleRepairMaintenanceVO>> listRepairMaintenancePage(
            HttpServletRequest httpServletRequest,
            @RequestBody VehicleWorkOrderQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            // 权限
            // if (CollectionUtils.isNotEmpty(loginVo.getStoreIdList()) &&
            // loginVo.getStoreIdList().contains(0L)) {
            // } else {
            // queryParam.setStoreIdList(loginVo.getStoreIdList());
            // }
            Result<PageListVo<VehicleRepairMaintenanceVO>> result = vehicleRepairMaintenanceOrderService
                    .listRepairMaintenanceOrder(queryParam, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController listVehicleOrderPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询所有维保项目")
    @PostMapping("/item/list/v1")
    public ResultMap<List<RepairMaintenanceItemVO>> listRepairMaintenanceItem(HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<List<RepairMaintenanceItemVO>> result = repairMaintenanceItemService
                    .listRepairMaintenanceItem(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController listRepairMaintenanceItem", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增维保项目")
    @PostMapping("/item/save/v1")
    public ResultMap<Integer> saveRepairMaintenanceItem(HttpServletRequest httpServletRequest,
            @RequestParam("itemName") @ApiParam("维保项目名称") String itemName) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = repairMaintenanceItemService
                    .saveItem(itemName, loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController saveRepairMaintenanceItem", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除维保项目")
    @PostMapping("/item/delete/v1")
    public ResultMap<Integer> deleteRepairMaintenanceItem(HttpServletRequest httpServletRequest,
            @RequestParam("id") @ApiParam("维保项目id") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = repairMaintenanceItemService
                    .deleteItem(id, loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController deleteRepairMaintenanceItem", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询所有维保厂")
    @PostMapping("/repair_depot/list/v1")
    public ResultMap<List<RepairDepotVO>> listRepairDepot(HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<List<RepairDepotVO>> result = repairDepotService.listRepairDepot(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController listRepairMaintenanceItem", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增维保厂")
    @PostMapping("/repair_depot/save/v1")
    public ResultMap<Integer> saveRepairDepot(HttpServletRequest httpServletRequest,
            @RequestParam("depotName") @ApiParam("维保厂名称") String depotName) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = repairDepotService.saveRepairDepot(depotName,
                    loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController saveRepairDepot", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除维保厂")
    @PostMapping("/repair_depot/delete/v1")
    public ResultMap<Integer> deleteRepairDepot(HttpServletRequest httpServletRequest,
            @RequestParam("id") @ApiParam("维保厂id") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = repairDepotService.deleteRepairDepot(id,
                    loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController deleteRepairDepot", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询费用项，下拉框")
    @PostMapping("/expense_item_prop/list/v1")
    public ResultMap<List<VehicleWorkOrderExpensePropVO>> listExpenseItemProp(HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<List<VehicleWorkOrderExpensePropVO>> result = vehicleWorkOrderExpensePropService
                    .listWorkOrderExpenseProp(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController listExpenseItemProp", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增车辆工单费用项-选项")
    @PostMapping("/expense_item_prop/save/v1")
    public ResultMap<Integer> saveExpenseItemProp(HttpServletRequest httpServletRequest,
            @RequestParam("itemName") @ApiParam("费用选项") String itemName) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleWorkOrderExpensePropService.saveItem(itemName,
                    loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController saveExpenseItemProp", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除车辆工单费用项")
    @PostMapping("/expense_item_prop/delete/v1")
    public ResultMap<Integer> deleteExpenseItemProp(HttpServletRequest httpServletRequest,
            @RequestParam("id") @ApiParam("费用项") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleWorkOrderExpensePropService.deleteItem(id,
                    loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController deleteExpenseItemProp", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增维保单")
    @PostMapping("/save/v1")
    @ApiResponse(code = 200, message = "占用库存时回抛出异常时，抛出错误码:VEHICLE_002")
    @WithSpan("创建车辆工单")
    public ResultMap<Integer> saveRepairMaintenance(HttpServletRequest httpServletRequest,
            @RequestBody VehicleRepairMaintenanceOrderParam param) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withString("param", param.toString()).withLong("车辆ID", param.getVehicleInfoId())
                    .withLong("订单号", param.getRelationOrderId());
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleRepairMaintenanceOrderService.saveRepairMaintenanceOrder(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getResultCode(), result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController saveRepairMaintenance", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR, e.getMessage());
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "处理维保单")
    @PostMapping("/handle/v1")
    public ResultMap<Integer> handleExpenseItemProp(HttpServletRequest httpServletRequest,
            @RequestBody VehicleRepairMaintenanceHandelParam param) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleRepairMaintenanceOrderService
                    .handleRepairMaintenanceOrder(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController handleExpenseItemProp", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询维保单详情")
    @PostMapping("/detail/v1")
    public ResultMap<VehicleRepairMaintenanceDetailVO> getRepairMaintenanceOrderDetail(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("维保单id") Long id) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<VehicleRepairMaintenanceDetailVO> result = vehicleRepairMaintenanceOrderService
                    .getRepairMaintenanceOrderDetail(id);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            if (!loginVo.getMerchantId().equals(result.getModel().getMerchantId())) {
                return ApiResultUtil.failResult("无权限查询");
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController getRepairMaintenanceOrderDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "维保单作废")
    @PostMapping("/cancel/v1")
    @WithSpan("删除车辆工单")
    public ResultMap<Integer> cancelRepairMaintenance(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("维保单id") Long id,
            @RequestParam("cancelReason") @ApiParam("作废原因") String cancelReason) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("工单号", id).withString("取消原因", cancelReason);
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleRepairMaintenanceOrderService
                    .cancelRepairMaintenance(id, cancelReason, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController cancelRepairMaintenanceOrder", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR, e.getMessage());
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "维保单Excel下载", notes = "维保单Excel下载")
    @PostMapping("/v1/downExcel")
    public void downFile(HttpServletRequest request, HttpServletResponse response,
            @RequestBody VehicleWorkOrderQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            queryParam.setMerchantId(loginVo.getMerchantId());

            List<Long> storeIdList = loginVo.getStoreIdList();
            if (storeIdList != null && storeIdList.contains(0L)) {
            } else {
                queryParam.setStoreIdList(storeIdList);
            }
            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "维保单列表",
                    "维保单列表",
                    VehicleRepairMaintenanceExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<VehicleRepairMaintenanceVO>> result = vehicleRepairMaintenanceOrderService
                                .listRepairMaintenanceOrder(queryParam, loginVo);
                        if (!result.isSuccess() || result.getModel() == null) {
                            return new ArrayList<>();
                        }

                        return result.getModel().getList();
                    },
                    vo -> {
                        // 直接转换，避免使用反射
                        VehicleRepairMaintenanceExcelBean bean = new VehicleRepairMaintenanceExcelBean();
                        bean.setWorkOrderNo(vo.getWorkOrderNo());
                        bean.setLicenseNo(vo.getLicenseNo());
                        bean.setVehicleModelName(vo.getVehicleModelName());
                        bean.setStoreNameUnion(vo.getStoreNameUnion());
                        bean.setStartTime(
                                DateUtil.getFormatDateStr(new Date(vo.getStartTime()), DateUtil.yyyyMMddHHmmss));
                        if (vo.getEndTime() != null) {
                            bean.setEndTime(
                                    DateUtil.getFormatDateStr(new Date(vo.getEndTime()), DateUtil.yyyyMMddHHmmss));
                        } else {
                            bean.setEndTime("");
                        }
                        bean.setWorkOrderTypeName(VehicleWorkOrderEnum.getNameByType(vo.getWorkOrderType()));
                        bean.setHandlerUserName(vo.getHandlerUserName());
                        bean.setStatus(RepairMaintenanceStatusEnum.getNameByStatus(vo.getStatus()));

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("VehicleMaintenanceRepairOrderController downExcel {}", e.getMessage());
        }
    }
}
