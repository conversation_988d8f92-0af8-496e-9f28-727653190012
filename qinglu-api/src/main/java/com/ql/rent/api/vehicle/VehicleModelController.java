package com.ql.rent.api.vehicle;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.excel.VehicleModelExcleBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.enums.trade.ServiceItemListVo;
import com.ql.rent.param.vehicle.*;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.BaseSubVehicleSeryVO;
import com.ql.rent.vo.store.ThirdStoreVo;
import com.ql.rent.vo.vehicle.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @desc: 车型相关控制器
 * @author: zhengminjie
 * @time: 2022-09-18 14:53
 * @Version: 1.0
 */
@RestController
@RequestMapping("/vehicle/model")
@Api(tags = "车型信息控制器")
@Slf4j
public class VehicleModelController {

    @Resource
    private IVehicleModelService vehicleModelService;
    @Resource
    private ITokenService tokenService;
    @Resource
    private IVehicleSubSeryService vehicleSubSeryService;
    @Resource
    private IThirdVehicleService thirdVehicleService;
    @Resource
    private IThirdVehicleMatchService vehicleMatchService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IVehicleTagService vehicleTagService;
    @Resource
    private IThirdVehicleMatchService thirdVehicleMatchService;

    /**
     * 新增车型基础信息
     *
     * @param vehicleModelCreateParam 品牌实体
     * @return 是否成功标记
     * <AUTHOR>
     * @date 2022/9/18 15:55
     */
    @ApiOperation(value = "新增车型基础信息", notes = "商家门店新增车型基础数据")
    @PostMapping("/v1/save")
    public ResultMap<Integer> saveVehicleModel(@RequestBody VehicleModelParam.VehicleModelCreateParam vehicleModelCreateParam, HttpServletRequest httpServletRequest) {
        try {
            Result<Integer> result = vehicleModelService.saveVehicleModel(vehicleModelCreateParam,
                tokenService.getUserByRequest(httpServletRequest));
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController saveVehicleBrande", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 修改车型基础信息
     *
     * @param vehicleModelModifyParam 表单参数
     * @return 是否成功标记
     * <AUTHOR>
     * @date 2022/10/6 14:56
     */
    @ApiOperation(value = "修改车型基础信息", notes = "商家门店修改车型数据")
    @PostMapping("/v1/update")
    public ResultMap<Integer> saveVehicleModel(@RequestBody VehicleModelParam.VehicleModelModifyParam vehicleModelModifyParam, HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleModelService.updateVehicleModel(vehicleModelModifyParam, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController saveVehicleBrande", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 根据id 删除车型
     *
     * @param id                 需要删除的车型id
     * @param httpServletRequest 请求对象
     * @return 是否成功
     * <AUTHOR>
     * @date 2022/9/18 16:46
     */
    @ApiOperation(value = "删除车型", notes = "商家门店删除车型")
    @PostMapping("/v1/delete")
    public ResultMap<Integer> deleteBaseVehicleModel(@RequestParam("id") @ApiParam("需要删除的品牌id") Long id, HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = vehicleModelService.deleteVehicleModel(id, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController deleteBaseVehicleModel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * @param queryParam 筛选条件+分页参数
     * @return 列表分页
     * <AUTHOR>
     * @date 2022/9/19 22:43
     */
    @ApiOperation(value = "门店车型列表", notes = "分页筛选商家的车型")
    @PostMapping("/v1/list")
    public ResultMap<PageListVo<VehicleModelListVO>> listVehicleModel(HttpServletRequest httpServletRequest, @RequestBody VehicleModelQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<VehicleModelListVO>> result = vehicleModelService.listVehicleModel(queryParam, loginVo.getLoginName());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController listVehicleModel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * @param request
     * @return 车型Excel下载
     * <AUTHOR>
     * @date 2022/9/19 22:43
     */
    @ApiOperation(value = "车型Excel下载", notes = "车型Excel下载")
    @PostMapping("/v1/downExcel")
    public void downFile(HttpServletRequest request, HttpServletResponse response, @RequestBody VehicleModelQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            queryParam.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "车型列表",
                    "车型列表",
                    VehicleModelExcleBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<VehicleModelListVO>> result = vehicleModelService.listVehicleModel(queryParam, loginVo.getLoginName());
                        if (!result.isSuccess() || result.getModel() == null) {
                            return new ArrayList<>();
                        }

                        return result.getModel().getList();
                    },
                    vo -> {
                        // 直接创建并设置属性，避免使用反射
                        VehicleModelExcleBean bean = new VehicleModelExcleBean();

                        // 基本信息
                        bean.setId(vo.getId());

                        // 车辆规格信息
                        bean.setLicenseType(vo.getLicenseType());
                        bean.setGearbox(vo.getGearbox());
                        bean.setDisplacement(vo.getDisplacement());
                        bean.setSeatNum(vo.getSeatNum());
                        bean.setFuelForm(vo.getFuelForm());

                        // -- 拼接车型字符串
                        StringBuilder unionNameBuilder = new StringBuilder();
                        unionNameBuilder.append(String.format("%s-%s %s ", vo.getId(), vo.getVehicleBrandName(), vo.getVehicleSeryName()));
                        if (StringUtils.isNotEmpty(vo.getVehicleSubSeryName())) {
                            unionNameBuilder.append(vo.getVehicleSubSeryName()).append(" ");
                        }
                        unionNameBuilder.append(vo.getLicenseType());
                        bean.setVehicleModelUnionName(unionNameBuilder.toString());

                        // 设置其他属性
                        bean.setFuelForm(vo.getFuelForm());
                        if (CollectionUtils.isNotEmpty(vo.getTagList())) {
                            List<String> tagList = vo.getTagList().stream().map(VehicleTagVO::getTagName).collect(Collectors.toList());
                            bean.setTagNames(String.join(",", tagList));
                        }

                        // 设置渠道信息
                        setExcelEntityChannel(bean, vo);

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("VehicleBrandController downFile {}", e.getMessage());
        }
    }

    /**
     * 查询车型数据
     *
     * @param id
     * @return 车型数据
     * <AUTHOR>
     */
    @ApiOperation(value = "根据车型id，查询车型的完整数据", notes = "用于车型查看页面")
    @PostMapping("/v1/get_by_id")
    public ResultMap<VehicleModelVO> getModelById(HttpServletRequest request, @RequestParam(value = "id", required = false) @ApiParam("车型id") Long id) {
        try {
            tokenService.getUserByRequest(request);
            Result<VehicleModelVO> result = vehicleModelService.getVehicleModeById(id);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController getModelById", e);
            return ApiResultUtil.failResult(e);
        }
    }


    /**
     * 展示车型 筛选下拉框
     *
     * @param searchKey 用户输入的查询此
     * @param request   http请求对象
     * @return 匹配的车型下拉框列表
     * <AUTHOR>
     * @date 2022/9/26 22:07
     */
    @ApiOperation(value = "查询门店可用的车型列表", notes = "用于车型列表 车型筛选框的下拉列表")
    @PostMapping("/v1/list/vehicle_model/select")
    public ResultMap<List<BaseVehicleModelVO>> listVehicleModel(HttpServletRequest request,
                                                                @RequestParam(value = "search_key", required = false) @ApiParam("用户输入的关键词") String searchKey) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            VehicleModelInnerQuery query = new VehicleModelInnerQuery();
            if (loginVo.isSaasUser() || loginVo.isSaasAdmin()) {
                query.setMerchantId(loginVo.getMerchantId());
            } else if (loginVo.isStoreAdmin() || loginVo.isStoreUser()) {
                query.setMerchantId(loginVo.getMerchantId());
            } else {
                log.error("非法的用户角色 {}", JSON.toJSONString(loginVo));
                return ApiResultUtil.failResult("非法的用户角色");
            }
            //三亚交管账号过滤车型
            List<Long> vehicleModelIdsParam = new ArrayList<>();
            if (query.getStoreId() != null) {
                vehicleModelIdsParam.add(query.getStoreId());
            } else {
                if (CollectionUtils.isNotEmpty(query.getStoreIdList())) {
                    vehicleModelIdsParam = query.getStoreIdList();
                }
            }
            List<VehicleSourceGroupVO> vehicleSourceGroupVOList =
                    vehicleInfoService.findAuthVehicleIds(loginVo.getLoginName(), loginVo.getMerchantId(), vehicleModelIdsParam);
            if (vehicleSourceGroupVOList != null) {
                if (CollectionUtils.isEmpty(vehicleSourceGroupVOList)) {
                    return ApiResultUtil.successResult(Collections.emptyList());
                } else {
                    List<Long> authVehicleModelIds = vehicleSourceGroupVOList.stream()
                            .map(VehicleSourceGroupVO::getVehicleModelId).distinct().collect(Collectors.toList());
                    query.setIdList(authVehicleModelIds);
                }
            }
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.listBaseVehicleModel(query);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController deleteBaseVehicleModel", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "搜索匹配第三方车型")
    @PostMapping("/third_model/v1")
    public ResultMap<List<ThirdVehicleModelVO>> searchThirdVehicle(@RequestBody ThirdModelSearchParam searchParam, HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<ThirdVehicleModelSelectVO> result = vehicleModelService.searchThirdVehicle(searchParam, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleInfoController searchThirdVehicle", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "根据子车系（车型）id，查询子车系的完整数据")
    @PostMapping("/v1/get_sub_sery")
    public ResultMap<BaseSubVehicleSeryVO> getSubSeryById(HttpServletRequest request,
                                                          @RequestParam(value = "id", required = false) @ApiParam("车型id") Long id) {
        try {
            tokenService.getUserByRequest(request);
            Result<BaseSubVehicleSeryVO> result = vehicleSubSeryService.getById(id);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController getSubSeryById", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "车型服务列表")
    @PostMapping("/v1/service/list")
    public ResultMap<ServiceItemListVo> getServiceList(@RequestParam("storeId") @ApiParam("门店ID") Long storeId,
                                                       @RequestParam("vehicleModelId") @ApiParam("车型ID") Long vehicleModelId,
                                                       HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            ServiceItemListVo vo = thirdVehicleService.selectModelServiceItemListVo(loginVo.getMerchantId(), storeId,
                OrderSourceEnum.OFFLINE.getSource().longValue(), vehicleModelId, null, null);
            return ApiResultUtil.successResult(vo);
        } catch (Exception e) {
            log.error("VehicleBrandController getServiceList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 车型信息设置网络平台id
     */
    private void setExcelEntityChannel(VehicleModelExcleBean excelBean, VehicleModelListVO vo) {
        List<VehicleBindVO> channelBindList = vo.getChannelBindList();
        if (CollectionUtils.isEmpty(channelBindList)) {
            return;
        }

        for (VehicleBindVO vehicleBind : channelBindList) {
            // todo 先硬编码
            if (vehicleBind.getChannelId() == 2L) {
                excelBean.setCtrip(vehicleBind.getBindChannelVehicleId());
            } else if (vehicleBind.getChannelId() == 3) {
                excelBean.setFeizhu(vehicleBind.getBindChannelVehicleId());
            } else if (vehicleBind.getChannelId() == 4) {
                excelBean.setHello(vehicleBind.getBindChannelVehicleId());
            } else if (vehicleBind.getChannelId() == 5) {
                excelBean.setZuzuche(vehicleBind.getBindChannelVehicleId());
            } else if (vehicleBind.getChannelId() == 6) {
                excelBean.setWukong(vehicleBind.getBindChannelVehicleId());
            }
        }
    }

    @ApiOperation("车辆适配批量入库")
    @PostMapping("/v1/third/match/add")
    public ResultMap<Integer> addThirdMatchInfo(@RequestBody @ApiParam("车辆适配批量入库") ThirdVehicleBatchAddVo req, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            req.setMerchantId(loginVo.getMerchantId());
            req.setOpUserId(loginVo.getUserId());
            Result<Integer> result = vehicleMatchService.addThirdVehicleInfo(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("车辆适配批量入库失败", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation("车型适配删除")
    @PostMapping("/v1/third/match/delete")
    public ResultMap<String> delThirdMatchInfo(@RequestParam @ApiParam("规则ID") Long id, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<String> result = vehicleMatchService.delMatchInfo(id, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController bindService deleteById error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("车辆适配管理列表")
    @GetMapping("/v1/third/match/list/get")
    public ResultMap<List<ThirdVehicleMatchVo>> delThirdMatchInfo(HttpServletRequest request, @RequestParam @ApiParam("渠道id") Long channelId) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            VehicleBindQueryParam param = new VehicleBindQueryParam();
            param.setChannelId(channelId);
            param.setMerchantId(loginVo.getMerchantId());
            Result<List<ThirdVehicleMatchVo>> result = vehicleMatchService.listVehicleMatchInfo(loginVo.getMerchantId(), channelId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController vehicleMatchService.listVehicleMatchInfo error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("爬虫-三方车型列表")
    @GetMapping("/v1/third/list/get")
    public ResultMap<List<VehicleThirdVO>> thirdStoreList(HttpServletRequest request, @RequestParam @ApiParam("渠道id") Long channelId) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<VehicleThirdVO>> result = vehicleModelService.searchThirdPlatformVehicle(loginVo.getMerchantId(), channelId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController searchThirdPlatformStore error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation("api-三方车型列表:哈啰")
    @PostMapping("/v1/third/list/gds/get")
    public ResultMap<List<VehicleThirdVO>> thirdModelListByApi(HttpServletRequest request,
                                                               @RequestBody ThirdModelSearchParam param ) {

        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<VehicleThirdVO>> result =
                thirdVehicleMatchService.searchThirdPlatformVehicleModelByApi(loginVo.getMerchantId(), param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("StoreInfoController searchThirdPlatformStore error", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "门店车型列表", notes = "根据多个门店ids查询商家车型列表")
    @PostMapping("/v1/queryListByStoreIds")
    public ResultMap<List<BaseVehicleModelVO>> queryListByStoreIds(HttpServletRequest httpServletRequest, @RequestBody VehicleModelQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.queryVehicleModelListByStoreIds(queryParam.getMerchantId(),queryParam.getStoreIdList());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController queryVehicleModelListByStoreIds", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "门店车型列表V2", notes = "根据多个门店ids查询商家车型列表")
    @PostMapping("/v1/store_model")
    public ResultMap<List<BaseVehicleModelVO>> queryListByStoreIdsV2(HttpServletRequest httpServletRequest, @RequestBody VehicleModelQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.queryV2VehicleModelListByStoreIds(queryParam, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController queryListByStoreIdsV2", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "保存车辆标签", notes = "保存车辆标签[分门店]")
    @PostMapping("/v1/save_tag")
    public ResultMap<List<Integer>> saveVehicleTags(HttpServletRequest httpServletRequest,
                                                    @RequestBody List<VehicleTagStoreVO> tagParams) {
        try {
            Result<Integer> result = vehicleTagService.saveVehicleTagV2(tagParams,
                tokenService.getUserByRequest(httpServletRequest));
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController saveVehicleTags", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "查询车辆标签 列表", notes = "查询车辆标签[分门店]")
    @PostMapping("/v1/tags")
    public ResultMap<PageListVo<VehicleModelTagVO>> vehicleTagPage(HttpServletRequest httpServletRequest,
                                                    @RequestBody VehicleModelQueryParam param) {
        try {
            LoginVo user = tokenService.getUserByRequest(httpServletRequest);
            param.setMerchantId(user.getMerchantId());
            Result<PageListVo<VehicleModelTagVO>> result = vehicleModelService.vehicleTagPage(param, user);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController vehicleTagPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "城市门店车型列表", notes = "根据多个城市ids查询商家车型列表")
    @PostMapping("/v1/queryListByCityIds")
    public ResultMap<List<BaseVehicleModelVO>> queryListByCityIds(HttpServletRequest httpServletRequest, @RequestBody VehicleModelQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            Result<List<BaseVehicleModelVO>> result = vehicleModelService.queryVehicleModelListByCityIds(queryParam.getMerchantId(),queryParam.getCityIds());

            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleModelController queryVehicleModelListByCityIds", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
