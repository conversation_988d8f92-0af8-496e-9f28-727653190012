package com.ql.rent.api.trade;

import com.ql.rent.api.excel.OrderBillExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.param.trade.BillRefundAuditParam;
import com.ql.rent.param.trade.BillRefundParam;
import com.ql.rent.param.trade.OrderBillQueryParam;
import com.ql.rent.service.trade.IOrderBillRefundApplyService;
import com.ql.rent.service.trade.IOrderBillService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.OrderBillPageVO;
import com.ql.rent.vo.trade.OrderBillRefundApplyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 账单明细相关接口
 */
@RestController
@Slf4j
@RequestMapping("/order/bill")
@Api(tags = "账单明细相关接口")
public class OrderBillController {

    @Resource
    private ITokenService tokenService;
    @Resource
    private IOrderBillRefundApplyService orderBillRefundApplyService;
    @Resource
    private IOrderBillService orderBillService;

    @ApiOperation(value = "分页查询账单明细列表")
    @PostMapping("/list/v1")
    public ResultMap<OrderBillPageVO> listPage(@RequestBody OrderBillQueryParam queryParam,
                                               HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            if (loginVo.isSaasUser() || loginVo.isSaasAdmin()) {
                if (queryParam.getMerchantId() == null) {
                    return ApiResultUtil.successResult("未选择商家id");
                }
            } else {
                queryParam.setMerchantId(loginVo.getMerchantId());
            }
            Result<PageListVo<OrderBillPageVO>> result = orderBillService.listOrderBillPage(queryParam);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderBillController listPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "账单明细Excel下载", notes = "账单明细Excel下载")
    @PostMapping("/downExcel/v1")
    public void downFile(HttpServletRequest request, HttpServletResponse response,
                         @RequestBody OrderBillQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            if (loginVo.isSaasUser() || loginVo.isSaasAdmin()) {
                if (queryParam.getMerchantId() == null) {
                    return;
                }
            } else {
                queryParam.setMerchantId(loginVo.getMerchantId());
            }

            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "账单明细",
                    "账单明细",
                    OrderBillExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<OrderBillPageVO>> result = orderBillService.listOrderBillPage(queryParam);
                        if (!result.isSuccess() || result.getModel() == null || CollectionUtils.isEmpty(result.getModel().getList())) {
                            return new ArrayList<>();
                        }

                        return result.getModel().getList();
                    },
                    OrderBillExcelBean::toExcelBean
            );
        } catch (Exception e) {
            log.error("OrderBillController downExcel", e);
        }
    }

    @ApiOperation(value = "查询退款申请详情")
    @PostMapping("/get_refund_apply/v1")
    public ResultMap<OrderBillPageVO> getActiveRefund(@RequestParam("orderBillId") @ApiParam("账单明细id") Long orderBillId,
                                                      HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<OrderBillRefundApplyVO> result =
                orderBillRefundApplyService.getActiveApplyByBillId(orderBillId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            // 越权检查
            if (!(loginVo.isSaasUser() && loginVo.isSaasAdmin())) {
//                OrderBillRefundApplyVO apply = result.getModel();
//                if (!loginVo.getMerchantId().equals(apply.getMerchantId())) {
//                    return ApiResultUtil.failResult("无权限查看");
//                }
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderBillController getActiveRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "账单明细-发起退款申请")
    @PostMapping("/apply_refund/v1")
    public ResultMap<Integer> applyRefund(@RequestBody BillRefundParam refundParam,
                                          HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = orderBillRefundApplyService.applyRefund(refundParam, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderBillController applyRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "账单明细-审核退款")
    @PostMapping("/audit_refund/v1")
    public ResultMap<Integer> auditRefund(@RequestBody BillRefundAuditParam refundParam,
                                          HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            // 越权检查
//            if (!(loginVo.isSaasUser() && loginVo.isSaasAdmin())) {
//                return ApiResultUtil.failResult("无权限操作");
//            }
            Result<Integer> result = orderBillRefundApplyService.auditOrderBillRefund(refundParam, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderBillController auditRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 临时扣商家年费
     */
    @PostMapping("/v1/year_fee")
    public ResultMap<OrderBillPageVO> getActiveRefund(@RequestParam("merchantId")  Long merchantId,
                                                      @RequestParam("amount") Long amount,
                                                      @RequestParam("remark") String remark,
                                                      HttpServletRequest httpServletRequest) {
        try {
            Result<Integer> result =
                orderBillService.decutionYearFee(merchantId, amount, remark);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderBillController getActiveRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
