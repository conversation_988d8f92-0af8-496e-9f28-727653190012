package com.ql.rent.api.vehicle;

import com.ql.rent.api.excel.ShuntingExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.enums.vehicle.ShuntingStatusEnum;
import com.ql.rent.param.vehicle.ShuntingListParam;
import com.ql.rent.param.vehicle.ShuntingListQueryParam;
import com.ql.rent.service.vehicle.IShuntingListService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.vehicle.ShuntingListVO;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.With;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @time 2022-10-18 22:30
 * @Version 1.0
 */
@Api(tags = "调车单相关")
@RestController
@Slf4j
@RequestMapping("/vehicle/shunting")
public class ShuntingListController {

    @Resource
    private IShuntingListService shuntingListService;
    @Resource
    private ITokenService tokenService;

    @ApiOperation(value = "查询调车单列表")
    @PostMapping("/v1/list")
    public ResultMap<PageListVo<ShuntingListVO>> listPage(@RequestBody ShuntingListQueryParam queryParam,
            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            queryParam.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<ShuntingListVO>> result = shuntingListService.listShuntingListPage(queryParam,
                    loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController listPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "工单Excel下载", notes = "工单Excel下载")
    @PostMapping("/v1/downExcel")
    public void downFile(HttpServletRequest request,
            HttpServletResponse response,
            @RequestBody ShuntingListQueryParam queryParam) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            queryParam.setMerchantId(loginVo.getMerchantId());

            // 使用流式下载，避免一次性加载大量数据到内存
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "调车单列表",
                    "调车单列表",
                    ShuntingExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        queryParam.setPageIndex(pageIndex);
                        queryParam.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<ShuntingListVO>> result = shuntingListService.listShuntingListPage(queryParam, loginVo);
                        if (!result.isSuccess() || result.getModel() == null || CollectionUtils.isEmpty(result.getModel().getList())) {
                            return new ArrayList<>();
                        }

                        return result.getModel().getList();
                    },
                    // 转换函数，直接映射字段而不使用反射
                    vo -> {
                        ShuntingExcelBean bean = new ShuntingExcelBean();

                        // 直接赋值，避免使用反射
                        bean.setLicense(vo.getLicense());
                        bean.setVehicleModelName(vo.getVehicleModelName());
                        bean.setTransferOutStoreName(vo.getTransferOutStoreName());
                        bean.setTransferInStoreName(vo.getTransferInStoreName());
                        bean.setShunter(vo.getShunter());
                        bean.setRemark(vo.getRemark());

                        // 设置状态
                        bean.setStatus(ShuntingStatusEnum.getNameByStatus(vo.getShuntingStatus()));

                        // 格式化调出时间
                        if (vo.getTransferOutTime() != null) {
                            bean.setTransferOutTime(DateUtil.getFormatDateStr(
                                    new Date(vo.getTransferOutTime()), DateUtil.yyyyMMddHHmmss));
                        }

                        // 格式化调入时间
                        if (vo.getTransferInTime() != null) {
                            bean.setTransferInTime(DateUtil.getFormatDateStr(
                                    new Date(vo.getTransferInTime()), DateUtil.yyyyMMddHHmmss));
                        }

                        return bean;
                    }
            );
        } catch (Exception e) {
            log.error("ShuntingListController downExcel {}", e.getMessage());
        }
    }

    @ApiOperation(value = "新增/修改调车单")
    @PostMapping("/v1/save")
    @WithSpan("创建调拨单")
    public ResultMap<Long> saveShunting(@RequestBody ShuntingListParam shuntingListParam,
            HttpServletRequest httpServletRequest) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withJson("param", shuntingListParam).withLong("车辆ID",
                    null != shuntingListParam ? shuntingListParam.getVehicleInfoId() : 0L);
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            shuntingListParam.setMerchantId(loginVo.getMerchantId());
            Result<Long> result = shuntingListService.saveOrUpdate(shuntingListParam, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController saveShunting", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR, e.getMessage());
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除调车单")
    @PostMapping("/v1/delete")
    public ResultMap<Integer> deleteShunting(@RequestParam Long id,
            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = shuntingListService.delete(id, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController delete", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "根据id查询")
    @PostMapping("/v1/get_by_id")
    public ResultMap<ShuntingListVO> getById(@RequestParam("id") @ApiParam("调车单id") Long id,
            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<ShuntingListVO> result = shuntingListService.getById(id);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController getById", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 确认到达
     *
     * @param id 调车单id
     */
    @ApiOperation(value = "确认到达")
    @PostMapping("/v1/confirm_arrived")
    public ResultMap<ShuntingListVO> confirmArrived(@RequestParam("id") @ApiParam("调车单id") Long id,
            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = shuntingListService.confirmArrive(id, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController confirmArrived", e);
            return ApiResultUtil.failResult(e.getMessage());
        }
    }

    @ApiOperation(value = "作废")
    @PostMapping("/cancel/v1")
    public ResultMap<Integer> cancelShunting(
            HttpServletRequest httpServletRequest, @RequestParam("id") @ApiParam("调拨单id") Long id,
            @RequestParam("cancelReason") @ApiParam("作废原因") String cancelReason) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<Integer> result = shuntingListService.cancelShunting(id, cancelReason, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("ShuntingListController cancelShunting", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
