package com.ql.rent.api.etc;



import com.ql.rent.api.excel.EtcOrderChargeExcelBean;
import com.ql.rent.api.excel.EtcVehicleExcelBean;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.ITokenService;
import com.ql.rent.param.etc.EtcDeviceReq;
import com.ql.rent.param.etc.EtcOrderChargeReq;
import com.ql.rent.param.etc.EtcOrderChargeResp;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.CurrencyUtils;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.etc.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "etc相关")
@RestController
@Slf4j
@RequestMapping("/etc/device")
public class EtcDeviceController {
    private final Logger logger = LoggerFactory.getLogger(EtcDeviceController.class);
    @Resource
    ITokenService tokenService;
    @Resource
    EtcDeviceService etcDeviceService;

    //etc 设备管理 分页查询
    @ApiOperation(value = "设备管理 分页查询")
    @PostMapping("/v1/obtainDeviceList")
    public ResultMap<PageListVo<EtcDeviceVo>> obtainDeviceList(@RequestBody EtcDeviceReq req,
                                                               HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<EtcDeviceVo>> result = etcDeviceService.obtainDeviceList(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController obtainDeviceList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    //获取etc车辆列表
    @ApiOperation(value = "获取etc车辆列表")
    @PostMapping("/v1/obtainVehicleList")
    public ResultMap<PageListVo<EtcVehicleVo>> obtainEtcVehicleList(@RequestBody EtcDeviceReq req,
                                                                HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<EtcVehicleVo>> result = etcDeviceService.obtainEtcVehicleList(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController obtainEtcVehicleList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "导出ETC车辆数据")
    @PostMapping("/v1/exportVehicle")
    public void exportVehicle(@RequestBody EtcDeviceReq req,
                                         HttpServletRequest request,HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            req.setPageIndex(1);
            req.setPageSize(1000);
            req.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<EtcVehicleVo>> result = etcDeviceService.obtainEtcVehicleList(req);
            List<EtcVehicleVo> list = result.getModel().getList();
            List<EtcVehicleExcelBean> excelBeans = list.stream().map(m -> {
                EtcVehicleExcelBean excelBean = new EtcVehicleExcelBean();
                BeanUtils.copyProperties(m, excelBean);
                excelBean.setTotalEtcOrderCount(m.getTotalEtcOrderCount()+"");
                return excelBean;
            }).collect(Collectors.toList());
            EasyExcelUtils.downLoad(httpResource, excelBeans, EtcVehicleExcelBean.class, "ETC车辆导出");
        } catch (Exception e) {
            log.error("EtcDeviceController exportVehicle", e);
            throw new RuntimeException(e);
        }
    }

    // 获取单个etc车辆信息
    @ApiOperation(value = "获取单个etc车辆信息")
    @PostMapping("/v1/getEtcVehicleById")
    public ResultMap<EtcVehicleVo> getEtcVehicleById(@RequestBody EtcDeviceReq req,
                                                      HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            Result<EtcVehicleVo> result = etcDeviceService.getEtcVehicleById(req.getVehicleId(),loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController getEtcVehicleById", e);
            return ApiResultUtil.failResult(e);
        }
    }

    //etc
    @ApiOperation(value = "etc 收益明细")
    @PostMapping("/v1/obtainRevenueDetails")
    public ResultMap<PageListVo<EtcOrderChargeVo>> obtainWithdrawalAmount(@RequestBody EtcDeviceReq req,
                                                                        HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<EtcOrderChargeVo>> result = etcDeviceService.obtainRevenueDetails(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController obtainWithdrawalAmount", e);
            return ApiResultUtil.failResult(e);
        }
    }
    // 当前可提现金额
    @ApiOperation(value = "当前可提现金额")
    @PostMapping("/v1/obtainWithdrawalAmount")
    public ResultMap<EtcOrderChargeResp> obtainRevenueDetails(@RequestBody EtcOrderChargeReq req,
                                                              HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            Result<EtcOrderChargeResp> result = etcDeviceService.obtainWithdrawalAmount(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController obtainRevenueDetails", e);
            return ApiResultUtil.failResult(e);
        }
    }
    // etc 提现按钮
    @ApiOperation(value = "提现按钮")
    @PostMapping("/v1/withdrawal")
    public ResultMap<Boolean> withdrawal(@RequestBody EtcOrderChargeReq req,
                                                                        HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            req.setOpUserId(loginVo.getUserId());
            Result<Boolean> result = etcDeviceService.withdrawal(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }

            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController withdrawal", e);
            return ApiResultUtil.failResult(e);
        }
    }

    // etc提现明细
    @ApiOperation(value = "提现明细")
    @PostMapping("/v1/obtainWithdrawal")
    public ResultMap<PageListVo<WithdrawalRecordInfoVo>> obtainWithdrawal(HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            EtcOrderChargeReq req = new EtcOrderChargeReq();
            req.setMerchantId(loginVo.getMerchantId());
            req.setOpUserId(loginVo.getUserId());
            Result<PageListVo<WithdrawalRecordInfoVo>> result = etcDeviceService.obtainWithdrawal(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController obtainWithdrawal", e);
            return ApiResultUtil.failResult(e);
        }
    }
    /**
     * etc订单列表
     */
    @ApiOperation(value = "检索etc订单号")
    @PostMapping("/v1/searchEtcOrderList")
    public ResultMap<List<String>> searchEtcOrderList(@RequestBody EtcOrderChargeReq req,
                                                                          HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            req.setMerchantId(loginVo.getMerchantId());
            req.setOpUserId(loginVo.getUserId());
            Result<List<String>> result = etcDeviceService.searchEtcOrderList(req);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController searchEtcOrderList", e);
            return ApiResultUtil.failResult(e);
        }
    }
    /**
     * 提现单收益明细导出
     */
    @ApiOperation(value = "提现单收益明细导出")
    @PostMapping("/v1/exportWithdrawalIncome")
    public void exportWithdrawalIncome(@RequestParam @ApiParam(value = "数据id",required = true) Long id,
                                                          HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            //根据提现单 查询 已提现收益明细
            List<EtcOrderChargeVo> etcOrderChargeVos = etcDeviceService.exportWithdrawalIncome(loginVo.getMerchantId(),id);

            List<EtcOrderChargeExcelBean> excelBeans = etcOrderChargeVos.stream().map(m -> {
                EtcOrderChargeExcelBean excelBean = new EtcOrderChargeExcelBean();
                BeanUtils.copyProperties(m, excelBean);
                excelBean.setStoreId(m.getStoreId().toString());
                excelBean.setTenancyFee(CurrencyUtils.saas2Ctrip(m.getTenancyFee(), 2).stripTrailingZeros().toPlainString());
                excelBean.setMerchantProfit(CurrencyUtils.saas2Ctrip(m.getMerchantProfit(), 2).stripTrailingZeros().toPlainString());
                excelBean.setCreateTime(DateUtil.getFormatDateStr(new Date(m.getCreateTime()),"yyyy-MM-dd HH:mm:ss"));
                excelBean.setOrderStatus(m.getOrderStatus());
                if(m.getEndPaymentTime()!=null){
                    excelBean.setEndPaymentTime(DateUtil.getFormatDateStr(new Date(m.getCreateTime()),"yyyy-MM-dd HH:mm:ss"));
                }
                return excelBean;
            }).collect(Collectors.toList());
            EasyExcelUtils.downLoad(httpResource, excelBeans, EtcOrderChargeExcelBean.class, "提现单明细导出");
        } catch (IOException e) {
            logger.error("EtcDeviceController exportWithdrawalIncome", e);
            throw new RuntimeException(e);
        }
    }
    /**
     * 获取已支付 订单支付方式
     */
    @ApiOperation(value = "获取已支付 订单支付方式")
    @PostMapping("/v1/obtainPaymentMethod")
    public  ResultMap<Map<Byte,String>> obtainPaymentMethod(HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Map<Byte,String> result = etcDeviceService.obtainPaymentMethod(loginVo.getMerchantId());
            return ApiResultUtil.successResult(result);
        } catch (Exception e) {
            log.error("EtcDeviceController obtainPaymentMethod", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 基于车牌号 检索 etc设备
     */
    @ApiOperation(value = "基于车牌号 检索 etc设备")
    @PostMapping("/v1/retrieveSampleEquipment")
    public  ResultMap<Map<Byte,String>> retrieveSampleEquipment(@RequestBody EtcDeviceVo samplesVo,HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            samplesVo.setMerchantId(loginVo.getMerchantId());
            Result<List<EtcDeviceVo>> result = etcDeviceService.retrieveSampleEquipment(samplesVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController retrieveSampleEquipment", e);
            return ApiResultUtil.failResult(e);
        }
    }
    /**
     * 基于样本数据 新增 设备
     */
    @ApiOperation(value = "基于样本数据 新增 设备")
    @PostMapping("/v1/addCopySample")
    public  ResultMap<Map<Byte,String>> addCopySample(@RequestBody EtcDeviceVo samplesVo,HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            samplesVo.setMerchantId(loginVo.getMerchantId());
            Result<Boolean> result = etcDeviceService.addCopySample(samplesVo,loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("EtcDeviceController addCopySample", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
