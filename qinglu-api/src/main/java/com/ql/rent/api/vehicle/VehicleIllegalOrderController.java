package com.ql.rent.api.vehicle;

import com.alibaba.excel.EasyExcel;
import com.ql.carengine.client.GpsRpc;
import com.ql.carengine.request.gps.GpsImportDeviceParam;
import com.ql.carengine.result.CarEngineBaseResponse;
import com.ql.rent.api.excel.*;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.IRedisService;
import com.ql.rent.common.ITokenService;
import com.ql.rent.constant.MallServiceItemConstant;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.trade.IllegalOrderFromSourceEnum;
import com.ql.rent.param.trade.*;
import com.ql.rent.service.trade.IVehicleIllegalOrderService;
import com.ql.rent.service.trade.IVehicleIllegalSearchService;
import com.ql.rent.service.trade.IVehicleIllegalTransferDetailService;
import com.ql.rent.share.result.*;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.ShuntingListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/vehicle/illegal_order")
@Api("车辆管理-违章记录相关接口")
public class VehicleIllegalOrderController {
    @Resource
    private ITokenService tokenService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehicleIllegalSearchService vehicleIllegalSearchService;
    @Resource
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;
    @Resource
    private IRedisService redisService;

    @ApiOperation(value = "分页查询违章列表")
    @PostMapping("/list/v1")
    public ResultMap<PageListVo<VehicleIllegalOrderPageVO>> listIllegalOrderPage(@RequestBody
                                                                                 VehicleIllegalOrderQueryParam query,
                                                                                 HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            // 商家用户，取自身商家id；平台用户根据页面传入
            query.setMerchantId(loginVo.getMerchantId());

            Result<PageListVo<VehicleIllegalOrderPageVO>> result =
                vehicleIllegalOrderService.listIllegalOrderPage(query, loginVo.getLoginName());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController listIllegalOrderPage", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 车辆管理 - 新增违章记录
     */
    @ApiOperation(value = "新增违章记录")
    @PostMapping("/save/v1")
    public ResultMap<Integer> save(@RequestBody @ApiParam("表单参数") VehicleIllegalOrderParam illegalOrder,
                                   HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            illegalOrder.setFromSource(IllegalOrderFromSourceEnum.VEHICLE.getSource());
            Result<Long> result = vehicleIllegalOrderService.saveIllegalOrderV2(illegalOrder, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController save", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "统计未处理、未缴纳 的违章数量")
    @PostMapping("/unhandle_num/v1")
    public ResultMap<UnHandlerIllegalOrderNumVO> countUnHandleNum(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<UnHandlerIllegalOrderNumVO> result =
                vehicleIllegalOrderService.countUnHandlerNum(loginVo.getMerchantId(), loginVo.getLoginName());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController countUnHandleNum", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询违章单详情")
    @PostMapping("/detail/v1")
    public ResultMap<VehicleIllegalOrderDetailVO> getIllegalOrderDetail(@RequestParam(value = "id", required = false)
                                                                        @ApiParam("违章单id") Long id,
                                                                        HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<VehicleIllegalOrderDetailVO> result =
                vehicleIllegalOrderService.getIllegalOrderDetail(id, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController getIllegalOrderDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "处理违章记录")
    @PostMapping("/handle/v1")
    public ResultMap<Integer> handleIllegalOrder(@RequestBody @ApiParam("表单参数") IllegalOrderHandleParam param,
                                                 HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> result = vehicleIllegalOrderService.handleIllegalOrder(param, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController handleIllegalOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "批量导入违章记录")
    @PostMapping("/import/v1")
    public ResultMap<Integer> importIllegalOrder(MultipartFile file, HttpServletRequest request) {
        try {
            LoginVo loginUser = tokenService.getUserByRequest(request);
            EasyExcel.read(file.getInputStream(), IllegalOrderImportExcelBean.class,
                new VehicleIllegalExcelListener(vehicleIllegalOrderService, loginUser)).sheet().doRead();
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController importIllegalOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "分页查询违章列表")
    @PostMapping("/v1/export")
    public void exportIllegalOrderPage(@RequestBody VehicleIllegalOrderQueryParam query,
                                       HttpServletRequest httpServletRequest, HttpServletResponse response) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            query.setMerchantId(loginVo.getMerchantId());
            query.setPageIndex(1);
            query.setPageSize(100000);
            Result<PageListVo<VehicleIllegalOrderPageVO>> result =
                vehicleIllegalOrderService.listIllegalOrderPage(query, loginVo.getLoginName());
            List<VehicleIllegalOrderExcelBean> list =
                VehicleIllegalOrderExcelBean.toExcelBean(result.getModel().getList());
            String name = "违章单列表";
            EasyExcelUtils.downLoad(response, list, VehicleIllegalOrderExcelBean.class, name);
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController exportIllegalOrderPage", e);
        }
    }

    /**
     * 根据Id修改订单
     */
    @ApiOperation(value = "根据Id修改订单")
    @PostMapping("/update/order/v1")
    public ResultMap<ShuntingListVO> updateOrderIdById(@RequestParam("id") @ApiParam("违章记录id") Long id,
                                                       @RequestParam("orderId") @ApiParam("订单id") Long orderId,
                                                       HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            VehicleIllegalOrderParam vehicleIllegalOrderParam = new VehicleIllegalOrderParam();
            vehicleIllegalOrderParam.setId(id);
            vehicleIllegalOrderParam.setOrderId(orderId);
            Result<Boolean> result = vehicleIllegalOrderService.updateIllegalOrderById(vehicleIllegalOrderParam, loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController updateOrderIdById", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 查询违章(By三方源)
     */
    @ApiOperation(value = "查询违章(By三方源)")
    @PostMapping("/search/v1")
    public ResultMap<VehicleIllegalSearchVO> listIllegalSearchAndSave(@RequestBody List<VehicleIllegalSearchParam> vehicleIllegalSearchParamList,
                                                       HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);;
            Result<VehicleIllegalSearchVO> result = vehicleIllegalSearchService.listIllegalSearchAndSave(vehicleIllegalSearchParamList, loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController listIllegalSearchAndSave", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 查询违章(By三方源)消耗记录
     */
    @ApiOperation(value = "查询违章(By三方源)消耗记录")
    @PostMapping("/record/v1")
    public ResultMap<PageListVo<VehicleIllegalRecordVO>> listBaseIllegalRecord(@RequestBody VehicleIllegalRecordQuery illegalRecordQuery,
                                                                  HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            illegalRecordQuery.setMerchantId(loginVo.getMerchantId());
            Result<PageListVo<VehicleIllegalRecordVO>> result = vehicleIllegalSearchService.listBaseIllegalRecord(illegalRecordQuery);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController listBaseIllegalRecord", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 下载详细消耗记录
     */
    @ApiOperation(value = "下载详细消耗记录")
    @PostMapping("/record/downExcel/v1")
    public ResultMap downloadIllegalRecord(@RequestBody VehicleIllegalRecordQuery illegalRecordQuery,
                                                                  HttpServletRequest request, HttpServletResponse response) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            illegalRecordQuery.setMerchantId(loginVo.getMerchantId());

            String rdkey = "IllegalRecordImport:" + loginVo.getMerchantId() + "_" + loginVo.getUserId();
            long check = redisService.setnx(rdkey, 30L);
            if (check > 1) {
                return ApiResultUtil.failResult("请勿频繁操作");
            }

            // 使用流式下载
            EasyExcelUtils.streamDownloadWithConvert(
                    response,
                    "查询违章消耗详情",
                    "查询违章消耗详情",
                    VehicleIllegalRecordExcelBean.class,
                    pageIndex -> {
                        // 设置分页参数
                        illegalRecordQuery.setPageIndex(pageIndex);
                        illegalRecordQuery.setPageSize(EasyExcelUtils.DEFAULT_PAGE_SIZE);

                        // 查询当前页数据
                        Result<PageListVo<VehicleIllegalRecordDetailVO>> pageResult =
                                vehicleIllegalSearchService.listIllegalRecord(illegalRecordQuery);
                        if (!pageResult.isSuccess() || pageResult.getModel() == null) {
                            return new ArrayList<>();
                        }

                        // 如果有数据，更新最小ID以便下次查询
                        List<VehicleIllegalRecordDetailVO> pageList = pageResult.getModel().getList();
                        if (CollectionUtils.isNotEmpty(pageList)) {
                            illegalRecordQuery.setMinId(pageList.get(pageList.size() - 1).getId());
                        }

                        return pageList;
                    },
                    vo -> {
                        // 直接创建并设置属性，避免使用反射
                        VehicleIllegalRecordExcelBean bean = new VehicleIllegalRecordExcelBean();
                        bean.setOpTime(DateUtil.getFormatDateStr(new Date(vo.getOpTime()), DateUtil.yyyyMMddHHmmss));

                        // 状态信息
                        bean.setStatus(vo.getStatus() == YesOrNoEnum.YES.getValue() ? "成功" : "失败");
                        bean.setFailReason(vo.getFailReason());
                        bean.setLicenseNo(vo.getLicenseNo());
                        bean.setItemName(vo.getItemName());
                        bean.setOpUser(vo.getOpUser());
                        bean.setServiceOrderNo(vo.getServiceOrderNo());
                        // 剩余次数信息
                        if (MallServiceItemConstant.ILLEGAL.TRANSFER.equals(vo.getItemSubPackage())) {
                            bean.setRemainingCount(null);
                        } else {
                            bean.setRemainingCount(vo.getRemainingCount());
                        }

                        return bean;
                    }
            );

            return ApiResultUtil.successResult(null);
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController downloadIllegalRecord {}", e.getMessage());
            return ApiResultUtil.failResult("下载明细异常");
        }
    }

    /**
     * 保存自动违章查询(By三方源)规则
     */
    @ApiOperation(value = "保存自动违章查询(By三方源)规则")
    @PostMapping("/setting/save/v1")
    public ResultMap<Boolean> saveSearchIllegalSetting(@RequestBody VehicleIllegalSettingParam param,
                                                                  HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            param.setMerchantId(loginVo.getMerchantId());
            Result<Boolean> result = vehicleIllegalSearchService.saveAutoSearchIllegalSetting(param, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController saveSearchIllegalSetting", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 查询自动违章查询(By三方源)规则
     */
    @ApiOperation(value = "查询自动违章查询(By三方源)规则")
    @PostMapping("/setting/find/v1")
    public ResultMap<VehicleIllegalSettingVO> saveSearchIllegalSetting(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<VehicleIllegalSettingVO> result = vehicleIllegalSearchService.getVehicleIllegalSetting(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController saveSearchIllegalSetting", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 违章查询进度
     */
    @ApiOperation(value = "违章查询进度")
    @PostMapping("/search/progress/v1")
    public ResultMap<IllegalSearchProgressVO> searchIllegalProgress(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<IllegalSearchProgressVO> result = vehicleIllegalSearchService.getProgress(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController searchIllegalProgress", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 违章转移-车辆绑定
     */
    @ApiOperation(value = "违章转移-车辆绑定")
    @PostMapping("/transfer/vehicle/save/v1")
    public ResultMap<Boolean> transferVehicleBind(HttpServletRequest request,
                                                                  @RequestBody List<Long> vehicleIdList,
                                                                  @RequestParam("orderId") Long orderId) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = vehicleIllegalTransferDetailService.saveTransferVehicleBind(loginVo, vehicleIdList, orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController transferVehicleBind", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 违章转移-查询车辆绑定
     */
    @ApiOperation(value = "违章转移-查询车辆绑定")
    @PostMapping("/transfer/vehicle/list/v1")
    public ResultMap<List<VehicleIllegalTransferDetailVo>> listTransferVehicleBind(HttpServletRequest request,
                                                                  @RequestBody TransferVehicleBindQuery query) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            query.setMerchantId(loginVo.getMerchantId());
            Result<List<VehicleIllegalTransferDetailVo>> result = vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController listTransferVehicleBind", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * todo 测试违章查询订阅公众号消息
     * @param request
     * @return
     */
    @PostMapping("/search/auto/wx/test")
    public ResultMap<Boolean> testPushForIllegalNotEnough(HttpServletRequest request, @RequestParam("timestamp") Long timestamp, @RequestParam("merchantId") Long merchantId) {
        try {
            vehicleIllegalSearchService.testPushForIllegalNotEnough(merchantId, new Date(timestamp));
            return ApiResultUtil.successResult(true);
        } catch (Exception e) {
            log.error("Vehicle VehicleIllegalOrderController testPushForIllegalNotEnough", e);
            return ApiResultUtil.failResult(e);
        }
    }
}
