package com.ql.dto.vehicle;

import lombok.Data;

@Data
public class VehicleEtcSyncReq {
    /**
     * 好人好车侧车辆唯一标识（可根据车辆id、车牌号二选一）优先使用vehId查询
     */
    private String vehId;
    /**
     * 车牌：京A00001
     * 说明：好人好车使用车牌+颜色作为车辆的逻辑唯一标识
     */
    private String plateNumber;
    /**
     *车牌颜色，枚举类
     * 0-蓝
     * 1-黄
     * 2-黑
     * 3-白
     * 4-绿白
     * 5-绿黄
     * 6-绿
     */
    private String plateColor;
    /**
     *企业侧车辆id
     */
    private String corpVehicleId;
    /**
     *车型种类，枚举值
     * 2：客车
     */
    private String type;
    /**
     *车辆型号：东风日产牌DFL7151VBL5
     */
    private String model;
    /**
     *车辆识别代码（车架号）
     */
    private String vin;
    /**
     *车辆发动机号
     */
    private String engineNo;
    /**
     *车轴数
     */
    private String axles;
    /**
     *车身长mm
     */
    private String length;
    /**
     *车身宽mm
     */
    private String width;
    /**
     *车身高mm
     */
    private String height;
    /**
     *总质量kg
     */
    private String totalWeight;
    /**
     *核定载人数
     */
    private String ac;
    /**
     *车身重量(整备质量)kg
     */
    private String grossMass;
    /**
     *车辆注册日期(格式“yyyy-MM-dd HH:mm:ss”)
     */
    private String registerDate;
    /**
     *行驶证发证日期(格式“yyyy-MM-dd HH:mm:ss”)
     */
    private String grantDate;
    /**
     *车辆所有人
     */
    private String ownerName;
    /**
     *设备id
     */
    private String deviceId;
    private String bizCode;


    //etc申请单状态
    //ORDER_CREATE:订单创建;ORDER_SYNCED:订单已同
    //步;SUCCESS_ACTIVATE:订单已激活;UNMOUNTING:注销
    //中;UNMOUNTED:已注销;
    private String order_status;

    /**
     * etc平台扣款协议号
     */
    private String biz_agreement_no;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * etc设备状态
     * USABLE-设备激活可⽤（可上⾼速正常使⽤）PENDING-设备激活挂
     * 起（限制消费） UNUSABLE-设备异常不可⽤
     */
    private String device_status;

    /**
     * 设备状态明细，能清楚说明etc设备此时状态（/卡签注销/卡签挂失/
     * 已过户/维修中/⿊名单/卡过期/⽋费/标签脱落/设备报警/正常/ETC
     * 停⽤等）
     */
    private String device_status_detail;
}
