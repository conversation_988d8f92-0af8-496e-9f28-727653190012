<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ql.rent.dao.trade.MallServiceOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.ql.rent.entity.trade.MallServiceOrderInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="temp_version" jdbcType="INTEGER" property="tempVersion" />
    <result column="out_id" jdbcType="VARCHAR" property="outId" />
    <result column="item_type" jdbcType="TINYINT" property="itemType" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="remaining_count" jdbcType="INTEGER" property="remainingCount" />
    <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="op_time" jdbcType="BIGINT" property="opTime" />
    <result column="op_user_id" jdbcType="BIGINT" property="opUserId" />
    <result column="item_count" jdbcType="INTEGER" property="itemCount" />
    <result column="order_price" jdbcType="INTEGER" property="orderPrice" />
    <result column="item_sub_package" jdbcType="VARCHAR" property="itemSubPackage" />
    <result column="order_deposit" jdbcType="INTEGER" property="orderDeposit" />
    <result column="device_source" jdbcType="INTEGER" property="deviceSource" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, temp_version, out_id, item_type, item_code, remaining_count, expiration_date, 
    order_status, merchant_id, deleted, create_time, create_user_id, op_time, op_user_id, 
    item_count, order_price, item_sub_package, order_deposit, device_source
  </sql>
  <select id="selectByExample" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from mall_service_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mall_service_order_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mall_service_order_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfoExample">
    delete from mall_service_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_service_order_info (order_no, temp_version, out_id, 
      item_type, item_code, remaining_count, 
      expiration_date, order_status, merchant_id, 
      deleted, create_time, create_user_id, 
      op_time, op_user_id, item_count, 
      order_price, item_sub_package, order_deposit, 
      device_source)
    values (#{orderNo,jdbcType=VARCHAR}, #{tempVersion,jdbcType=INTEGER}, #{outId,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=TINYINT}, #{itemCode,jdbcType=VARCHAR}, #{remainingCount,jdbcType=INTEGER}, 
      #{expirationDate,jdbcType=TIMESTAMP}, #{orderStatus,jdbcType=TINYINT}, #{merchantId,jdbcType=BIGINT}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{createUserId,jdbcType=BIGINT}, 
      #{opTime,jdbcType=BIGINT}, #{opUserId,jdbcType=BIGINT}, #{itemCount,jdbcType=INTEGER}, 
      #{orderPrice,jdbcType=INTEGER}, #{itemSubPackage,jdbcType=VARCHAR}, #{orderDeposit,jdbcType=INTEGER}, 
      #{deviceSource,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mall_service_order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="tempVersion != null">
        temp_version,
      </if>
      <if test="outId != null">
        out_id,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="remainingCount != null">
        remaining_count,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="opUserId != null">
        op_user_id,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="itemSubPackage != null">
        item_sub_package,
      </if>
      <if test="orderDeposit != null">
        order_deposit,
      </if>
      <if test="deviceSource != null">
        device_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tempVersion != null">
        #{tempVersion,jdbcType=INTEGER},
      </if>
      <if test="outId != null">
        #{outId,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=TINYINT},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="remainingCount != null">
        #{remainingCount,jdbcType=INTEGER},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="itemSubPackage != null">
        #{itemSubPackage,jdbcType=VARCHAR},
      </if>
      <if test="orderDeposit != null">
        #{orderDeposit,jdbcType=INTEGER},
      </if>
      <if test="deviceSource != null">
        #{deviceSource,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfoExample" resultType="java.lang.Long">
    select count(*) from mall_service_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mall_service_order_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.tempVersion != null">
        temp_version = #{record.tempVersion,jdbcType=INTEGER},
      </if>
      <if test="record.outId != null">
        out_id = #{record.outId,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=TINYINT},
      </if>
      <if test="record.itemCode != null">
        item_code = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.remainingCount != null">
        remaining_count = #{record.remainingCount,jdbcType=INTEGER},
      </if>
      <if test="record.expirationDate != null">
        expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=BIGINT},
      </if>
      <if test="record.opUserId != null">
        op_user_id = #{record.opUserId,jdbcType=BIGINT},
      </if>
      <if test="record.itemCount != null">
        item_count = #{record.itemCount,jdbcType=INTEGER},
      </if>
      <if test="record.orderPrice != null">
        order_price = #{record.orderPrice,jdbcType=INTEGER},
      </if>
      <if test="record.itemSubPackage != null">
        item_sub_package = #{record.itemSubPackage,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDeposit != null">
        order_deposit = #{record.orderDeposit,jdbcType=INTEGER},
      </if>
      <if test="record.deviceSource != null">
        device_source = #{record.deviceSource,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mall_service_order_info
    set id = #{record.id,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      temp_version = #{record.tempVersion,jdbcType=INTEGER},
      out_id = #{record.outId,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=TINYINT},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      remaining_count = #{record.remainingCount,jdbcType=INTEGER},
      expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      order_status = #{record.orderStatus,jdbcType=TINYINT},
      merchant_id = #{record.merchantId,jdbcType=BIGINT},
      deleted = #{record.deleted,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=BIGINT},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      op_time = #{record.opTime,jdbcType=BIGINT},
      op_user_id = #{record.opUserId,jdbcType=BIGINT},
      item_count = #{record.itemCount,jdbcType=INTEGER},
      order_price = #{record.orderPrice,jdbcType=INTEGER},
      item_sub_package = #{record.itemSubPackage,jdbcType=VARCHAR},
      order_deposit = #{record.orderDeposit,jdbcType=INTEGER},
      device_source = #{record.deviceSource,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfo">
    update mall_service_order_info
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tempVersion != null">
        temp_version = #{tempVersion,jdbcType=INTEGER},
      </if>
      <if test="outId != null">
        out_id = #{outId,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=TINYINT},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="remainingCount != null">
        remaining_count = #{remainingCount,jdbcType=INTEGER},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=BIGINT},
      </if>
      <if test="opUserId != null">
        op_user_id = #{opUserId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="itemSubPackage != null">
        item_sub_package = #{itemSubPackage,jdbcType=VARCHAR},
      </if>
      <if test="orderDeposit != null">
        order_deposit = #{orderDeposit,jdbcType=INTEGER},
      </if>
      <if test="deviceSource != null">
        device_source = #{deviceSource,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ql.rent.entity.trade.MallServiceOrderInfo">
    update mall_service_order_info
    set order_no = #{orderNo,jdbcType=VARCHAR},
      temp_version = #{tempVersion,jdbcType=INTEGER},
      out_id = #{outId,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=TINYINT},
      item_code = #{itemCode,jdbcType=VARCHAR},
      remaining_count = #{remainingCount,jdbcType=INTEGER},
      expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=TINYINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      create_user_id = #{createUserId,jdbcType=BIGINT},
      op_time = #{opTime,jdbcType=BIGINT},
      op_user_id = #{opUserId,jdbcType=BIGINT},
      item_count = #{itemCount,jdbcType=INTEGER},
      order_price = #{orderPrice,jdbcType=INTEGER},
      item_sub_package = #{itemSubPackage,jdbcType=VARCHAR},
      order_deposit = #{orderDeposit,jdbcType=INTEGER},
      device_source = #{deviceSource,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_service_order_info
    (order_no, temp_version, out_id, item_type, item_code, remaining_count, expiration_date, 
      order_status, merchant_id, deleted, create_time, create_user_id, op_time, op_user_id, 
      item_count, order_price, item_sub_package, order_deposit, device_source)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.tempVersion,jdbcType=INTEGER}, #{item.outId,jdbcType=VARCHAR}, 
        #{item.itemType,jdbcType=TINYINT}, #{item.itemCode,jdbcType=VARCHAR}, #{item.remainingCount,jdbcType=INTEGER}, 
        #{item.expirationDate,jdbcType=TIMESTAMP}, #{item.orderStatus,jdbcType=TINYINT}, 
        #{item.merchantId,jdbcType=BIGINT}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, 
        #{item.createUserId,jdbcType=BIGINT}, #{item.opTime,jdbcType=BIGINT}, #{item.opUserId,jdbcType=BIGINT}, 
        #{item.itemCount,jdbcType=INTEGER}, #{item.orderPrice,jdbcType=INTEGER}, #{item.itemSubPackage,jdbcType=VARCHAR}, 
        #{item.orderDeposit,jdbcType=INTEGER}, #{item.deviceSource,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    insert into mall_service_order_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'temp_version'.toString() == column.value">
          #{item.tempVersion,jdbcType=INTEGER}
        </if>
        <if test="'out_id'.toString() == column.value">
          #{item.outId,jdbcType=VARCHAR}
        </if>
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=TINYINT}
        </if>
        <if test="'item_code'.toString() == column.value">
          #{item.itemCode,jdbcType=VARCHAR}
        </if>
        <if test="'remaining_count'.toString() == column.value">
          #{item.remainingCount,jdbcType=INTEGER}
        </if>
        <if test="'expiration_date'.toString() == column.value">
          #{item.expirationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=TINYINT}
        </if>
        <if test="'merchant_id'.toString() == column.value">
          #{item.merchantId,jdbcType=BIGINT}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=TINYINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=BIGINT}
        </if>
        <if test="'create_user_id'.toString() == column.value">
          #{item.createUserId,jdbcType=BIGINT}
        </if>
        <if test="'op_time'.toString() == column.value">
          #{item.opTime,jdbcType=BIGINT}
        </if>
        <if test="'op_user_id'.toString() == column.value">
          #{item.opUserId,jdbcType=BIGINT}
        </if>
        <if test="'item_count'.toString() == column.value">
          #{item.itemCount,jdbcType=INTEGER}
        </if>
        <if test="'order_price'.toString() == column.value">
          #{item.orderPrice,jdbcType=INTEGER}
        </if>
        <if test="'item_sub_package'.toString() == column.value">
          #{item.itemSubPackage,jdbcType=VARCHAR}
        </if>
        <if test="'order_deposit'.toString() == column.value">
          #{item.orderDeposit,jdbcType=INTEGER}
        </if>
        <if test="'device_source'.toString() == column.value">
          #{item.deviceSource,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>