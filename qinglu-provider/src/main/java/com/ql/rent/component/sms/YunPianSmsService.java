package com.ql.rent.component.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.enums.SmsTemplateEnum;
import com.ql.rent.common.IRedisService;
import com.ql.rent.api.aggregate.remote.yunpian.YunPianClient;
import com.ql.rent.api.aggregate.remote.yunpian.YunPianResponse;
import com.ql.rent.api.aggregate.remote.yunpian.YunPianBatchResponse;
import com.ql.rent.config.SmsConfig;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.util.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 云片网短信服务实现
 */
@Component
@Slf4j
public class YunPianSmsService implements ISmsService {

    @Resource
    private YunPianClient yunPianClient;

    @Resource
    private SmsConfig smsConfig;

    @Resource
    private IRedisService redisService;

    @Value("${profile.env}")
    private String profileEnv;

    /**
     * 提供商名称常量
     */
    private static final String PROVIDER_NAME = "YunPian";

    @Override
    public boolean sendSms(String phoneNumber, String templateParam, SmsTemplateEnum templateEnum) {
        if (!this.sendRealMsg()) {
            return true;
        }

        String lastMobile;
        try {
            lastMobile = AesUtil.decrypt(phoneNumber, AesUtil.MODE, AesUtil.PADDING);
        } catch (Exception e) {
            lastMobile = phoneNumber;
        }

        try {
            // 构建短信内容，替换模板参数
            String messageText = buildMessageText(templateParam, templateEnum);
            
            // 调用云片网API发送短信
            YunPianResponse response = yunPianClient.sendSingleSms(
                    smsConfig.getYunpianApiKey(),
                    lastMobile,
                    messageText
            );
            
            // 记录发送结果
            SmsSendResult result;
            if (response.isSuccess()) {
                result = SmsSendResult.success(PROVIDER_NAME, response.getSid());
            } else {
                result = SmsSendResult.failure(PROVIDER_NAME, String.valueOf(response.getCode()), response.getMsg());
            }
            
            log.info("YunPianSms sendSms mobile:{}, result:{}", lastMobile, JSON.toJSONString(result));
            
            return result.isSuccess();
        } catch (Exception e) {
            log.error("YunPianSms sendSms error, phoneNumber:{}, templateParam:{}, templateEnum:{}, error:{}",
                    phoneNumber, templateParam, templateEnum, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean batchSendSms(Collection<String> phoneNumbers, String templateParams, SmsTemplateEnum templateEnum) {
        if (!this.sendRealMsg() || phoneNumbers == null || phoneNumbers.isEmpty()) {
            return true;
        }

        // 解密手机号
        Collection<String> mobileList = phoneNumbers.stream()
                .map(p -> {
                    try {
                        return AesUtil.decrypt(p, AesUtil.MODE, AesUtil.PADDING);
                    } catch (Exception e) {
                        return p;
                    }
                })
                .collect(Collectors.toList());

        try {
            // 将手机号拼接为逗号分隔的字符串
            String mobilesText = String.join(",", mobileList);
            
            // 云片网批量发送API仅支持相同内容，所以这里取第一个参数构建短信内容
            JSONArray paramsArray = JSON.parseArray(templateParams);
            String messageText;

            // 批量发送时，所有收信人收到相同内容的短信
            if (paramsArray.size() > 1) {
                // 取第一组参数构建短信内容
                Object firstParam = paramsArray.get(0);
                String firstParamJson = JSON.toJSONString(firstParam);
                messageText = buildMessageText(firstParamJson, templateEnum);
            } else {
                // 没有参数时，直接使用模板内容
                messageText = templateEnum.getYunpianTemplateText();
            }

            // 调用云片网API批量发送短信
            YunPianBatchResponse response = yunPianClient.sendBatchSms(
                    smsConfig.getYunpianApiKey(),
                    mobilesText,
                    messageText
            );
            
            // 记录发送结果
            SmsSendResult result;
            if (response.isSuccess()) {
                // 批量发送时可能没有sid，或者有多个，我们取第一个或使用其他标识
                String messageId = "";
                if (response.getData() != null && !response.getData().isEmpty()) {
                    messageId = response.getData().get(0).getSid();
                }
                result = SmsSendResult.success(PROVIDER_NAME, messageId);
            } else {
                result = SmsSendResult.failure(PROVIDER_NAME, String.valueOf(response.getCode()), response.getMsg());
            }
            
            log.info("YunPianSms batchSendSms mobiles:{}, result:{}", mobilesText, JSON.toJSONString(result));
            
            return result.isSuccess();
        } catch (Exception e) {
            log.error("YunPianSms batchSendSms error, phoneNumbers:{}, templateParams:{}, templateEnum:{}, error:{}",
                    phoneNumbers, templateParams, templateEnum, e.getMessage());
            return false;
        }
    }

    /**
     * 构建短信内容，替换模板中的参数
     */
    private String buildMessageText(String templateParamJson, SmsTemplateEnum templateEnum) {
        String messageTemplate = templateEnum.getYunpianTemplateText();
        
        if (StringUtils.isEmpty(templateParamJson)) {
            return messageTemplate;
        }
        
        try {
            // 解析JSON参数
            Map<String, Object> params = JSON.parseObject(templateParamJson, Map.class);
            
            // 使用正则表达式查找并替换模板中的参数占位符
            Pattern pattern = Pattern.compile("#([^#]*)#");
            Matcher matcher = pattern.matcher(messageTemplate);
            StringBuffer sb = new StringBuffer();
            
            while (matcher.find()) {
                String paramName = matcher.group(1);
                Object paramValue = params.get(paramName);
                String replacement = paramValue != null ? paramValue.toString() : "";
                matcher.appendReplacement(sb, replacement);
            }
            
            matcher.appendTail(sb);
            return sb.toString();
        } catch (Exception e) {
            log.error("Error building message text: {}", e.getMessage(), e);
            return messageTemplate;
        }
    }

    /**
     * 检查是否应该发送真实短信
     */
    private boolean sendRealMsg() {
        if ("local".equals(profileEnv)) {
            return true;
        }
        Object obj = redisService.get(RedisConstant.CodeRedisKey.SEND_SMS_FLG);
        boolean sendFlg = false;
        if (obj == null || obj.toString().equals("1")) {
            if (obj == null) {
                redisService.set(RedisConstant.CodeRedisKey.SEND_SMS_FLG, "1", -1);
            }
            sendFlg = true;
        }
        return sendFlg;
    }

    /**
     * 判断是否应该发送真实消息
     */
    @Override
    public boolean sendRealSettingMsg() {
        return sendRealMsg(); // 云片网短信默认始终发送真实消息，可以根据实际需求修改
    }
} 