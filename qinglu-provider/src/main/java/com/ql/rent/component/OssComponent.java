package com.ql.rent.component;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.ql.dto.OssUploadDTO;
import com.ql.rent.common.IRedisService;
import com.ql.rent.config.OssPrivateConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

import static com.ql.rent.constant.RedisConstant.MsgTopic.FILE_UPLOAD_MSG_TOPIC;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OssComponent {

    @Resource
    private OssPrivateConfig ossPrivateConfig;
    @Resource
    private IRedisService redisService;

    /**
     * 将私有的osskey 生成一个临时可访问的url
     * @param expireSecond
     * @param ossKey
     */
    public String buildStsSignUrl(String ossKey, Long expireSecond) {
        if (StringUtils.isBlank(ossKey)) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.startsWith(ossKey, "http://") || StringUtils.startsWith(ossKey, "https://")) {
            return ossKey;
        }
        OSS ossClient =
            new OSSClient(ossPrivateConfig.getRealEndpoint(),
                ossPrivateConfig.getAccessKeyId(), ossPrivateConfig.getAccessKeySecret());
        try {
            // saas库里存储的key 有以"/"开头，需要删除
            if (StringUtils.startsWith(ossKey, "/")) {
                ossKey = StringUtils.removeStart(ossKey,"/");
            }

            Date expiration = new Date(new Date().getTime() + expireSecond * 1000);
            URL url = ossClient.generatePresignedUrl(ossPrivateConfig.getBucketName(), ossKey, expiration);
            if (url == null) {
                return "";
            }
            String result = url.toString();
            // 生成的是一个http的地址，浏览器访问可能提示安全问题。
            result = result.replaceFirst("http://", "https://");
            return result;
        } finally {
            ossClient.shutdown();
        }
    }

    /**
     * 过期时间单位 为秒
     */
    public String buildStsSignUrl(String ossKey) {
       return this.buildStsSignUrl(ossKey, 600L);
    }

    /**
     * 根据url上传
     */
    public String uploadByUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        String fileExtension = this.splitExtensionNameFromUrl(url);
        String fileName = ossPrivateConfig.getFileDir() + "/" + UUID.randomUUID() + "." + fileExtension;
        OssUploadDTO dto = new OssUploadDTO();
        dto.setUrl(url);
        dto.setOssName(fileName);
        redisService.convertAndSend(FILE_UPLOAD_MSG_TOPIC, JSON.toJSONString(dto));
        return "/" + fileName;
    }


    /**
     * 上传网络流(ByUrl)
     */
    public void doUploadByUrl(OssUploadDTO dto) {
        String url = dto.getUrl();
        if (StringUtils.isBlank(url)) {
            return;
        }

        OSS ossClient = null;
        // 读取的文件,先转成Buffered,避免上oss内部读取的异常
        try (InputStream inputStream = new URL(url).openStream();
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)) {
            // 创建OSSClient实例。
            ossClient = new OSSClient(ossPrivateConfig.getRealEndpoint(),
                ossPrivateConfig.getAccessKeyId(), ossPrivateConfig.getAccessKeySecret());
            String fileName = dto.getOssName();
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossPrivateConfig.getBucketName(), fileName, bufferedInputStream);
            // 创建PutObject请求。
            ossClient.putObject(putObjectRequest);
        } catch (Exception oe) {
            log.error("上传文件到oss错误, 文件:{},  ossName:{}", dto.getUrl(), dto.getOssName(), oe);
        }  finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    /**
     * 从url中截取文件扩展名
     */
    private String splitExtensionNameFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        String filePath = StringUtils.split(url, "?")[0];
        if (StringUtils.isBlank(filePath)) {
            return "";
        }
        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);

        if (!fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 上传数据流到oss
     * 目前仅ETC二维码上传使用
     */
    public String uploadByStream(InputStream inputStream, String fileName) {
        if (StringUtils.isBlank(fileName) || inputStream == null) {
            return "";
        }
        OSS ossClient = null;
        // 读取的文件,先转成Buffered,避免上oss内部读取的异常
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)) {
            // 创建OSSClient实例。
            ossClient = new OSSClient(ossPrivateConfig.getRealEndpoint(),
                    ossPrivateConfig.getAccessKeyId(), ossPrivateConfig.getAccessKeySecret());

            String ossName = ossPrivateConfig.getFileDir() + "/" + fileName;
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossPrivateConfig.getBucketName(), ossName, bufferedInputStream);
//            // 设置Content-Type为image/png
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("image/png");
            putObjectRequest.setMetadata(metadata);
            // 创建PutObject请求。
            ossClient.putObject(putObjectRequest);
            return ossName;
        } catch (Exception oe) {
            log.error("上传文件到oss错误, ossName:{}", fileName, oe);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }
}
