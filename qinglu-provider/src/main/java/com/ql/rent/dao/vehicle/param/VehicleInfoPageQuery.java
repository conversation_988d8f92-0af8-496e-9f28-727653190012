package com.ql.rent.dao.vehicle.param;

import com.ql.rent.param.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 */
@Data
public class VehicleInfoPageQuery extends BaseQuery {

    private static final long serialVersionUID = -3356103341857032474L;

    private Byte vehicleStatus;

    private List<String> licenses;

    private List<String> frameNumList;

    private Byte vehicleSource;


    private Collection<Byte> vehicleSourcesNotIn;

    private Long vehicleModelId;

    private Long storeId;

    private List<Long> storeIdList;

    private List<Long> idList;

    private Byte ctripVehicleAuditStatus;

    private Byte ctripVehicleStatus;

    private Byte notAudit;

    private String shareholder;

    private Boolean includeDel;

    /**
     * 保养是否过期
     */
    private Byte maintenanceExpired;

    private Long yearInspectionExpireDate;

    private Long insuranceExpireDate;

    private Byte platformSold;

    /**
     * 售卖中状态
     */
    private Byte leasedStatus;

    /**
     * 是否是ETC发行车辆查询
     */
    private Byte etcPublishSearch;
}
