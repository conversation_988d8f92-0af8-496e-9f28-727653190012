package com.ql.rent.schedule.runner;

import com.ql.rent.schedule.QuartzEntity;
import com.ql.rent.schedule.task.CtripLimitPriceTask;
import com.ql.rent.share.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * booker
 */
@Component
@Slf4j
public class CtripLimitPriceRunner implements ApplicationRunner {

    @Autowired
    @Qualifier("Scheduler")
    private Scheduler scheduler;

    @Value("${quartz.ctrip.limit.price.cron}")
    private String updateInvoiceCron;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            String jobName = "CtripLimitPriceTask";
            log.info("CtripLimitPriceRunner startTime:{}", DateUtil.getFormatDateStr(new Date(), DateUtil.yyyyMMddHHmmss));
            QuartzEntity quartz = new QuartzEntity();
            quartz.setJobName(jobName);
            quartz.setJobGroup("limitPrice");
            quartz.setCronExpression(updateInvoiceCron);

            // 直接使用类引用，避免反射加载
            JobDetail job = JobBuilder.newJob(CtripLimitPriceTask.class)
                    .withIdentity(quartz.getJobName(), quartz.getJobGroup())
                    .withDescription(quartz.getDescription())
                    .build();

            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(quartz.getJobName(), quartz.getJobGroup())
                    .withSchedule(CronScheduleBuilder.cronSchedule(quartz.getCronExpression()))
                    .build();

            scheduler.scheduleJob(job, trigger);
            log.info("CtripLimitPriceRunner endTime:{}", DateUtil.getFormatDateStr(new Date(), DateUtil.yyyyMMddHHmmss));
        } catch (Exception e) {
            log.error("CtripLimitPriceRunner error:{}", e.getMessage());
        }
    }
}
