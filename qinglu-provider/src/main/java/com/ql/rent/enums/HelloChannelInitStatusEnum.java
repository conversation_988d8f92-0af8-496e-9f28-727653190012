package com.ql.rent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum HelloChannelInitStatusEnum {

    STORE_MAPPING(10, "保存门店mapping"),
    INIT_CIRCLE(20, "初始化服务圈"),
    VEHICLE_BIND(30, "保存车型绑定"),
    SAVE_SHOP(40, "保存商品映射"),
    SAVE_VEHICLE(50, "保存车辆"),
    INIT_PRICE(60, "初始化价格"),
    INIT_STOCK(70, "初始化库存"),
    INIT_ORDER(80, "初始化订单")
    ;

    public static final int MAX_STEP = Arrays.stream(values()).mapToInt(HelloChannelInitStatusEnum::getIndex).max().getAsInt();
    private final int index;

    private final String sceneName;


    public static String getDesc(Integer status) {
        if (status == null) {
            return "";
        }
        for (HelloChannelInitStatusEnum value : values()) {
            if (value.index == status) {
                return value.sceneName;
            }
        }
        return "";
    }

}
