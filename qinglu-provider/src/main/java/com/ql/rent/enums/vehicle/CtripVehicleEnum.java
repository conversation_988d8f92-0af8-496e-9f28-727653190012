package com.ql.rent.enums.vehicle;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ql.enums.VehicleInfoEnums;
import com.ql.enums.VehicleInfoEnums.VehicleChannelAuditStatusEnum;
import com.ql.enums.VehicleInfoEnums.VehicleChannelVehicleStatusEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 携程车辆模块相关枚举
 * <AUTHOR>
 */
public interface CtripVehicleEnum {


    /**
     * 携程车辆审核状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum VehicleAuditStatusEnum {

        /**
         * 系统待审
         */
        AUDIT_ACCEPTED(0, VehicleChannelAuditStatusEnum.AUDIT_PROCESSING),
        /**
         * 爬虫中
         */
        CRAWLING(1, VehicleChannelAuditStatusEnum.AUDIT_PROCESSING),
        /**
         * 人工待审
         */
        MAN_WAIT(4, VehicleChannelAuditStatusEnum.AUDIT_PROCESSING),
        /**
         * 系统审核成功
         */
        SYS_PASS(2, VehicleChannelAuditStatusEnum.AUDIT_SUCCESS),
        /**
         * 系统审核失败
         */
        SYS_UNPASS(3, VehicleChannelAuditStatusEnum.AUDIT_FAILED),
        /**
         * 人工审核成功
         */
        MAN_PASS(5, VehicleChannelAuditStatusEnum.AUDIT_SUCCESS),
        /**
         * 人工审核失败
         */
        MAN_UNPASS(6, VehicleChannelAuditStatusEnum.AUDIT_FAILED),
        /**
         * 撤单
         */
        AUDIT_REVERT(8, VehicleChannelAuditStatusEnum.UNKNOWN),

        // -------灰度期间，老枚举依然有效-------

        AUDIT_SUCCESS(1, VehicleChannelAuditStatusEnum.AUDIT_SUCCESS),

        AUDIT_FAIL(2, VehicleChannelAuditStatusEnum.AUDIT_FAILED),

        AUDIT_PROCESSING(0, VehicleChannelAuditStatusEnum.AUDIT_PROCESSING),
        // -------灰度期间，老枚举依然有效-------
        ;

        private final Integer ctripStatus;
        private final VehicleChannelAuditStatusEnum saasStatus;
    }

    /**
     * 携程车辆状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum VehicleCarStatusEnum {
        FOR_RENT(VehicleChannelVehicleStatusEnum.FOR_RENT),
        FOR_ONLINE(VehicleChannelVehicleStatusEnum.FOR_ONLINE),
        SYSTEM_OFFLINE(VehicleChannelVehicleStatusEnum.SYSTEM_OFFLINE),
        FOREVER_OFFLINE(VehicleChannelVehicleStatusEnum.FOREVER_OFFLINE);
        private final VehicleInfoEnums.VehicleChannelVehicleStatusEnum carStatus;
    }

    /**
     * 携程售卖状态枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum VehicleSaleStatusEnum {
        SALE_ENABLE(VehicleInfoEnums.VehicleChannelSaleStatusEnum.SALE_ENABLE),
        SALE_PART_DISABLE(VehicleInfoEnums.VehicleChannelSaleStatusEnum.SALE_PART_DISABLE),
        SALE_ALL_DISABLE(VehicleInfoEnums.VehicleChannelSaleStatusEnum.SALE_ALL_DISABLE);
        private final VehicleInfoEnums.VehicleChannelSaleStatusEnum saleStatusEnum;
    }

    /**
     * ocr类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum OcrItemTypeEnum {
        DRIVING_LICENSE("DrivingLicense", "行驶证"),

        ANNUAL_CHECK("AnnualCheck", "年检证"),

        COMMERCIAL("Commercial", "商业险"),

        COMPULSORY("Compulsory", "交强险")
        ;
        private final String ctripCode;

        private final String name;
    }


    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    enum VehiclePushErrorMsgEnum {
        OTHER_VENDOR("car not belong to vendor", "车辆被其他供应商占用,请联系携程商务确认"),

//        ENDTIME_INVALID("endTime is inValid", "车辆保险到期时间已过期，请重新上传保险信息"),

        DUPLICATE_VEHICLE("License plate and frame number is duplicate", "车牌号或车架号重复，请检查车辆信息"),

        DUPLICATE_POLICY_NUMBER("Duplicate policy number", "车辆多份保险的保单号相同"),

        THIRDPARTYCOVERAGEIS_INVALID("thirdPartyCoverageis inValid", "车辆三者险额度低于50w，无法在携程售卖"),

        ANNUAL_CHECK("annualCheck end time is invalid", "车辆年检证已过期，请重新上传年检证"),
        ;
        private final String ctripMsg;

        private final String saasMsg;

        public static String transferCtripMsg(String ctripCode, String ctripMsg) {
            if ("4002".equals(ctripCode)) {
                try {
                    JSONObject jsonObject = JSON.parseObject(ctripMsg);
                    String vendorId = jsonObject.getString("vendorId");
                    String vendorName = jsonObject.getString("vendorName");
                    return "车辆被其他供应商占用,供应商:" + vendorId + "-" + vendorName;
                } catch (Exception e) {
                    return "车辆被其他供应商占用,请联系携程商务确认";
                }
            }
            if ("3002".equals(ctripCode)) {
                return "门店未在携程上线，请上线门店后，再次保存车辆";
            }

            if ("execute exception".equals(ctripMsg)) {
                return "";
            }
            // 兜底文案
            if (StringUtils.isBlank(ctripMsg)) {
                return "车辆信息不符合携程要求，请重新保存。";
            }
            // 保险报错信息可能有多个，特殊处理
            if (ctripMsg.contains("endTime is inValid")) {
                return "车辆保险到期时间已过期，请重新上传保险信息";
            }
            Optional<VehiclePushErrorMsgEnum> first =
                Arrays.stream(values()).filter(e -> e.getCtripMsg().equals(ctripMsg)).findFirst();
            if (first.isPresent()) {
                return first.get().getSaasMsg();
            }
            return "车辆信息不符合携程要求，请重新保存。";
        }
    }

    /**
     * public class CtripVehicleReason {
     *
     *     private String module;
     *     private String filed;
     *     private String reasonKey;
     * }
     * 携程给的理由字段会是CtripVehicleReason格式的一个json字符串，展示的时候需要做一下解析。
     */
     static List<String> transferCtripVehicleReason(String reasonStr) {
        if (reasonStr == null || reasonStr.isEmpty()) {
            return Collections.emptyList();
        }

         List<String> chineseReasonList = new ArrayList<>();
         try {
             Map<String, List<Map<String, String>>> reasonMap = JSONObject.parseObject(reasonStr, Map.class);
             reasonMap.forEach((key, value) -> {
                 if (CollectionUtils.isNotEmpty(value)) {
                     for (Map<String, String> singleReason : value) {
                         String reason = singleReason.get("reasonValue");
                         if (reason != null && !reason.isEmpty()) {
                             chineseReasonList.add(reason);
                         }
                     }
                 }
             });
         } catch (Exception e) {
            // 解析失败
         }
         return chineseReasonList;
    }


    /**
     * 车辆审核：是否只有子车系审核失败。 只有reasonKey，为SYS_VL_500
     * param 类似：{\"VL\":[{\"module\":\"VL\",\"field\":\"VEHICLE_TYPE\",\"reasonKey\":\"SYS_VL_500\",\"reasonValue\":\"录入归属子车系与行驶证不一致，请以行驶证信息为准如实填写。\"}]}
     * return 是否只有子车系审核失败
     */
    static boolean onlySubSeryAuditFailed(String reasonStr) {
         if (reasonStr == null || reasonStr.isEmpty()) {
             return false;
         }
         try {
             Map<String, List<Map<String, String>>> reasonMap = JSONObject.parseObject(reasonStr, Map.class);
             if (reasonMap.size() != 1) {
                 return false;
             }
             for (List<Map<String, String>> value : reasonMap.values()) {
                 for (Map<String, String> singleReason : value) {
                     String reason = singleReason.get("reasonKey");
                     if ("SYS_VL_500".equals(reason)) {
                         return true;
                     }
                 }
             }
             return false;
         } catch (Throwable e) {
             return false;
         }
    }

    /**
     * 携程车辆保险附件的外网可访问域名
     */
    String CTRIP_PUBLIC_INSURANCE_URL = "https://file.c-ctrip.com";


    /**
     * 携程车辆保险附件给的内部地址
     */
    String CTRIP_PRIVATE_INSURANCE_URL = "https://ws.downloadfile.fx.ctripcorp.com";

    /**
     * 将携程的给的不可访问地址，替换成可访问的地址
     */
    static String transfer2CtripPublicUrl(String privateUrl) {
        if (StringUtils.startsWith(privateUrl, CTRIP_PRIVATE_INSURANCE_URL)) {
            return StringUtils.replaceOnce(privateUrl, CTRIP_PRIVATE_INSURANCE_URL, CTRIP_PUBLIC_INSURANCE_URL);
        }
        return privateUrl;
    }

    /**
     * 将携程的给的可访问地址，替换成不可访问的内部地址
     */
    static String transfer2CtripPrivateUrl(String publicUrl) {
//        if (StringUtils.startsWith(publicUrl, CTRIP_PUBLIC_INSURANCE_URL)) {
//            return StringUtils.replaceOnce(publicUrl, CTRIP_PUBLIC_INSURANCE_URL, CTRIP_PRIVATE_INSURANCE_URL);
//        }
        return publicUrl;
    }

}
