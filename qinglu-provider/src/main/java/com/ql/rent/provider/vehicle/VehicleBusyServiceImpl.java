package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.Constant;
import com.ql.dto.open.request.stock.OpenStockMerchantCheckReq;
import com.ql.dto.open.response.OpenStockFeeResponse;
import com.ql.dto.open.response.StockFeeResponse;
import com.ql.enums.VehicleInfoEnums;
import com.ql.enums.open.OpenVehicleEnum;
import com.ql.rent.api.aggregate.model.dto.AllopatryRuleDTO;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.converter.vehicle.VehicleBusyConverter;
import com.ql.rent.dao.vehicle.VehicleBusyMapper;
import com.ql.rent.dao.vehicle.VehicleChannelMapper;
import com.ql.rent.dao.vehicle.ex.VehicleBusyMapperEx;
import com.ql.rent.dto.trade.PickReturnTimeTileDTO;
import com.ql.rent.dto.trade.UpdateOrderVehicleDTO;
import com.ql.rent.entity.vehicle.VehicleBusy;
import com.ql.rent.entity.vehicle.VehicleBusyExample;
import com.ql.rent.entity.vehicle.VehicleChannelExample;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.common.VehicleResultCode;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.enums.trade.OrderStatusEnum;
import com.ql.rent.enums.vehicle.EventConstantsEnum;
import com.ql.rent.enums.vehicle.ThirdCheckStockEnum;
import com.ql.rent.enums.vehicle.VehicleBusyEnum;
import com.ql.rent.enums.vehicle.VehicleStatusEnum;
import com.ql.rent.event.EventPublisher;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.common.ApiConnBusiParam;
import com.ql.rent.param.sync.CtripStockTransactionParam;
import com.ql.rent.param.vehicle.*;
import com.ql.rent.provider.merchant.OpenMerchantComponent;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IChannelService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.store.IAllopatryRuleService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.trade.IOrderMemberService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.service.trade.IVehiclePickReturnService;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.third.vo.SaasResponse;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.ApiConnVo;
import com.ql.rent.vo.common.ChannelVO;
import com.ql.rent.vo.price.RentVehicleParam;
import com.ql.rent.vo.price.RentVehicleVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.OrderExtraVo;
import com.ql.rent.vo.trade.OrderInfoVo;
import com.ql.rent.vo.trade.OrderMemberVo;
import com.ql.rent.vo.vehicle.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ql.enums.VehicleInfoEnums.VehicleSourceEnum;

/**
 * @desc: 车型车辆繁忙业务&排车
 * @author: pijiu
 * @time: 2022-10-27 21:08
 * @Version: 1.0
 */
@Service
@Slf4j
public class VehicleBusyServiceImpl implements IVehicleBusyService {

    @Resource
    private VehicleBusyMapper vehicleBusyMapper;
    @Resource
    private IVehicleModelService vehicleModelService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IOrderMemberService orderMemberService;
    @Resource
    private IChannelService channelService;
    @Resource
    private IApiConnService apiConnService;
    @Resource
    private IShuntingListService shuntingListService;
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private IRedisService redisService;
    @Resource
    private IRentMainService rentMainService;
    @Resource
    private IThirdBusyService thirdBusyService;
    @Resource
    private IOrderService orderService;
    @Resource
    private VehicleBusyMapperEx vehicleBusyMapperEx;
    @Resource
    private EventPublisher eventPublisher;

    @Resource
    private IAllopatryRuleService allopatryRuleService;

    @Resource
    private IThirdCtripService thirdCtripService;

    @Resource
    private IThirdIdRelationService thirdIdRelationService;

    @Resource
    private IThirdVehicleIdRelationService thirdVehicleIdRelationService;

    @Resource
    private IVehiclePickReturnService iVehiclePickReturnService;
    @Resource
    private VehicleChannelMapper vehicleChannelMapper;
    @Resource
    private OpenMerchantComponent openMerchantComponent;
    @Resource
    private IVehicleTagService vehicleTagService;
    @Resource
    private PlatformBiz platformBiz;

    private final Lock lock = new ReentrantLock();

    private final Date MAX_END_TIME = new Date(4102374689000L);

    @Override
    public void getVehicleModelSpare(Long storeId) {

    }

    @Override
    public void getVehicleSpare(Long storeId) {

    }

    private void checkStockParam(VehicleBusyParam param) {
        if (param.getSourceId() == null) {
            throw new BizException("库存占用关联ID不能为空");
        }
        if (param.getSourceType() == null) {
            throw new BizException("库存占用类型不能为空");
        }
        if (param.getStartTime() == null) {
            throw new BizException("库存占用开始时间不能为空");
        }
        if (param.getEndTime() == null) {
            throw new BizException("库存占用结束时间不能为空");
        }
        if (param.getVehicleModelId() == null) {
            throw new BizException("库存占用车型ID不能为空");
        }
        if (param.getSourceType() == VehicleBusyEnum.SUBORDER.getValue().intValue()) {
            if (param.getParentSourceId() == null) {
                throw new BizException("续租单必须传入主订单ID");
            }
        }
    }

    @Override
    public List<VehicleBusyFreeVO> getStockFeeModels(Long merchantId, Long storeId, Long startTime, Long endTime, Long channelId) {
        endTime = endTime - 1;
        return vehicleBusyMapperEx.selectFeeModelIds(merchantId, storeId, startTime, endTime, channelId);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public Result<Long> getVehicleAndLock(VehicleBusyParam param) {
        lock.lock();
        try {
            param.setStartTime(trimSecond(param.getStartTime()));
            param.setEndTime(trimSecond(param.getEndTime()));
            SimpleDateFormat convert = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            log.info("库存占用,占用;sourceId={},车辆={},时间={}，param{}", param.getSourceId(), param.getVehicleId(),
                    convert.format(new Date(param.getStartTime())) + " - " + convert.format(new Date(param.getEndTime())),
                    JSON.toJSONString(param));
            checkStockParam(param);

            // 上汽商户校验库存
            boolean openMerchant = openMerchantComponent.isOpenMerchant(param.getMerchantId());
            boolean openHelloMerchant = openMerchantComponent.isOpenHelloMerchant(param.getMerchantId());
            boolean openCheckSaasStock = openMerchantComponent.isOpenCheckSaasStock(param.getMerchantId());
            if (openMerchant || (openHelloMerchant && !openCheckSaasStock)) {
                log.info("直连商家,库存校验,checkStock_" + param.getMerchantId());
                int merSourceType = 0;
                // 主单下单
                if (VehicleBusyEnum.ORDER.getValueInt().equals(param.getSourceType())) {
                    merSourceType = ThirdCheckStockEnum.CREATE_ORDER.getType();
                } else if (VehicleBusyEnum.SUBORDER.getValueInt().equals(param.getSourceType())) {
                    //续租下单
                    merSourceType = ThirdCheckStockEnum.RERENT_ORDER.getType();
                }
                StockFeeResponse stockFeeResponse = this.handleStock(merSourceType, param, !openHelloMerchant);
                if (Objects.isNull(stockFeeResponse) || CollectionUtils.isEmpty(stockFeeResponse.getSaasVehicleIds())) {
                    log.error("直连商家,库存校验,库存校验未返回有效库存, 请求参数={}", JSON.toJSONString(JSON.toJSONString(param)));
                    throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                }
                if (Constant.ChannelId.WUKONG.equals(param.getChannelId())) {
                    for (Long vehicleId : stockFeeResponse.getSaasVehicleIds()) {
                        if (checkPlatAudit(param.getChannelId(), vehicleId, param.getMerchantId())) {
                            // 返回已审核的车型
                            return ResultUtil.successResult(vehicleId);
                        }
                    }
                    // 续租单无审核通过车辆返回第一个
                    if (merSourceType == ThirdCheckStockEnum.CREATE_ORDER.getType()) {
                        log.error("直连商家,库存校验,车辆审核悟空未通过,车辆ids={}", JSON.toJSONString(stockFeeResponse.getSaasVehicleIds()));
                        throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                    }
                }
                if (openHelloMerchant) {
                    param.setVehicleId(stockFeeResponse.getSaasVehicleIds().get(0));
                } else {
                    return ResultUtil.successResult(stockFeeResponse.getSaasVehicleIds().get(0));
                }
            }

            VehicleBusy vehicle = new VehicleBusy();
            BeanUtils.copyProperties(param, vehicle);
            vehicle.setDeleted(YesOrNoEnum.NO.getValue());

            long orderInterval = 0;
            AllopatryRuleDTO allopatryRuleDTO ;
            if (VehicleBusyEnum.isOrderBusy(param.getSourceType())) {
                if (param.getStoreId() != null && param.getReturnStoreId() != null && !param.getStoreId().equals(param.getReturnStoreId())) {
                    // 获取异门店规则
                    allopatryRuleDTO = allopatryRuleService
                            .getAllopatryRule(param.getMerchantId(), param.getChannelId(), param.getStoreId(),
                                    param.getReturnStoreId(), param.getVehicleModelId(), new Date(param.getEndTime()));
                    // 直连商家该字段数据存在-1，非业务有效数据
                    orderInterval = (Objects.nonNull(allopatryRuleDTO) && Objects.nonNull(allopatryRuleDTO.getDuration())
                            && allopatryRuleDTO.getDuration() >= 0)
                            ? allopatryRuleDTO.getDuration() * 3600L * 1000 : 0;
                    if (Objects.nonNull(allopatryRuleDTO)) {
                        log.info("异门店规则 merchantId:{}  allopatryRuleDTO:{} ", param.getMerchantId(), JSON.toJSONString(allopatryRuleDTO));
                    } else {
                        log.info("异门店规则 merchantId:{}  allopatryRuleDTO is null ", param.getMerchantId());
                    }
                    // 未匹配到异门店规则
                    if (allopatryRuleDTO == null || allopatryRuleDTO.getDuration() < 0) {
                        orderInterval = storeInfoService.getOrderInterval(param.getStoreId(), param.getChannelId(), param.getVehicleModelId());
                    }
                } else {
                    // 同门店规则
                    orderInterval = storeInfoService.getOrderInterval(param.getStoreId(), param.getChannelId(), param.getVehicleModelId());
                }
            }
            if (YesOrNoEnum.isYes(param.getSaasSync())) {
                orderInterval = 0;
            }
            log.info("库存占用,占用;sourceId={},订单间隔 {} 毫秒 {} 分钟", param.getSourceId(), orderInterval, orderInterval / 1000 / 60);
            orderInterval = orderInterval  - 1;
            // 指定车辆
            if (param.getVehicleId() != null) {
                Result<VehicleInfoVO> vehicleResult = vehicleInfoService.getBaseById(param.getVehicleId(), false);
                if (vehicleResult.getModel() == null ||
                        vehicleResult.getModel().getVehicleStatus().equals(VehicleStatusEnum.NOT_ONLINE.getStatus()) ||
                        !vehicleResult.getModel().getSaleStatus().equals(VehicleInfoEnums.SaleStatusEnum.SALE_ENABLE.getStatus()) ||
                        YesOrNoEnum.isYes(vehicleResult.getModel().getDeleted())) {
                    // 神州渠道下线&&删除也能占用库存
                    if (!Constant.ChannelId.SHENZHOU.equals(param.getChannelId())) {
                        throw new BizException("车辆不存在或未上线或未售卖");
                    }
                }
                vehicle.setAutoSchedule(YesOrNoEnum.NO.getValue());
                // 续租单
                if (param.getParentSourceId() != null) {
                    // 子订单上级订单占用存在
                    VehicleBusyExample exampleOrder = new VehicleBusyExample();
                    exampleOrder.createCriteria().andParentSourceIdEqualTo(param.getParentSourceId())
                            .andMerchantIdEqualTo(param.getMerchantId())
                            .andSourceTypeIn(Arrays.asList(VehicleBusyEnum.ORDER.getValueInt(), VehicleBusyEnum.SUBORDER.getValueInt()))
                            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    exampleOrder.setOrderByClause("id desc");
                    List<VehicleBusy> orderList = vehicleBusyMapper.selectByExample(exampleOrder);
                    if (CollectionUtils.isEmpty(orderList)) {
                        throw new BizException("主订单不存在");
                    }
                    Set<Long> pushCtripVehicleIds = new HashSet<>();
                    for (VehicleBusy order : orderList) {
                        if (!param.getSourceId().equals(order.getSourceId())) {
                            VehicleBusy beforeBusy = new VehicleBusy();
                            BeanUtils.copyProperties(order, beforeBusy);
                            order.setEndIntervalTime(order.getEndTime() - 1);
                            order.setOpTime(System.currentTimeMillis());
                            vehicleBusyMapper.updateByPrimaryKey(order);
                            pushCtripVehicleIds.add(order.getVehicleId());
                            pushCtripVehicleIds.add(beforeBusy.getVehicleId());
                            busyEvent(EventConstantsEnum.Action.UPDATE.toString(), order, beforeBusy);
                            break;
                        }
                    }
                    // 携程推送库存信息
                    CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
                    ctripStockTransactionParam.setMerchantId(param.getMerchantId());
                    ctripStockTransactionParam.setVehicleIds(new ArrayList<>(pushCtripVehicleIds));
                    eventPublisher.publishEvent(ctripStockTransactionParam);
                    // 强制排车==FALSE
                    if (YesOrNoEnum.isNo(param.getFrcedScheduling())) {
                        // 数据是否已存在
                        VehicleBusyExample exampleBusy = new VehicleBusyExample();
                        exampleBusy.createCriteria().andSourceIdEqualTo(param.getSourceId()).
                                andSourceTypeEqualTo(param.getSourceType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                        List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
                        // 查询占用数
                        List<VehicleBusy> busyList = this.selectBusyVehicleList(param.getStoreId(), param.getVehicleId(),
                                param.getStartTime(), param.getEndTime() + orderInterval);
                        if (CollectionUtils.isEmpty(existBusyList)) {
                            if (CollectionUtils.isNotEmpty(busyList)) {
                                log.info("库存占用,占用;sourceId={},与库存数据{}冲突", param.getSourceId(), busyList.get(0).getId());
                                throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                            }
                        } else {
                            VehicleBusy vehicleBusy = existBusyList.get(0);
                            List<VehicleBusy> tmpList =
                                    busyList.stream().filter(e -> !e.getId().equals(vehicleBusy.getId())).collect(
                                            Collectors.toList());
                            if (CollectionUtils.isNotEmpty(tmpList)) {
                                log.info("库存占用,占用;sourceId={},与库存数据{}冲突", param.getSourceId(), tmpList.get(0).getId());
                                throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                            }
                            this.updateVehicleBusy(vehicleBusy, param, orderInterval);
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    }
                    // 更新逻辑未处理
                } else {
                    // 数据是否已存在
                    List<VehicleBusy> existBusyList = this.getExistVehicleBusy(param);
                    // 强制排车==FALSE
                    if (YesOrNoEnum.isNo(param.getFrcedScheduling())) {
                        // 查询占用数
                        List<VehicleBusy> busyList = this.selectBusyVehicleList(param.getStoreId(), param.getVehicleId(),
                                param.getStartTime(), param.getEndTime() + orderInterval);
                        if (CollectionUtils.isEmpty(existBusyList)) {
                            if (CollectionUtils.isNotEmpty(busyList)) {
                                log.info("库存占用,占用;sourceId={},与库存数据{}冲突", param.getSourceId(), busyList.get(0).getId());
                                throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                            }
                        } else {
                            VehicleBusy vehicleBusy = existBusyList.get(0);
                            List<VehicleBusy> tmpList =
                                    busyList.stream().filter(e -> !e.getId().equals(vehicleBusy.getId())).collect(
                                            Collectors.toList());
                            if (CollectionUtils.isNotEmpty(tmpList)) {
                                log.info("库存占用,占用;sourceId={},与库存数据{}冲突", param.getSourceId(), tmpList.get(0).getId());
                                throw new BizException(VehicleResultCode.VEHICLE_STOCK_BUSY);
                            }
                            this.updateVehicleBusy(vehicleBusy, param, orderInterval);
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(existBusyList)) {
                            VehicleBusy vehicleBusy = existBusyList.get(0);
                            this.updateVehicleBusy(vehicleBusy, param, orderInterval);
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    }
                }
            } else {
                vehicle.setAutoSchedule(YesOrNoEnum.YES.getValue());
                // 获取所有车型下的车辆
                Result<List<VehicleSampleVO>> vehiclesResult =
                        vehicleInfoService.findVehiclesByModelId(param.getStoreId(), param.getVehicleModelId());
                List<VehicleSampleVO> vehicles = vehiclesResult.getModel();
                // 查询占用数据
                List<VehicleBusy> busyList =
                        selectBusyModelList(param.getStoreId(), param.getVehicleModelId(), null, param.getStartTime(),
                                param.getEndTime() + orderInterval);
                // 繁忙车辆ids
                List<Long> busyIds = busyList.stream().map(VehicleBusy::getVehicleId)
                        .distinct().collect(Collectors.toList());
                // 可用车辆ids
                List<VehicleSampleVO> vehicleSampleVOList = this.getAvailableVehicleSorted(vehicles, busyIds, param);
                if (CollectionUtils.isEmpty(vehicleSampleVOList)) {
                    log.info("库存占用,占用;sourceId={},车辆无库存,车型{}", param.getSourceId(), param.getVehicleModelId());
                    throw new BizException("无可用车辆");
                }

                Long freeId = null;
                for (VehicleSampleVO vehicleSampleVO : vehicleSampleVOList) {
                    if (YesOrNoEnum.isYes(param.getSelfServiceReturn())) {
                        //etc 服务
                        if (YesOrNoEnum.isYes(param.getTiIncludeEtc()) && YesOrNoEnum.isYes(vehicleSampleVO.getTiIncludeEtc())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                        if (YesOrNoEnum.isYes(vehicleSampleVO.getSelfServiceReturn())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                        //etc 服务
                    } else if (YesOrNoEnum.isYes(param.getTiIncludeEtc()) && YesOrNoEnum.isYes(vehicleSampleVO.getTiIncludeEtc())) {
                        freeId = vehicleSampleVO.getId();
                        break;
                    } else if (Constant.ChannelId.WUKONG.equals(param.getChannelId()) || Constant.ChannelId.HELLO.equals(param.getChannelId())) {
                        if (checkPlatAudit(param.getChannelId(), vehicleSampleVO.getId(), param.getMerchantId())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                    } else {
                        // 平台审核检查
                        //if (!VehicleSourceEnum.PLATFORM_VEHICLE.getSource().equals(vehicleSampleVO.getVehicleSource())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        //}
                    }

                }
                // 自助取还
                if (YesOrNoEnum.isYes(param.getSelfServiceReturn()) && freeId == null) {
                    log.error("库存校验,车型下未找到支持自助取还的车辆,车型id={}", param.getVehicleModelId());
                    throw new BizException("无可用车辆");
                }
                // 悟空渠道
                if (Constant.ChannelId.WUKONG.equals(param.getChannelId()) && freeId == null) {
                    log.error("库存校验,车型下未找到悟空通过的车辆,车型id={}", param.getVehicleModelId());
                    throw new BizException("无可用车辆");
                }
                // 哈啰渠道
                if (Constant.ChannelId.HELLO.equals(param.getChannelId()) && freeId == null) {
                    log.error("库存校验,车型下未找到哈啰通过的车辆,车型id={}", param.getVehicleModelId());
                    return ResultUtil.successResult(null);
                }
                if (freeId == null) {
                    freeId = vehicleSampleVOList.get(0).getId();
                }
                vehicle.setVehicleId(freeId);
            }
            vehicle.setLockFlg(YesOrNoEnum.YES.getValue());
            vehicle.setEndIntervalTime(vehicle.getEndTime() + orderInterval);
            vehicle.setParentSourceId(param.getParentSourceId());
            vehicle.setCreateTime(System.currentTimeMillis());
            vehicle.setOpTime(System.currentTimeMillis());
            if (vehicle.getParentSourceId() == null) {
                vehicle.setParentSourceId(param.getSourceId());
            }
            setBusyExtFiled(vehicle, param.getBusyExt());
            //if (1==1) {return ResultUtil.successResult(vehicle.getVehicleId());}
            vehicleBusyMapper.insertSelective(vehicle);
            log.info("库存占用,占用;sourceId={},insert ok", param.getSourceId());
            busyEvent(EventConstantsEnum.Action.ADD.toString(), vehicle, null);
            // 携程推送库存信息
            CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
            ctripStockTransactionParam.setMerchantId(vehicle.getMerchantId());
            ctripStockTransactionParam.setVehicleIds(Collections.singletonList(vehicle.getVehicleId()));
            eventPublisher.publishEvent(ctripStockTransactionParam);
            return ResultUtil.successResult(vehicle.getVehicleId());
        } catch (BizException e) {
            throw new BizException(changErrCode(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("排车异常", e);
            throw new BizException(changErrCode(null), "排车异常");
        } finally {
            lock.unlock();
        }
    }

    /**
     * BizException code 转换
     * @param code
     * @return
     */
    private String changErrCode(String code) {
        if (code == null || code.equals("0") || code.equals(VehicleResultCode.VEHICLE_STOCK_BUSY.getCode())) {
            return "2002";
        }
        return code;
    }

    public boolean checkPlatAudit(long channelId, long vehicleId, Long merchantId) {
        if (channelId == OrderSourceEnum.WUKONG.longValue()) {
            VehicleChannelExample example = new VehicleChannelExample();
            example.createCriteria().andChannelIdEqualTo(channelId).andVehicleIdEqualTo(vehicleId).andAuditStatusEqualTo(YesOrNoEnum.YES.getValue()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            long cnt = vehicleChannelMapper.countByExample(example);
            return cnt > 0;
        } else if (channelId == OrderSourceEnum.HELLO.longValue()) {
            String thirdId = thirdVehicleIdRelationService.getMappingForThird(channelId, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), vehicleId, merchantId);
            return thirdId != null;
        } else {
            return true;
        }
    }

    private void setBusyExtFiled(VehicleBusy busy, VehicleBusyExtParam param) {
        try {
            if (param == null) {
                if (StringUtils.isEmpty(busy.getExt())) {
                    busy.setExt(StringUtils.EMPTY);
                }
                return;
            }
            JSONObject sourceObj = (JSONObject) JSONObject.toJSON(param);
            JSONObject targetObj = new JSONObject();
            if (!StringUtils.isEmpty(busy.getExt())) {
                targetObj = (JSONObject) JSONObject.toJSON(busy.getExt());
            }
            for (Map.Entry entry : sourceObj.entrySet()) {
                targetObj.put((String) entry.getKey(), entry.getValue());
            }
            busy.setExt(targetObj.toJSONString());
        } catch (Exception e) {
            log.error("库存占用,库存Ext转换失败,busy={},param={}", JSONObject.toJSONString(busy), JSONObject.toJSONString(param));
        }
    }

    private VehicleBusyExtParam getBusyExtFiled(String ext) {
        try {
            if (StringUtils.isNotEmpty(ext)) {
                return JSONObject.parseObject(ext, VehicleBusyExtParam.class);
            }
        } catch (Exception e) {
            log.error("库存Ext转Bean失败,ext={}", ext);
        }
        return null;
    }

    /**
     * 查询业务数据对应已存在库存占用数据
     *
     * @param param 库存占用参数
     */
    private List<VehicleBusy> getExistVehicleBusy(VehicleBusyParam param) {
        VehicleBusyExample exampleBusy = new VehicleBusyExample();
        // 临时停售，没有业务id, 数据查询不到就ok
        if (VehicleBusyEnum.TEMPORARY_OFF.getValueInt().equals(param.getSourceType())) {
            exampleBusy.createCriteria().andIdEqualTo(-1L);
        } else {
            // 其他类型。根据业务id精准查询
            exampleBusy.createCriteria().andSourceIdEqualTo(param.getSourceId())
                    .andSourceTypeEqualTo(param.getSourceType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        }
        return vehicleBusyMapper.selectByExample(exampleBusy);
    }


    /**
     * 平台库存同步
     *
     * @param action
     * @param afterBusyBean
     * @param beforeBusyBean
     */
    public void busyEvent(String action, VehicleBusy afterBusyBean, VehicleBusy beforeBusyBean) {
        platformBiz.busyEvent(action, afterBusyBean, beforeBusyBean);
    }

    @Override
    public Result<Long> getVehicleAndLockCheck(VehicleBusyParam param) {
        try {
            Long freeId = null;
            param.setStartTime(trimSecond(param.getStartTime()));
            param.setEndTime(trimSecond(param.getEndTime()));
            SimpleDateFormat convert = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            log.info("库存占用,订单校验;sourceId={},车辆{},时间{}，param{}", param.getSourceId(), param.getVehicleId(),
                    convert.format(new Date(param.getStartTime())) + " - " + convert.format(new Date(param.getEndTime())),
                    JSON.toJSONString(param));
            checkStockParam(param);
            // 上汽商户校验库存
            boolean openMerchant = openMerchantComponent.isOpenMerchant(param.getMerchantId());
            boolean openHelloMerchant = openMerchantComponent.isOpenHelloMerchant(param.getMerchantId());
            boolean openCheckSaasStock = openMerchantComponent.isOpenCheckSaasStock(param.getMerchantId());
            if (openMerchant || (openHelloMerchant && !openCheckSaasStock)) {
                log.info("直连商家,库存校验,checkStock_" + param.getMerchantId());
                try {
                    int merSourceType = 0;
                    if (VehicleBusyEnum.DETAIL.getValueInt().equals(param.getSourceType())) {
                        merSourceType = ThirdCheckStockEnum.DETAIL_CHECK.getType(); //详情页校验
                    } else if (VehicleBusyEnum.ORDER.getValueInt().equals(param.getSourceType())) {
                        merSourceType = ThirdCheckStockEnum.CREATE_CHECK.getType(); //下单前校验
                    }
                    StockFeeResponse stockFeeResponse = this.handleStock(merSourceType, param, !openHelloMerchant);
                    if (Objects.isNull(stockFeeResponse) || CollectionUtils.isEmpty(stockFeeResponse.getSaasVehicleIds())) {
                        log.error("直连商家,库存校验,库存校验未返回有效库存, 请求参数={}", JSON.toJSONString(JSON.toJSONString(param)));
                        return ResultUtil.successResult(freeId);
                    }
                    if (Constant.ChannelId.WUKONG.equals(param.getChannelId())) {
                        for (Long vehicleId : stockFeeResponse.getSaasVehicleIds()) {
                            if (checkPlatAudit(param.getChannelId(), vehicleId, param.getMerchantId())) {
                                // 返回已审核的车型
                                return ResultUtil.successResult(vehicleId);
                            }
                        }
                        // 续租单无审核通过车辆返回第一个
                        if (merSourceType == ThirdCheckStockEnum.CREATE_ORDER.getType()) {
                            log.error("直连商家,库存校验,车辆审核悟空未通过,车辆ids={}", JSON.toJSONString(stockFeeResponse.getSaasVehicleIds()));
                            return ResultUtil.successResult(freeId);  // 返回null
                        }
                    }
                    return ResultUtil.successResult(stockFeeResponse.getSaasVehicleIds().get(0));
                } catch (BizException e) {
                    throw new BizException(e.getCode(), e.getMessage());
                } catch (Exception e) {
                    log.info( "直连商家,库存校验调用接口,系统异常:{}", e.getMessage());
                    return ResultUtil.successResult(freeId); //验证ok后，像改为 false
                }
            }
            int orderInterval = 0;
            AllopatryRuleDTO allopatryRuleDTO = null;
            if (VehicleBusyEnum.isOrderBusy(param.getSourceType()) && OrderSourceEnum.HELLO.longValue() != param.getChannelId()) {
                if (param.getStoreId() != null && param.getReturnStoreId() != null && !param.getStoreId().equals(param.getReturnStoreId())) {
                    // 获取异门店规则
                    allopatryRuleDTO = allopatryRuleService
                            .getAllopatryRule(param.getMerchantId(), param.getChannelId(), param.getStoreId(),
                                    param.getReturnStoreId(), param.getVehicleModelId(), new Date(param.getEndTime()));
                    // 直连商家该字段数据存在-1，非业务有效数据
                    orderInterval = (Objects.nonNull(allopatryRuleDTO) && Objects.nonNull(allopatryRuleDTO.getDuration())
                            && allopatryRuleDTO.getDuration() >= 0)
                            ? allopatryRuleDTO.getDuration() * 3600 * 1000 : 0;
                    if (Objects.nonNull(allopatryRuleDTO)) {
                        log.info("异门店规则 merchantId:{}  allopatryRuleDTO:{} ", param.getMerchantId(), JSON.toJSONString(allopatryRuleDTO));
                    } else {
                        log.info("异门店规则 merchantId:{}  allopatryRuleDTO is null ", param.getMerchantId());
                    }
                    // 未匹配到异门店规则
                    if (allopatryRuleDTO == null || allopatryRuleDTO.getDuration() < 0) {
                        orderInterval = storeInfoService.getOrderInterval(param.getStoreId(), param.getChannelId(), param.getVehicleModelId());
                    }
                } else {
                    // 同门店规则
                    orderInterval = storeInfoService.getOrderInterval(param.getStoreId(), param.getChannelId(), param.getVehicleModelId());
                }
            }
            if (OrderSourceEnum.HELLO.longValue() == param.getChannelId()) {
                orderInterval = param.getDiffTime();
            }
            /*
            if (param.getSourceType() == VehicleBusyEnum.ORDER.getValue().intValue() ||
                param.getSourceType() == VehicleBusyEnum.SUBORDER.getValue().intValue()) {
                orderInterval = storeInfoService.getOrderInterval(param.getStoreId(), param.getChannelId(), param.getVehicleModelId(), param.getIsAllopatry());
            }*/

            orderInterval = orderInterval  - 1;

            // 指定车辆
            if (param.getVehicleId() != null) {
                Result<VehicleInfoVO> vehicleResult = vehicleInfoService.getBaseById(param.getVehicleId(), false);
                if (vehicleResult.getModel() == null ||
                        vehicleResult.getModel().getVehicleStatus().equals(VehicleStatusEnum.NOT_ONLINE.getStatus()) ||
                        !vehicleResult.getModel().getSaleStatus().equals(VehicleInfoEnums.SaleStatusEnum.SALE_ENABLE.getStatus()) ||
                        YesOrNoEnum.isYes(vehicleResult.getModel().getDeleted())) {
                    //throw new BizException("车辆不存在或未上线");
                    log.info("===stock===vechileId({}) not found", param.getVehicleId());
                    return ResultUtil.successResult(null);
                }
                // 续租单
                if (param.getParentSourceId() != null) {
                    // 子订单上级订单占用存在
                    VehicleBusyExample exampleOrder = new VehicleBusyExample();
                    exampleOrder.createCriteria().andParentSourceIdEqualTo(param.getParentSourceId())
                            .andMerchantIdEqualTo(param.getMerchantId())
                            .andSourceTypeIn(Arrays.asList(VehicleBusyEnum.ORDER.getValueInt(), VehicleBusyEnum.SUBORDER.getValueInt()))
                            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    exampleOrder.setOrderByClause("id desc");
                    List<VehicleBusy> orderList = vehicleBusyMapper.selectByExample(exampleOrder);
                    if (CollectionUtils.isEmpty(orderList)) {
                        log.info("===stock===parent orderId({}) not found", param.getParentSourceId());
                        //throw new BizException("主订单不存在");
                        return ResultUtil.successResult(null);
                    }
                    // 强制排车==FALSE
                    if (YesOrNoEnum.isNo(param.getFrcedScheduling())) {
                        // 数据是否已存在
                        VehicleBusyExample exampleBusy = new VehicleBusyExample();
                        exampleBusy.createCriteria().andSourceIdEqualTo(param.getSourceId()).
                                andSourceTypeEqualTo(param.getSourceType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                        List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
                        // 查询占用数
                        List<VehicleBusy> busyList = this.selectBusyVehicleList(param.getStoreId(), param.getVehicleId(),
                                param.getStartTime(), param.getEndTime() + orderInterval);
                        if (CollectionUtils.isEmpty(existBusyList)) {
                            // 排除同一个订单的主单或续租数据
                            if (orderList.get(0).getEndTime().compareTo(param.getStartTime()) == 0){
                                busyList.removeIf(e -> e.getId().equals(orderList.get(0).getId()));
                            }
                            if (CollectionUtils.isNotEmpty(busyList)) {
                                log.info("库存占用,订单校验;sourceId={},与库存数据{}冲突", param.getSourceId(), busyList.get(0).getId());
                                //throw new BizException("车辆被占用");
                                return ResultUtil.successResult(null);
                            }
                            return ResultUtil.successResult(param.getVehicleId());
                        } else {
                            VehicleBusy vehicleBusy = existBusyList.get(0);
                            List<VehicleBusy> tmpList =
                                    busyList.stream().filter(e -> !e.getId().equals(vehicleBusy.getId())).collect(
                                            Collectors.toList());
                            if (CollectionUtils.isNotEmpty(tmpList)) {
                                log.info("库存占用,订单校验;sourceId={},与库存数据{}冲突", param.getSourceId(), tmpList.get(0).getId());
                                //throw new BizException("车辆被占用");
                                return ResultUtil.successResult(null);
                            }
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    }
                    // 更新逻辑未处理
                } else {
                    // 数据是否已存在
                    VehicleBusyExample exampleBusy = new VehicleBusyExample();
                    exampleBusy.createCriteria().andSourceIdEqualTo(param.getSourceId()).
                            andSourceTypeEqualTo(param.getSourceType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
                    // 强制排车==FALSE
                    if (YesOrNoEnum.isNo(param.getFrcedScheduling())) {
                        // 查询占用数
                        List<VehicleBusy> busyList = this.selectBusyVehicleList(param.getStoreId(), param.getVehicleId(),
                                param.getStartTime(), param.getEndTime() + orderInterval);
                        if (CollectionUtils.isEmpty(existBusyList)) {
                            if (CollectionUtils.isNotEmpty(busyList)) {
                                log.info("库存占用,订单校验;sourceId={},与库存数据{}冲突", param.getSourceId(), busyList.get(0).getId());
                                //throw new BizException("车辆被占用");
                                return ResultUtil.successResult(freeId);
                            }
                            freeId = param.getVehicleId();
                        } else {
                            VehicleBusy vehicleBusy = existBusyList.get(0);
                            List<VehicleBusy> tmpList =
                                    busyList.stream().filter(e -> !e.getId().equals(vehicleBusy.getId())).collect(
                                            Collectors.toList());
                            if (CollectionUtils.isNotEmpty(tmpList)) {
                                log.info("库存占用,订单校验;sourceId={},与库存数据{}冲突", param.getSourceId(), tmpList.get(0).getId());
                                //throw new BizException("车辆被占用");
                                return ResultUtil.successResult(freeId);
                            }
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(existBusyList)) {
                            return ResultUtil.successResult(param.getVehicleId());
                        }
                    }
                }
            } else {
                // 获取所有车型下的车辆
                Result<List<VehicleSampleVO>> vehiclesResult =
                        vehicleInfoService.findVehiclesByModelId(param.getStoreId(), param.getVehicleModelId());
                List<VehicleSampleVO> vehicles = vehiclesResult.getModel();
                // 查询占用数据
                List<VehicleBusy> busyList = selectBusyModelList(param.getStoreId(), param.getVehicleModelId(),
                    null, param.getStartTime(), param.getEndTime() + orderInterval);
                // 繁忙车辆ids
                List<Long> busyIds = busyList.stream().map(VehicleBusy::getVehicleId)
                        .distinct().collect(Collectors.toList());
                // 可用车辆ids
                List<VehicleSampleVO> vehicleSampleVOList = this.getAvailableVehicleSorted(vehicles, busyIds, param);
                if (CollectionUtils.isEmpty(vehicleSampleVOList)) {
                    log.info("库存占用,占用;sourceId={},车辆无库存,车型{}", param.getSourceId(), param.getVehicleModelId());
                    return ResultUtil.successResult(null);
                }
                for (VehicleSampleVO vehicleSampleVO : vehicleSampleVOList) {
                    if (YesOrNoEnum.isYes(param.getSelfServiceReturn())) {
                        //etc 服务
                        if (YesOrNoEnum.isYes(param.getTiIncludeEtc()) && YesOrNoEnum.isYes(vehicleSampleVO.getTiIncludeEtc())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                        if (YesOrNoEnum.isYes(vehicleSampleVO.getSelfServiceReturn())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                    }
                    //etc 服务
                    else if (YesOrNoEnum.isYes(param.getTiIncludeEtc()) && YesOrNoEnum.isYes(vehicleSampleVO.getTiIncludeEtc())) {
                        freeId = vehicleSampleVO.getId();
                        break;
                    }
                    else if (param.getChannelId() == OrderSourceEnum.WUKONG.longValue() || param.getChannelId() == OrderSourceEnum.HELLO.longValue()) {
                        if (checkPlatAudit(param.getChannelId(), vehicleSampleVO.getId(), param.getMerchantId())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        }
                    } else {
                        // 平台审核检查
                        //if (!VehicleSourceEnum.PLATFORM_VEHICLE.getSource().equals(vehicleSampleVO.getVehicleSource())) {
                            freeId = vehicleSampleVO.getId();
                            break;
                        //}
                    }
                }
                // 自助取还
                if (YesOrNoEnum.isYes(param.getSelfServiceReturn()) && freeId == null) {
                    log.error("库存校验,车型下未找到支持自助取还的车辆,车型id={}", param.getVehicleModelId());
                    return ResultUtil.successResult(null);
                }
                // 悟空渠道
                if (param.getChannelId() == OrderSourceEnum.WUKONG.longValue() && freeId == null) {
                    log.error("库存校验,车型下未找到悟空通过的车辆,车型id={}", param.getVehicleModelId());
                    throw new BizException("无可用车辆");
                }
                // 哈啰渠道
                if (Constant.ChannelId.HELLO.equals(param.getChannelId()) && freeId == null) {
                    log.error("库存校验,车型下未找到哈啰通过的车辆,车型id={}", param.getVehicleModelId());
                    return ResultUtil.successResult(null);
                }
                if (freeId == null) {
                    freeId = vehicleSampleVOList.get(0).getId();
                }
            }
            return ResultUtil.successResult(freeId);
        } catch (BizException e) {
            throw new BizException(changErrCode(e.getCode()), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(changErrCode(null), "排车check异常");
        }
    }

    /**
     * 获取可用车辆id,并排序
     */
    private List<VehicleSampleVO> getAvailableVehicleSorted(List<VehicleSampleVO> sampleVOs, Collection<Long> busyVehicleIds,
                                   VehicleBusyParam busyParam) {

        return sampleVOs.stream().filter(sampleVO -> {
            // 车辆状态不能是未上线
            if (VehicleStatusEnum.isNotOnline(sampleVO.getVehicleStatus())) {
                return false;
            }
            // 售卖状态需要是可售卖
            if (VehicleInfoEnums.SaleStatusEnum.isSoldOut(sampleVO.getSaleStatus())) {
                return false;
            }
            // 没有重复库存
            if (busyVehicleIds.contains(sampleVO.getId())) {
                return false;
            }
            // 有车龄限制要求时，非虚拟库存的车要排车龄小的（注册日期晚于指定时间）
            if (busyParam.getLevelCorrEarliestRegisterTime() != null && YesOrNoEnum.isYes(sampleVO.getPlatformSold())) {
                return sampleVO.getRegDateMillSecond() != null
                    && sampleVO.getRegDateMillSecond() >= busyParam.getLevelCorrEarliestRegisterTime();
            }
            // 没有车龄限制或者虚拟库存都可卖
            return true;
        }).sorted(Comparator.comparing(sampleVO -> {
            // 虚拟库存排最前
            if (YesOrNoEnum.isNo(sampleVO.getPlatformSold())) {
                return Long.MIN_VALUE;
            }
            // 车辆没有注册日期的情况，外部没有限制车龄，优先排没有车龄的；否则把无车龄的排最后
            if (sampleVO.getRegDateMillSecond() == null) {
                if (busyParam.getLevelCorrEarliestRegisterTime() == null) {
                    return 0L;
                }
                return Long.MAX_VALUE;
            }
            // 按照车龄从老到新排序
            return sampleVO.getRegDateMillSecond();
        })).collect(Collectors.toList());
    }

    @Override
    public void prReturnVehicle(Long orderId, Integer sourceType, Date prTime) {
        log.info("库存占用,提前还车;订单={},库存类型={},时间={}", orderId, sourceType, prTime);
        prTime.setSeconds(0);
        Long endTime = prTime.getTime() / 1000 * 1000;
        if (sourceType != null) {
            // 工单等
            VehicleBusyExample exampleBusy = new VehicleBusyExample();
            exampleBusy.createCriteria().andSourceIdEqualTo(orderId).
                    andSourceTypeEqualTo(sourceType).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
            if (CollectionUtils.isNotEmpty(existBusyList)) {
                VehicleBusy busy = existBusyList.get(0);
                VehicleBusy beforeBusy = new VehicleBusy();
                BeanUtils.copyProperties(busy, beforeBusy);
                busy.setEndIntervalTime(endTime + busy.getEndIntervalTime() - busy.getEndTime());
                busy.setEndTime(endTime);
                if (busy.getStartTime().compareTo(busy.getEndTime()) >= 0) {
                    busy.setDeleted(YesOrNoEnum.YES.getValue());
                    busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, beforeBusy);
                } else {
                    busyEvent(EventConstantsEnum.Action.UPDATE.toString(), busy, beforeBusy);
                }
                busy.setOpTime(System.currentTimeMillis());
                vehicleBusyMapper.updateByPrimaryKey(busy);
                // 携程推送库存信息
                CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
                ctripStockTransactionParam.setMerchantId(busy.getMerchantId());
                ctripStockTransactionParam.setVehicleIds(Collections.singletonList(busy.getVehicleId()));
                eventPublisher.publishEvent(ctripStockTransactionParam);
            }
        } else {
            // 主订单
            VehicleBusyExample exampleBusy = new VehicleBusyExample();
            exampleBusy.createCriteria().andParentSourceIdEqualTo(orderId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
            for (VehicleBusy busy : existBusyList) {
                VehicleBusy beforeBusy = new VehicleBusy();
                BeanUtils.copyProperties(busy, beforeBusy);
                if (endTime.compareTo(busy.getEndTime()) >= 0) {
                    continue;
                } else if (endTime.compareTo(busy.getStartTime()) >= 0 && endTime.compareTo(busy.getEndTime()) <= 0) {
                    busy.setEndIntervalTime(endTime + busy.getEndIntervalTime() - busy.getEndTime());
                    busy.setEndTime(endTime);
                    busyEvent(EventConstantsEnum.Action.UPDATE.toString(), busy, beforeBusy);
                } else {
                    busy.setDeleted(YesOrNoEnum.YES.getValue());
                    busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, busy);
                }
                busy.setOpTime(System.currentTimeMillis());
                vehicleBusyMapper.updateByPrimaryKey(busy);
                // 携程推送库存信息
                CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
                ctripStockTransactionParam.setMerchantId(busy.getMerchantId());
                ctripStockTransactionParam.setVehicleIds(Collections.singletonList(busy.getVehicleId()));
                eventPublisher.publishEvent(ctripStockTransactionParam);
            }
        }
    }

    @Override
    public void forceUpdateByMainOrder(Long merchantId, Long orderId, Long vehicleModelId, Long vehicleId) {
        List<Integer> orderBusyTypes = Arrays.asList(VehicleBusyEnum.ORDER.getValueInt(), VehicleBusyEnum.SUBORDER.getValueInt());
        VehicleBusyExample exampleBusy = new VehicleBusyExample();
        exampleBusy.createCriteria().andMerchantIdEqualTo(merchantId)
                .andParentSourceIdEqualTo(orderId).andSourceTypeIn(orderBusyTypes)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
        Long oldVehicleId = null;
        for (VehicleBusy beforeBusy : existBusyList) {
            long t = System.currentTimeMillis();
            VehicleBusy busy = new VehicleBusy();
            busy.setId(beforeBusy.getId());
            busy.setVehicleModelId(vehicleModelId);
            busy.setVehicleId(vehicleId);
            busy.setOpTime(t);
            if (StringUtils.isEmpty(beforeBusy.getBusyDesc())) {
                busy.setBusyDesc("强制改排");
            }
            vehicleBusyMapper.updateByPrimaryKeySelective(busy);
            // 推送第三方库存
            BeanUtils.copyProperties(beforeBusy, busy);
            busy.setVehicleModelId(vehicleModelId);
            busy.setVehicleId(vehicleId);
            busy.setOpTime(t);
            busyEvent(EventConstantsEnum.Action.UPDATE.toString(), busy, beforeBusy);
            oldVehicleId = beforeBusy.getVehicleId();
        }
        if (oldVehicleId == null) {
            return;
        }
        // 携程推送库存信息
        CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
        ctripStockTransactionParam.setMerchantId(merchantId);
        ctripStockTransactionParam.setVehicleIds(Arrays.asList(vehicleId, oldVehicleId));
        eventPublisher.publishEvent(ctripStockTransactionParam);
    }

    @Override
    public List<VehicleBusyEntityVO> busyPlatCreate(List<VehicleBusyEntityVO> params) {
        List<VehicleBusy> list = new ArrayList<>();
        Iterator<VehicleBusyEntityVO> iterator = params.iterator();
        while (iterator.hasNext()) {
            VehicleBusyEntityVO vo = iterator.next();
            VehicleBusy lock = new VehicleBusy();
            BeanUtils.copyProperties(vo, lock);
            if (null == lock.getMerchantId() || null == lock.getStoreId() || null == lock.getVehicleModelId() ||
                    null == lock.getVehicleId() || null == lock.getStartTime() || null == lock.getEndTime() ||
                    null == lock.getChannelId() || null == lock.getSourceType()) {
                throw new BizException("参数错误");
            }
            if (null == lock.getSourceId()) {
                lock.setSourceId(0L);
            }
            if (null == lock.getParentSourceId()) {
                lock.setParentSourceId(0L);
            }
            if (null == lock.getThirdSourceId()) {
                lock.setThirdSourceId(StringUtils.EMPTY);
            }
            if (null == lock.getThirdParentSourceId()) {
                lock.setThirdParentSourceId(StringUtils.EMPTY);
            }
            if (null != lock.getEndIntervalTime()) {
                lock.setEndIntervalTime(lock.getEndIntervalTime() - 1);
            } else {
                lock.setEndIntervalTime(lock.getEndTime() - 1);
            }
            if (StringUtils.isEmpty(lock.getThirdParentSourceId())) {
                lock.setThirdParentSourceId(lock.getThirdSourceId());
            }
            lock.setDeleted(YesOrNoEnum.NO.getValue());
            lock.setLockFlg(YesOrNoEnum.YES.getValue());
            lock.setAutoSchedule(YesOrNoEnum.NO.getValue());
            lock.setCreateTime(System.currentTimeMillis());
            lock.setOpTime(System.currentTimeMillis());
            if (lock.getBusyDesc() == null) {
                lock.setBusyDesc(StringUtils.EMPTY);
            }
            Result<VehicleInfoVO> vehicleInfoVOResult = vehicleInfoService.getBaseById(vo.getVehicleId(), false);
            if (vehicleInfoVOResult.getModel() == null) {
                log.error("平台同步入库,车辆id不存在,vehicleId={},lock={},vo={}", vo.getVehicleId(),
                        JSON.toJSONString(lock), JSON.toJSONString(vo));
            }
            if (!vehicleInfoVOResult.getModel().getVehicleModelId().equals(vo.getVehicleModelId())) {
                log.info("平台同步入库,处于修改车型状态,车型id不一致,暂不同步,vehicleInfo={},param={}",
                        JSON.toJSONString(vehicleInfoVOResult.getModel()), JSON.toJSONString(vo));
                iterator.remove();
                continue;
            }
            setBusyExtFiled(lock, vo.getBusyExt());
            list.add(lock);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            if (list.size() == 1) {
                vehicleBusyMapper.insertSelective(list.get(0));
            } else {
                vehicleBusyMapper.batchInsert(list);
            }
            // 携程推送库存信息
            Map<Long, List<VehicleBusy>> map = list.stream().collect(Collectors.groupingBy(VehicleBusy::getMerchantId));
            for (Map.Entry<Long, List<VehicleBusy>> longListEntry : map.entrySet()) {
                List<Long> param = longListEntry.getValue().stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
                CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
                ctripStockTransactionParam.setMerchantId(longListEntry.getKey());
                ctripStockTransactionParam.setVehicleIds(param);
                eventPublisher.publishEvent(ctripStockTransactionParam);
            }
        }
        int idx = 0;
        for (VehicleBusyEntityVO vo : params) {
            vo.setId(list.get(idx).getId());
            busyEvent(EventConstantsEnum.Action.ADD.toString(), list.get(idx), null);
            idx++;
        }

        return params;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public void busyPlatUpdate(List<VehicleBusyEntityVO> param) {
        List<VehicleBusy> realChangeList = new ArrayList<>();
        for (VehicleBusyEntityVO vo : param) {
            VehicleBusy lock = new VehicleBusy();
            BeanUtils.copyProperties(vo, lock);
            if (null == lock.getId()) {
                throw new BizException("参数错误");
            }
            if (null != lock.getEndIntervalTime()) {
                lock.setEndIntervalTime(lock.getEndIntervalTime() - 1);
            }
            if (vo.getDeleted() != null) {
                lock.setDeleted(vo.getDeleted().byteValue());
            }
            Result<VehicleInfoVO> vehicleInfoVOResult = vehicleInfoService.getBaseById(vo.getVehicleId(), false);
            if (vehicleInfoVOResult.getModel().getVehicleModelId().compareTo(vo.getVehicleModelId()) != 0) {
                log.info("平台同步更新,处于修改车型状态,车型id不一致,暂不同步,vehicleInfo={},param={}",
                        JSON.toJSONString(vehicleInfoVOResult.getModel()), JSON.toJSONString(vo));
                continue;
            }
            lock.setOpTime(System.currentTimeMillis());
            vehicleBusyMapper.updateByPrimaryKeySelective(lock);
            realChangeList.add(lock);
        }
        // 携程推送库存信息
        Map<Long, List<VehicleBusy>> map = realChangeList.stream().collect(Collectors.groupingBy(VehicleBusy::getMerchantId));
        for (Map.Entry<Long, List<VehicleBusy>> longListEntry : map.entrySet()) {
            List<Long> Ids = longListEntry.getValue().stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
            CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
            ctripStockTransactionParam.setMerchantId(longListEntry.getKey());
            ctripStockTransactionParam.setVehicleIds(Ids);
            eventPublisher.publishEvent(ctripStockTransactionParam);
        }

        if (CollectionUtils.isNotEmpty(realChangeList)) {
            try {
                List<Long> ids = realChangeList.stream().map(e -> e.getId()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ids)) {
                    return;
                }
                VehicleBusyExample exampleBusy = new VehicleBusyExample();
                exampleBusy.createCriteria().andIdIn(ids);
                List<VehicleBusy> beforeList = vehicleBusyMapper.selectByExample(exampleBusy);

                for (VehicleBusy after : realChangeList) {
//                    // 非哈喽 先返回，不推送
//                    if (Byte.compare(after.getChannelId().byteValue(), PaySourceEnum.HELLO.getSource()) != 0) {
//                        continue;
//                    }
                    VehicleBusy before = beforeList.stream()
                            .filter(e -> e.getId().equals(after.getId())).findFirst().orElse(null);
                    if (before == null) {
                        continue;
                    }
                    busyEvent(EventConstantsEnum.Action.UPDATE.toString(), after, before);
                }
            } catch (Exception e) {
                log.info("平台同步,更新,异常,param={},messge={}", JSON.toJSONString(param), e.getMessage());
            }
        }
    }

    @Override
    public List<StockVehicleBusyVO> shuntingStockCheck(Long vehicleId, Date startTime, Date endTime) {
//        List<Integer> types = new ArrayList<>(
//            Arrays.asList(
//                VehicleBusyEnum.ORDER.getValue().intValue(),
//                VehicleBusyEnum.SUBORDER.getValue().intValue())
//        );
        VehicleBusyExample exampleBusy = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = exampleBusy.createCriteria();
        criteria.andVehicleIdEqualTo(vehicleId)//.andSourceTypeIn(types)
                .andEndIntervalTimeGreaterThan(endTime.getTime())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (!startTime.equals(endTime)) {
            exampleBusy.or().andVehicleIdEqualTo(vehicleId)//.andSourceTypeIn(types)
                    .andEndIntervalTimeGreaterThan(startTime.getTime())
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        }
        List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
        List<StockVehicleBusyVO> retList = new ArrayList<>();
        for (VehicleBusy busy : existBusyList) {
            StockVehicleBusyVO vo = new StockVehicleBusyVO();
            BeanUtils.copyProperties(busy, vo);
            retList.add(vo);
        }
        return retList;
    }

    @Override
    public List<StockVehicleBusyVO> vehicleOfflineCheck(Long vehicleId) {
        VehicleBusyExample exampleBusy = new VehicleBusyExample();
        // 这里先比较endTime即可。线下后，已经查询不到数据了。
        exampleBusy.createCriteria().andVehicleIdEqualTo(vehicleId)
                .andEndTimeGreaterThanOrEqualTo(System.currentTimeMillis())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());

        List<VehicleBusy> existBusyList = vehicleBusyMapper.selectByExample(exampleBusy);
        if (CollectionUtils.isEmpty(existBusyList)) {
            return Collections.emptyList();
        }
        List<StockVehicleBusyVO> retList = new ArrayList<>(existBusyList.size());
        for (VehicleBusy busy : existBusyList) {
            StockVehicleBusyVO vo = new StockVehicleBusyVO();
            BeanUtils.copyProperties(busy, vo);
            retList.add(vo);
        }
        return retList;
    }

    private void updateVehicleBusy(VehicleBusy vehicleBusy, VehicleBusyParam param, Long orderInterval) {
        VehicleBusy beforeBusy = new VehicleBusy();
        BeanUtils.copyProperties(vehicleBusy, beforeBusy);
        vehicleBusy.setVehicleModelId(param.getVehicleModelId());
        vehicleBusy.setVehicleId(param.getVehicleId());
        if (param.getUpdTimeFlg()) {
            vehicleBusy.setStartTime(param.getStartTime());
            vehicleBusy.setEndTime(param.getEndTime());
            vehicleBusy.setEndIntervalTime(param.getEndTime() + orderInterval);
        }
        vehicleBusy.setAutoSchedule(YesOrNoEnum.NO.getValue());
        vehicleBusy.setOpTime(System.currentTimeMillis());
        vehicleBusyMapper.updateByPrimaryKeySelective(vehicleBusy);
        busyEvent(EventConstantsEnum.Action.UPDATE.toString(), vehicleBusy, beforeBusy);
        // 携程推送库存信息
        CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
        ctripStockTransactionParam.setMerchantId(vehicleBusy.getMerchantId());
        ctripStockTransactionParam.setVehicleIds(Arrays.asList(vehicleBusy.getVehicleId(), beforeBusy.getVehicleId()));
        eventPublisher.publishEvent(ctripStockTransactionParam);
    }

//    @Override
//    public void commitLock(Long lockId){
//        //这个方法感觉没什么用 todo pijiu
//    }

    @Override
    public void cancelLock(Integer sourceType, Long sourceId) {
        log.info("库存占用,取消;sourceId={},库存类型={}", sourceId, sourceType);
        VehicleBusyExample example = new VehicleBusyExample();
        example.createCriteria().andSourceIdEqualTo(sourceId).andSourceTypeEqualTo(sourceType);
        VehicleBusy record = new VehicleBusy();
        record.setOpTime(System.currentTimeMillis());
        record.setDeleted(YesOrNoEnum.YES.getValue());
        List<VehicleBusy> list = vehicleBusyMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(list)) {
            // 取消续租单，修改主订单库存数据
            if (VehicleBusyEnum.SUBORDER.getValueInt().equals(sourceType)) {
                // 注：此逻辑如果遇到商家修改了订单时间间隔，可能会出现问题，需和产品确认
                // 1.续租单，查询出 主订单, 主库存数据
                // 2.根据 end_interval_time - end_time 计算得出订单时间间隔
                // 3.修改 主库存数据 end_interval_time + （end_interval_time - end_time）
                VehicleBusy vehicleBusy = list.stream().findFirst().orElse(null);
                if (Objects.nonNull(vehicleBusy)) {
                    long duration = vehicleBusy.getEndIntervalTime() - vehicleBusy.getEndTime();
                    Long parentSourceId = vehicleBusy.getParentSourceId();
                    VehicleBusyExample parentSourceBusyExample = new VehicleBusyExample();
                    parentSourceBusyExample.createCriteria()
                            .andParentSourceIdEqualTo(parentSourceId)
                            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    List<VehicleBusy> busies = vehicleBusyMapper.selectByExample(parentSourceBusyExample);
                    busies.stream().filter(et -> !Objects.equals(et.getId(), vehicleBusy.getId()))
                            .max(Comparator.comparing(VehicleBusy::getCreateTime))
                            .ifPresent(et -> {
                                et.setEndIntervalTime(et.getEndTime() + duration);
                                et.setOpTime(System.currentTimeMillis());
                                vehicleBusyMapper.updateByPrimaryKey(et);
                            });
                }
            }

            vehicleBusyMapper.updateByExampleSelective(record, example);
            busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, list.get(0));
            // 携程推送库存信息
            CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
            ctripStockTransactionParam.setMerchantId(list.get(0).getMerchantId());
            ctripStockTransactionParam.setVehicleIds(Collections.singletonList(list.get(0).getVehicleId()));
            eventPublisher.publishEvent(ctripStockTransactionParam);
        }
    }

    @Override
    public void cancelAllLockByMainOrderId(Long parentSourceId) {
        log.info("库存占用,取消;主订单={}", parentSourceId);
        VehicleBusyExample example = new VehicleBusyExample();
        example.createCriteria().andParentSourceIdEqualTo(parentSourceId)
                .andSourceTypeEqualTo(VehicleBusyEnum.ORDER.getValueInt());
        example.or().andParentSourceIdEqualTo(parentSourceId)
                .andSourceTypeEqualTo(VehicleBusyEnum.SUBORDER.getValueInt());
        VehicleBusy record = new VehicleBusy();
        record.setDeleted(YesOrNoEnum.YES.getValue());
        record.setOpTime(System.currentTimeMillis());
        List<VehicleBusy> list = vehicleBusyMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            vehicleBusyMapper.updateByExampleSelective(record, example);
            for (VehicleBusy busy : list) {
                busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, busy);
            }
            // 携程推送库存信息
            Map<Long, List<VehicleBusy>> map = list.stream().collect(Collectors.groupingBy(VehicleBusy::getMerchantId));
            for (Map.Entry<Long, List<VehicleBusy>> entry : map.entrySet()) {
                List<Long> vehicleIds = entry.getValue().stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
                CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
                ctripStockTransactionParam.setMerchantId(entry.getKey());
                ctripStockTransactionParam.setVehicleIds(vehicleIds);
                eventPublisher.publishEvent(ctripStockTransactionParam);
            }
        }
    }

    @Override
    public void cancelPlatLocks(Long merchantId, List<Long> ids, String busyDesc) {
        VehicleBusy record = new VehicleBusy();
        record.setDeleted(YesOrNoEnum.YES.getValue());
        record.setOpTime(System.currentTimeMillis());
        if (null != busyDesc) {
            record.setBusyDesc(busyDesc);
        }
        VehicleBusyExample example = new VehicleBusyExample();
        example.createCriteria().andIdIn(ids).andMerchantIdEqualTo(merchantId);
        List<VehicleBusy> list = vehicleBusyMapper.selectByExample(example);
        vehicleBusyMapper.updateByExampleSelective(record, example);
        for (VehicleBusy busy : list) {
            busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, busy);
        }
        // 携程推送库存信息
        List<Long> vehicleIds = list.stream().map(VehicleBusy::getVehicleId).collect(Collectors.toList());
        CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
        ctripStockTransactionParam.setMerchantId(list.get(0).getMerchantId());
        ctripStockTransactionParam.setVehicleIds(vehicleIds);
        eventPublisher.publishEvent(ctripStockTransactionParam);
    }

    @Override
    public List<VehicleBusyEntityVO> selectPlatLocks(VehicleBusyPlatParam param) {
        List<VehicleBusyEntityVO> retList = new ArrayList<>();
        // 查询占用数据
        List<VehicleBusy> busyList = this.selectBusyMerchantList(param.getMerchantId(), param.getStoreId(),
                param.getVehicleIds(), param.getChannelId(), param.getStartTime(), param.getEndTime());
        for (VehicleBusy bean : busyList) {
            VehicleBusyEntityVO vo = new VehicleBusyEntityVO();
            BeanUtils.copyProperties(bean, vo);
            if (!StringUtils.isEmpty(bean.getExt())) {
                try {
                    VehicleBusyExtParam busyExt = JSONObject.parseObject(bean.getExt(), VehicleBusyExtParam.class);
                    vo.setBusyExt(busyExt);
                } catch (Exception e) {
                    log.error("库存同步,库存Ext转换失败,id={},param={}", bean.getId(), bean.getExt());
                }
            }
            retList.add(vo);
        }
        return retList;
    }

    @Override
    public void cronCancelLock() {
//        VehicleBusyExample example = new VehicleBusyExample();
//        example.createCriteria().andLockFlgEqualTo(YesOrNoEnum.NO.getValue())
//            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
//        VehicleBusy record = new VehicleBusy();
//        record.setDeleted(YesOrNoEnum.YES.getValue());
//        vehicleBusyMapper.updateByExampleSelective(record, example);
    }

    /**
     * 库存汇总视图
     *
     * @param query
     */
    @Override
    public Result<StockSummaryVO> stockSummary(StockSummaryQuery query, String loginName) {
        StockSummaryVO retVo = new StockSummaryVO();
        // 获取所有车型下的车辆
        List<VehicleSampleVO> vehicles = new ArrayList<>();
        if (query.getVehicleModelId() != null && query.getVehicleModelId() == 0L)
            query.setVehicleModelId(null);
        List<Long> storeIdsParam = new ArrayList<>();
        storeIdsParam.add(query.getStoreId());
        List<VehicleSourceGroupVO> vehicleSourceGroupVOList = vehicleInfoService
                .findAuthVehicleIds(loginName, query.getMerchantId(), storeIdsParam);
        Map<Long, String> authVehicleIdMap;
        if (vehicleSourceGroupVOList != null) {
            authVehicleIdMap = vehicleSourceGroupVOList.stream().collect(
                    Collectors.toMap(key -> key.getVehicleId(), value -> ""));
        } else {
            authVehicleIdMap = new HashMap<>();
        }
        if (query.getVehicleModelId() == null || query.getVehicleModelId() == 0L) {
            // 查询可管理车型
            Result<RentVehicleParam> relationModel = rentMainService.findRelationModelList(query.getStoreId(), null);
            List<Long> modes =
                    relationModel.getModel().getVehicleList().stream().map(RentVehicleVo::getVehicleModelId).distinct()
                            .collect(Collectors.toList());
            Result<List<VehicleSampleVO>> vehiclesResult =
                    vehicleInfoService.findVehiclesByModelId(query.getStoreId(), null);
            if (vehicleSourceGroupVOList == null) {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> modes.contains(e.getVehicleModelId())).collect(
                                Collectors.toList()));
            } else {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> modes.contains(e.getVehicleModelId())
                                && authVehicleIdMap.containsKey(e.getId())).collect(Collectors.toList()));
            }
        } else {
            Result<List<VehicleSampleVO>> vehiclesResult =
                    vehicleInfoService.findVehiclesByModelId(query.getStoreId(), query.getVehicleModelId());
            if (vehicleSourceGroupVOList == null) {
                vehicles.addAll(vehiclesResult.getModel());
            } else {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> authVehicleIdMap.containsKey(e.getId()))
                                .collect(Collectors.toList()));
            }
        }

        long usableCount = vehicles.size();
        List<Long> stopVehicleIds = vehicles.stream()
                .filter(e -> e.getVehicleStatus().equals(VehicleStatusEnum.NOT_ONLINE.getStatus()))
                .map(e -> e.getId()).collect(Collectors.toList());
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};

        // 查询日期时间段占用数据
        query.setEndTime(query.getEndTime() + 24 * 60 * 60 * 1000 - 1);
        List<VehicleBusy> busyList =
                selectBusyModelList(query.getStoreId(), query.getVehicleModelId(), null, query.getStartTime(),
                        query.getEndTime());

        if (vehicleSourceGroupVOList != null) {
            busyList = busyList.stream().filter(e -> authVehicleIdMap.containsKey(e.getVehicleId()))
                    .collect(Collectors.toList());
        }
        ;
        // 过滤有冲突数据
        List<VehicleBusy> overBusyList = new ArrayList<>();
        List<Long> checkIds = new ArrayList<>();
        for (VehicleBusy busy : busyList) {
            List<VehicleBusy> tmpList =
                    busyList.stream()
                            .filter(e -> !checkIds.contains(e.getId()) && e.getVehicleId().equals(busy.getVehicleId()))
                            .collect(Collectors.toList());
            tmpList = getBusyListByDate(tmpList, busy.getStartTime(), busy.getEndIntervalTime());
            if (CollectionUtils.isNotEmpty(tmpList)) {
                if (tmpList.size() > 1) {
                    overBusyList.addAll(tmpList);
                }
            }
            checkIds.add(busy.getId());
        }
        overBusyList = overBusyList.stream().distinct().collect(Collectors.toList());
        retVo.setOverCount(overBusyList.size());

        // 日期循环
        long day = (query.getEndTime() + 1 - query.getStartTime()) / (24 * 60 * 60 * 1000);
        List<StockSummaryVO.RowVO> usableList = new ArrayList<>();
        Long startDate = query.getStartTime();
        for (int i = 0; i < day; i++) {
            StockSummaryVO.RowVO rowVO = new StockSummaryVO.RowVO();
            rowVO.setRowDate(startDate);
            rowVO.setRowWeek(weekDays[new Date(startDate).getDay()]);
            // 24小时数据详细
            List<StockSummaryVO.DateVO> dateVOList = new ArrayList<>();
            for (int x = 0; x < 24; x++) {
                StockSummaryVO.DateVO dateVO = new StockSummaryVO.DateVO();
                dateVO.setUsableAllCount(usableCount);
                // 计算可用车辆数量
                long startTime = startDate + x * 60 * 60 * 1000;
                long endTime = startDate + (x + 1) * 60 * 60 * 1000 - 1;
                List<VehicleBusy> tmpList = getBusyListByDate(busyList, startTime, endTime);
                List<Long> busyIds = tmpList.stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
                long canUsed = vehicles.stream()
                        .filter(e -> !stopVehicleIds.contains(e.getId()))
                        .filter(e -> !busyIds.contains(e.getId()))
                        .count();
                dateVO.setUsableCount(canUsed);
                // 是否有冲突数据
                List<Long> ovryVIds = new ArrayList<>();
                List<Long> checkVIds = new ArrayList<>();
                for (VehicleBusy busy : busyList) {
                    List<VehicleBusy> tmp2List = tmpList.stream().filter(
                                    e -> !checkVIds.contains(e.getVehicleId()) && e.getVehicleId().equals(busy.getVehicleId()))
                            .collect(Collectors.toList());
                    tmp2List = getBusyListByDate(tmp2List, startTime, endTime);
                    if (tmp2List.size() > 1) {
                        for (VehicleBusy busy2 : tmp2List) {
                            List<VehicleBusy> tmp3List = getBusyListByDate(tmp2List, busy2.getStartTime(), busy2.getEndIntervalTime());
                            if (tmp3List.size() > 1) {
                                for (VehicleBusy busy3 : tmp3List) {
                                    ovryVIds.add(busy3.getVehicleId());
                                }
                            }
                        }
                    }
                    checkVIds.add(busy.getVehicleId());
                }
                ovryVIds = ovryVIds.stream().distinct().collect(Collectors.toList());
                dateVO.setOverFlg(ovryVIds.size() > 0 ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue());
                dateVOList.add(dateVO);
            }
            rowVO.setDateList(dateVOList);
            usableList.add(rowVO);
            startDate = startDate + 24 * 60 * 60 * 1000;
        }
        retVo.setUsableList(usableList);
        return ResultUtil.successResult(retVo);
    }

    /**
     * 时间段内查询可用车辆
     *
     * @param param
     * @return
     */
    @Override
    public Result<List<VehicleSampleVO>> stockFreeVehicle(VehicleFreeParam param, String loginName, Long merchantId) {
        // 获取所有车型下的车辆
        List<VehicleSampleVO> vehicles = new ArrayList<>();
        if (param.getVehicleModelId() != null && param.getVehicleModelId() == 0L)
            param.setVehicleModelId(null);

        List<Long> storeIdsParam = new ArrayList<>();
        storeIdsParam.add(param.getStoreId());
        List<VehicleSourceGroupVO> vehicleSourceGroupVOList = vehicleInfoService
                .findAuthVehicleIds(loginName, merchantId, storeIdsParam);
        Map<Long, String> authVehicleIdMap;
        if (vehicleSourceGroupVOList != null) {
            authVehicleIdMap = vehicleSourceGroupVOList.stream().collect(
                    Collectors.toMap(key -> key.getVehicleId(), value -> ""));
        } else {
            authVehicleIdMap = new HashMap<>();
        }
        if (param.getVehicleModelId() == null || param.getVehicleModelId() == 0L) {
            // 查询可管理车型
            Result<RentVehicleParam> relationModel = rentMainService.findRelationModelList(param.getStoreId(), null);
            List<Long> modes =
                    relationModel.getModel().getVehicleList().stream().map(RentVehicleVo::getVehicleModelId).distinct()
                            .collect(Collectors.toList());
            Result<List<VehicleSampleVO>> vehiclesResult =
                    vehicleInfoService.findVehiclesByModelId(param.getStoreId(), null);
            if (vehicleSourceGroupVOList == null) {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> modes.contains(e.getVehicleModelId())).collect(
                                Collectors.toList()));
            } else {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> modes.contains(e.getVehicleModelId())
                                && authVehicleIdMap.containsKey(e.getId())).collect(Collectors.toList()));
            }
        } else {
            Result<List<VehicleSampleVO>> vehiclesResult =
                    vehicleInfoService.findVehiclesByModelId(param.getStoreId(), param.getVehicleModelId());
            if (vehicleSourceGroupVOList == null) {
                vehicles.addAll(vehiclesResult.getModel());
            } else {
                vehicles.addAll(
                        vehiclesResult.getModel().stream().filter(e -> authVehicleIdMap.containsKey(e.getId()))
                                .collect(Collectors.toList()));
            }
        }
        // 查询占用数据
        List<VehicleBusy> busyList =
                selectBusyModelList(param.getStoreId(), param.getVehicleModelId(), null, param.getStartTime(),
                        param.getEndTime());
        // 排除订单占用数据
        if (param.getOrderId() != null) {
            busyList = busyList.stream().filter(e -> !e.getParentSourceId().equals(param.getOrderId()))
                    .collect(Collectors.toList());
        }
        // 繁忙车辆ids
        List<Long> busyIds = busyList.stream().map(VehicleBusy::getVehicleId).collect(Collectors.toList());
        // 可用车辆
        List<VehicleSampleVO> list = vehicles.stream().filter(e -> !busyIds.contains(e.getId()) &&
                !e.getVehicleStatus().equals(VehicleStatusEnum.NOT_ONLINE.getStatus())).collect(Collectors.toList());
        return ResultUtil.successResult(list);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public Result<Boolean> stockChange(StockChangeParam param) {
        if (param.getFromVehicleId().equals(param.getToVehicleId())) {
            throw new BizException("库存调换车辆不能相同");
        }
        String rdkey = "stockChange:" + param.getStoreId();
        long check = redisService.setnx(rdkey, 2L);
        if (check > 1) {
            throw new BizException("请勿频繁操作");
        }
        ApiConnBusiParam connParam = new ApiConnBusiParam();
        connParam.setMerchantId(param.getMerchantId());
        connParam.setIncludeOffLine(YesOrNoEnum.NO.getValue());
        connParam.setIncludeSub(YesOrNoEnum.NO.getValue());
        connParam.setIncludeSync(YesOrNoEnum.YES.getValue());
        connParam.setIncludeApi(YesOrNoEnum.NO.getValue());
        Result<List<ApiConnVo>> apiConnResult = apiConnService.listChannelByBusi(connParam);
        List<Long> channelSync = apiConnResult.getModel().stream().map(e -> e.getChannelId()).collect(Collectors.toList());

        Long storeId = param.getStoreId();
        Long fromVehicleId = param.getFromVehicleId();
        Long toVehicleId = param.getToVehicleId();
        List<Long> vehicleIds = new ArrayList<>();
        Long nowTime = System.currentTimeMillis();
        vehicleIds.add(fromVehicleId);
        vehicleIds.add(toVehicleId);
        VehicleBusyExample example = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
                .andVehicleIdIn(vehicleIds)
                .andEndIntervalTimeGreaterThan(nowTime)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleBusy> busyList = vehicleBusyMapper.selectByExample(example);

        // 是否已提车Check
        List<UpdateOrderVehicleDTO> paramlist = new ArrayList<>();
        for (VehicleBusy busy : busyList) {
            if (!Arrays.asList(1, 3).contains(busy.getSourceType())) {
                continue;
            }
            UpdateOrderVehicleDTO dto = new UpdateOrderVehicleDTO();
//      库存表中三种情况 source_id parent_source_id third_source_id third_parent_source_id
//         非同步订单     1             1              null               null
//         同步订单       0             0              1                  null
//         同步订单       0             0              1                  1
            if (StringUtils.isNotEmpty(busy.getThirdSourceId())) {
                dto.setType((byte) 1);
                dto.setSourceId(busy.getThirdSourceId());
                if (StringUtils.isNotEmpty(busy.getThirdParentSourceId())) {
                    dto.setParentSourceId(busy.getThirdParentSourceId());
                } else {
                    dto.setParentSourceId(busy.getThirdSourceId());
                }
            } else {
                dto.setType((byte) 0);
                dto.setSourceId(busy.getSourceId().toString());
                dto.setParentSourceId(busy.getParentSourceId().toString());
            }
            if (channelSync.contains(busy.getChannelId())) {
                throw new BizException("同步订单不支持调换");
            }
            paramlist.add(dto);
        }
        if (CollectionUtils.isEmpty(paramlist)) {
            throw new BizException("无库存调换数据");
        }
        Result<Map<String, Boolean>> retCheckPickup = orderService.checkOrderPickup(paramlist);
        List<String> pickupOrder = new ArrayList<>();
        List<String> noPickupOrder = new ArrayList<>();
        for (String key : retCheckPickup.getModel().keySet()) {
            if (retCheckPickup.getModel().get(key)) {
                pickupOrder.add(key);
            } else {
                noPickupOrder.add(key);
            }
        }

        List<VehicleBusy> fromBusyList = busyList.stream()
                .filter(e -> e.getVehicleId().equals(fromVehicleId)).collect(Collectors.toList());
        List<VehicleBusy> toBusyList = busyList.stream()
                .filter(e -> e.getVehicleId().equals(toVehicleId)).collect(Collectors.toList());

        // 库存交换是否冲突检查
        // 库存更新IDS
        List<Long> updIds = new ArrayList<>();
        // 订单更新对象
        paramlist = new ArrayList<>();
        // 检查from到to
        List<VehicleBusy> toTmpBusyList = toBusyList.stream()
                .filter(e -> !Arrays.asList(1, 3).contains(e.getSourceType()) ||
                        pickupOrder.contains(e.getParentSourceId().toString()) ||
                        pickupOrder.contains(e.getThirdParentSourceId())
                ).collect(Collectors.toList());
        List<VehicleBusy> fromTmpBusyList = fromBusyList.stream()
                .filter(e -> Arrays.asList(1, 3).contains(e.getSourceType()) &&
                        (noPickupOrder.contains(e.getParentSourceId().toString()) || noPickupOrder.contains(e.getThirdParentSourceId()))
                ).collect(Collectors.toList());
        for (VehicleBusy busy : fromTmpBusyList) {
            List<VehicleBusy> tmpList = getBusyListByDate(toTmpBusyList, busy.getStartTime(), busy.getEndIntervalTime());
            if (tmpList.size() > 0) {
                redisService.remove(rdkey);
                String fromSourceId = busy.getSourceId() == 0 ? busy.getThirdSourceId() : busy.getSourceId().toString();
                String toSourceId = tmpList.get(0).getSourceId() == 0 ? tmpList.get(0).getThirdSourceId() : tmpList.get(0).getSourceId().toString();
                throw new BizException("订单:" + fromSourceId + "," + toSourceId + "冲突");
            }
            UpdateOrderVehicleDTO dto = new UpdateOrderVehicleDTO();
            if (StringUtils.isNotEmpty(busy.getThirdParentSourceId())) {
                dto.setType((byte) 1);
                dto.setSourceId(busy.getThirdSourceId());
                dto.setParentSourceId(busy.getThirdParentSourceId());
            } else {
                dto.setType((byte) 0);
                dto.setSourceId(busy.getSourceId().toString());
                dto.setParentSourceId(busy.getParentSourceId().toString());
            }
            dto.setVehicleId(toVehicleId);
            dto.setOpUserId(param.getUserId());
            paramlist.add(dto);
            updIds.add(busy.getId());
        }
        // 检查to到from
        fromTmpBusyList = fromBusyList.stream()
                .filter(e -> !Arrays.asList(1, 3).contains(e.getSourceType()) ||
                        pickupOrder.contains(e.getParentSourceId().toString()) ||
                        pickupOrder.contains(e.getThirdParentSourceId())
                ).collect(Collectors.toList());
        toTmpBusyList = toBusyList.stream()
                .filter(e -> Arrays.asList(1, 3).contains(e.getSourceType()) &&
                        (noPickupOrder.contains(e.getParentSourceId().toString()) || noPickupOrder.contains(e.getThirdParentSourceId()))
                ).collect(Collectors.toList());
        for (VehicleBusy busy : toTmpBusyList) {
            List<VehicleBusy> tmpList = getBusyListByDate(fromTmpBusyList, busy.getStartTime(), busy.getEndIntervalTime());
            if (tmpList.size() > 0) {
                redisService.remove(rdkey);
                String fromSourceId = busy.getSourceId() == 0 ? busy.getThirdSourceId() : busy.getSourceId().toString();
                String toSourceId = tmpList.get(0).getSourceId() == 0 ? tmpList.get(0).getThirdSourceId() : tmpList.get(0).getSourceId().toString();
                throw new BizException("订单:" + fromSourceId + "," + toSourceId + "冲突");
            }
            UpdateOrderVehicleDTO dto = new UpdateOrderVehicleDTO();
            if (StringUtils.isNotEmpty(busy.getThirdParentSourceId())) {
                dto.setType((byte) 1);
                dto.setSourceId(busy.getThirdSourceId());
                dto.setParentSourceId(busy.getThirdParentSourceId());
            } else {
                dto.setType((byte) 0);
                dto.setSourceId(busy.getSourceId().toString());
                dto.setParentSourceId(busy.getParentSourceId().toString());
            }
            dto.setVehicleId(fromVehicleId);
            dto.setOpUserId(param.getUserId());
            paramlist.add(dto);
            updIds.add(busy.getId());
        }

        // 更新订单
        Result<Boolean> orderUpdResult = orderService.updateOrderVehicleId(paramlist);
        if (orderUpdResult == null || orderUpdResult.isSuccess() == false) {
            throw new BizException("订单更新车辆失败：" + orderUpdResult.getMessage());
        }

        // 更新库存
        List<Long> fromIds = new ArrayList<>();
        List<Long> toIds = new ArrayList<>();
        List<VehicleBusy> updTmpBusyList = busyList.stream().filter(
                e -> Arrays.asList(1, 3).contains(e.getSourceType()) && updIds.contains(e.getId())
        ).collect(Collectors.toList());
        VehicleBusy afterBusy;
        for (VehicleBusy busy : updTmpBusyList) {
            afterBusy = new VehicleBusy();
            BeanUtils.copyProperties(busy, afterBusy);
            if (busy.getVehicleId().equals(fromVehicleId)) {
                fromIds.add(busy.getId());
                afterBusy.setVehicleId(toVehicleId);
                //busyEvent(EventConstantsEnum.Action.UPDATE.toString(), afterBusy, busy);
                busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, busy);
                busyEvent(EventConstantsEnum.Action.ADD.toString(), afterBusy, null);
            } else {
                toIds.add(busy.getId());
                afterBusy.setVehicleId(fromVehicleId);
                //busyEvent(EventConstantsEnum.Action.UPDATE.toString(), afterBusy, busy);
                busyEvent(EventConstantsEnum.Action.DELETE.toString(), null, busy);
                busyEvent(EventConstantsEnum.Action.ADD.toString(), afterBusy, null);
            }
        }
        if (CollectionUtils.isNotEmpty(fromIds)) {
            VehicleBusy record = new VehicleBusy();
            record.setVehicleId(toVehicleId);
            record.setOpTime(System.currentTimeMillis());
            VehicleBusyExample updExample = new VehicleBusyExample();
            updExample.createCriteria().andIdIn(fromIds);
            vehicleBusyMapper.updateByExampleSelective(record, updExample);
        }
        if (CollectionUtils.isNotEmpty(toIds)) {
            VehicleBusy record = new VehicleBusy();
            record.setVehicleId(fromVehicleId);
            record.setOpTime(System.currentTimeMillis());
            VehicleBusyExample updExample = new VehicleBusyExample();
            updExample.createCriteria().andIdIn(toIds);
            vehicleBusyMapper.updateByExampleSelective(record, updExample);
        }
        // 携程推送库存信息
        CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
        ctripStockTransactionParam.setMerchantId(param.getMerchantId());
        ctripStockTransactionParam.setVehicleIds(Arrays.asList(param.getFromVehicleId(), param.getToVehicleId()));
        eventPublisher.publishEvent(ctripStockTransactionParam);
        redisService.remove(rdkey);
        return ResultUtil.successResult(true);
    }

    /**
     * 库存占用视图
     *
     * @param query
     */
    @Override
    public Result<StockOccupyVO> stockOccupy(StockOccupyQuery query, Long merchantId, String loginName) {
        // 查询日期时间段占用数据
        query.setEndTime(query.getEndTime() + 24 * 60 * 60 * 1000 - 1);
        List<VehicleBusy> busyList =
                selectBusyModelIdsList(query.getStoreId(), query.getVehicleModelIds(), query.getVehicleIds(),
                        query.getStartTime(),
                        query.getEndTime());

        VehicleModelInnerQuery innerQuery = new VehicleModelInnerQuery();
        List<Long> modes = new ArrayList<>();
        if (CollectionUtils.isEmpty(query.getVehicleIds())) {
            // 查询所有车型
            Result<RentVehicleParam> relationModel = rentMainService.findRelationModelList(query.getStoreId(), null);
            modes = relationModel.getModel().getVehicleList().stream().map(RentVehicleVo::getVehicleModelId).distinct()
                    .collect(Collectors.toList());
        } else {
            for (Long vehicleId : query.getVehicleIds()) {
                Result<VehicleInfoVO> vehicleInfoVOResult = vehicleInfoService.getBaseById(vehicleId, true);
                if (BooleanUtils.isTrue(vehicleInfoVOResult.isSuccess()) && Objects.nonNull(vehicleInfoVOResult.getModel())) {
                    modes.add(vehicleInfoVOResult.getModel().getVehicleModelId());
                    modes = modes.stream().distinct().collect(Collectors.toList());
                }
            }
            // 车型 + 车辆 搜索时，取消
            if (CollectionUtils.isNotEmpty(query.getVehicleModelIds())) {
                modes.retainAll(query.getVehicleModelIds());
            }
        }
        if (CollectionUtils.isNotEmpty(query.getVehicleModelIds()) && CollectionUtils.isEmpty(query.getVehicleIds())) {
            modes.clear();
            modes.addAll(query.getVehicleModelIds());
        }

        innerQuery.setIdList(modes);
        Result<List<BaseVehicleModelVO>> listModelResult = vehicleModelService.listBaseVehicleModel(innerQuery);
        if (CollectionUtils.isEmpty(listModelResult.getModel())) {
            StockOccupyVO retVo = new StockOccupyVO();
            List<StockOccupyVO.ListVO> list = new ArrayList<>();
            retVo.setOverList(list);
            retVo.setOccupyList(list);
            return ResultUtil.successResult(retVo);
        }
        Map<Long, BaseVehicleModelVO> modelMap = listModelResult.getModel().stream()
                .collect(Collectors.toMap(BaseVehicleModelVO::getId, vo -> vo));

        // 过滤有冲突数据
        List<VehicleBusy> overBusyList = new ArrayList<>();
        List<Long> checkIds = new ArrayList<>();
        for (VehicleBusy busy : busyList) {
            List<VehicleBusy> tmpList =
                    busyList.stream()
                            .filter(e -> !checkIds.contains(e.getId()) && e.getVehicleId().equals(busy.getVehicleId()))
                            .collect(Collectors.toList());
            tmpList = getBusyListByDate(tmpList, busy.getStartTime(), busy.getEndIntervalTime());
            if (CollectionUtils.isNotEmpty(tmpList)) {
                if (tmpList.size() > 1) {
                    overBusyList.addAll(tmpList);
                }
            }
            checkIds.add(busy.getId());
        }
        overBusyList = overBusyList.stream().distinct().collect(Collectors.toList());

        // 调拨单信息
        List<Long> shuntingIds = busyList.stream()
                .filter(e -> VehicleBusyEnum.SHUNTING.getValueInt().equals(e.getSourceType()))
                .map(VehicleBusy::getSourceId).distinct().collect(Collectors.toList());
        Map<Long, ShuntingListVO> shuntingListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(shuntingIds)) {
            Result<List<ShuntingListVO>> shuntingResult = shuntingListService.shuntingList(shuntingIds);
            List<ShuntingListVO> shuntingList = shuntingResult.getModel();
            shuntingListMap = shuntingList.stream().collect(Collectors.toMap(ShuntingListVO::getId, vo -> vo));
        }

        // 订单信息
        List<Long> orderIds = busyList.stream()
                .filter(e ->
                    VehicleBusyEnum.ORDER.getValueInt().equals(e.getSourceType()) && e.getSourceId() != 0)
                .map(VehicleBusy::getSourceId).distinct().collect(Collectors.toList());
        // 查续租单的主单状态
        List<Long> sOrderIds = busyList.stream()
                .filter(
                    e -> VehicleBusyEnum.SUBORDER.getValueInt().equals(e.getSourceType())
                        && e.getSourceId() != 0)
                .map(VehicleBusy::getParentSourceId).distinct().collect(Collectors.toList());
        orderIds.addAll(sOrderIds);
        orderIds = orderIds.stream().distinct().filter(e -> e != null && e != 0L).collect(Collectors.toList());
        Map<Long, OrderMemberVo> orderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            Result<List<OrderMemberVo>> orderResult = orderMemberService.getUserInfoByOrderIdList(orderIds);
            List<OrderMemberVo> orderList = orderResult.getModel();
            if (CollectionUtils.isNotEmpty(orderList)) {
                orderMap = orderList.stream().collect(Collectors.toMap(OrderMemberVo::getOrderId, vo -> vo));
            }
        }

        List<String> sourceInnerOrderIds = busyList.stream()
                .filter(e ->
                    VehicleBusyEnum.ORDER.getValueInt().equals(e.getSourceType())
                        && StringUtils.isNotEmpty(e.getThirdSourceId())
                )
                .map(VehicleBusy::getThirdSourceId).distinct().collect(Collectors.toList());
        Map<String, OrderMemberVo> orderThirdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sourceInnerOrderIds)) {
            Result<List<OrderMemberVo>> orderResult = orderMemberService.getUserInfoBySourceInnerOrderIds(sourceInnerOrderIds, merchantId);
            List<OrderMemberVo> orderList = orderResult.getModel();
            if (CollectionUtils.isNotEmpty(orderList)) {
                orderThirdMap = orderList.stream().collect(Collectors.toMap(OrderMemberVo::getSourceInnerOrderId,
                        Function.identity(), (existingValue, newValue) -> newValue));
            }
        }

        // 子订单信息
        List<Long> subOrderIds = busyList.stream()
                .filter(
                    e -> VehicleBusyEnum.SUBORDER.getValueInt().equals(e.getSourceType())
                        && e.getSourceId() != 0)
                .map(VehicleBusy::getSourceId).distinct().collect(Collectors.toList());
        Map<Long, OrderMemberVo> subOrderMap = new HashMap<>();
        Map<Long, PickReturnTimeTileDTO> pickReturnTimeTileMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(subOrderIds)) {
            Result<List<OrderMemberVo>> orderResult = orderMemberService.getUserInfoBySubOrderIdList(subOrderIds);
            List<OrderMemberVo> orderList = orderResult.getModel();
            if (CollectionUtils.isNotEmpty(orderList)) {
                subOrderMap = orderList.stream().collect(Collectors.toMap(OrderMemberVo::getOrderId, vo -> vo));
            }
        }
        if (CollectionUtils.isNotEmpty(orderIds)) {
            Result<List<PickReturnTimeTileDTO>> pickReturnResult = iVehiclePickReturnService.listPickReturnByOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(pickReturnResult.getModel())) {
                pickReturnTimeTileMap = pickReturnResult.getModel().stream()
                        .collect(Collectors.toMap(PickReturnTimeTileDTO::getOrderId, Function.identity(), (k1, k2) -> k1));
            }
        }

        sourceInnerOrderIds = busyList.stream()
            .filter(e -> VehicleBusyEnum.SUBORDER.getValueInt().equals(e.getSourceType())
                    && StringUtils.isNotEmpty(e.getThirdSourceId())
                )
                .map(VehicleBusy::getThirdSourceId).distinct().collect(Collectors.toList());
        Map<String, OrderMemberVo> subOrderThirdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sourceInnerOrderIds)) {
            Result<List<OrderMemberVo>> orderResult = orderMemberService.getUserInfoBySubSourceInnerOrderIds(sourceInnerOrderIds, merchantId);
            List<OrderMemberVo> orderList = orderResult.getModel();
            if (CollectionUtils.isNotEmpty(orderList)) {
                subOrderThirdMap = orderList.stream().collect(Collectors.toMap(OrderMemberVo::getSourceInnerOrderId, vo -> vo));
            }
        }

        // 获取所有车型下的车辆
        List<VehicleSampleVO> vehicles = new ArrayList<>();
        List<Long> storeIdsParam = new ArrayList<>();
        storeIdsParam.add(query.getStoreId());
        List<VehicleSourceGroupVO> vehicleSourceGroupVOList = vehicleInfoService.findAuthVehicleIds(loginName, merchantId, storeIdsParam);
        Map<Long, String> authVehicleIdMap;
        if (vehicleSourceGroupVOList != null) {
            authVehicleIdMap = vehicleSourceGroupVOList.stream().collect(Collectors.toMap(key -> key.getVehicleId(), value -> ""));
        } else {
            authVehicleIdMap = new HashMap<>();
        }
        if (CollectionUtils.isEmpty(query.getVehicleModelIds())) {
            List<VehicleSampleVO> vehiclesResult =
                    vehicleInfoService.vehiclesDeletedByModelIds(query.getStoreId(), modes, true);
            if (CollectionUtils.isNotEmpty(vehiclesResult)) {
                vehicles.addAll(vehiclesResult);
            }
            if (vehicleSourceGroupVOList != null) {
                vehicles = vehicles.stream().filter(e -> authVehicleIdMap.containsKey(e.getId()))
                        .collect(Collectors.toList());
            }
        } else {
            List<VehicleSampleVO> vehiclesResult =
                    vehicleInfoService.vehiclesDeletedByModelIds(query.getStoreId(), query.getVehicleModelIds(), true);
            if (CollectionUtils.isEmpty(query.getVehicleIds())) {
                if (vehicleSourceGroupVOList == null) {
                    vehicles.addAll(vehiclesResult);
                } else {
                    vehicles.addAll(
                            vehiclesResult.stream().filter(e -> authVehicleIdMap.containsKey(e.getId()))
                                    .collect(Collectors.toList()));
                }
            } else {
                if (vehicleSourceGroupVOList == null) {
                    vehicles.addAll(
                            vehiclesResult.stream().filter(e -> query.getVehicleIds().contains(e.getId()))
                                    .collect(Collectors.toList()));
                } else {
                    vehicles.addAll(
                            vehiclesResult.stream().filter(e -> query.getVehicleIds().contains(e.getId())
                                            && authVehicleIdMap.containsKey(e.getId()))
                                    .collect(Collectors.toList()));
                }
            }
        }

        // 查询渠道名称
        Result<List<ChannelVO>> channelResult = channelService.listAllChannel(YesOrNoEnum.YES.getValue());
        List<ChannelVO> channelList = channelResult.getModel();
        Result<List<ApiConnVo>> apiConnResult = apiConnService.listChannel(merchantId);
        for (ChannelVO vo : channelList) {
            long cnt = apiConnResult.getModel().stream().filter(
                    e -> e.getChannelId().equals(vo.getId()) && e.getConnType() == 2).count();
            if (cnt > 0) {
                vo.setChannelName(vo.getChannelName() + "同步");
            }
        }
        Map<Long, String> channelMap =
                channelList.stream().collect(Collectors.toMap(ChannelVO::getId, channelVO -> channelVO.getChannelName()));

        // 数据组装返回
        StockOccupyVO retVo = new StockOccupyVO();
        List<StockOccupyVO.ListVO> overList =
            setOverDate(overBusyList, vehicles, channelMap, orderMap, shuntingListMap, modelMap, subOrderMap,
                orderThirdMap, subOrderThirdMap, pickReturnTimeTileMap)
                .stream().filter(o -> Objects.nonNull(o.getVehicleModelId()))
                .sorted(Comparator.comparing(StockOccupyVO.ListVO::getVehicleModelId).thenComparing(StockOccupyVO.ListVO::getVehicleId))
                .collect(Collectors.toList());
        retVo.setOverList(overList);
        List<StockOccupyVO.ListVO> occupyList = setOccupyDate(busyList, vehicles, channelMap, orderMap, shuntingListMap,
            modelMap, subOrderMap, orderThirdMap, subOrderThirdMap, pickReturnTimeTileMap, merchantId)
                    .stream().filter(o -> Objects.nonNull(o.getVehicleModelId()))
                    .collect(Collectors.toList());;
        occupyList.sort(Comparator.comparing(StockOccupyVO.ListVO::getVehicleModelId)
                .thenComparing(StockOccupyVO.ListVO::getVehicleId));
        retVo.setOccupyList(occupyList);
        // 返回门店信息
        retVo.setStoreId(query.getStoreId());
        Result<StoreInfoVo> storeInfoVoResult = storeInfoService.storeInfoBaseFind(query.getStoreId());
        if (storeInfoVoResult.isSuccess() && storeInfoVoResult.getModel() != null) {
            retVo.setStoreName(storeInfoVoResult.getModel().getStoreName());
        }
        return ResultUtil.successResult(retVo);
    }

    /**
     * 库存冲突For悟空
     *
     * @param query
     */
    @Override
    public Result<StockOccupyVO> stockOverForWk(StockOccupyQuery query, Long merchantId) {
        // 查询日期时间段占用数据
        query.setEndTime(query.getEndTime() + 24 * 60 * 60 * 1000 - 1);
        List<VehicleBusy> busyList =
                selectBusyModelList(query.getStoreId(), query.getVehicleModelId(), query.getVehicleIds(),
                        query.getStartTime(),
                        query.getEndTime());

        // 查询所有车型
        Result<RentVehicleParam> relationModel = rentMainService.findRelationModelList(query.getStoreId(), null);
        List<Long> modes = relationModel.getModel().getVehicleList().stream().map(RentVehicleVo::getVehicleModelId).distinct()
                .collect(Collectors.toList());

        // 过滤有冲突数据
        List<VehicleBusy> overBusyList = new ArrayList<>();
        List<Long> checkIds = new ArrayList<>();
        for (VehicleBusy busy : busyList) {
            List<VehicleBusy> tmpList =
                    busyList.stream()
                            .filter(e -> !checkIds.contains(e.getId()) && e.getVehicleId().equals(busy.getVehicleId()))
                            .collect(Collectors.toList());
            tmpList = getBusyListByDate(tmpList, busy.getStartTime(), busy.getEndIntervalTime());
            if (CollectionUtils.isNotEmpty(tmpList)) {
                if (tmpList.size() > 1) {
                    overBusyList.addAll(tmpList);
                }
            }
            checkIds.add(busy.getId());
        }
        overBusyList = overBusyList.stream().distinct().collect(Collectors.toList());

        // 获取所有车型下的车辆
        List<VehicleSampleVO> vehicles = new ArrayList<>();
        for (Long modelId : modes) {
            Result<List<VehicleSampleVO>> vehiclesResult =
                    vehicleInfoService.findVehiclesByModelId(query.getStoreId(), modelId);
            vehicles.addAll(vehiclesResult.getModel());
        }

        // 数据组装返回
        StockOccupyVO retVo = new StockOccupyVO();
        List<StockOccupyVO.ListVO> retList = new ArrayList<>();
        List<Long> vehicleIds =
                overBusyList.stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
        for (Long vehicle : vehicleIds) {
            StockOccupyVO.ListVO rowVo = new StockOccupyVO.ListVO();
            List<VehicleSampleVO> sampleVehicle =
                    vehicles.stream().filter(e -> e.getId().equals(vehicle)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sampleVehicle)) {
                VehicleSampleVO vo = sampleVehicle.get(0);
                rowVo.setLicense(vo.getLicense());
                rowVo.setVehicleId(vo.getId());
                rowVo.setVehicleModelId(vo.getVehicleModelId());
                rowVo.setStatus(vo.getVehicleStatus());
                List<VehicleBusy> tmpList =
                        overBusyList.stream().filter(e -> e.getVehicleId().equals(vehicle)).collect(Collectors.toList());
                List<StockOccupyVO.DetailVO> detailList = new ArrayList<>();
                for (VehicleBusy over : tmpList) {
                    StockOccupyVO.DetailVO detailVO = new StockOccupyVO.DetailVO();
                    BeanUtils.copyProperties(over, detailVO);
                    detailVO.setStartTime(new Date(over.getStartTime()));
                    detailVO.setEndTime(new Date(over.getEndTime()));
                    detailVO.setEndIntervalTime(new Date(over.getEndIntervalTime()));
                    long intervalHour = (over.getEndIntervalTime() - over.getEndTime() + 1) / (60 * 60 * 1000);
                    detailVO.setIntervalHour((int) intervalHour);
                    detailVO.setCanRelease(false);
                    detailVO.setChannelId(over.getChannelId());
                    detailList.add(detailVO);
                }
                rowVo.setDetailList(detailList);
            }
            retList.add(rowVo);
        }
        retVo.setOccupyList(retList);
        return ResultUtil.successResult(retVo);
    }

    @Override
    public Result<Boolean> stockDelete(Long id, Long merchantId, String busyDesc) {
        VehicleBusy busy = vehicleBusyMapper.selectByPrimaryKey(id);
        if (busy == null) {
            throw new BizException("数据不存在");
        } else if (!busy.getMerchantId().equals(merchantId)) {
            throw new BizException("无权删除");
        }
        cancelPlatLocks(merchantId, Arrays.asList(id), busyDesc);
        return ResultUtil.successResult(true);
    }

    @Override
    public List<VehicleBusyEntityVO> selectBusyModelList(Long storeId, Long vehicleModelId, Long startTime, Long endTime) {
        List<VehicleBusy> list = selectBusyModelList(storeId, vehicleModelId, null, startTime, endTime);
        List<VehicleBusyEntityVO> retList = new ArrayList<>();
        for (VehicleBusy busy : list) {
            VehicleBusyEntityVO vo = new VehicleBusyEntityVO();
            BeanUtils.copyProperties(busy, vo);
            retList.add(vo);
        }
        return retList;
    }

    /**
     * 车型占用数据查询
     *
     * @param storeId
     * @param vehicleModelId
     * @param vehicleIds
     * @param startTime
     * @param endTime
     * @return
     */
    private List<VehicleBusy> selectBusyModelListOld(Long storeId, Long vehicleModelId, List<Long> vehicleIds,
                                                     Long startTime, Long endTime) {
        endTime = endTime - 1;
        VehicleBusyExample example = new VehicleBusyExample();
        if (vehicleModelId != null) {
            VehicleBusyExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(storeId).andVehicleModelIdEqualTo(vehicleModelId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andStartTimeBetween(startTime, endTime);
            if (CollectionUtils.isNotEmpty(vehicleIds)) {
                criteria.andVehicleIdIn(vehicleIds);
            }
            VehicleBusyExample.Criteria criteriaOr = example.or();
            criteriaOr.andStoreIdEqualTo(storeId).andVehicleModelIdEqualTo(vehicleModelId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andEndIntervalTimeBetween(startTime, endTime);
            if (CollectionUtils.isNotEmpty(vehicleIds)) {
                criteriaOr.andVehicleIdIn(vehicleIds);
            }
            VehicleBusyExample.Criteria criteriaOr1 = example.or();
            criteriaOr1.andStoreIdEqualTo(storeId).andVehicleModelIdEqualTo(vehicleModelId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andStartTimeLessThan(startTime).andEndIntervalTimeGreaterThan(endTime);
            if (CollectionUtils.isNotEmpty(vehicleIds)) {
                criteriaOr1.andVehicleIdIn(vehicleIds);
            }
        } else {
            VehicleBusyExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(storeId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andStartTimeBetween(startTime, endTime);
            VehicleBusyExample.Criteria criteriaOr = example.or();
            criteriaOr.andStoreIdEqualTo(storeId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andEndIntervalTimeBetween(startTime, endTime);
            VehicleBusyExample.Criteria criteriaOr1 = example.or();
            criteriaOr1.andStoreIdEqualTo(storeId)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andStartTimeLessThan(startTime).andEndIntervalTimeGreaterThan(endTime);
        }
        example.setOrderByClause("vehicle_model_id asc");
        return vehicleBusyMapper.selectByExample(example);
    }


    private List<VehicleBusy> selectBusyModelIdsList(Long storeId, List<Long> vehicleModelIds, List<Long> vehicleIds,
                                                     Long startTime, Long endTime) {
        endTime = endTime - 1;
        VehicleBusyExample example = new VehicleBusyExample();

        VehicleBusyExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andEndIntervalTimeGreaterThanOrEqualTo(startTime)
                .andStartTimeLessThanOrEqualTo(endTime);
        if (CollectionUtils.isNotEmpty(vehicleModelIds)) {
            criteria.andVehicleModelIdIn(vehicleModelIds);
        }
        if (CollectionUtils.isNotEmpty(vehicleIds)) {
            criteria.andVehicleIdIn(vehicleIds);
        }
        // example.setOrderByClause("vehicle_model_id asc");
        return vehicleBusyMapper.selectByExample(example);
    }

    private List<VehicleBusy> selectBusyModelList(Long storeId, Long vehicleModelId, List<Long> vehicleIds,
                                                  Long startTime, Long endTime) {
        endTime = endTime - 1;
        VehicleBusyExample example = new VehicleBusyExample();

        VehicleBusyExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andEndIntervalTimeGreaterThanOrEqualTo(startTime)
                .andStartTimeLessThanOrEqualTo(endTime);
        if (vehicleModelId != null) {
            criteria.andVehicleModelIdEqualTo(vehicleModelId);
        }
        if (CollectionUtils.isNotEmpty(vehicleIds)) {
            criteria.andVehicleIdIn(vehicleIds);
        }
        // example.setOrderByClause("vehicle_model_id asc");
        return vehicleBusyMapper.selectByExample(example);
    }

    /**
     * 车辆占用数据查询
     *
     * @param vehicleId
     * @param startTime
     * @param endTime
     * @return
     */
    private List<VehicleBusy> selectBusyVehicleList(Long storeId, Long vehicleId, Long startTime, Long endTime) {
        endTime = endTime - 1;
        VehicleBusyExample example = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId).andVehicleIdEqualTo(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStartTimeBetween(startTime, endTime);
        VehicleBusyExample.Criteria criteriaOr = example.or();
        criteriaOr.andStoreIdEqualTo(storeId).andVehicleIdEqualTo(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andEndIntervalTimeBetween(startTime, endTime);
        VehicleBusyExample.Criteria criteriaOr1 = example.or();
        criteriaOr1.andStoreIdEqualTo(storeId).andVehicleIdEqualTo(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStartTimeLessThan(startTime).andEndIntervalTimeGreaterThan(endTime);
        return vehicleBusyMapper.selectByExample(example);
    }

    private List<VehicleBusy> selectBusyVehicleList(Long storeId, List<Long> vehicleId, Long startTime, Long endTime) {
        endTime = endTime - 1;
        VehicleBusyExample example = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId).andVehicleIdIn(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStartTimeBetween(startTime, endTime);
        VehicleBusyExample.Criteria criteriaOr = example.or();
        criteriaOr.andStoreIdEqualTo(storeId).andVehicleIdIn(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andEndIntervalTimeBetween(startTime, endTime);
        VehicleBusyExample.Criteria criteriaOr1 = example.or();
        criteriaOr1.andStoreIdEqualTo(storeId).andVehicleIdIn(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStartTimeLessThan(startTime).andEndIntervalTimeGreaterThan(endTime);
        return vehicleBusyMapper.selectByExample(example);
    }

    /**
     * 车辆占用数据查询
     *
     * @param merchantId
     * @param startTime
     * @param endTime
     * @return
     */
    private List<VehicleBusy> selectBusyMerchantList(Long merchantId, Long storeId, List<Long> vehicleIds, Long channelId, Long startTime, Long endTime) {
        VehicleBusyExample example = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = example.createCriteria();
        //占车单开始时间<=查询结束时间,占车单结束时间>=查询开始时间
        criteria.andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStartTimeLessThanOrEqualTo(endTime).andEndIntervalTimeGreaterThanOrEqualTo(startTime);
        if (null != storeId) {
            criteria.andStoreIdEqualTo(storeId);
        }
        if (null != channelId) {
            criteria.andChannelIdEqualTo(channelId);
        }
        if (null != vehicleIds && !vehicleIds.isEmpty()) {
            criteria.andVehicleIdIn(vehicleIds);
        }
        return vehicleBusyMapper.selectByExample(example);
    }

    /**
     * 车辆占用数据查询(for order)
     *
     * @param vehicleId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Result<List<StockVehicleBusyVO>> selectBusyVehicleListForOrder(Long merchantId, Long storeId, Long returnStoreId, Long vehicleModelId, Long vehicleId,
                                                                          Long orderId, String thirdOrderNo, Long channelId, Long startTime, Long endTime, int thirdSourceType) {

        List<StockVehicleBusyVO> retList = new ArrayList<>();
        boolean openMerchant = openMerchantComponent.isOpenMerchant(merchantId);
        boolean openHelloMerchant = openMerchantComponent.isOpenHelloMerchant(merchantId);
        boolean openCheckSaasStock = openMerchantComponent.isOpenCheckSaasStock(merchantId);
        // 获取上汽商家id，和上汽逻辑相反，如果有车辆id，则返回list为0，没有车辆则返回list为1
        if (openMerchant || (openHelloMerchant && !openCheckSaasStock)) {
            try {
                VehicleBusyParam param = new VehicleBusyParam();
                param.setMerchantId(merchantId);
                param.setStoreId(storeId);
                param.setChannelId(channelId);
                param.setReturnStoreId(returnStoreId);
                param.setVehicleModelId(vehicleModelId);
                param.setVehicleId(vehicleId);
                param.setStartTime(startTime);
                param.setEndTime(endTime);
                param.setSourceId(-1L);
                param.setParentSourceId(orderId);
                param.setThirdSourceId(thirdOrderNo);
                //param.setSourceType(sourceType);
                StockFeeResponse stockFeeResponse = this.handleStock(thirdSourceType, param, !openHelloMerchant);
                if (Objects.isNull(stockFeeResponse) || CollectionUtils.isEmpty(stockFeeResponse.getSaasVehicleIds())) {
                    StockVehicleBusyVO stockVehicleBusyVO = new StockVehicleBusyVO();
                    stockVehicleBusyVO.setSourceType(channelId.intValue());
                    stockVehicleBusyVO.setParentSourceId(0L);
                    retList.add(stockVehicleBusyVO);
                    return ResultUtil.successResult(retList);
                }
                return ResultUtil.successResult(retList);
            } catch (Exception e) {
                log.info("直连商家,库存检查,调用接口,系统异常:{}", e.getMessage());
                StockVehicleBusyVO stockVehicleBusyVO = new StockVehicleBusyVO();
                stockVehicleBusyVO.setSourceType(channelId.intValue());
                stockVehicleBusyVO.setParentSourceId(0L);
                retList.add(stockVehicleBusyVO);
                return ResultUtil.successResult(retList); //验证ok后，像改为 false
            }
        } else {
            int orderInterval;
            if (storeId != null && !storeId.equals(returnStoreId)) {
                // 获取异门店规则
                AllopatryRuleDTO allopatryRuleDTO = allopatryRuleService
                        .getAllopatryRule(merchantId, channelId, storeId,
                                returnStoreId, vehicleModelId, new Date(endTime));
                // 直连商家该字段数据存在-1，非业务有效数据
                orderInterval = (Objects.nonNull(allopatryRuleDTO) && Objects.nonNull(allopatryRuleDTO.getDuration())
                        && allopatryRuleDTO.getDuration() >= 0)
                        ? allopatryRuleDTO.getDuration() * 3600 * 1000 : 0;
                if (Objects.nonNull(allopatryRuleDTO)) {
                    log.info("异门店规则 merchantId:{}  allopatryRuleDTO:{} ", merchantId, JSON.toJSONString(allopatryRuleDTO));
                } else {
                    log.info("异门店规则 merchantId:{}  allopatryRuleDTO is null ", merchantId);
                }
                // 未匹配到异门店规则
                if (allopatryRuleDTO == null || allopatryRuleDTO.getDuration() < 0) {
                    orderInterval = storeInfoService.getOrderInterval(storeId, channelId, vehicleModelId);
                }
            } else {
                // 同门店规则
                orderInterval = storeInfoService.getOrderInterval(storeId, channelId, vehicleModelId);
            }
            endTime = endTime + ((long) orderInterval  - 1);
            List<VehicleBusy> list = selectBusyVehicleList(storeId, vehicleId, startTime, endTime);
            for (VehicleBusy busy : list) {
                StockVehicleBusyVO vo = new StockVehicleBusyVO();
                BeanUtils.copyProperties(busy, vo);
                vo.setSourceTypName(VehicleBusyEnum.getByName(busy.getSourceType()));
                retList.add(vo);
            }
        }

        return ResultUtil.successResult(retList);
    }

    @Override
    public Result<List<StockVehicleBusyVO>> selectBusyVehicleByIdsForOrder(Long storeId, List<Long> vehicleIds, Long startTime, Long endTime) {
        List<VehicleBusy> list = selectBusyVehicleList(storeId, vehicleIds, startTime, endTime);
        List<StockVehicleBusyVO> retList = new ArrayList<>();
        for (VehicleBusy vehicleBusy : list) {
            if (vehicleIds.contains(vehicleBusy.getVehicleId())) {
                StockVehicleBusyVO vo = new StockVehicleBusyVO();
                BeanUtils.copyProperties(vehicleBusy, vo);
                vo.setSourceTypName(VehicleBusyEnum.getByName(vehicleBusy.getSourceType()));
                retList.add(vo);
            }
        }


//        for (Long vehicleId : vehicleIds) {
//            long cnt = list.stream().filter(e -> e.getVehicleId().equals(vehicleId)).count();
//            if (cnt > 0) {
//                retList.add(vehicleId);
//            }
//        }
        return ResultUtil.successResult(retList);
    }

    /**
     * 占用冲突数据
     *
     * @param overBusyList
     * @return
     */
    private List<StockOccupyVO.ListVO> setOverDate(List<VehicleBusy> overBusyList, List<VehicleSampleVO> vehicles,
                                                   Map<Long, String> channelMap, Map<Long, OrderMemberVo> orderMap,
                                                   Map<Long, ShuntingListVO> shuntingListMap,
                                                   Map<Long, BaseVehicleModelVO> modelMap, Map<Long, OrderMemberVo> subOrderMap,
                                                   Map<String, OrderMemberVo> orderThirdMap,
                                                   Map<String, OrderMemberVo> subOrderThirdMap,
                                                   Map<Long, PickReturnTimeTileDTO> pickReturnTimeTileMap) {
        List<StockOccupyVO.ListVO> retList = new ArrayList<>();
        List<Long> vehicleIds =
                overBusyList.stream().map(VehicleBusy::getVehicleId).distinct().collect(Collectors.toList());
        for (Long vehicle : vehicleIds) {
            StockOccupyVO.ListVO rowVo = new StockOccupyVO.ListVO();
            List<VehicleSampleVO> sampleVehicle =
                    vehicles.stream().filter(e -> e.getId().equals(vehicle)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sampleVehicle)) {
                VehicleSampleVO vo = sampleVehicle.get(0);
                rowVo.setLicense(vo.getLicense());
                rowVo.setVehicleId(vo.getId());
                rowVo.setVehicleModelId(vo.getVehicleModelId());
                rowVo.setStatus(vo.getVehicleStatus());
                rowVo.setVehicleSelfServiceReturn(vo.getSelfServiceReturn());
                rowVo.setSaleStatus(vo.getSaleStatus());
                BaseVehicleModelVO modelVo = modelMap.get(vo.getVehicleModelId());
                if (modelVo != null) {
                    rowVo.setVehicleModelName(modelVo.getVehicleUnionName());
                    rowVo.setVehicleModelSelfServiceReturn(modelVo.getSelfServiceReturn());
                }
            }
            // 设置订单渠道调拨等信息
            List<VehicleBusy> tmpList =
                    overBusyList.stream().filter(e -> e.getVehicleId().equals(vehicle)).collect(Collectors.toList());
            List<StockOccupyVO.DetailVO> detailList =
                    setShowInfo(tmpList, channelMap, orderMap, shuntingListMap, subOrderMap, orderThirdMap, subOrderThirdMap, pickReturnTimeTileMap);
            rowVo.setDetailList(detailList);
            retList.add(rowVo);
        }
        return retList;
    }

    /**
     * 占用数据
     *
     * @param busyList
     * @param vehicles
     * @return
     */
    private List<StockOccupyVO.ListVO> setOccupyDate(List<VehicleBusy> busyList, List<VehicleSampleVO> vehicles,
                                                     Map<Long, String> channelMap, Map<Long, OrderMemberVo> orderMap,
                                                     Map<Long, ShuntingListVO> shuntingListMap,
                                                     Map<Long, BaseVehicleModelVO> modelMap, Map<Long, OrderMemberVo> subOrderMap,
                                                     Map<String, OrderMemberVo> orderThirdMap,
                                                     Map<String, OrderMemberVo> subOrderThirdMap,
                                                     Map<Long, PickReturnTimeTileDTO> pickReturnTimeTileMap, Long merchantId) {
        List<StockOccupyVO.ListVO> retList = new ArrayList<>();
        List<Long> vehicleIds = vehicles.stream().map(VehicleSampleVO::getId).distinct().collect(Collectors.toList());

        // 查询前台车辆展示标签
        Result<List<VehicleInfoTagV2VO>> vehicleInfoTagResult = vehicleTagService.listInfoTagByVehicleIds(vehicleIds, merchantId);
        Map<Long, List<String>> vehicleTagMap = Optional.ofNullable(vehicleInfoTagResult.getModel()).map(List::stream)
                .map(stream -> stream.collect(Collectors.toMap(VehicleInfoTagV2VO::getVehicleId, VehicleInfoTagV2VO::getVehicleTagNames)))
                .orElseGet(Collections::emptyMap);

        for (VehicleSampleVO vehicle : vehicles) {
            StockOccupyVO.ListVO rowVo = new StockOccupyVO.ListVO();
            rowVo.setLicense(vehicle.getLicense());
            rowVo.setVehicleId(vehicle.getId());
            rowVo.setVehicleModelId(vehicle.getVehicleModelId());
            rowVo.setStatus(vehicle.getVehicleStatus());
            rowVo.setVehicleSource(vehicle.getVehicleSource());
            rowVo.setVehicleSelfServiceReturn(vehicle.getSelfServiceReturn());
            rowVo.setVehicleInfoTags(vehicleTagMap.get(vehicle.getId()));
            rowVo.setSaleStatus(vehicle.getSaleStatus());
            BaseVehicleModelVO modelVo = modelMap.get(vehicle.getVehicleModelId());
            if (modelVo != null) {
                rowVo.setVehicleModelName(modelVo.getVehicleUnionName());
                rowVo.setVehicleModelSelfServiceReturn(modelVo.getSelfServiceReturn());
            }
            // 设置订单渠道调拨等信息
            List<VehicleBusy> tmpList =
                    busyList.stream().filter(e -> e.getVehicleId().equals(vehicle.getId())).collect(Collectors.toList());

            List<StockOccupyVO.DetailVO> detailList = new ArrayList<>(
                this.setShowInfo(tmpList, channelMap, orderMap, shuntingListMap, subOrderMap, orderThirdMap,
                    subOrderThirdMap, pickReturnTimeTileMap));
            if (VehicleInfoEnums.SaleStatusEnum.isSoldOut(vehicle.getSaleStatus())) {
                detailList.add(this.buildSoldOutBusy());
            }
            rowVo.setDetailList(detailList);
            retList.add(rowVo);
        }
        return retList;
    }

    private StockOccupyVO.DetailVO buildSoldOutBusy() {
        StockOccupyVO.DetailVO vo = new StockOccupyVO.DetailVO();
        vo.setAutoSchedule(YesOrNoEnum.NO.getValue());
        vo.setCanRelease(false);
        vo.setChannelId(0L);
        vo.setStartTime(new Date(0));
        vo.setEndTime(MAX_END_TIME);
        vo.setEndIntervalTime(MAX_END_TIME);
        vo.setSourceType(VehicleBusyEnum.SOLD_OUT_TYPE);
        return vo;
    }

    private List<StockOccupyVO.DetailVO> setShowInfo(List<VehicleBusy> busyList, Map<Long, String> channelMap,
                                                     Map<Long, OrderMemberVo> orderMap,
                                                     Map<Long, ShuntingListVO> shuntingListMap,
                                                     Map<Long, OrderMemberVo> subOrderMap,
                                                     Map<String, OrderMemberVo> orderThirdMap,
                                                     Map<String, OrderMemberVo> subOrderThirdMap,
                                                     Map<Long, PickReturnTimeTileDTO> pickReturnTimeTileMap) {
        List<StockOccupyVO.DetailVO> detailList = new ArrayList<>();
        for (VehicleBusy over : busyList) {
            StockOccupyVO.DetailVO detailVO = new StockOccupyVO.DetailVO();
            BeanUtils.copyProperties(over, detailVO);
            detailVO.setStartTime(new Date(over.getStartTime()));
            detailVO.setEndTime(new Date(over.getEndTime()));
            detailVO.setEndIntervalTime(new Date(over.getEndIntervalTime()));
            long intervalHour = (over.getEndIntervalTime() - over.getEndTime() + 1) / (60 * 60 * 1000);
            detailVO.setIntervalHour((int) intervalHour);
            detailVO.setCanRelease(false);
            detailVO.setChannelId(over.getChannelId());
            // 订单占用
            if (VehicleBusyEnum.ORDER.getValueInt().equals(over.getSourceType())) {
                StockOccupyVO.OrderDetailVO orderDetailVO = null;
                OrderMemberVo orderUserVo = null;
                if (over.getSourceId() != 0) {
                    orderUserVo = orderMap.get(over.getSourceId());
                } else if (StringUtils.isNotEmpty(over.getThirdSourceId())) {
                    orderUserVo = orderThirdMap.get(over.getThirdSourceId());
                }
                if (orderUserVo != null) {
                    orderDetailVO = new StockOccupyVO.OrderDetailVO();
                    orderDetailVO.setChannelId((long) orderUserVo.getOrderSource());
                    orderDetailVO.setChannelName(channelMap.get(orderDetailVO.getChannelId()));
                    orderDetailVO.setUserMobile(orderUserVo.getMobile());
                    orderDetailVO.setUserName(orderUserVo.getUserName());
                    orderDetailVO.setStatus(orderUserVo.getOrderStatus());
                    orderDetailVO.setCreateTime(orderUserVo.getCreateTime());
                    orderDetailVO.setThirdOutOrderNo(orderUserVo.getSourceOrderId());
                    orderDetailVO.setOrderId(orderUserVo.getOrderId());
                    orderDetailVO.setCanUpdatePlan(orderUserVo.getCanUpdatePlan());
                }
                detailVO.setOrderDetail(orderDetailVO);
            }

            if (VehicleBusyEnum.SUBORDER.getValueInt().equals(over.getSourceType())) {
                StockOccupyVO.OrderDetailVO orderDetailVO = null;
                OrderMemberVo orderUserVo = null;
                if (over.getSourceId() != 0) {
                    orderUserVo = subOrderMap.get(over.getSourceId());
                } else if (StringUtils.isNotEmpty(over.getThirdSourceId())) {
                    orderUserVo = subOrderThirdMap.get(over.getThirdSourceId());
                }
                if (orderUserVo != null) {
                    orderDetailVO = new StockOccupyVO.OrderDetailVO();
                    orderDetailVO.setChannelId((long) orderUserVo.getOrderSource());
                    orderDetailVO.setChannelName(channelMap.get(orderDetailVO.getChannelId()));
                    orderDetailVO.setUserMobile(orderUserVo.getMobile());
                    orderDetailVO.setUserName(orderUserVo.getUserName());
                    orderDetailVO.setStatus(orderUserVo.getOrderStatus());
                    orderDetailVO.setCreateTime(orderUserVo.getCreateTime());
                    orderDetailVO.setThirdOutOrderNo(orderUserVo.getSourceOrderId());
                    orderDetailVO.setOrderId(orderUserVo.getOrderId());
                    orderDetailVO.setCanUpdatePlan(orderUserVo.getCanUpdatePlan());
                    if (over.getParentSourceId() != 0) {
                        // 续租单状态与主单一致
                        orderUserVo = orderMap.get(over.getParentSourceId());
                        if (orderUserVo != null) {
                            orderDetailVO.setStatus(orderUserVo.getOrderStatus());
                        }
                    }
                }
                detailVO.setOrderDetail(orderDetailVO);
            }
            if (VehicleBusyEnum.SHUNTING.getValueInt().equals(over.getSourceType())) {
                StockOccupyVO.ShuntingDetailVO shuntingDetailVO = new StockOccupyVO.ShuntingDetailVO();
                ShuntingListVO shuntingVo = shuntingListMap.get(over.getSourceId());
                if (shuntingVo != null) {
                    shuntingDetailVO.setInStoreName(shuntingVo.getTransferInStoreName());
                    shuntingDetailVO.setOutStoreName(shuntingVo.getTransferOutStoreName());
                    shuntingDetailVO.setRemark(shuntingVo.getRemark());
                    shuntingDetailVO.setStatus(shuntingVo.getShuntingStatus());
                }
                detailVO.setShuntingDetail(shuntingDetailVO);
            }
            if (null == detailVO.getOrderDetail()) {
                StockOccupyVO.OrderDetailVO orderDetailVO = new StockOccupyVO.OrderDetailVO();
                orderDetailVO.setChannelId(over.getChannelId());
                orderDetailVO.setChannelName(channelMap.get(over.getChannelId()));
                detailVO.setOrderDetail(orderDetailVO);
            }


            if (VehicleBusyEnum.isOrderBusy(over.getSourceType())) {
                if (over.getEndIntervalTime() > over.getEndTime()) {
                    // 已还车，非rpa的 主单 或 续租 可以释放库存
                    OrderMemberVo orderMemberVo = orderMap.get(detailVO.getParentSourceId());
                    if (orderMemberVo != null
                            && Objects.equals(orderMemberVo.getOrderStatus(), OrderStatusEnum.RETURNED.getStatus())
                            && !(orderMemberVo.getOrderExtraVo() != null && Integer.valueOf(1).equals(orderMemberVo.getOrderExtraVo().getIsReptile()))) {
                        detailVO.setCanRelease(true);
                    }
                }
                if (MapUtils.isNotEmpty(pickReturnTimeTileMap)) {
                    Long orderId = over.getSourceId();
                    if (VehicleBusyEnum.SUBORDER.getValueInt().equals(over.getSourceType())) {
                        orderId = over.getParentSourceId();
                    }
                    Long l = Optional.ofNullable(pickReturnTimeTileMap.get(orderId))
                            .map(PickReturnTimeTileDTO::getPickUpdate).orElse(null);
                    if (Objects.nonNull(l)) {
                        detailVO.setPickUpTime(new Date(l));
                    }
                }
            }
            detailVO.setExtObj(getBusyExtFiled(over.getExt()));
            if (detailVO.getExtObj() != null && detailVO.getExtObj().getStockMerchantShareFrom() != null) {
                detailVO.setAuthFlg(false);
            }
            detailList.add(detailVO);
        }
        return detailList;
    }

    private List<VehicleBusy> getBusyListByDate(List<VehicleBusy> busyList, Long startDate, Long endDate) {
//        return busyList.stream().filter(e ->
//            (e.getEndTime() > startDate && e.getEndTime() <= endDate) ||
//                (e.getStartTime() >= startDate && e.getStartTime() < endDate) ||
//                (startDate > e.getStartTime() && e.getEndTime() > endDate)
//        ).collect(Collectors.toList());
        return busyList.stream().filter(e ->
                (startDate >= e.getStartTime() && startDate <= e.getEndIntervalTime()) ||
                        (endDate >= e.getStartTime() && endDate <= e.getEndIntervalTime()) ||
                        (e.getStartTime() >= startDate && e.getStartTime() <= endDate) ||
                        (e.getEndIntervalTime() >= startDate && e.getEndIntervalTime() <= endDate)
        ).collect(Collectors.toList());
    }

    private static Long trimSecond(Long prTime) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return (new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(sdf.format(new Date(prTime)))).getTime();
    }

    public static void main1(String[] args) throws Exception {
        Date tprTime = new Date();
        tprTime.setSeconds(0);
        Long endTime = tprTime.getTime() / 1000 * 1000;

        Long prTime = 1673528159248L;
        prTime = trimSecond(prTime);
        int i = 0;
    }

    @Override
    public Result<List<StockVehicleBusyVO>> getStockCountForReport(Long merchantId, Long storeId, Long channelId, Long startTime, Long endTime) {
        List<StockVehicleBusyVO> retList = new ArrayList<>();
        // 查询占用数据
        List<VehicleBusy> busyList = this.selectBusyMerchantList(merchantId, storeId, null, channelId, startTime, endTime);
        for (VehicleBusy bean : busyList) {
            StockVehicleBusyVO vo = new StockVehicleBusyVO();
            BeanUtils.copyProperties(bean, vo);
            retList.add(vo);
        }
        return ResultUtil.successResult(retList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public Result<Long> temporaryOffline(TemporaryOfflineParam param, LoginVo opUser) {
        if (param == null  || opUser == null
                || ObjectUtils.anyNull(param.getVehicleId(), param.getStartTime(), param.getEndTime())) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        if (param.getStartTime() >= param.getEndTime()) {
            return ResultUtil.failResult("结束时间应大于开始时间");
        }

        Result<VehicleInfoVO> vehicleResult = vehicleInfoService.getBaseById(param.getVehicleId(), false);
        if (ResultUtil.isModelNull(vehicleResult)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }
        VehicleInfoVO vehicleInfo = vehicleResult.getModel();
        if (ObjectUtils.notEqual(vehicleInfo.getMerchantId(), opUser.getMerchantId())) {
            return ResultUtil.failResult("您无权限操作对应车辆");
        }

        log.info("临时停售, 车牌:{}, 操作人:{}, opUserId:{}, vehicleId:{} ",
            vehicleInfo.getLicense(), opUser.getName(), opUser.getUserId(), param.getVehicleId());

        VehicleBusyParam vehicleBusyParam = new VehicleBusyParam();
        vehicleBusyParam.setVehicleId(param.getVehicleId());
        vehicleBusyParam.setSourceType(VehicleBusyEnum.TEMPORARY_OFF.getValueInt());
        vehicleBusyParam.setMerchantId(vehicleInfo.getMerchantId());
        vehicleBusyParam.setSourceId(0L);
        vehicleBusyParam.setStartTime(param.getStartTime());
        vehicleBusyParam.setEndTime(param.getEndTime());
        vehicleBusyParam.setStoreId(vehicleInfo.getStoreId());
        vehicleBusyParam.setVehicleModelId(vehicleInfo.getVehicleModelId());
        vehicleBusyParam.setBusyDesc(param.getBusyDesc());
        return this.getVehicleAndLock(vehicleBusyParam);
    }

    public static void main(String[] a) {
        Long startDate = 1L;
        Long endDate = 4L;
        List<VehicleBusy> busyList = new ArrayList<>();
        VehicleBusy aa = new VehicleBusy();
        aa.setStartTime(1L);
        aa.setEndIntervalTime(4L);
        busyList.add(aa);

        aa = new VehicleBusy();
        aa.setStartTime(1L);
        aa.setEndIntervalTime(2L);
        busyList.add(aa);

        aa = new VehicleBusy();
        aa.setStartTime(3L);
        aa.setEndIntervalTime(8L);
        busyList.add(aa);

        List<VehicleBusy> t = busyList.stream().filter(e ->
                (startDate >= e.getStartTime() && startDate <= e.getEndIntervalTime()) ||
                        (endDate >= e.getStartTime() && endDate <= e.getEndIntervalTime()) ||
                        (e.getStartTime() >= startDate && e.getStartTime() <= endDate) ||
                        (e.getEndIntervalTime() >= startDate && e.getEndIntervalTime() <= endDate)
        ).collect(Collectors.toList());
        System.out.println(t.size());
    }

    @Override
    public Result<VehicleBusyEntityVO> findById(Long id) {
        VehicleBusy busy = vehicleBusyMapper.selectByPrimaryKey(id);
        if (busy == null) {
            return ResultUtil.successResult(busy);
        }
        VehicleBusyEntityVO vo = new VehicleBusyEntityVO();
        BeanUtils.copyProperties(busy, vo);
        return ResultUtil.successResult(vo);
    }

    @Override
    public Integer saveAll(List<VehicleBusyEntityVO> params) {
        List<VehicleBusy> entities = VehicleBusyConverter.INSTANCE.toEntities(params);
        List<VehicleBusy> result = entities.stream().filter(et -> {
            Long storeId = et.getStoreId();
            Long vehicleId = et.getVehicleId();
            Long startTime = et.getStartTime();
            Long endIntervalTime = et.getEndIntervalTime();
            VehicleBusyExample vehicleBusyExample = new VehicleBusyExample();
            vehicleBusyExample.createCriteria().andStoreIdEqualTo(storeId)
                    .andVehicleIdEqualTo(vehicleId)
                    .andStartTimeEqualTo(startTime)
                    .andEndIntervalTimeEqualTo(endIntervalTime);
            et.setExt(StringUtils.EMPTY);
            List<VehicleBusy> vehicleBusies = vehicleBusyMapper.selectByExample(vehicleBusyExample);
            return !CollectionUtils.isNotEmpty(vehicleBusies);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return 0;
        }

        return vehicleBusyMapper.batchInsert(result);
    }

    @Override
    public int updSourceIdForThirdId(Long sourceId, Long parentSourceId, String thirdSorceId) {
        if (sourceId == null || parentSourceId == null || StringUtils.isEmpty(thirdSorceId)) {
            throw new BizException("参数错误");
        }
        VehicleBusy record = new VehicleBusy();
        VehicleBusyExample example = new VehicleBusyExample();
        record.setSourceId(sourceId);
        record.setParentSourceId(parentSourceId);
        record.setOpTime(System.currentTimeMillis());
        example.createCriteria().andThirdSourceIdEqualTo(thirdSorceId);
        return vehicleBusyMapper.updateByExampleSelective(record, example);
    }

    @Override
    public Result<Boolean> stockRelease(Long id, Long merchantId, String busyDesc) {
        if (id == null) {
            throw new BizException("参数错误");
        }
        log.info("库存占用,释放库存;库存ID={}", id);
        VehicleBusy beforeBusy = vehicleBusyMapper.selectByPrimaryKey(id);
        if (beforeBusy == null) {
            throw new BizException("库存不存在");
        }
        if (beforeBusy.getMerchantId().compareTo(merchantId) != 0) {
            throw new BizException("无权操作");
        }
        if (VehicleBusyEnum.isOrderBusy(beforeBusy.getSourceType())) {
            if (beforeBusy.getEndIntervalTime() < beforeBusy.getEndTime()) {
                throw new BizException("此单库存已释放或存在子订单");
            }

            Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(beforeBusy.getParentSourceId());
            if (ResultUtil.isModelNull(orderInfoVoResult)) {
                throw new BizException("订单不存在");
            }

            OrderInfoVo orderInfoVo = orderInfoVoResult.getModel();
            if (orderInfoVo.getOrderStatus() < OrderStatusEnum.RETURNED.getStatus()) {
                throw new BizException("此订单尚未还车");
            }
            OrderExtraVo orderExtraVo = orderInfoVo.getOrderExtraVo();
            if (orderExtraVo != null && Integer.valueOf(1).equals(orderExtraVo.getIsReptile())) {
                throw new BizException("同步订单无法释放间隔");
            }
            VehicleBusy busy = new VehicleBusy();
            BeanUtils.copyProperties(beforeBusy, busy);
            busy.setEndIntervalTime(beforeBusy.getEndTime() - 1L);
            if (null != busyDesc) {
                busy.setBusyDesc(busyDesc);
            }
            busy.setOpTime(System.currentTimeMillis());
            if (vehicleBusyMapper.updateByPrimaryKeySelective(busy) <= 0) {
                throw new BizException("释放库存失败");
            }
            busyEvent(EventConstantsEnum.Action.UPDATE.toString(), busy, beforeBusy);
            // 携程推送库存信息
            CtripStockTransactionParam ctripStockTransactionParam = new CtripStockTransactionParam();
            ctripStockTransactionParam.setMerchantId(busy.getMerchantId());
            ctripStockTransactionParam.setVehicleIds(Collections.singletonList(busy.getVehicleId()));
            eventPublisher.publishEvent(ctripStockTransactionParam);
            return ResultUtil.successResult(true);
        } else {
            throw new BizException("此类订单库存无法释放");
        }
    }

    @Override
    public Result<Integer> batchUpdateVehicleModelId(Long vehicleId, Long vehicleModelId) {
        if (vehicleId == null || vehicleModelId == null) {
            return ResultUtil.failResult("参数错误");
        }
        VehicleBusyExample vehicleBusyExample = new VehicleBusyExample();
        vehicleBusyExample.createCriteria().andVehicleIdEqualTo(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleBusy> vehicleBusyList = vehicleBusyMapper.selectByExample(vehicleBusyExample);
        int num = 0;
        for (VehicleBusy busy : vehicleBusyList) {
            VehicleBusy vehicleBusy = new VehicleBusy();
            vehicleBusy.setId(busy.getId());
            vehicleBusy.setVehicleModelId(vehicleModelId);
            vehicleBusy.setOpTime(System.currentTimeMillis());
            int n = vehicleBusyMapper.updateByPrimaryKeySelective(vehicleBusy);
            if (n == 1) {
                num++;
            }
        }
        return ResultUtil.successResult(num);
    }

    @Override
    public List<VehicleBusyEntityVO> getStockByLicense(Long merchantId, String license, Long time) {
        if (merchantId == null || license == null) {
            throw new BizException("参数错误");
        }
        List<VehicleBusyEntityVO> vehicleBusyList = vehicleBusyMapperEx.selectList(merchantId, license, time);
        return vehicleBusyList;
    }

    @Override
    public List<VehicleBusyEntityVO> getStockByVehicleId(Long merchantId, Long vehicleId, Long time) {
        VehicleBusyExample vehicleBusyExample = new VehicleBusyExample();
        vehicleBusyExample.createCriteria().andVehicleIdEqualTo(vehicleId).andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andEndIntervalTimeGreaterThanOrEqualTo(time);
        List<VehicleBusy> vehicleBusies = vehicleBusyMapper.selectByExample(vehicleBusyExample);
        List<VehicleBusyEntityVO> vehicleBusyEntityVOList = new ArrayList<>();
        for (VehicleBusy vehicleBusy : vehicleBusies) {
            VehicleBusyEntityVO vehicleBusyEntityVO = new VehicleBusyEntityVO();
            BeanUtils.copyProperties(vehicleBusy, vehicleBusyEntityVO);
            vehicleBusyEntityVOList.add(vehicleBusyEntityVO);
        }
        return vehicleBusyEntityVOList;
    }

    @Override
    public List<VehicleBusyEntityVO> getStockForOpen(StockForOpenParam param) {
        Long merchantId = param.getMerchantId();
        Long time = param.getTime();
        Long storeId = param.getStoreId();
        if (time == null) {
            time = System.currentTimeMillis();
        }
        VehicleBusyExample vehicleBusyExample = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = vehicleBusyExample.createCriteria();
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andStoreIdEqualTo(storeId)
                .andMerchantIdEqualTo(merchantId);
        if (param.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(param.getVehicleModelId());
        }
        if (param.getType().intValue() == 1) {
            criteria.andEndTimeGreaterThan(time);
        } else if (param.getType().intValue() == 2) {
            criteria.andEndIntervalTimeGreaterThan(time);
        }
        List<VehicleBusy> vehicleBusyList = vehicleBusyMapper.selectByExample(vehicleBusyExample);
        List<VehicleBusyEntityVO> voList = new ArrayList<>();
        for (VehicleBusy vehicleBusy : vehicleBusyList) {
            VehicleBusyEntityVO vehicleBusyEntityVO = new VehicleBusyEntityVO();
            BeanUtils.copyProperties(vehicleBusy, vehicleBusyEntityVO);
            voList.add(vehicleBusyEntityVO);
        }
        return voList;
    }

    @Override
    public void updateOpTime() {
        int i = 0;
        long timeMillis = System.currentTimeMillis();
        while (true) {
            VehicleBusyExample vehicleBusyExample = new VehicleBusyExample();
            vehicleBusyExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andEndIntervalTimeGreaterThan(timeMillis);
            vehicleBusyExample.setOrderByClause(String.format("id limit %d,1000", i));
            List<VehicleBusy> vehicleBusies = vehicleBusyMapper.selectByExample(vehicleBusyExample);

            if (CollectionUtils.isEmpty(vehicleBusies)) {
                break;
            }

            List<Long> idList = vehicleBusies.stream().map(VehicleBusy::getId).collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(idList, 200);
            for (List<Long> ids : partition) {
                if (CollectionUtils.isNotEmpty(ids)) {
                    vehicleBusyMapperEx.updateOptime(ids);
                }
            }

            i += 1000;
        }
        log.info("更新库存操作时间完成");
    }

    @Override
    public Result<List<VehicleBusyEntityVO>> queryByParam(VehicleBusyQueryParam queryParam) {
        if (queryParam == null || queryParam.getMerchantId() == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        VehicleBusyExample busyExample = new VehicleBusyExample();
        VehicleBusyExample.Criteria criteria = busyExample.createCriteria()
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andMerchantIdEqualTo(queryParam.getMerchantId());
        if (queryParam.getEndTimeGreaterOrEquals() != null) {
            criteria.andEndTimeGreaterThanOrEqualTo(queryParam.getEndTimeGreaterOrEquals());
        }
        if (queryParam.getStartTimeLessOrEquals() != null) {
            criteria.andStartTimeLessThanOrEqualTo(queryParam.getStartTimeLessOrEquals());
        }
        if (queryParam.getSourceId() != null) {
            criteria.andSourceIdEqualTo(queryParam.getSourceId());
        }
        if (queryParam.getSourceType() != null) {
            criteria.andSourceTypeEqualTo(queryParam.getSourceType());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getSourceTypes())) {
            criteria.andSourceTypeIn(queryParam.getSourceTypes());
        }
        if (CollectionUtils.isNotEmpty(queryParam.getVehicleIds())) {
            criteria.andVehicleIdIn(queryParam.getVehicleIds());
        }
        if (queryParam.getParentSourceId() != null) {
            criteria.andParentSourceIdEqualTo(queryParam.getParentSourceId());
        }

        List<VehicleBusy> vehicleBusies = vehicleBusyMapper.selectByExample(busyExample);
        if (CollectionUtils.isEmpty(vehicleBusies)) {
            return ResultUtil.successResult(Collections.emptyList());
        }

        List<VehicleBusyEntityVO> resultList = new ArrayList<>(vehicleBusies.size());
        for (VehicleBusy vehicleBusy : vehicleBusies) {
            VehicleBusyEntityVO result = new VehicleBusyEntityVO();
            BeanUtils.copyProperties(vehicleBusy, result);
            resultList.add(result);
        }
        return ResultUtil.successResult(resultList);
    }


    /**
     * 直连商家库存处理
     * 将saas的数据转化为三方的数据
     */
    private StockFeeResponse handleStock(int sourceType, VehicleBusyParam param, boolean mappingCheck) {
        OpenStockMerchantCheckReq stockCheckRequest = new OpenStockMerchantCheckReq();
        Long merchantId = param.getMerchantId();
        String stockKey = "直连商家,库存校验,checkStock_" + param.getMerchantId() + ",";
        log.info(stockKey + "handleStock 入参: param={}", JSON.toJSONString(param));
        if (Objects.nonNull(param)) {
            stockCheckRequest = Objects.nonNull(param.getOpenStockMerchantCheckReq()) ? param.getOpenStockMerchantCheckReq() : new OpenStockMerchantCheckReq();
        }
        Long storeId = param.getStoreId();
        Long returnStoreId = param.getReturnStoreId();
        Long vehicleModelId = param.getVehicleModelId();
        Long vehicleId = param.getVehicleId();
        stockCheckRequest.setSaasStoreId(storeId);
        stockCheckRequest.setSaasModelId(vehicleModelId);
        stockCheckRequest.setSaasVehicleId(vehicleId);
        stockCheckRequest.setLevelCorrEarliestRegisterTime(param.getLevelCorrEarliestRegisterTime());
        String thirdStoreId = thirdIdRelationService.getMappingForThird(
                1L, IdRelationEnum.STORE.getType(), storeId, merchantId);
        if (StringUtils.isBlank(thirdStoreId) && mappingCheck) {
            log.error(stockKey + ",关联第三方门店id{}不存在", storeId);
            throw new BizException("关联第三方门店id=" + storeId + "不存在");
        }
        String thirdVehicleModelId = thirdVehicleIdRelationService.getMappingForThird(
                1L, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(),
                vehicleModelId, merchantId);
        if (StringUtils.isBlank(thirdVehicleModelId) && mappingCheck) {
            log.error(stockKey + ",关联第三方车型id{}不存在", vehicleModelId);
            throw new BizException("关联第三方车型id=" + vehicleModelId + "不存在");
        }
        if (Objects.nonNull(vehicleId)) {
            String thirdVehicleId = thirdVehicleIdRelationService.getMappingForThird(
                    1L, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(),
                    vehicleId, merchantId);
            if (StringUtils.isBlank(thirdVehicleId) && mappingCheck) {
                log.error(stockKey + "关联第三方车辆id{}不存在", vehicleId);
                throw new BizException("关联第三方车辆id=" + vehicleModelId + "不存在");
            }
            stockCheckRequest.setVehicleId(thirdVehicleId);
        }
        if (Objects.nonNull(stockCheckRequest) && Objects.nonNull(stockCheckRequest.getMainOrder())) {
            if (Objects.nonNull(stockCheckRequest.getMainOrder().getPickupAddrType())
                    && stockCheckRequest.getMainOrder().getPickupAddrType() == 2 &&
                    StringUtils.isNotEmpty(stockCheckRequest.getMainOrder().getPickupCircleId())) {
                String thirdCircleId = thirdIdRelationService.getMappingForThird(
                        1L, IdRelationEnum.CICLE.getType(),
                        Long.valueOf(stockCheckRequest.getMainOrder().getPickupCircleId()), merchantId);
                if (StringUtils.isNotBlank(thirdCircleId)) {
                    stockCheckRequest.getMainOrder().setPickupCircleId(thirdCircleId);
                } else {
                    log.error(stockKey + "关联第三方圈id{}不存在", stockCheckRequest.getMainOrder().getPickupCircleId());
                }
            }
            if (Objects.nonNull(stockCheckRequest.getMainOrder().getReturnAddrType())
                    && stockCheckRequest.getMainOrder().getReturnAddrType() == 2 &&
                    StringUtils.isNotEmpty(stockCheckRequest.getMainOrder().getReturnCircleId())) {
                String thirdCircleId = thirdIdRelationService.getMappingForThird(
                        1L, IdRelationEnum.CICLE.getType(),
                        Long.valueOf(stockCheckRequest.getMainOrder().getReturnCircleId()), merchantId);
                if (StringUtils.isNotBlank(thirdCircleId)) {
                    stockCheckRequest.getMainOrder().setReturnCircleId(thirdCircleId);
                } else {
                    log.error(stockKey + "关联第三方圈id{}不存在", stockCheckRequest.getMainOrder().getReturnCircleId());
                }
            }
        }
        if (Objects.nonNull(returnStoreId)) {
            String thirdReturnStoreId = thirdIdRelationService.getMappingForThird(
                    1L, IdRelationEnum.STORE.getType(), returnStoreId, merchantId);
            stockCheckRequest.setReturnStoreId(thirdReturnStoreId);
        }
        stockCheckRequest.setStoreId(thirdStoreId);
        stockCheckRequest.setVehicleModelId(thirdVehicleModelId);
        stockCheckRequest.setStartTime(param.getStartTime());
        stockCheckRequest.setEndTime(param.getEndTime());
        stockCheckRequest.setChannelId(param.getChannelId());
        stockCheckRequest.setSourceType(sourceType);
        String saasOrderId = null;
       if (ThirdCheckStockEnum.RERENT_CHECK.getType() == sourceType || ThirdCheckStockEnum.RERENT_ORDER.getType() == sourceType) {
            if (param.getParentSourceId() != null && param.getParentSourceId() > 0L) {
                saasOrderId = param.getParentSourceId().toString();
            }
        }
        stockCheckRequest.setSaasOrderId(saasOrderId);
        if (StringUtils.isNotEmpty(param.getThirdSourceId())) {
            stockCheckRequest.setPlatOrderNo(param.getThirdSourceId());
        }
        return this.shangQiStockCheck(stockCheckRequest, merchantId, param.getChannelId(), mappingCheck);
    }


    /**
     * 上汽的库存校验
     */
    private StockFeeResponse shangQiStockCheck(OpenStockMerchantCheckReq request, Long merchantId, Long channelId, boolean mappingCheck) {
        String stockKey = "直连商家,库存校验,checkStock_" + merchantId + ",";
        StockFeeResponse response = new StockFeeResponse();
        SaasResponse ctripStockResponse = thirdCtripService.checkStock(merchantId, request);
        log.info(stockKey + "调用直连商家接口,request={} , response={}", JSON.toJSONString(request), JSON.toJSONString(ctripStockResponse));

        OpenStockFeeResponse rep = JSON.parseObject(String.valueOf(ctripStockResponse.getData()), OpenStockFeeResponse.class);
        if (Objects.isNull(rep)) {
            log.info("直连商家 merchantId:{} channelId:{} rep is null", merchantId, channelId);
            return null;
        }
        Long mappingForSaas = thirdVehicleIdRelationService.getMappingForSaas(1L, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(),
                rep.getVehicleModelId(), merchantId);
        if (mappingForSaas == null && mappingCheck) {
            log.error(stockKey + ",关联第三方车型id{}不存在", rep.getVehicleModelId());
            throw new BizException("关联第三方车型id=" + rep.getVehicleModelId() + "不存在");
        }
        response.setVehicleModelId(rep.getSaasModelId());
        if (mappingForSaas != null) {
            response.setVehicleModelId(mappingForSaas);
        }
        response.setVehicleIdList(rep.getVehicleIdList());
        response.setSaasVehicleIds(rep.getSaasVehicleIdList());
        if (!mappingCheck) {
            log.info(stockKey + "转换Mapping后={}", JSON.toJSONString(response));
            return response;
        }
        List<Long> saasVehicleIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(rep.getVehicleIdList())) {
            Result<List<IdVehicleRelationVO>> listResult = thirdVehicleIdRelationService.listByThirdIds(rep.getVehicleIdList(), merchantId, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), 1L);
            if (BooleanUtils.isFalse(listResult.isSuccess()) || CollectionUtils.isEmpty(listResult.getModel()) && mappingCheck){
                log.error(stockKey + ",关联第三方车辆id{}不存在", JSON.toJSONString( rep.getVehicleIdList()));
                return response;
            }

            Map<String, IdVehicleRelationVO> relationVOMap = listResult.getModel().stream()
                    .collect(Collectors.toMap(IdVehicleRelationVO::getThirdId, Function.identity(), (k1, k2) -> k1));

            for (String thirdVehicleId : rep.getVehicleIdList()) {
                IdVehicleRelationVO vo = relationVOMap.get(thirdVehicleId);
                if (Objects.isNull(vo)) {
                    continue;
                }
                saasVehicleIds.add(vo.getSaasId());
            }
            response.setSaasVehicleIds(saasVehicleIds);
        }
        log.info(stockKey + "转换Mapping后={}", JSON.toJSONString(response));
        return response;
    }

}
