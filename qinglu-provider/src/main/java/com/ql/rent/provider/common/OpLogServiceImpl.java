package com.ql.rent.provider.common;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.ql.rent.api.aggregate.model.contant.OpLogEnum;
import com.ql.rent.dao.common.OpLogMapper;
import com.ql.rent.dao.slave.common.OpLogSlaveMapper;
import com.ql.rent.entity.common.OpLogExample;
import com.ql.rent.entity.common.OpLogWithBLOBs;
import com.ql.rent.param.common.OpLogQuery;
import com.ql.rent.provider.common.mapper.OpLogStructMapper;
import com.ql.rent.provider.store.StoreInfoServiceImpl;
import com.ql.rent.service.price.IOpLogService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.common.OpLogVo;
import com.ql.rent.vo.store.StoreSimpleVo;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OpLogServiceImpl implements IOpLogService {
    @Resource
    private OpLogSlaveMapper opLogSlaveMapper;

    @Resource
    private OpLogMapper opLogMapper;

    @Resource
    private StoreInfoServiceImpl storeInfoService;


    @Override
    public Result<PageListVo<OpLogVo>> selectByQuery(OpLogQuery opLogQuery) {
        OpLogExample opLogExample = new OpLogExample();
        // 设置查询参数
        OpLogExample.Criteria criteria = opLogExample.createCriteria();
        criteria
                .andModuleEqualTo(opLogQuery.getModule())
                .andMerchantIdEqualTo(opLogQuery.getMerchantId())
                .andStoreIdEqualTo(opLogQuery.getStoreId());

        if(StringUtils.isNoneBlank(opLogQuery.getVehicleModelId())){
            criteria.andVehicleModelIdLike("%" + opLogQuery.getVehicleModelId() + "%");
        }
        if(ObjectUtils.isNotEmpty(opLogQuery.getSource())){
            criteria.andSourceEqualTo(opLogQuery.getSource().byteValue());
        }
        if(StringUtils.isNoneBlank(opLogQuery.getChannelId())){
            criteria.andChannelIdLike("%" + opLogQuery.getChannelId() + "%");
        }
        if(StringUtils.isNoneBlank(opLogQuery.getOperationType())){
            criteria.andOperationTypeEqualTo(opLogQuery.getOperationType());
        }
        if(ObjectUtils.isNotEmpty(opLogQuery.getOpUserId())){
            criteria.andOpUserIdEqualTo(opLogQuery.getOpUserId());
        }
        // 设置排序
        opLogExample.setOrderByClause("create_time desc");
        // 查询数量

        Page<OpLogWithBLOBs> page = PageHelper.startPage(opLogQuery.getPageIndex(), opLogQuery.getPageSize());
        // 查询日志记录
        List<OpLogWithBLOBs> opLogs = opLogSlaveMapper.selectByExampleWithBLOBs(opLogExample);
        //获取门店数据
        List<Long> storeIdList = opLogs.stream().map(OpLogWithBLOBs::getStoreId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, StoreSimpleVo> storeMap;
        Result<List<StoreSimpleVo>> listResult = storeInfoService.storeSampleForUnion(storeIdList);
        if (CollectionUtils.isNotEmpty(listResult.getModel())) {
            storeMap = listResult.getModel().stream().collect(Collectors.toMap(StoreSimpleVo::getStoreId, Function.identity()));
        } else {
            storeMap = Maps.newHashMap();
        }
        // 转成VO
        List<OpLogVo> opLogVos = new ArrayList<>();
        opLogs.forEach(item -> {
            OpLogVo opLogVo = OpLogStructMapper.INSTANCE.toVo(item);
            OpLogEnum opLogEnum = OpLogEnum.forByModuleAndOperationType(item.getModule(), item.getOperationType());

            opLogVo.setModule(opLogEnum.getModuleName());
            String storeName = Optional.ofNullable(storeMap.get(item.getStoreId()))
                    .map(StoreSimpleVo::getStoreName)
                    .orElse("");
            opLogVo.setStore(item.getStoreId() + "-" + storeName);
            opLogVo.setOpUser(item.getOpUserId() + "-" + item.getOpUserName());
            opLogVo.setOperationType(opLogEnum.getOperationTypeName());
            opLogVos.add(opLogVo);
        });
        return ResultUtil.successResult(PageListVo.buildPageList(page.getTotal(), opLogVos));
    }

    @Override
    public void writeOpLogDiff(OpLogVo opLogVo) {
        Span span = Span.current();
        String traceId = span.getSpanContext().getTraceId();
        OpLogWithBLOBs opLogWithBLOBs = OpLogStructMapper.INSTANCE.toEntity(opLogVo);
        // 设置创建时间
        opLogWithBLOBs.setCreateTime(System.currentTimeMillis());
        opLogWithBLOBs.setTrace(traceId);
        opLogWithBLOBs.setOpUserName(opLogVo.getOpUser());
        opLogMapper.insert(opLogWithBLOBs);
    }

}
