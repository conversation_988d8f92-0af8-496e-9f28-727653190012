package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.carengine.client.GpsRpc;
import com.ql.carengine.result.gps.GpsSectionDetailInfoDTO;
import com.ql.rent.common.AesUtil;
import com.ql.rent.component.PaymentStrategy;
import com.ql.rent.constant.MallServiceConstant;
import com.ql.rent.constant.MallServiceItemConstant;
import com.ql.rent.converter.trade.MallServiceOrderInfoConverter;
import com.ql.rent.dao.trade.MallServiceBillInfoMapper;
import com.ql.rent.dao.trade.MallServiceItemMapper;
import com.ql.rent.dao.trade.MallServiceOrderInfoMapper;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.EtcConstantsEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.trade.MallOrderItemEnum;
import com.ql.rent.enums.trade.MallServiceOrderStatusEnum;
import com.ql.rent.enums.trade.OrderStatusEnum;
import com.ql.rent.event.EventPublisher;
import com.ql.rent.param.trade.*;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.service.merchant.IMerchantItemUsageService;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.trade.IMallServiceOrderInfoService;
import com.ql.rent.service.trade.IServiceItemService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.CurrencyUtils;
import com.ql.rent.share.utils.JsonUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.merchant.MallServiceItemUsageDTO;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.trade.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ql.rent.common.FileUploader.addFilePrivatePrefix;
import static com.ql.rent.common.FileUploader.removeFilePrefix;
import static com.ql.rent.enums.trade.MallOrderItemEnum.ETC_SERVICE_ITEM;

@Slf4j
@Service
public class MallServiceOrderInfoServiceImpl implements IMallServiceOrderInfoService {

    @Resource
    private MallServiceOrderInfoMapper mallServiceOrderInfoMapper;

    @Resource
    private IServiceItemService iServiceItemService;

    @Resource
    private IMerchantItemUsageService iMerchantItemUsageService;

    @Resource
    private PaymentStrategy paymentStrategy;

    @Resource
    MallServiceBillInfoMapper mallServiceBillInfoMapper;

    @Resource
    EtcDeviceService etcDeviceService;

    @Resource
    private EventPublisher eventPublisher;

    @Resource
    MallServiceItemMapper mallServiceItemMapper;

    /** 商户APIV3密钥 */
    @Value("${wechatPay.apiV3key}")
    public String apiV3key;

    @Resource
    private GpsRpc gpsRpc;

    @Resource
    private MerchantInfoService merchantInfoService;


    @Override
    public List<MallServiceOrderInfoDTO> mallOrderList(Long merchantId, MallOrderQuery query) {
        if (Objects.isNull(merchantId)) {
            log.error("merchantId is null");
            return Lists.newArrayList();
        }
        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        MallServiceOrderInfoExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (Objects.nonNull(query) && Objects.nonNull(query.getItemType())) {
            criteria.andItemTypeEqualTo(query.getItemType().byteValue());
        }
        if (Objects.nonNull(query) && Objects.nonNull(query.getOrderStatus())) {
            criteria.andOrderStatusEqualTo(query.getOrderStatus().byteValue());
        }
        if (Objects.nonNull(query) && CollectionUtils.isNotEmpty(query.getOrderNos())) {
            criteria.andOrderNoIn(query.getOrderNos());
        }
        if (Objects.nonNull(query) && CollectionUtils.isNotEmpty(query.getOrderIds())) {
            criteria.andIdIn(query.getOrderIds());
        }
        List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mallServiceOrderInfos)) {
            return Lists.newArrayList();
        }
        List<MallServiceOrderInfoDTO> dtos = MallServiceOrderInfoConverter.INSTANCE.toDTOS(mallServiceOrderInfos);

        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(query.getItemType(), null);
        Map<String, MallServiceItemDTO> itemMap = itemDTOS.stream().collect(Collectors.toMap(MallServiceItemDTO::getItemCode,
                Function.identity(),
                (k1, k2) -> k1));

        Map<String, MallServiceBillInfo> orderExInfoMapByOrderNo = Maps.newConcurrentMap();
        if (!mallServiceOrderInfos.isEmpty()) {
            List<String> orderNoList = mallServiceOrderInfos.stream().map(MallServiceOrderInfo::getOrderNo).distinct().collect(Collectors.toList());
            //获取已支付订单的 支付信息
            MallServiceBillInfoExample mallServiceBillInfoExample = new MallServiceBillInfoExample();
            mallServiceBillInfoExample.createCriteria()
                    .andOrderNoIn(orderNoList);
            List<MallServiceBillInfo> mallServiceBillInfos = mallServiceBillInfoMapper.selectByExample(mallServiceBillInfoExample);
            if (!mallServiceBillInfos.isEmpty()) {
                orderExInfoMapByOrderNo = mallServiceBillInfos.stream().collect(Collectors.toMap(MallServiceBillInfo::getOrderNo, Function.identity(), (k1, k2) -> k2));
            }
        }

        Map<String, MallServiceBillInfo> finalOrderExInfoMapByOrderNo = orderExInfoMapByOrderNo;
        dtos.forEach(dto -> {
            MallServiceItemDTO itemDTO = itemMap.get(dto.getItemCode());
            if (Objects.isNull(itemDTO)) {
                return;
            }
            dto.setItemName(itemDTO.getItemName());
            dto.setDuration(itemDTO.getDuration());
            dto.setPaymentMethod(Optional.ofNullable(finalOrderExInfoMapByOrderNo.get(dto.getOrderNo()))
                    .map(MallServiceBillInfo::getPayMethod).orElse(null));

            // 新增返回ETC设备来源
            if(Objects.equals(ETC_SERVICE_ITEM.getItemType(), dto.getItemType())){
                dto.setDeviceSourceStr(EtcConstantsEnum.ThirdSourceEnum.forByCode(dto.getDeviceSource()).getName());
            }

        });
        dtos.sort(Comparator.comparing(MallServiceOrderInfoDTO::getExpirationDate).reversed());
        return dtos;
    }

    @Override
    public List<MallServiceOrderInfoDTO> availableMallOrderList(Long merchantId, Byte itemType) {
        if (Objects.isNull(merchantId)) {
            log.error("merchantId is null");
            return Lists.newArrayList();
        }
        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId).andOrderStatusEqualTo(MallServiceOrderStatusEnum.ALL_PAID.getStatus())
                .andExpirationDateGreaterThanOrEqualTo(new Date()).andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andItemTypeEqualTo(itemType)
                .andRemainingCountGreaterThan(0);
        List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mallServiceOrderInfos)) {
            return Lists.newArrayList();
        }

        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(itemType.intValue(), null);
        List<MallServiceOrderInfoDTO> dtos = MallServiceOrderInfoConverter.INSTANCE.toDTOS(mallServiceOrderInfos);
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return dtos;
        }
        Map<String, MallServiceItemDTO> itemMap = itemDTOS.stream().collect(Collectors.toMap(MallServiceItemDTO::getItemCode,
                Function.identity(),
                (k1, k2) -> k1));
        dtos.forEach(dto -> {
            dto.setItemName(itemMap.get(dto.getItemCode()).getItemName());
            dto.setOrderPrice(itemMap.get(dto.getItemCode()).getItemPrice().intValue());
            dto.setItemPrice(itemMap.get(dto.getItemCode()).getItemPrice());
            dto.setDeposit(itemMap.get(dto.getItemCode()).getDeposit());
            dto.setDuration(itemMap.get(dto.getItemCode()).getDuration());
        });
        return dtos;
    }

    @Override
    public List<MallItemServiceGroupDTO> availableItemOrderList(Long merchantId, Byte itemType) {
        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId)
                .andOrderStatusEqualTo(MallServiceOrderStatusEnum.ALL_PAID.getStatus())
                .andExpirationDateGreaterThanOrEqualTo(new Date())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andItemTypeEqualTo(itemType);
        // .andRemainingCountGreaterThan(0);
        List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mallServiceOrderInfos)) {
            return Lists.newArrayList();
        }
        mallServiceOrderInfos = mallServiceOrderInfos.stream().filter(data -> {
            if (MallServiceItemConstant.ILLEGAL.SERVICE.equals(data.getItemSubPackage())) {
                return data.getRemainingCount() > 0;
            }
            return true;
        }).collect(Collectors.toList());
        Result<MallServiceItemUsageDTO> serviceItemUsage = iMerchantItemUsageService.mallServiceItemUsage(merchantId, itemType.intValue());
        if (BooleanUtils.isFalse(serviceItemUsage.isSuccess()) || Objects.isNull(serviceItemUsage.getModel())) {
            return Lists.newArrayList();
        }
        MallServiceItemUsageDTO serviceItemUsageModel = serviceItemUsage.getModel();
        String itemCode = "";
        if (Objects.nonNull(serviceItemUsageModel.getItem())) {
            itemCode = serviceItemUsageModel.getItem().getItemCode();
        }
        if (CollectionUtils.isNotEmpty(serviceItemUsageModel.getIllTranItem())) {
            itemCode = serviceItemUsageModel.getIllTranItem().get(0).getItemCode();
        }

        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(itemType.intValue(), null);
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return Lists.newArrayList();
        }
        List<MallServiceOrderInfoDTO> dtos = MallServiceOrderInfoConverter.INSTANCE.toDTOS(mallServiceOrderInfos);
        Map<String, MallServiceItemDTO> itemMap = itemDTOS.stream().collect(Collectors.toMap(MallServiceItemDTO::getItemCode,
                Function.identity(),
                (k1, k2) -> k1));
        dtos.forEach(dto -> {
            MallServiceItemDTO itemDTO = itemMap.get(dto.getItemCode());
            if (Objects.isNull(itemDTO)) {
                return;
            }
            dto.setItemName(itemDTO.getItemName());
            dto.setItemCount(dto.getItemCount());
            dto.setDuration(itemDTO.getDuration());
            dto.setItemPrice(itemDTO.getItemPrice());
            dto.setDeposit(itemDTO.getDeposit());
        });

        String finalItemCode = itemCode;
        return dtos.stream()
                .collect(Collectors.groupingBy(MallServiceOrderInfoDTO::getItemCode,
                        Collectors.mapping(Function.identity(), Collectors.toList())))
                .entrySet().stream()
                .map(entry -> {
                    MallServiceItemDTO itemDTO = itemMap.get(entry.getKey());
                    if (Objects.isNull(itemDTO)) {
                        return null;
                    }
                    MallItemServiceGroupDTO groupDTO = new MallItemServiceGroupDTO();
                    groupDTO.setItemCode(entry.getKey());
                    groupDTO.setItemName(itemDTO.getItemName());
                    groupDTO.setIsCurrentItem(finalItemCode.equals(entry.getKey()));
                    groupDTO.setServiceOrderList(entry.getValue());
                    return groupDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public MallServiceOrderInfoDTO detail(Long merchantId, Long dataId, String orderNo) {
        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        MallServiceOrderInfoExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (Objects.nonNull(merchantId)) {
            criteria.andMerchantIdEqualTo(merchantId);
        }
        if (Objects.nonNull(dataId)) {
            criteria.andIdEqualTo(dataId);
        }
        if (StringUtils.isNotBlank(orderNo)) {
            criteria.andOrderNoEqualTo(orderNo);
        }
        List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(mallServiceOrderInfos)) {
            return null;
        }
        MallServiceOrderInfo orderInfo = mallServiceOrderInfos.get(0);
        MallServiceOrderInfoDTO dto = MallServiceOrderInfoConverter.INSTANCE.toDTO(orderInfo);
        if (Objects.isNull(dto)) {
            return null;
        }
        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(null, dto.getItemCode());
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return null;
        }
        MallServiceItemDTO itemDTO = itemDTOS.stream().findFirst().orElse(null);
        if (Objects.isNull(itemDTO)) {
            return null;
        }
        dto.setItemName(itemDTO.getItemName());
        dto.setDuration(itemDTO.getDuration());
        dto.setItemType(itemDTO.getItemType());
        dto.setItemPrice(itemDTO.getItemPrice());
        dto.setDeposit(itemDTO.getDeposit());
        return dto;
    }

    @Override
    public List<MallServiceOrderInfoDTO> serviceOrderList(Long merchantId, MallServiceOrderParam mallServiceOrderParam) {
        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        MallServiceOrderInfoExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(merchantId);
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (Objects.isNull(merchantId)) {
            throw new BizException("merchantId 不能为空");
        }
        if (Objects.nonNull(mallServiceOrderParam) && CollectionUtils.isNotEmpty(mallServiceOrderParam.getOrderIds())) {
            criteria.andIdIn(mallServiceOrderParam.getOrderIds());
        }

        if (Objects.nonNull(mallServiceOrderParam) && CollectionUtils.isNotEmpty(mallServiceOrderParam.getOrderNoList())) {
            criteria.andOrderNoIn(mallServiceOrderParam.getOrderNoList());
        }
        if (Objects.nonNull(mallServiceOrderParam) && Objects.nonNull(mallServiceOrderParam.getOrderStatus())) {
            criteria.andOrderStatusEqualTo(mallServiceOrderParam.getOrderStatus().byteValue());
        }
        if (Objects.nonNull(mallServiceOrderParam) && Objects.nonNull(mallServiceOrderParam.getStartExpirationDate())) {
            criteria.andExpirationDateGreaterThan(mallServiceOrderParam.getStartExpirationDate());
        }
        if (Objects.nonNull(mallServiceOrderParam) && Objects.nonNull(mallServiceOrderParam.getItemType())) {
            criteria.andItemTypeEqualTo(mallServiceOrderParam.getItemType().byteValue());
        }

        List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(mallServiceOrderInfos)) {
            return Lists.newArrayList();
        }
        List<MallServiceOrderInfoDTO> dtoList = MallServiceOrderInfoConverter.INSTANCE.toDTOS(mallServiceOrderInfos);

        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(null, null);
        Map<String, MallServiceItemDTO> itemDTOMap = itemDTOS.stream()
                .collect(Collectors.toMap(MallServiceItemDTO::getItemCode, Function.identity(),
                        (k1, k2) -> k1));

        dtoList.forEach(dto -> {
            MallServiceItemDTO itemDTO = itemDTOMap.getOrDefault(dto.getItemCode(), new MallServiceItemDTO());
            dto.setItemName(itemDTO.getItemName());
            dto.setDuration(itemDTO.getDuration());
        });

        return dtoList;
    }

    @Override
    public MallCreatOrderVO createMallServiceOrder(CreateMallOrderRequest mallOrderRequest, LoginVo loginVo) {
        Long merchantId = loginVo.getMerchantId();
        if (Objects.isNull(mallOrderRequest) || StringUtils.isBlank(mallOrderRequest.getItemCode())) {
            log.info("mallOrderRequest Exception :{}", JSON.toJSONString(mallOrderRequest));
            throw new BizException("参数异常");
        }
        List<MallServiceItemDTO> itemDTOS = iServiceItemService.orderItemList(null, mallOrderRequest.getItemCode());
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return null;
        }
        MallServiceItemDTO itemDTO = itemDTOS.stream().findFirst().orElse(null);
        if (Objects.isNull(itemDTO)) {
            return null;
        }
        if (itemDTO.getDeleted() == YesOrNoEnum.YES.getValue()) {
            throw new BizException("该套餐已废弃，不支持购买");
        }
        int itemCount = 0;
        if (ETC_SERVICE_ITEM.getItemType().equals(mallOrderRequest.getItemType())) {
            itemCount = mallOrderRequest.getProductQuantity();
        } else {
            if (Objects.nonNull(mallOrderRequest.getItemCount())) {
                itemCount = mallOrderRequest.getItemCount();
            } else {
                MallServiceItemDTO serviceItemDTO = itemDTOS.stream().filter(item -> mallOrderRequest.getItemCode().equals(item.getItemCode()))
                        .findFirst().orElse(null);
                if (Objects.isNull(serviceItemDTO)){
                    throw new BizException("该套餐不存在");
                }
                itemCount = serviceItemDTO.getItemCount();
            }
        }
        // ETC/GPS都是10个起卖
        if (MallOrderItemEnum.ETC_SERVICE_ITEM.getItemType().equals(mallOrderRequest.getItemType())
                || MallOrderItemEnum.GPS_DEVICE.getItemType().equals(mallOrderRequest.getItemType())) {
            if (itemCount < 10){
                throw new BizException("该套餐10个起卖");
            }
        }
        Date date = new Date();
        // 违章查询和违章转移 订单互斥，
        // 1.已存在「查询」 不支持创建「转移」订单 2.已存在「转移」  不支持创建「查询」
        MallServiceOrderInfoExample mallServiceOrderInfoExample = new MallServiceOrderInfoExample();
        mallServiceOrderInfoExample.createCriteria()
                .andMerchantIdEqualTo(merchantId)
                .andItemTypeEqualTo(MallOrderItemEnum.ILLEGAL_SEARCH.getItemType().byteValue())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andExpirationDateGreaterThan(date);
        //  List<MallServiceOrderInfo> mallServiceOrderInfos = mallServiceOrderInfoMapper.selectByExample(mallServiceOrderInfoExample);
        // 下单套餐与未过期的套餐，不相符
       /* if (CollectionUtils.isNotEmpty(mallServiceOrderInfos)
                && Objects.equals(mallOrderRequest.getItemType(), MallOrderItemEnum.ILLEGAL_SEARCH.getItemType())) {
            MallServiceOrderInfo orderInfo = mallServiceOrderInfos.stream()
                    .filter(order -> !Objects.equals(itemDTO.getItemSubPackage(), order.getItemSubPackage()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(orderInfo)) {
                throw new BizException("违章查询｜违章转移 不允许同时使用");
            }
        }*/

        Date now = date;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmsssss");
        String orderNo = MallServiceConstant.RRE_VIOORD + merchantId + formatter.format(now);
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now().plusMonths(itemDTO.getDuration()), LocalTime.MAX).withNano(0);
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();

        MallCreatOrderVO orderVO = new MallCreatOrderVO();

        MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
        example.createCriteria()
                .andMerchantIdEqualTo(merchantId)
                .andItemCodeEqualTo(mallOrderRequest.getItemCode())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andOrderStatusEqualTo(OrderStatusEnum.UNSUBMITTED.getStatus());
        List<MallServiceOrderInfo> todoPayOrderList = mallServiceOrderInfoMapper.selectByExample(example);

        // 过滤掉 已提交 的 对公汇款 的 订单
        List<String> orderNos = todoPayOrderList.stream().map(MallServiceOrderInfo::getOrderNo).collect(Collectors.toList());
        List<MallServiceBillInfo> mallServiceBillInfos = Lists.newArrayList();
        List<String> fileOrderNos = Lists.newArrayList();
        if (!orderNos.isEmpty()) {
            MallServiceBillInfoExample mallServiceBillInfoExample = new MallServiceBillInfoExample();
            mallServiceBillInfoExample.createCriteria()
                    .andOrderNoIn(orderNos);
            mallServiceBillInfos = mallServiceBillInfoMapper.selectByExample(mallServiceBillInfoExample);
            fileOrderNos = mallServiceBillInfos.stream().map(MallServiceBillInfo::getOrderNo).collect(Collectors.toList());
            List<String> finalFileOrderNos = fileOrderNos;
            todoPayOrderList = todoPayOrderList.stream().filter(f -> !finalFileOrderNos.contains(f.getOrderNo())).collect(Collectors.toList());
        }
        int orderPrice = Math.toIntExact(itemDTO.getItemPrice() * itemCount);
        int orderDeposit = Math.toIntExact(itemDTO.getDeposit().longValue() * itemCount);
        List<MallServiceOrderInfoDTO> todoPayOrderDTOList = MallServiceOrderInfoConverter.INSTANCE.toDTOS(todoPayOrderList);
        // 同一规格，已存在待支付订单
        if (CollectionUtils.isNotEmpty(todoPayOrderDTOList)) {
            MallServiceOrderInfoDTO orderInfoDTO = todoPayOrderDTOList.stream().findFirst().orElse(null);
            if (Objects.nonNull(orderInfoDTO)) {
                MallServiceOrderInfo db = new MallServiceOrderInfo();
                BeanUtils.copyProperties(orderInfoDTO, db);
                //违章查询不需要按照数量计算
                if (MallServiceItemConstant.ILLEGAL.SERVICE.equals(orderInfoDTO.getItemSubPackage())) {
                    orderPrice = itemDTO.getItemPrice().intValue();
                    orderVO.setOrderNo(db.getOrderNo());
                    orderVO.setOrderPrice((long) orderPrice);
                } else {
                    //计算最新 订单金额 和 订单 押金
                    orderPrice = itemDTO.getItemPrice().intValue() * itemCount;
                }
                db.setRemainingCount(itemCount);
                db.setOrderPrice(orderPrice);
                db.setOrderDeposit(orderDeposit);
                db.setItemCount(itemCount);
                db.setOpUserId(loginVo.getUserId());
                db.setOpTime(now.getTime());
                db.setTempVersion(db.getTempVersion() + 1);
                db.setExpirationDate(new Date(instant.toEpochMilli()));
                mallServiceOrderInfoMapper.updateByPrimaryKeySelective(db);
                orderVO.setOrderPrice((long) orderPrice);
                orderVO.setOrderDeposit((long) orderDeposit);
                orderVO.setOrderNo(db.getOrderNo());
                return orderVO;
            }
        }
        MallServiceOrderInfo orderInfo = new MallServiceOrderInfo();
        orderInfo.setMerchantId(merchantId);
        orderInfo.setOrderNo(orderNo);
        orderInfo.setItemType(itemDTO.getItemType().byteValue());
        orderInfo.setItemCode(itemDTO.getItemCode());
        orderInfo.setDeleted(YesOrNoEnum.NO.getValue());
        orderInfo.setCreateTime(now.getTime());
        orderInfo.setOpTime(now.getTime());
        orderInfo.setCreateUserId(loginVo.getUserId());
        orderInfo.setOpUserId(loginVo.getUserId());
        orderInfo.setTempVersion(0);
        orderInfo.setItemSubPackage(itemDTO.getItemSubPackage());


        // 仅违章查询不需要计算金额
        if (orderInfo.getItemType() == MallOrderItemEnum.ILLEGAL_SEARCH.getItemType().intValue()
                && orderInfo.getItemSubPackage().equals(MallServiceItemConstant.ILLEGAL.SERVICE)) {
            orderPrice = Math.toIntExact(itemDTO.getItemPrice());
        }
        orderInfo.setOrderDeposit(orderDeposit);
        orderInfo.setRemainingCount(itemCount);
        orderInfo.setExpirationDate(new Date(instant.toEpochMilli()));
        orderInfo.setOrderStatus(MallServiceOrderStatusEnum.UNPAID.getStatus());
        orderInfo.setItemCount(itemCount);
        orderInfo.setOrderPrice(orderPrice);

        // 【ETC自营项目】增加设备来源字段
        if(Objects.equals(ETC_SERVICE_ITEM.getItemType(),orderInfo.getItemType().intValue())){
            // todo 这里确认是只能新增自营了？
            orderInfo.setDeviceSource(EtcConstantsEnum.ThirdSourceEnum.SAAS_SELF.getType().intValue());
        }

        mallServiceOrderInfoMapper.insert(orderInfo);
        orderVO.setOrderNo(orderNo);
        orderVO.setOrderPrice((long) orderPrice);
        orderVO.setOrderDeposit((long) orderDeposit);
        // gps订单，单独调用gpsRPC
        if (MallOrderItemEnum.GPS_DEVICE.getItemType().equals(itemDTO.getItemType())) {
            GpsSectionDetailInfoDTO dto = new GpsSectionDetailInfoDTO();
            dto.setMerchantId(merchantId);
            Result<MerchantInfoVo> merchantResult = merchantInfoService.findById(merchantId);
            if (merchantResult.isSuccess() && Objects.nonNull(merchantResult.getModel())){
                dto.setMerchantName(merchantResult.getModel().getName());
            }
            gpsRpc.liberalSection(dto);
        }
        return orderVO;
    }

    @Override
    public void updateOrderStatus(Long dataId, String outId, String orderNo, Integer payStatus, Long opUserId, Byte payMethod) {
        log.info("updateOrderStatus dataId:{} outId:{} orderNo:{} payStatus:{}", dataId, outId, orderNo, payStatus);
        MallServiceOrderInfo orderInfo = null;
        if (Objects.nonNull(dataId)) {
            orderInfo = mallServiceOrderInfoMapper.selectByPrimaryKey(dataId);
        } else if (StringUtils.isNotBlank(orderNo)) {
            MallServiceOrderInfoExample example = new MallServiceOrderInfoExample();
            example.createCriteria()
                    .andOrderNoEqualTo(orderNo)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            List<MallServiceOrderInfo> serviceOrderInfos = mallServiceOrderInfoMapper.selectByExample(example);
            orderInfo = serviceOrderInfos.stream()
                    .filter(Objects::nonNull)
                    .findFirst().orElse(null);
        }
        if (Objects.isNull(orderInfo)) {
            log.error("updateOrderStatus  dataId:{} orderNo:{} orderInfo is null", dataId, orderNo);
            return;
        }
        MallServiceItemDTO orderItem = iServiceItemService.orderItemList(null, orderInfo.getItemCode())
                .stream().findFirst().orElse(null);
        if (Objects.isNull(orderItem)) {
            log.error("updateOrderStatus  dataId:{} orderNo:{} orderItem is null", dataId, orderNo);
            return;
        }
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now().plusMonths(orderItem.getDuration()), LocalTime.MAX).withNano(0);
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        orderInfo.setExpirationDate(new Date(instant.toEpochMilli()));
        orderInfo.setOrderStatus(payStatus.byteValue());
        orderInfo.setOpTime(new Date().getTime());
        orderInfo.setOpUserId(opUserId);
        orderInfo.setOutId(outId);
        mallServiceOrderInfoMapper.updateByPrimaryKey(orderInfo);

        if (payStatus != 1) {
            return;
        }

        MallOrderQuery query = new MallOrderQuery();
        query.setItemType(orderItem.getItemType());
        query.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue());
        List<MallServiceOrderInfoDTO> serviceOrderInfoDTOS = this.mallOrderList(orderInfo.getMerchantId(), query);
        List<MallServiceOrderInfoDTO> orderResult = serviceOrderInfoDTOS.stream()
                .filter(dto -> !dto.getOrderNo().equals(orderNo))
                .collect(Collectors.toList());
        log.info("updateOrderStatus 自动切换套餐确认数据 serviceOrderInfoDTOS:{}", JSON.toJSONString(serviceOrderInfoDTOS));
        boolean autoSwitchItem = false;
        // 自动切换套餐:1.新用户首单
        if (CollectionUtils.isEmpty(orderResult)) {
            autoSwitchItem = true;
        } else {
            // 自动切换套餐: 2.过期用户首单 3.次数耗尽用户首单
            MallServiceOrderInfoDTO effectiveOrder = orderResult.stream()
                    .filter(dto -> dto.getExpirationDate().after(new Date()) && dto.getRemainingCount() > 0)
                    .findFirst().orElse(null);
            if (Objects.isNull(effectiveOrder)) {
                autoSwitchItem = true;
            }
        }
        LoginVo loginVo = new LoginVo();
        loginVo.setMerchantId(orderInfo.getMerchantId());
        loginVo.setUserId(orderInfo.getOpUserId());
        // 自动切换套餐
        if (BooleanUtils.isTrue(autoSwitchItem)) {
            log.info("updateOrderStatus 自动切换套餐 ");
            iMerchantItemUsageService.updateMallServiceItem(orderInfo.getId(), loginVo);
        }
        //记录购买 附加信息和 支付方式
        if (payMethod == 1 || payMethod == 2) {
            long l = System.currentTimeMillis();
            //线上支付记录 转账信息
            MallServiceBillInfo db = MallServiceBillInfo.builder()
                    .itemType(orderInfo.getItemType())
                    .opUserId(opUserId)
                    .payMethod(payMethod)
                    .createTime(l)
                    .opTime(l)
                    .orderNo(orderInfo.getOrderNo())
                    .build();
            mallServiceBillInfoMapper.insertSelective(db);
        }
        //支付完成后置处理 todo
        if (orderInfo.getItemType() == ETC_SERVICE_ITEM.getItemType().byteValue()) {
            //etc 支付完成处理 todo
//         推送 商家信息给好人 好车
            etcDeviceService.pushStore(orderInfo.getMerchantId());
        }


        log.info("updateOrderStatus dataId:{} orderNo:{} payStatus:{} success", dataId, orderNo, payStatus);
    }

    @Override
    public void updateMallOrderInfo(Long id, Integer count) {
        MallServiceOrderInfo orderInfo = mallServiceOrderInfoMapper.selectByPrimaryKey(id);
        orderInfo.setRemainingCount(orderInfo.getRemainingCount() - count);
        mallServiceOrderInfoMapper.updateByPrimaryKey(orderInfo);
    }

    @Override
    public String generatedPayQrCode(Long merchantId, String platform, String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号不能为空");
        }

        // 通过订单号查询订单价格
        MallServiceOrderInfoDTO detail = this.detail(merchantId, null, orderNo);
        MallServiceOrderInfo mallServiceOrderInfo = mallServiceOrderInfoMapper.selectByPrimaryKey(detail.getId());
        mallServiceOrderInfo.setTempVersion(mallServiceOrderInfo.getTempVersion() + 1);
        mallServiceOrderInfoMapper.updateByPrimaryKey(mallServiceOrderInfo);
        String realOrderNoFormat = "%s_%s";
        String realOrderNo = String.format(realOrderNoFormat, mallServiceOrderInfo.getOrderNo(), mallServiceOrderInfo.getTempVersion());
        Integer price = detail.getOrderPrice() + detail.getOrderDeposit();
        String qrCodeUrl = paymentStrategy.payQrCode(platform, realOrderNo, price);
        log.info("merchantId={} platform={} orderNo={} result={}", merchantId, platform, realOrderNo, qrCodeUrl);
        return qrCodeUrl;
    }

    @Override
    public void payCallback(String platform, String outId, String orderNo, String payState) {
        log.info("platform:{} outId:{} orderNo:{} payState:{}", platform, outId, orderNo, payState);
        int indexOf = orderNo.indexOf("_");
        String realOrderNo = orderNo.substring(0, indexOf);
        int payStatus = 0;
        Byte payMethod = 0;
        // 更新订单状态
        switch (platform) {
            case MallServiceConstant.ALI_PAY_PLATFORM:
                if ("TRADE_SUCCESS".equals(payState)) {
                    payStatus = MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue();
                    payMethod = (byte)1;
                }
                break;
            case MallServiceConstant.WECHAT_PAY_PLATFORM:
                if ("SUCCESS".equals(payState)) {
                    payStatus = MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue();
                    payMethod = (byte)2;
                }
                break;
            default:
                break;
        }
        this.updateOrderStatus(null, outId, realOrderNo, payStatus, 0L,payMethod);

    }

    @Override
    public void payCallBack(String platform, Map<String, Object> callBackParamMap) {

        // 更新订单状态
        switch (platform) {
            case MallServiceConstant.ALI_PAY_PLATFORM:
                try {
                    //动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容
                    log.info("+++++++aliPay.callback params:{}", callBackParamMap);
                    String tradeStatus = (String) callBackParamMap.getOrDefault("trade_status", "");//交易状态

                    if ("TRADE_SUCCESS".equals(tradeStatus)) {
                        //支付宝交易ID
                        String transactionId = (String) callBackParamMap.getOrDefault("trade_no", "");
                        //支付ID
                        String outTradeNo = (String) callBackParamMap.getOrDefault("out_trade_no", "");//商户订单号
                        if (outTradeNo.startsWith(MallServiceConstant.RRE_VIOORD)) {
                            this.payCallback(MallServiceConstant.ALI_PAY_PLATFORM, transactionId, outTradeNo, tradeStatus);
                            return;
                        }
                    }

                } catch (Exception e) {
                    log.error("aliPay.callback Exception:", e.getMessage());
                }
                break;
            case MallServiceConstant.WECHAT_PAY_PLATFORM:
                try {
                    if (MapUtils.isEmpty(callBackParamMap)) {
                        log.info("callBackParamMap is null");
                        return;
                    }
                    String originalType = (String) callBackParamMap.get("original_type");
                    byte[] associatedData = callBackParamMap.getOrDefault("associated_data", "").toString().getBytes(StandardCharsets.UTF_8);
                    byte[] nonce = callBackParamMap.getOrDefault("nonce", "").toString().getBytes(StandardCharsets.UTF_8);
                    String ciphertext = callBackParamMap.getOrDefault("ciphertext", "").toString();
                    AesUtil aesUtil = new AesUtil(apiV3key.getBytes(StandardCharsets.UTF_8));
                    String payJson = aesUtil.decryptToString(associatedData, nonce, ciphertext);
                    if (StringUtils.isBlank(payJson)) {
                        log.info("payJson is null");
                        return;
                    }
                    log.info("wechatPay.callback payJson:{}", payJson);
                    Map<String, Object> payMap = (Map<String, Object>) JsonUtil.jsonToBean(payJson, Map.class);
                    if (MapUtils.isEmpty(payMap)) {
                        log.info("payMap is null");
                        return;
                    }
                    if ("transaction".equals(originalType)) {
                        String tradeState = (String) payMap.get("trade_state");
                        String transactionId = (String) payMap.get("transaction_id");
                        if ("SUCCESS".equals(tradeState)) {
                            //微信交易ID
                            String outTradeNo = (String) payMap.get("out_trade_no");
                            //支付ID
                            if (outTradeNo.startsWith(MallServiceConstant.RRE_VIOORD)) {
                                log.info("违章订单套餐处理:{} payMap={}", outTradeNo, JSON.toJSONString(payMap));
                                this.payCallback(MallServiceConstant.WECHAT_PAY_PLATFORM, transactionId, outTradeNo, tradeState);
                                return;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("e", e);
                }
                break;
            default:
                break;
        }
    }

    //对公汇款 记录汇款账单 完成订单状态
    @Override
    public void corporateRemittance(MallServiceBillInfoReq req){
        // 查询订单主表
        MallServiceOrderInfoExample orderInfoExample = new MallServiceOrderInfoExample();
        orderInfoExample.createCriteria()
                .andOrderNoEqualTo(req.getOrderNo())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<MallServiceOrderInfo> serviceOrderInfos = mallServiceOrderInfoMapper.selectByExample(orderInfoExample);
        MallServiceOrderInfo orderInfo = serviceOrderInfos.stream()
                .filter(Objects::nonNull)
                .findFirst().orElse(null);

        MallServiceItemExample serviceItemExample = new MallServiceItemExample();
        serviceItemExample.createCriteria()
                .andItemTypeEqualTo(orderInfo.getItemType())
                .andItemCodeEqualTo(orderInfo.getItemCode())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<MallServiceItem> mallServiceItems = mallServiceItemMapper.selectByExample(serviceItemExample);
        MallServiceItem mallServiceItem = mallServiceItems.stream()
                .filter(Objects::nonNull)
                .findFirst().orElse(null);
        //更新订单状态
        this.updateOrderStatus(null,null, req.getOrderNo(), req.getOrderStatus(), req.getOpUserId(),(byte)3);
        //记录对公账单

        String url = removeFilePrefix(req.getUrl());
        MallServiceBillInfoExample example = new MallServiceBillInfoExample();
        example.createCriteria().andOrderNoEqualTo(req.getOrderNo())
                .andPayMethodEqualTo((byte)3)
                .andItemTypeEqualTo(orderInfo.getItemType());
        List<MallServiceBillInfo> mallServiceBillInfos = mallServiceBillInfoMapper.selectByExample(example);
        if(!mallServiceBillInfos.isEmpty()){
            MallServiceBillInfo mallServiceBillInfo = mallServiceBillInfos.get(0);
            mallServiceBillInfo.setTransferVoucherUrl(url);
            mallServiceBillInfoMapper.updateByPrimaryKey(mallServiceBillInfo);
            return;
        }
        long l = System.currentTimeMillis();


        MallServiceBillInfo db = MallServiceBillInfo.builder()
                .itemType(orderInfo.getItemType())
                .opUserId(req.getOpUserId())
                .payMethod((byte) 3)
                .createTime(l)
                .opTime(l)
                .orderNo(req.getOrderNo())
                .transferVoucherUrl(url)
                .build();

        mallServiceBillInfoMapper.insertSelective(db);
        //发送 邮件通知
        //发送 邮件
        RefundEmailParam refundEmailParam = new RefundEmailParam();
        refundEmailParam.setMerchantId(req.getMerchantId());
        refundEmailParam.setType(2);
        refundEmailParam.setItemTypeMsg(MallOrderItemEnum.forByItemType(orderInfo.getItemType().intValue()).getItemName());
        Integer remittanceAmount = orderInfo.getOrderPrice()+orderInfo.getOrderDeposit();
        refundEmailParam.setRemittanceAmount(CurrencyUtils.saas2Ctrip(remittanceAmount,2).stripTrailingZeros().toPlainString());
        eventPublisher.publishEvent(refundEmailParam);
    }
    @Override
    public MallServiceBillInfoVo corporateRemittanceInfo(Long merchantId, MallOrderQuery query) {
        // 查询订单主表
        MallServiceOrderInfoExample orderInfoExample = new MallServiceOrderInfoExample();
        orderInfoExample.createCriteria()
                .andOrderNoIn(query.getOrderNos())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<MallServiceOrderInfo> serviceOrderInfos = mallServiceOrderInfoMapper.selectByExample(orderInfoExample);
        MallServiceOrderInfo orderInfo = serviceOrderInfos.stream()
                .filter(Objects::nonNull)
                .findFirst().orElse(null);



        MallServiceBillInfoExample example = new MallServiceBillInfoExample();
        example.createCriteria().andOrderNoIn(query.getOrderNos())
                .andItemTypeEqualTo(query.getItemType().byteValue());
        List<MallServiceBillInfo> mallServiceBillInfos = mallServiceBillInfoMapper.selectByExample(example);
        Optional<MallServiceBillInfoVo> voOpt = mallServiceBillInfos.stream().findFirst().map(m -> {
            MallServiceBillInfoVo vo = new MallServiceBillInfoVo();
            BeanUtils.copyProperties(m, vo);
            vo.setPaymentDepositAmount(orderInfo.getOrderDeposit().longValue());
            vo.setPaymentThaliAmount(orderInfo.getOrderPrice().longValue());
            vo.setTransferVoucherUrl(addFilePrivatePrefix(m.getTransferVoucherUrl()));
            vo.setOrderStatus(orderInfo.getOrderStatus());
            return vo;
        });

        MallServiceBillInfoVo other = new MallServiceBillInfoVo();
        other.setOrderStatus(orderInfo.getOrderStatus());
        other.setItemType(orderInfo.getItemType());
        other.setOrderNo(other.getOrderNo());
        other.setPayMethod((byte)1);
        other.setPaymentThaliAmount(orderInfo.getOrderPrice().longValue());
        other.setPaymentDepositAmount(orderInfo.getOrderDeposit().longValue());
        return voOpt.orElse(other);
    }

    @Override
    public void corporateRemittanceApprove(MallServiceBillInfoReq req) {
        //更新订单状态
        this.updateOrderStatus(null,null, req.getOrderNo(), req.getOrderStatus(), req.getOpUserId(),(byte)3);
    }

}
