package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ql.dto.ApiResultResp;
import com.ql.enums.ThirdPayEnum;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInfoStatusCallBackReq;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInspectionReq;
import com.ql.rent.api.aggregate.remote.wk.dto.SaasOrderCallbackResp;
import com.ql.rent.common.FileUploader;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.slave.trade.OrderInfoSlaveMapper;
import com.ql.rent.dao.trade.*;
import com.ql.rent.dto.trade.PickReturnTimeTileDTO;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.enums.vehicle.VehicleStatusEnum;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.pay.ThirdPayQuery;
import com.ql.rent.param.price.RentKeyQuery;
import com.ql.rent.param.trade.AdditionalPickReturnAttParam;
import com.ql.rent.param.trade.DepositParam;
import com.ql.rent.param.trade.VehicleIllegalOrderParam;
import com.ql.rent.param.trade.VehiclePickReturnParam;
import com.ql.rent.param.vehicle.VehicleFreeParam;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.service.pay.IThirdPayService;
import com.ql.rent.service.price.AccessoryInventoryService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.bill.ThirdPayExtraVO;
import com.ql.rent.vo.bill.ThirdPayFeeItemVO;
import com.ql.rent.vo.bill.ThirdPayVO;
import com.ql.rent.vo.price.RentUndepostitVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleModelVO;
import com.ql.rent.vo.vehicle.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ql.rent.service.trade.IVehicleReturnExpenseItemService.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc
 * @time 2022-10-26 21:56
 */
@Service
@Slf4j
public class VehiclePickReturnServiceImpl implements IVehiclePickReturnService {

    @Resource
    private VehiclePickReturnMapper vehiclePickReturnMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private IOrderService orderService;
    @Resource
    private VehiclePickReturnAttMapper vehiclePickReturnAttMapper;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;
    @Resource
    private IVehicleDamageOrderService vehicleDamageOrderService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IOrderDepositService orderDepositService;
    @Resource
    private IRentMainService rentMainService;
    @Resource
    private IVehicleBusyService vehicleBusyService;
    @Resource
    private IOrderSnapshotService orderSnapshotService;
    @Resource
    private OrderComponent orderComponent;
    @Resource
    private IOrderBillService orderBillService;
    @Resource
    private IVehiclePickDriverService vehiclePickDriverService;
    @Resource
    private IOrderInspectionService orderInspectionService;
    @Resource
    private OrderMsgProducer orderMsgProducer;
    @Resource
    private PlatformBiz platformBiz;
    @Resource
    private IOrderContractService orderContractService;
    @Resource
    private OrderSnapshotMapper orderSnapshotMapper;
    @Resource
    private OrderDiscountMapper orderDiscountMapper;
    @Resource
    private IVehicleModelService iVehicleModelService;
    @Resource
    private IVehicleIllegalContractService vehicleIllegalContractService;
    @Resource
    private EtcDeviceService etcDeviceService;
    @Resource
    private AccessoryInventoryService accessoryInventoryService;
    @Resource
    private IOrderReconciliationService orderReconciliationService;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private OrderInfoSlaveMapper orderInfoSlaveMapper;
    @Resource
    private IOrderSlaveService orderSlaveService;

    @Resource
    private IThirdPayService thirdPayService;


    /**
     * 临时的事务问题解决方案：
     * 1、先把所有的业务异常（包括订单、车辆状态）进行check，见方法checkUpdateVehicleStatusWhenPick
     * 2、写trade库 -》 请求第三方（如果需要） -》写vehicle库
     * 3、
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    public Result<Integer> pickUpVehicle(VehiclePickReturnParam param, LoginVo loginVo) {
        log.info("订单取车开始 param:{}, opUserId:{}, merchantId:{}",
            JSON.toJSONString(param), loginVo.getUserId(), loginVo.getMerchantId());
        if (param == null || param.getOrderId() == null || param.getPrTime() == null
            || param.getMileage() == null || param.getOilLiter() == null) {
            return ResultUtil.failResult("参数错误");
        }
        if (orderComponent.orderLocked(param.getOrderId(), 2L)) {
            return ResultUtil.failResult("订单更新中，请稍后再试");
        }
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(param.getOrderId());
        if (orderInfo == null) {
            return ResultUtil.failResult("未查询到对应的订单信息，请刷新页面");
        }
        if (!OrderStatusEnum.isScheduled(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前订单状态无法取车");
        }
        if (orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue())) {
            return ResultUtil.failResult("自助取还订单不可使用商家取车");
        }

        Long merchantId = loginVo.getMerchantId();
        Long opUserId = loginVo.getUserId();
        if (!merchantId.equals(orderInfo.getMerchantId())) {
            return ResultUtil.failResult(ResultEnum.e007);
        }

        // check一下 三方平台对取车的限制
        this.checkPickUpRuleForThird(orderInfo, param);
        // 判断当前是否可以取车（包括订单状态 和 车辆状态都预先check，后续直接写库）
        this.checkUpdateVehicleStatusWhenPick(orderInfo);
        // 操作押金
        DepositParam depositParam = param.getDeposit();
        Result<DepositVO> payDepositResult =
            orderDepositService.payDeposit(orderInfo.getId(), depositParam, opUserId);
        if (!payDepositResult.isSuccess()) {
            return ResultUtil.failResult(payDepositResult.getMessage());
        }

        // 记录订单来源
        Byte orderSource = OrderSourceEnum.transferCtripResale(orderInfo.getOrderSource());
        VehiclePickReturn vehiclePickReturn = new VehiclePickReturn();
        BeanUtils.copyProperties(param, vehiclePickReturn);
        vehiclePickReturn.setPrType(VehiclePickReturnEnum.PICK.getType());
        vehiclePickReturn.setDeductionPayType((byte)0);
        vehiclePickReturn.setRefundPayType((byte)0);
        this.savePickReturn(vehiclePickReturn, param.getAttList(), param.getContract(), orderInfo, opUserId);
        // 保存验车单
        this.saveVehicleInspection(param.getInspection(), orderInfo.getId(),
            VehiclePickReturnEnum.PICK.getType(), opUserId);

        // 如果选择附加产品
        if (CollectionUtils.isNotEmpty(param.getAddServiceIdList()) || CollectionUtils.isNotEmpty(param.getInsuranceServiceIdList())) {
            orderService.addOrderAddedServiceItem(orderInfo.getId(), param.getAddServiceIdList(), param.getInsuranceServiceIdList(), opUserId, param.getPayKind());
        }
        // 修改订单状态
        orderService.updateOrderStatus(orderInfo.getId(), OrderStatusEnum.PICKED_UP.getStatus(), opUserId);
        // 保存取车司机
        vehiclePickDriverService.saveForPickReturn(orderInfo.getId(), orderInfo.getPickupStoreId(), opUserId, VehiclePickReturnEnum.PICK.getType());

        // 回调三方平台订单状态
        if (StringUtils.isNotBlank(orderInfo.getSourceOrderId())) {
            OrderInfoStatusCallBackReq thirdRequest = new OrderInfoStatusCallBackReq();
            // 携程定制化字段
            thirdRequest.setActualPickupTime(
                DateUtil.getFormatDateStr(vehiclePickReturn.getPrTime(), DateUtil.yyyyMMddHHmmss));
            thirdRequest.setOperateSerialNumber(
                orderInfo.getId() + "-" +  DateUtil.getFormatDateStr(vehiclePickReturn.getPrTime(), DateUtil.yyyyMMddHHmm));
            thirdRequest.setCtripOrderId(orderInfo.getSourceOrderId());

            // 哈啰/飞猪/悟空
            thirdRequest.setOrderNo(String.valueOf(orderInfo.getId()));
            thirdRequest.setThirdOrderNo(orderInfo.getSourceOrderId());
            thirdRequest.setPickUpTime(
                DateUtil.getFormatDateStr(vehiclePickReturn.getPrTime(), DateUtil.yyyyMMddHHmmss));
            thirdRequest.setStatusCode("pickup");
            // 悟空定制字段
            thirdRequest.setFuel(this.getFule(param.getOilLiter(), param.getMaxOilLiter()));
            thirdRequest.setMiles(param.getMileage());
            DepositVO deposit = payDepositResult.getModel();
            if (deposit.getIllegalOrderDepositAmount() != null) {
                thirdRequest.setIllegalDepositAmount(Long.valueOf(deposit.getIllegalOrderDepositAmount()));
            }
            if (deposit.getVehicleDepositAmount() != null) {
                thirdRequest.setDepositAmount(Long.valueOf(deposit.getVehicleDepositAmount()));
            }
            thirdRequest.setOrderPickupTime(DateUtil.getFormatDateStr(orderInfo.getPickupDate(),  DateUtil.yyyyMMddHHmmss));
            thirdRequest.setLicense(orderInfo.getVehicleNo());
            thirdRequest.setOperatorName(loginVo.getName());

            // 图片
            List<OrderInspectionReq> inspectionUrls = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(param.getAttList())) {
                for (VehiclePickReturnAttVO vo : param.getAttList()) {
                    OrderInspectionReq req = new OrderInspectionReq();
                    req.setUrl(FileUploader.addFilePrivatePrefix(vo.getAttUrl()));
                    req.setType(vo.getAttType().intValue());
                    inspectionUrls.add(req);
                }
            }
            thirdRequest.setUrlList(inspectionUrls);
            platformBiz.pickUp(thirdRequest, orderSource, orderInfo.getMerchantId());
        }

        // 修改车辆状态为租赁中
        vehicleInfoService.pickUpVehicle(vehiclePickReturn.getVehicleId(), opUserId);
        log.info("订单取车成功 orderId:{}, 取车车辆id:{}", param.getOrderId(), vehiclePickReturn.getVehicleId());

        this.transactionSyncAfterPickReturn(param, loginVo,
            OrderMsgTypeEnum.PICKUP, VehiclePickReturnEnum.PICK.getType(), vehiclePickReturn.getPrTime(), orderInfo.getLastReturnDate());
        return ResultUtil.successResult(1);
    }

    private void transactionSyncAfterPickReturn(VehiclePickReturnParam param, LoginVo loginVo,
                                                OrderMsgTypeEnum orderMsgTypeEnum, Byte prType, Date realPickTime, Date endTime) {
        // 注册事务同步回调，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 在事务提交后异步调用消息发送
                orderMsgProducer.sendOrderMsg(param.getOrderId(), orderMsgTypeEnum);
                try {
                    // 如果需要，推送线下合同给三方平台
                    orderContractService.postVehiclePickReturn(param, prType, loginVo);
                } catch (Exception e) {
                    log.error("上传合同到三方失败",e);
                }
                if (VehiclePickReturnEnum.PICK.getType().equals(prType)) {
                    // 违章转移录入合同
                    vehicleIllegalContractService.asyncSubmitContract(param.getOrderId(), realPickTime, endTime);
                } else {
                    // 违章转移录入合同
                    vehicleIllegalContractService.asyncAbortContractByOrderId(param.getOrderId());
                    // 模拟消息，异步创建对账明细和收支明细
                    asyncPromiseExecutor.execute(() ->
                        orderReconciliationService.createReconciliation(param.getOrderId()));
                }
            }
        });
    }


    /**
     * 临时油量刻度
     */
    private Integer getFule(Integer oilLiter, Integer maxOilLiter) {
        if (maxOilLiter == null) {
            maxOilLiter = 20;
        }
        if (oilLiter >= maxOilLiter) {
            return 100;
        }
        return (oilLiter * 100) / maxOilLiter;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.TRADE)
    public Result<Integer> returnVehicle(VehiclePickReturnParam param, LoginVo loginVo) {
        log.info("订单还车: opUserId :{}, param :{} ", loginVo.getUserId(), JSON.toJSONString(param));
        if (param == null || param.getOrderId() == null || param.getMileage() == null
            || param.getOilLiter() == null || param.getPrTime() == null) {
            return ResultUtil.failResult("参数错误");
        }
        if (orderComponent.orderLocked(param.getOrderId(), 2L)) {
            return ResultUtil.failResult("订单更新中，请稍后再试");
        }
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(param.getOrderId());
        if (orderInfo == null) {
            return ResultUtil.failResult("未查询到对应的订单信息，请刷新页面");
        }

        Long merchantId = loginVo.getMerchantId();
        Long opUserId = loginVo.getUserId();
        if (!merchantId.equals(orderInfo.getMerchantId())) {
            return ResultUtil.failResult(ResultEnum.e007);
        }
        // 判断订单是否可在saas履约
        this.checkReturnRuleForThird(orderInfo, param);
        if (OrderStatusEnum.isReturn(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前车辆已还车，无法还车");
        }
        if (!OrderStatusEnum.isPickedUp(orderInfo.getOrderStatus())) {
            return ResultUtil.failResult("当前订单状态状态无法还车");
        }

        if (orderInfo.getSelfPrOrder().equals(SelfPrOrderEnum.SELF_PR.getValue())) {
            return ResultUtil.failResult("自助取还订单不可使用该还车，如需商家帮还车，请使用确认还车");
        }

        VehiclePickReturnExample example = new VehiclePickReturnExample();
        example.createCriteria().andOrderIdEqualTo(param.getOrderId())
            .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType());
        List<VehiclePickReturn> list = vehiclePickReturnMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return ResultUtil.failResult("当前车辆未取车，无法还车");
        }
        // 悟空订单特殊检查
        if (StringUtils.isNotBlank(orderInfo.getSourceOrderId())
            && OrderSourceEnum.WUKONG.getSource().equals(orderInfo.getOrderSource())
            && param.getMileage() < list.get(0).getMileage()) {
            return ResultUtil.failResult("当前车辆里程小于于取车时的车辆里程，请检查");
        }

        Byte orderSource = OrderSourceEnum.transferCtripResale(orderInfo.getOrderSource());
        // 是否要更改 车辆状态到未租赁
        boolean updateVehicleStatus = orderComponent.checkVehicleOrderLeaveUnused(
            orderInfo.getVehicleId(), orderInfo.getId(), orderInfo.getMerchantId());
        VehiclePickReturn vehiclePickReturn = new VehiclePickReturn();
        BeanUtils.copyProperties(param, vehiclePickReturn);
        vehiclePickReturn.setPrType(VehiclePickReturnEnum.RETURN_BACK.getType());
        this.buildRefundAndDeductionType(orderInfo, vehiclePickReturn, param);
        // 查询押金数据
        Result<DepositVO> depositResult = orderDepositService.getByOrderId(orderInfo.getId());

        // todo 目前只针对小程序订单，check退扣金额，其他渠道不清楚用户实付
        this.checkReturnAmount(param, orderInfo, depositResult.getModel());

        this.savePickReturn(vehiclePickReturn, param.getAttList(), param.getContract(), orderInfo, opUserId);
        param.setId(vehiclePickReturn.getId());

        // 保存验车单
        this.saveVehicleInspection(param.getInspection(), orderInfo.getId(),
            VehiclePickReturnEnum.PICK.getType(), opUserId);

        // 完成订单
        orderService.updateOrderStatus(orderInfo.getId(), OrderStatusEnum.RETURNED.getStatus(), opUserId);
        // 2、生成取还车扣、退费记录 和 还车司机
        Result<Integer> result = vehicleReturnExpenseItemService.saveVehicleReturnExpenseItemWhenReturn(param, opUserId);
        log.info("保存取还车明细 result:{}", JSON.toJSONString(result));
        vehiclePickDriverService.saveForPickReturn(orderInfo.getId(), orderInfo.getReturnStoreId(), opUserId, VehiclePickReturnEnum.RETURN_BACK.getType());
        // 3、如有车损 保存车损记录
        this.handlerDamageAndIllegalOrderWhenReturn(param, orderInfo, depositResult.getModel(), opUserId);
        // Api下单 或者 铁行订单 进行抽佣
        if (StringUtils.isNotBlank(orderInfo.getSourceOrderId())) {
            if (!orderComponent.isReptileOrder(orderInfo) || OrderSourceEnum.TIE_XING.getSource().equals(orderInfo.getOrderSource())) {
                Long returnDeductionAmount = this.getReturnDeductionAmount(param);
                orderBillService.createOrderBill(orderInfo.getId(), returnDeductionAmount);
            }
        }
        if (StringUtils.isNotBlank(orderInfo.getSourceOrderId())) {
            // 携程字段
            OrderInfoStatusCallBackReq finishedReq;
            if (OrderSourceEnum.CTRIP.getSource().equals(orderSource)) {
                finishedReq = orderComponent.getOrderInfoCallbackStatusFinishedReq(param, orderInfo, list);
            } else {
                finishedReq = new OrderInfoStatusCallBackReq();
            }

            // 平台通用字段
            finishedReq.setOrderNo(String.valueOf(orderInfo.getId()));
            finishedReq.setThirdOrderNo(orderInfo.getSourceOrderId());
            finishedReq.setPickUpTime(
                DateUtil.getFormatDateStr(list.get(0).getPrTime(), DateUtil.yyyyMMddHHmmss));
            finishedReq.setDropOffTime(
                DateUtil.getFormatDateStr(vehiclePickReturn.getPrTime(), DateUtil.yyyyMMddHHmmss));
            finishedReq.setStatusCode("completed");
            finishedReq.setFuel(this.getFule(param.getOilLiter(), param.getMaxOilLiter()));
            finishedReq.setMiles(param.getMileage());
            finishedReq.setOrderPickupTime(DateUtil.getFormatDateStr(orderInfo.getPickupDate(),  DateUtil.yyyyMMddHHmmss));
            // 实际还车时间在订单还车时间之前
            if (vehiclePickReturn.getPrTime().getTime() < orderInfo.getLastReturnDate().getTime()) {
                finishedReq.setOrderReturnTime(finishedReq.getDropOffTime());
            } else {
                finishedReq.setOrderReturnTime(DateUtil.getFormatDateStr(orderInfo.getLastReturnDate(), DateUtil.yyyyMMddHHmmss));
            }
            // 图片
            List<OrderInspectionReq> inspectionUrls = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(param.getAttList())) {
                for (VehiclePickReturnAttVO vo : param.getAttList()) {
                    OrderInspectionReq req = new OrderInspectionReq();
                    req.setUrl(FileUploader.addFilePrivatePrefix(vo.getAttUrl()));
                    req.setType(vo.getAttType().intValue());
                    inspectionUrls.add(req);
                }
            }
            finishedReq.setUrlList(inspectionUrls);
            finishedReq.setOperatorName(loginVo.getName());
            ApiResultResp resultResp = platformBiz.returnVehicle(finishedReq, orderSource, orderInfo.getMerchantId(), param, depositResult.getModel());
            this.handleResultResp(orderInfo.getId(), orderSource, vehiclePickReturn, resultResp);
        }

        // 提前还车修改库存
        long returnTime = orderInfo.getLastReturnDate().getTime();
        boolean returnEarly = returnTime > param.getPrTime().getTime();
        Result<Integer> vehicleInfoResult = vehicleInfoService
            .updateWhenReturnVehicle(vehiclePickReturn.getVehicleId(), updateVehicleStatus, param.getMileage(),
                opUserId, returnEarly, orderInfo.getId(), param.getPrTime());
        if (ResultUtil.isResultNotSuccess(vehicleInfoResult)) {
            throw new BizException(vehicleInfoResult.getMessage());
        }

        // 释放儿童座椅
        accessoryInventoryService.childrenSeatInventoryRelease(orderInfo.getPickupStoreId(), String.valueOf(orderInfo.getId()), null);
        //关闭etc订单
        log.info("关闭etc订单,merchantId{},orderId{},vehicleId{}", orderInfo.getMerchantId(), orderInfo.getId(),orderInfo.getVehicleId());
        etcDeviceService.asynchronousCancelEtcOrderTask(orderInfo.getMerchantId(), orderInfo.getId(),orderInfo.getVehicleId());
        // 结束ETC订单（擎路自营）
        etcDeviceService.asyncEndSaasEtcOrder(orderInfo.getId());
        this.transactionSyncAfterPickReturn(param, loginVo, OrderMsgTypeEnum.RETURN,
            VehiclePickReturnEnum.RETURN_BACK.getType(), null, null);
        return ResultUtil.successResult(1);
    }

    /**
     * check还车退扣金额
     * @param param
     * @param orderInfo
     * @param deposit
     */
    private void checkReturnAmount(VehiclePickReturnParam param, OrderInfo orderInfo, DepositVO deposit) {
        if (OrderSourceEnum.ALIPAY_MINI_PROGRAM.getSource().equals(orderInfo.getOrderSource())) {
            Result<OrderBillDetailVo> orderBillDetailResult = orderService.getOrderBillDetail(param.getOrderId(), null);
            if (ResultUtil.isModelNull(orderBillDetailResult)) {
                throw new BizException("费用计算错误");
            }
            OrderBillDetailVo orderBillDetail = orderBillDetailResult.getModel();
            long onlinePayAmount = 0L;

            // 1. 累加扣款项的费用 只check线上扣
            if (param.getDeductionPayType() != null && param.getDeductionPayType().intValue() == 1
                    && CollectionUtils.isNotEmpty(param.getReturnDeductionItemList())) {
                onlinePayAmount += param.getReturnDeductionItemList().stream()
                        .mapToLong(VehicleReturnExpenseItemVO::getExpenseAmount)
                        .sum();
            }

            if (param.getDamageOrder() != null && deposit.getDepositPayType() != null && deposit.getDepositAmount() != null && deposit.getDepositPayType() == 3) {
                // 2. 累加车损中的费用
                onlinePayAmount += Stream.of(
                                param.getDamageOrder().getDepreciationFee(),
                                param.getDamageOrder().getOtherFee(),
                                param.getDamageOrder().getOutageFee(),
                                param.getDamageOrder().getRepairFee()
                        )
                        .filter(Objects::nonNull)
                        .mapToLong(Long::longValue)
                        .sum();
                // 扣款总金额不能大于剩余冻结总金额
                if (onlinePayAmount > deposit.getDepositAmount().longValue()) {
                    throw new BizException("扣款总金额不能大于剩余押金冻结总金额" + deposit.getDepositAmount() / 100.0 + "元");
                }
            }
            if (param.getRefundPayType() != null && param.getRefundPayType().intValue() == 1 && CollectionUtils.isNotEmpty(param.getReturnRefundItemList())) {
                int onlineRefundAmount = 0;
                for (VehicleReturnExpenseItemVO vehicleReturnExpenseItemVO : param.getReturnRefundItemList()) {
                    onlineRefundAmount += Math.toIntExact(vehicleReturnExpenseItemVO.getExpenseAmount());
                }
                // 退款总金额不能大于线上支付总金额
                if (onlineRefundAmount > orderBillDetail.getOnlinePayAmount()) {
                    throw new BizException("退款总金额不能大于线上支付总金额" + orderBillDetail.getOnlinePayAmount() / 100.0 + "元");
                }
            }
        }
    }

    /**
     * 设置取还车记录 扣款/退款方式。
     * 悟空订单直接认为有上线扣款/退款。
     * 其他订单，根据用户输入设置
     * @param param
     * @param vehiclePickReturn
     * @param orderInfo
     */
    private void buildRefundAndDeductionType(OrderInfo orderInfo, VehiclePickReturn vehiclePickReturn,
                                             VehiclePickReturnParam param) {
        if (OrderSourceEnum.WUKONG.getSource().equals(orderInfo.getOrderSource())
            && StringUtils.isNotBlank(orderInfo.getSourceOrderId())) {
            vehiclePickReturn.setRefundPayType(OrderDeductionTypeEnum.PLATFORM_PROXY.getType());
            vehiclePickReturn.setDeductionPayType(OrderDeductionTypeEnum.PLATFORM_PROXY.getType());
            return;
        }
        vehiclePickReturn.setRefundPayType(CollectionUtils.isEmpty(param.getReturnRefundItemList()) ?
            (byte)0 : vehiclePickReturn.getRefundPayType());
        vehiclePickReturn.setDeductionPayType(
            CollectionUtils.isEmpty(param.getReturnDeductionItemList()) ?
                (byte)0 : vehiclePickReturn.getDeductionPayType());
    }

    private Long getReturnDeductionAmount(VehiclePickReturnParam param) {
        long amount = 0L;
        // 存在线上扣款时计算
        if (CollectionUtils.isNotEmpty(param.getReturnDeductionItemList()) && OrderDeductionTypeEnum.isPlatformProxy(param.getDeductionPayType())) {
            long deductAmount = param.getReturnDeductionItemList().stream()
                .map(VehicleReturnExpenseItemVO::getExpenseAmount).filter(Objects::nonNull)
                .mapToLong(Long::valueOf).sum();
            amount += deductAmount;
        }
        return amount;
    }

    /**
     * 处理还车的响应
     * @param orderId
     * @param orderSource
     * @param resultResp
     */
    private void handleResultResp(Long orderId, Byte orderSource,
                                  VehiclePickReturn vehiclePickReturn, ApiResultResp resultResp) {
        if (OrderSourceEnum.WUKONG.getSource().equals(orderSource)) {
            if (resultResp.success() && resultResp.getData() != null) {
                settlementOrder(orderId, vehiclePickReturn, JSON.toJSONString(resultResp.getData()));
            }
        }
    }

    @Override
    public void settlementOrder(Long orderId, String settlementData) {
        VehiclePickReturnExample vehiclePickReturnExample = new VehiclePickReturnExample();
        vehiclePickReturnExample.createCriteria().andOrderIdEqualTo(orderId)
                .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> vehiclePickReturnList = vehiclePickReturnMapper.selectByExample(vehiclePickReturnExample);
        if (CollectionUtils.isEmpty(vehiclePickReturnList)) {
            log.warn("悟空还车返回, orderId={}, 没有找到还车记录", orderId);
            return;
        }
        VehiclePickReturn vehiclePickReturn = vehiclePickReturnList.get(0);
        settlementOrder(orderId, vehiclePickReturn, settlementData);
    }


    public void settlementOrder(Long orderId, VehiclePickReturn vehiclePickReturn, String dataJson) {
        OrderSnapshot record = new OrderSnapshot();
        record.setOrderId(orderId);
        record.setSnapshotType(OrderSnapshotTypeEnum.PLATFROM_BILL.getType());
        record.setSourceId(0L);
        record.setOrderType((byte)0);
        record.setContent(dataJson);
        record.setOpTime(System.currentTimeMillis());
        record.setCreateTime(record.getOpTime());
        SaasOrderCallbackResp saasOrderCallbackResp = JSON.parseObject(dataJson, SaasOrderCallbackResp.class);
        orderSnapshotMapper.insertSelective(record);
        log.info("悟空还车返回, orderId={}, data={}, saasOrderCallbackResp={}", orderId, dataJson, JSON.toJSONString(saasOrderCallbackResp));
        if (saasOrderCallbackResp != null) {

            BigDecimal multiply = BigDecimal.valueOf(100);

            // 先平优惠
            Map<Long, List<OrderDiscount>> orderDiscountMap = orderComponent.getDiscountMap(Arrays.asList(orderId), null);
            if (CollectionUtils.isNotEmpty(orderDiscountMap.get(orderId))) {
                // 悟空的优惠
                BigDecimal couponPayAmountDecimal = saasOrderCallbackResp.getCouponPayAmount();
                long couponPayAmount = couponPayAmountDecimal != null ? couponPayAmountDecimal.multiply(multiply).longValue() : 0;

                // 悟空的活动
                BigDecimal discountAmountDecimal = saasOrderCallbackResp.getDiscountAmount();
                long discountAmount = couponPayAmountDecimal != null ? discountAmountDecimal.multiply(multiply).longValue() : 0;

                // 悟空优惠总额
                Integer wkDiscountAmount = Math.toIntExact(couponPayAmount + discountAmount);
                Integer saasDiscountAmount = 0;

                List<OrderDiscount> orderDiscounts = orderDiscountMap.get(orderId);
                for (OrderDiscount orderDiscount : orderDiscounts) {
                    if (orderDiscount.getDiscountKind().intValue() == OrderDiscountKindEnum.ACTIVITY.getType().intValue()
                            && discountAmount != orderDiscount.getMerchantAssumeAmount().intValue()) {
                        saasDiscountAmount = saasDiscountAmount + orderDiscount.getMerchantAssumeAmount();
                        orderDiscount.setMerchantAssumeAmount(Math.toIntExact(discountAmount));
                        orderDiscount.setDiscountAmount(orderDiscount.getMerchantAssumeAmount());
                        orderDiscountMapper.updateByPrimaryKeySelective(orderDiscount);

                        log.info("活动金额不等, 替换为还车时的活动金额, orderDiscount={}", JSON.toJSONString(orderDiscount));
                    }

                    if (orderDiscount.getDiscountKind().intValue() == OrderDiscountKindEnum.COUPON.getType().intValue()
                            && couponPayAmount != orderDiscount.getMerchantAssumeAmount().intValue()) {
                        saasDiscountAmount = saasDiscountAmount + orderDiscount.getMerchantAssumeAmount();
                        orderDiscount.setMerchantAssumeAmount(Math.toIntExact(couponPayAmount));
                        orderDiscount.setDiscountAmount(orderDiscount.getMerchantAssumeAmount());
                        orderDiscountMapper.updateByPrimaryKeySelective(orderDiscount);
                        log.info("优惠金额不等, 替换为还车时的优惠金额, orderDiscount={}", JSON.toJSONString(orderDiscount));
                    }
                }

                if (wkDiscountAmount != saasDiscountAmount.intValue()) {
                    OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
                    log.info("活动金额不等, 悟空总优惠={}, saas总优惠={}, 订单实付金额={}", wkDiscountAmount, saasDiscountAmount, orderInfo.getPayAmount());
                    orderInfo.setPayAmount(orderInfo.getPayAmount() - (wkDiscountAmount - saasDiscountAmount.intValue()));
                    log.info("活动金额不等, 悟空总优惠={}, saas总优惠={}, 订单实付金额更新为={}", wkDiscountAmount, saasDiscountAmount, orderInfo.getPayAmount());
                    orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
                }
            }

            Result<OrderBillDetailVo> billResult = orderService.getOrderBillDetail(orderId, null);
            log.info("获取订单账单详细orderId={}, billResult={}", orderId, JSON.toJSONString(billResult));
            OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
            if (billResult.getModel() != null) {
                Integer mainOrderPayAmount = billResult.getModel().getMainOrderPayAmount();
                log.info("更新主单实收, orderId={}, 新的实收={}, saas原订单实收={}", orderId, mainOrderPayAmount, orderInfo.getPayAmount());
                orderInfo.setPayAmount(mainOrderPayAmount);
                orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
            }

            Result<Map<Long, OrderFeeVo>> result = orderService.getOrderExcelListById(Arrays.asList(orderId));
            OrderFeeVo orderFeeVo = result.getModel().get(orderId);
            long onlineTotalAmount = orderFeeVo.getOnlineTotalAmount().longValue() * 100;
            log.info("订单SaaS费用信息， orderId={}, 线上总实收={}", orderId, onlineTotalAmount);

            // saas的线上租车费（包含续租和保险）
            long saasRentTotalAmount = (orderFeeVo.getOnlineRentAmount().longValue() + orderFeeVo.getOnlineRerentAmount().longValue()
                        + orderFeeVo.getOnlineBaseInsuranceAmount().longValue() + orderFeeVo.getOnlineExclusiveInsuranceAmount().longValue()
                        + orderFeeVo.getOnlinePremiumInsuranceAmount().longValue()) * 100;


            BigDecimal fuelAmountDecimal = saasOrderCallbackResp.getFuelAmount();
            long fuelAmount = fuelAmountDecimal != null ? fuelAmountDecimal.multiply(multiply).longValue() : 0;

            // 保险费
            BigDecimal insuranceAmountDecimal = saasOrderCallbackResp.getInsuranceAmount();
            long insuranceAmount = insuranceAmountDecimal != null ? insuranceAmountDecimal.multiply(multiply).longValue() : 0;
            BigDecimal optionalServiceAmountDecimal = saasOrderCallbackResp.getOptionalServiceAmount();
            long optionalServiceAmount = optionalServiceAmountDecimal != null ? optionalServiceAmountDecimal.multiply(multiply).longValue() : 0;
            BigDecimal rentAmountDecimal = saasOrderCallbackResp.getRentAmount();
            long rentAmount = rentAmountDecimal != null ? rentAmountDecimal.multiply(multiply).longValue() : 0;
            long rentTotalAmount = rentAmount + insuranceAmount + optionalServiceAmount;
            // 租车差额
            long rentMarginAmount = saasRentTotalAmount - rentTotalAmount;
            log.info("saas的线上租车费（算上续租和保险）={}, 悟空租车费={}, 租车差额={}", saasRentTotalAmount, rentTotalAmount, rentMarginAmount);

            if (rentMarginAmount > 0) {
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        rentMarginAmount, RETURN_EARLY_PROP, vehiclePickReturn.getOpUserId());
                log.info("租车差额大于0, 需要退款, 保存提前还车费={}", rentMarginAmount);
            }

            if (rentMarginAmount < 0) {
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        Math.abs(rentMarginAmount) , RETURN_DELAY_PROP, vehiclePickReturn.getOpUserId());
                log.info("租车差额小于0, 需要扣款, 保存延迟还车扣款费={}",  Math.abs(rentMarginAmount));
            }

            // 加油服务费
            BigDecimal refuelingAmountDecimal = saasOrderCallbackResp.getRefuelingAmount();
            long refuelingAmount = refuelingAmountDecimal != null ? refuelingAmountDecimal.multiply(multiply).longValue() : 0;
            // 以上都是悟空计算收费项，需要插入到还车收费项中
            if (fuelAmount > 0) {
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        fuelAmount, FUEL_FEE_PROP, vehiclePickReturn.getOpUserId());
            }
            if (fuelAmount < 0) {
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        -fuelAmount, RETURN_FUEL_FEE_PROP, vehiclePickReturn.getOpUserId());
            }
            if (refuelingAmount > 0) {
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        refuelingAmount + refuelingAmount, FUEL_SERVICE_FEE_PROP, vehiclePickReturn.getOpUserId());
            }

            // 悟空实收
            BigDecimal totalAmountDecimal = saasOrderCallbackResp.sumPayAmount();

            if (saasOrderCallbackResp.sumPayAmount().intValue() != saasOrderCallbackResp.sumPayAmountV3().intValue()) {
                log.info("orderId={}, 悟空对账实收v1金额不等于v1金额, v1={}, v3={}", saasOrderCallbackResp.sumPayAmount().intValue(), saasOrderCallbackResp.sumPayAmountV3().intValue());
            }

            long totalAmount = totalAmountDecimal != null ? totalAmountDecimal.multiply(multiply).longValue() : 0;
            // 差额
            long marginAmount = onlineTotalAmount - totalAmount;
            long finalMarginAmount = marginAmount - rentMarginAmount;
            log.info("订单SaaS总实收（算上保存的油电费和加油服务费）={}, 悟空总实收={}, 总差额={}, 租车差额={}, 最后差额={}",
                    onlineTotalAmount, totalAmount, marginAmount, rentMarginAmount, finalMarginAmount);

            // 如果差额大于0，要将差额改为提前还车费，如果差额小于0，说明还有退费项，将差额加到其他费
            if (finalMarginAmount > 0) {
                // 保存退款项
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                        finalMarginAmount, OTHER_PROP, vehiclePickReturn.getOpUserId());
                log.info("最后差额大于0, 需要退款, 保存其他退款费={}", finalMarginAmount);
            }
            if (finalMarginAmount < 0) {
                // 保存收款项
                vehicleReturnExpenseItemService.saveExpense(orderId, vehiclePickReturn.getId(),
                    - finalMarginAmount, OTHER_FEE_PROP, vehiclePickReturn.getOpUserId());
                log.info("最后差额小于0, 需要扣款, 保存其他扣款={}", finalMarginAmount);
            }

        }
    }

    @Override
    public Result<OrderPickReturnVO> getOrderPickReturn(Long orderId) {
        if (orderId == null) {
            return ResultUtil.failResult("参数错误");
        }
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);

        VehiclePickReturnExample pickReturnExample = new VehiclePickReturnExample();
        pickReturnExample.createCriteria().andOrderIdEqualTo(orderId);

        List<VehiclePickReturn> vehiclePickReturns = vehiclePickReturnMapper.selectByExample(pickReturnExample);
        // 封装结果
        OrderPickReturnVO result = new OrderPickReturnVO();
        result.setSourceName(OrderSourceEnum.getNameByStatus(orderInfo.getOrderSource()));
        result.setOrderId(orderId);
        result.setVehicleNo(orderInfo.getVehicleNo());
        result.setOrderStatus(orderInfo.getOrderStatus());
        result.setOrderSource(orderInfo.getOrderSource());
        result.setSourceOrderId(orderInfo.getSourceOrderId());
        for (VehiclePickReturn vehiclePickReturn : vehiclePickReturns) {
            VehiclePickReturnVO pickInfoVo = new VehiclePickReturnVO();
            BeanUtils.copyProperties(vehiclePickReturn, pickInfoVo);
            pickInfoVo.setVehicleId(orderInfo.getVehicleId());
            pickInfoVo.setPrTime(vehiclePickReturn.getPrTime());
            // 封装附件
            this.convertAtt(pickInfoVo);

            if (VehiclePickReturnEnum.isPick(vehiclePickReturn.getPrType())) {
                result.setPickupVehicleInfo(pickInfoVo);
            } else if (VehiclePickReturnEnum.isReturnBack(vehiclePickReturn.getPrType())) {
                result.setReturnVehicleInfo(pickInfoVo);
                // 已经还车的情况,查询收/退款明细
                this.assembleReturnExpenseItem(result, pickInfoVo.getId());
            }
        }

        // 查询车辆信息
        BaseVehicleModelVO baseVehicleModelVO = null;
        Result<VehicleInfoTagVO> vehicleSnapshotResult = orderSnapshotService.getOrderVehicleSnapshot(orderId);
        if (ResultUtil.isModelNotNull(vehicleSnapshotResult)) {
            VehicleInfoTagVO vehicleInfoTagVO = vehicleSnapshotResult.getModel();
            baseVehicleModelVO = vehicleInfoTagVO.getVehicleModel();
            result.setVehicleUnionName(baseVehicleModelVO.getVehicleUnionName());
            result.setVehicleName(baseVehicleModelVO.getVehicleBrandName());
            result.setCurrentMileage(vehicleInfoTagVO.getMileage());
        }
        // 查询已添加的附加服务
        Result<List<ServiceItemAmountVo>> addedServiceListResult = orderService.getAddedServiceList(orderId);
        if (ResultUtil.isModelNotNull(addedServiceListResult)) {
            result.setAddedServiceItemList(new ArrayList<>(addedServiceListResult.getModel()));
        }

        // 查询押金数据
        Result<DepositVO> depositResult = orderDepositService.getByOrderId(orderId);
        if (ResultUtil.isModelNotNull(depositResult)) {
            result.setDepositVO(depositResult.getModel());
        }
        // 是否支持 线上渠道认证免押
        Boolean ifSupportUnLineExemptCharge =
            this.queryIfSupportUnLineExemptCharge(baseVehicleModelVO, orderInfo.getPickupStoreId());
        result.setSupportUnderlineExempt(ifSupportUnLineExemptCharge);
        // 是否支持线上扣款
        result.setAllowedOnlinePay(orderComponent.getOrderAllowedOnlinePay(orderInfo));
        // 是否支持线上退款
        result.setAllowOnlineRefund(orderComponent.getOrderAllowedOnlineRefund(orderInfo));

        // 查询封装验车单数据
        Result<List<VehicleInspectionVO>> inspectionResult = orderInspectionService.getByOrderId(orderInfo.getId());
        if (ResultUtil.isModelNotNull(inspectionResult)) {
            for (VehicleInspectionVO inspection : inspectionResult.getModel()) {
                if (VehiclePickReturnEnum.isPick(inspection.getPrType())) {
                    result.setPickInspection(inspection);
                } else if (VehiclePickReturnEnum.isReturnBack(inspection.getPrType())){
                    result.setReturnInspection(inspection);
                }
            }
        }


        ThirdPayQuery query = new ThirdPayQuery();
        query.setRelationId(orderId);
        query.setRelationType(ThirdPayEnum.RelationType.RETURN_PAY.getType());
        query.setMerchantId(orderInfo.getMerchantId());
        query.setPayStatus(ThirdPayEnum.ThirdPayStatus.ALL_PAID.getStatus());
        Result<List<ThirdPayVO>> payListResult = thirdPayService.getPayDetailList(query);
        log.info("还车, 获取支付信息, query={}, payListResult={}", JSON.toJSONString(query), JSON.toJSONString(payListResult));
        if (ResultUtil.isResultSuccess(payListResult) && CollectionUtils.isNotEmpty(payListResult.getModel())) {
            Result<Map<Long, VehicleReturnExpenseItemPropVO>> mapResult = vehicleReturnExpenseItemService.getAllReturnExpenseItemProp();
            if (mapResult.isSuccess() && mapResult.getModel() != null) {
                Map<Long, VehicleReturnExpenseItemPropVO> itemPropMap = mapResult.getModel();
                List<VehicleReturnExpenseItemVO> deductionExpenseItemList = new ArrayList<>();
                for (ThirdPayVO thirdPayVO : payListResult.getModel()) {
                    ThirdPayExtraVO extraVO = thirdPayVO.getExtraVO();
                    for (ThirdPayFeeItemVO vo : extraVO.getItemList()) {
                        if (vo.getExpenseItemPropId() != null && itemPropMap.get(vo.getExpenseItemPropId()) != null) {
                            VehicleReturnExpenseItemPropVO expenseItemPropVO = itemPropMap.get(vo.getExpenseItemPropId());
                            VehicleReturnExpenseItemVO vehicleReturnExpenseItemVO = new VehicleReturnExpenseItemVO();
                            vehicleReturnExpenseItemVO.setExpenseItemPropId(vo.getExpenseItemPropId());
                            vehicleReturnExpenseItemVO.setItemName(expenseItemPropVO.getItemName());
                            vehicleReturnExpenseItemVO.setItemType(expenseItemPropVO.getItemType());
                            vehicleReturnExpenseItemVO.setReturnPaid(true);
                            vehicleReturnExpenseItemVO.setExpenseAmount(Long.valueOf(vo.getAmount()));
                            vehicleReturnExpenseItemVO.setPayNo(thirdPayVO.getPayNo());
                            vehicleReturnExpenseItemVO.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                            vehicleReturnExpenseItemVO.setPayKind(PayKindEnum.ALI_PAY_CODE.getKind());
                            deductionExpenseItemList.add(vehicleReturnExpenseItemVO);
                            log.info("还车, 支付信息匹配, vo={}, vehicleReturnExpenseItemVO={}", JSON.toJSONString(vo), JSON.toJSONString(vehicleReturnExpenseItemVO));

                        }
                    }
                }
                if (CollectionUtils.isEmpty(result.getDeductionExpenseItemList())) {
                    result.setDeductionExpenseItemList(deductionExpenseItemList);
                }
            }
        }
        log.info("取还详细, 封装结果, result={}", JSON.toJSONString(result));
        return ResultUtil.successResult(result);
    }


    @Override
    public Result<List<VehicleInfoTagVO>> listOrderAvailableVehicle(Long orderId, Long vehicleModelId, String loginName, Long merchantId) {
        if (orderId == null) {
            return ResultUtil.failResult("参数错误");
        }

        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            return ResultUtil.failResult("未查询到对应的订单");
        }
        VehicleFreeParam vehicleFreeParam = new VehicleFreeParam();
        vehicleFreeParam.setStoreId(orderInfo.getPickupStoreId());
        vehicleFreeParam.setVehicleModelId(vehicleModelId);
        vehicleFreeParam.setStartTime(orderInfo.getPickupDate().getTime());
        vehicleFreeParam.setEndTime(orderInfo.getReturnDate().getTime());
        vehicleFreeParam.setOrderId(orderId);
        Result<List<VehicleSampleVO>> vehicleFreeResult = vehicleBusyService.stockFreeVehicle(vehicleFreeParam, loginName, merchantId);
        if (ResultUtil.isModelNull(vehicleFreeResult) || CollectionUtils.isEmpty(vehicleFreeResult.getModel())) {
            return ResultUtil.successResult(Collections.emptyList());
        }
        List<Long> vehicleInfoIdList =
            vehicleFreeResult.getModel().stream().map(VehicleSampleVO::getId).collect(Collectors.toList());
        vehicleInfoIdList.removeIf(item -> (item.equals(orderInfo.getVehicleId())));

        Result<List<VehicleInfoTagVO>> listResult = vehicleInfoService.listVehicleModelAndTag(vehicleInfoIdList);
        // 如果是自助订单，自助车型，返回的车辆信息筛选为支持自助取还的车辆
        if (Objects.equals(SelfPrOrderEnum.SELF_PR.getValue(), orderInfo.getSelfPrOrder()) && Objects.nonNull(vehicleModelId)) {
            Result<VehicleModelVO> vehicleModelVOResult = iVehicleModelService.getVehicleModelBaseById(vehicleModelId);
            if (BooleanUtils.isFalse(vehicleModelVOResult.isSuccess()) || Objects.isNull(vehicleModelVOResult.getModel())) {
                return ResultUtil.failResult("车型不存在");
            }
            if (YesOrNoEnum.YES.getValue() == vehicleModelVOResult.getModel().getSelfServiceReturn()) {
                List<VehicleInfoTagVO> collect = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(listResult.getModel())) {
                    collect = listResult.getModel().stream()
                            .filter(vehicleInfoTagVO -> YesOrNoEnum.YES.getValue() == vehicleInfoTagVO.getSelfServiceReturn())
                            .collect(Collectors.toList());
                }
                return ResultUtil.successResult(collect);
            }
        }

        return listResult;
    }

    @Override
    public Result<Integer> addPickReturnAtt(AdditionalPickReturnAttParam attParam, Long opUserId) {
        if (attParam == null || opUserId == null || attParam.getOrderId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        VehiclePickReturnExample vehiclePickReturnExample = new VehiclePickReturnExample();
        vehiclePickReturnExample.createCriteria().andOrderIdEqualTo(attParam.getOrderId());
        List<VehiclePickReturn> vehiclePickAndReturn =
            vehiclePickReturnMapper.selectByExample(vehiclePickReturnExample);
        if (CollectionUtils.isEmpty(vehiclePickAndReturn)) {
            return ResultUtil.failResult("该订单未取车/还车");
        }

        List<VehiclePickReturnAtt> attList = new ArrayList<>();
        vehiclePickAndReturn.stream()
            .filter(e -> VehiclePickReturnEnum.isPick(e.getPrType())).findAny()
            .ifPresent(pick -> {
                if (CollectionUtils.isNotEmpty(attParam.getPickAttList())) {
                    for (String attUrl : attParam.getPickAttList()) {
                        VehiclePickReturnAtt pickAtt = new VehiclePickReturnAtt();
                        pickAtt.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.VIDEO.getType());
                        pickAtt.setAttUrl(FileUploader.removeFilePrefix(attUrl));
                        pickAtt.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
                        pickAtt.setVehiclePickReturnId(pick.getId());
                        pickAtt.setOrderId(attParam.getOrderId());
                        pickAtt.setPrType(VehiclePickReturnEnum.PICK.getType());
                        pickAtt.setSource(VehiclePickReturnAttEnum.AttSourceEnum.OFFLINE.getSource());
                        pickAtt.setCreateTime(System.currentTimeMillis());
                        pickAtt.setOpTime(System.currentTimeMillis());
                        pickAtt.setOpUserId(opUserId);
                        attList.add(pickAtt);
                    }
                }
            });
        vehiclePickAndReturn.stream()
            .filter(e -> VehiclePickReturnEnum.isReturnBack(e.getPrType())).findAny()
            .ifPresent(returnInfo -> {
                if (CollectionUtils.isNotEmpty(attParam.getPickAttList())) {
                    for (String attUrl : attParam.getPickAttList()) {
                        VehiclePickReturnAtt returnAtt = new VehiclePickReturnAtt();
                        returnAtt.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.VIDEO.getType());
                        returnAtt.setAttUrl(FileUploader.removeFilePrefix(attUrl));
                        returnAtt.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
                        returnAtt.setOrderId(attParam.getOrderId());
                        returnAtt.setPrType(VehiclePickReturnEnum.RETURN_BACK .getType());
                        returnAtt.setSource(VehiclePickReturnAttEnum.AttSourceEnum.OFFLINE.getSource());
                        returnAtt.setVehiclePickReturnId(returnInfo.getId());
                        returnAtt.setCreateTime(System.currentTimeMillis());
                        returnAtt.setOpTime(System.currentTimeMillis());
                        returnAtt.setOpUserId(opUserId);
                        attList.add(returnAtt);
                    }
                }
            });

        if (CollectionUtils.isNotEmpty(attList)) {
            vehiclePickReturnAttMapper.batchInsert(attList);
        }
        return ResultUtil.successResult(1);
    }

    @Override
    public Result<List<PickReturnTimeTileDTO>> listPickReturnByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return ResultUtil.successResult(Collections.emptyList());
        }
        VehiclePickReturnExample example = new VehiclePickReturnExample();
        example.createCriteria().andOrderIdIn(orderIds).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> vehiclePickReturnList = vehiclePickReturnMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(vehiclePickReturnList)) {
            return ResultUtil.successResult(Collections.emptyList());
        }

        // key为订单id value取还车信息
        Map<Long, List<VehiclePickReturn>> orderPickReturnMap = vehiclePickReturnList
            .stream().collect(Collectors.groupingBy(VehiclePickReturn::getOrderId));

        List<PickReturnTimeTileDTO> resultList = new ArrayList<>(orderPickReturnMap.size());
        for (Map.Entry<Long, List<VehiclePickReturn>> entry : orderPickReturnMap.entrySet()) {
            PickReturnTimeTileDTO timeTile = new PickReturnTimeTileDTO();
            timeTile.setOrderId(entry.getKey());
            List<VehiclePickReturn> pickReturnList = entry.getValue();
            for (VehiclePickReturn vehiclePickReturn : pickReturnList) {
                if (VehiclePickReturnEnum.PICK.getType().equals(vehiclePickReturn.getPrType())) {
                    timeTile.setPickUpId(vehiclePickReturn.getId());
                    timeTile.setPickUpdate(vehiclePickReturn.getPrTime().getTime());
                } else if (VehiclePickReturnEnum.RETURN_BACK.getType().equals(vehiclePickReturn.getPrType())) {
                    timeTile.setReturnId(vehiclePickReturn.getId());
                    timeTile.setReturnDate(vehiclePickReturn.getPrTime().getTime());
                }
            }
            resultList.add(timeTile);
        }
        return ResultUtil.successResult(resultList);
    }

    @Override
    public Result<VehicleInspectionVO> getLastVehicleInspectionVO(Long orderId) {
        if (orderId == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        Result<OrderInfoVo> orderInfoResult = orderService.getOrderInfo(orderId);
        if (ResultUtil.isModelNull(orderInfoResult)) {
            return ResultUtil.failResult(ResultEnum.e004);
        }

        VehiclePickReturnExample example = new VehiclePickReturnExample();
        example.createCriteria().andVehicleIdEqualTo(orderInfoResult.getModel().getVehicleId())
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        example.setOrderByClause(VehiclePickReturn.Column.createTime.desc() + " limit 1");
        List<VehiclePickReturn> lastPickReturns = vehiclePickReturnMapper.selectByExample(example);
        // 首次操作的车辆，需要返回默认全部完好的验车单
        if (CollectionUtils.isEmpty(lastPickReturns)) {
            return ResultUtil.successResult(orderInspectionService.emptyInspection().getModel());
        }

        VehiclePickReturn lastPickReturn = lastPickReturns.get(0);

        VehicleInspectionVO lastInspection = null;
        Result<List<VehicleInspectionVO>> lastInspectionResult =
            orderInspectionService.getByOrderId(lastPickReturn.getOrderId());
        if (ResultUtil.isModelNotNull(lastInspectionResult)) {
            List<VehicleInspectionVO> lastInspections = lastInspectionResult.getModel();
            lastInspection =
                lastInspections.stream().filter(e -> lastPickReturn.getPrType().equals(e.getPrType())).findAny()
                    .orElse(null);
        }
        return ResultUtil.successResult(lastInspection);
    }


    @Override
    public Result<List<Long>> getOrderIdsByTime(Long vehicleId, Date time) {
        VehiclePickReturnExample pickupExample = new VehiclePickReturnExample();
        pickupExample.createCriteria().andVehicleIdEqualTo(vehicleId).andPrTimeLessThanOrEqualTo(time)
                .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> pickList = vehiclePickReturnMapper.selectByExample(pickupExample);
        List<Long> pickupOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pickList)) {
            pickupOrderIdList = pickList.stream().map(c -> c.getOrderId()).collect(Collectors.toList());
        } else {
            return ResultUtil.failResult("无对应订单");
        }
        VehiclePickReturnExample returnExample = new VehiclePickReturnExample();
        returnExample.createCriteria().andVehicleIdEqualTo(vehicleId).andPrTimeGreaterThanOrEqualTo(time)
                .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> returnList = vehiclePickReturnMapper.selectByExample(returnExample);
        List<Long> returnOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(returnList)) {
            returnOrderIdList = returnList.stream().map(c -> c.getOrderId()).collect(Collectors.toList());
        } else {
            return ResultUtil.failResult("无对应订单");
        }
        if (CollectionUtils.isEmpty(pickupOrderIdList) || CollectionUtils.isEmpty(returnOrderIdList)) {
            return ResultUtil.failResult("无对应订单");
        } else {
            pickupOrderIdList.retainAll(returnOrderIdList);
            return ResultUtil.successResult(pickupOrderIdList);
        }

    }

    @Override
    public Result<List<Long>> getOrderIdByTimeV2(Long vehicleId, Date time, Long merchantId) {
        VehiclePickReturnExample pickupExample = new VehiclePickReturnExample();
        pickupExample.createCriteria().andVehicleIdEqualTo(vehicleId).andPrTimeLessThanOrEqualTo(time)
                .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> pickList = vehiclePickReturnMapper.selectByExample(pickupExample);
        List<Long> pickupOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pickList)) {
            pickupOrderIdList = pickList.stream().map(c -> c.getOrderId()).collect(Collectors.toList());
        }

        VehiclePickReturnExample returnExample = new VehiclePickReturnExample();
        returnExample.createCriteria().andVehicleIdEqualTo(vehicleId).andPrTimeGreaterThanOrEqualTo(time)
                .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehiclePickReturn> returnList = vehiclePickReturnMapper.selectByExample(returnExample);
        List<Long> returnOrderIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(returnList)) {
            returnOrderIdList = returnList.stream().map(c -> c.getOrderId()).collect(Collectors.toList());
        }

        // 取所有实际的取还车订单id
        Set<Long> allActualOrderIdList = new HashSet<>(pickupOrderIdList);
        allActualOrderIdList.addAll(returnOrderIdList);

        if (CollectionUtils.isNotEmpty(allActualOrderIdList)) {
            OrderInfoExample orderInfoExample = new OrderInfoExample();
            orderInfoExample.createCriteria().andIdIn(new ArrayList<>(allActualOrderIdList)).andOrderStatusIn(
                    Arrays.asList(OrderStatusEnum.PICKED_UP.getStatus(), OrderStatusEnum.RETURNED.getStatus()));
            Map<Long, OrderInfo> orderInfoMap = orderInfoSlaveMapper.selectByExample(orderInfoExample)
                    .stream().collect(Collectors.toMap(OrderInfo::getId, e -> e, (k1, k2) -> k1));

            // 如果能直接匹配到实际时间，则用实际,需要排除非已取车和已还车的订单
            if (CollectionUtils.isNotEmpty(pickupOrderIdList) && CollectionUtils.isNotEmpty(returnOrderIdList)) {
                List<Long> tempList = new ArrayList<>(pickupOrderIdList);
                tempList.retainAll(returnOrderIdList);
                if (CollectionUtils.isNotEmpty(tempList)) {
                    // 排除不正确订单状态
                    tempList.retainAll(orderInfoMap.keySet());
                    if (CollectionUtils.isNotEmpty(tempList)) {
                        return ResultUtil.successResult(tempList);
                    }
                }
            }

            // 如果仅有实际取车，则用实际取车+预计还车
            if (CollectionUtils.isNotEmpty(pickupOrderIdList)) {
                List<Long> tempList = new ArrayList<>(pickupOrderIdList);
                tempList = tempList.stream().filter(e -> orderInfoMap.containsKey(e) && orderInfoMap.get(e).getLastReturnDate()
                        .compareTo(time) >= 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tempList)) {
                    // 排除有实际还车的订单
                    VehiclePickReturnExample excludeExample = new VehiclePickReturnExample();
                    excludeExample.createCriteria().andOrderIdIn(tempList)
                            .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    tempList.removeAll(vehiclePickReturnMapper.selectByExample(excludeExample).stream()
                            .map(VehiclePickReturn::getOrderId).distinct().collect(Collectors.toList()));
                    if (CollectionUtils.isNotEmpty(tempList)) {
                        return ResultUtil.successResult(tempList);
                    }
                }
            }

            // 如果仅有实际还车，则用预计取车+实际还车
            if (CollectionUtils.isNotEmpty(returnOrderIdList)) {
                List<Long> tempList = new ArrayList<>(returnOrderIdList);
                tempList = tempList.stream().filter(e -> orderInfoMap.containsKey(e) && orderInfoMap.get(e).getPickupDate()
                        .compareTo(time) <= 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tempList)) {
                    // 排除有实际取车的订单
                    VehiclePickReturnExample excludeExample = new VehiclePickReturnExample();
                    excludeExample.createCriteria().andOrderIdIn(tempList)
                            .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    tempList.removeAll(vehiclePickReturnMapper.selectByExample(excludeExample).stream()
                            .map(VehiclePickReturn::getOrderId).distinct().collect(Collectors.toList()));
                    if (CollectionUtils.isNotEmpty(tempList)) {
                        return ResultUtil.successResult(tempList);
                    }
                }
            }
        }

        // 如果都没有实际取还，则用预计取还
        Result<List<OrderInfoVo>> listResult = orderSlaveService.getBaseOrderByTimeAndVehicle(merchantId, vehicleId, time);
        if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getModel())) {
            List<Long> tempList = listResult.getModel().stream().map(OrderInfoVo::getId).collect(Collectors.toList());
            // 排除有实际取还的订单
            tempList.removeAll(allActualOrderIdList);
            if (CollectionUtils.isNotEmpty(tempList)) {
                // 排除有实际取车的订单
                VehiclePickReturnExample excludeExample = new VehiclePickReturnExample();
                excludeExample.createCriteria().andOrderIdIn(tempList).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                tempList.removeAll(vehiclePickReturnMapper.selectByExample(excludeExample).stream()
                        .map(VehiclePickReturn::getOrderId).distinct().collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(tempList)) {
                    return ResultUtil.successResult(tempList);
                }
            }
        }

        return ResultUtil.failResult("无对应订单");
    }

    private Boolean queryIfSupportUnLineExemptCharge(BaseVehicleModelVO baseVehicleModel, Long storeId) {
        if (baseVehicleModel == null) {
            return null;
        }
        RentKeyQuery storeModelKey = new RentKeyQuery();
        storeModelKey.setStoreId(storeId);
        storeModelKey.setVehicleModelIdList(Collections.singletonList(baseVehicleModel.getId()));
        Result<List<RentUndepostitVo>> depositListResult =
            rentMainService.selectModelDeposit(storeModelKey);
        if (ResultUtil.isModelNull(depositListResult)) {
            return false;
        }

        List<RentUndepostitVo> depositList = depositListResult.getModel();
        return depositList.stream().anyMatch(e -> e.getChannelId() == 1L);
    }

    /**
     * 设置取还车扣、退款明细
     */
    private void assembleReturnExpenseItem(OrderPickReturnVO pickReturn, Long returnId) {
        Result<List<VehicleReturnExpenseItemVO>> itemListResult =
            vehicleReturnExpenseItemService.listReturnExpenseItem(returnId);
        if (ResultUtil.isModelNull(itemListResult) || CollectionUtils.isEmpty(itemListResult.getModel())) {
            pickReturn.setRefundExpenseItemList(Collections.emptyList());
            pickReturn.setDeductionExpenseItemList(Collections.emptyList());
            return;
        }
        List<VehicleReturnExpenseItemVO> itemList = itemListResult.getModel();
        pickReturn.setRefundExpenseItemList(
            itemList.stream().filter(e -> e.getItemType() == 2).collect(Collectors.toList()));
        pickReturn.setDeductionExpenseItemList(
            itemList.stream().filter(e -> e.getItemType() == 1).collect(Collectors.toList()));
    }


   /**
    * 保存取还车附件
    * @param vehiclePickReturn 取还车记录
    * @param attParamList      取还车附件
    * @param contractParam     电子合同参数
    * @param orderInfo         订单
    * @param opUserId          操作人id
    * <AUTHOR>
    */
    private void savePickReturn(VehiclePickReturn vehiclePickReturn, List<VehiclePickReturnAttVO> attParamList,
                                VehiclePickReturnParam.PickReturnContractParam contractParam,
                                OrderInfo orderInfo, Long opUserId) {
        vehiclePickReturn.setVehicleId(orderInfo.getVehicleId());
        vehiclePickReturn.setOrderId(orderInfo.getId());
        vehiclePickReturn.setLastVer(1);
        vehiclePickReturn.setOpUserId(opUserId);
        vehiclePickReturn.setMerchantId(orderInfo.getMerchantId());
        vehiclePickReturn.setDeleted(YesOrNoEnum.NO.getValue());
        vehiclePickReturn.setCreateTime(System.currentTimeMillis());
        vehiclePickReturn.setOpTime(System.currentTimeMillis());
        if (vehiclePickReturnMapper.insertSelective(vehiclePickReturn) != 1) {
            throw new BizException("取还车失败");
        }

        List<VehiclePickReturnAtt> insertList = new ArrayList<>();
        // 保存验车视频图片附件
        if (CollectionUtils.isNotEmpty(attParamList)) {
            for (VehiclePickReturnAttVO attParam : attParamList) {
                VehiclePickReturnAtt vehiclePickReturnAtt = new VehiclePickReturnAtt();
                vehiclePickReturnAtt.setAttUrl(FileUploader.removeFilePrefix(attParam.getAttUrl()));
                vehiclePickReturnAtt.setVehiclePickReturnId(vehiclePickReturn.getId());
                vehiclePickReturnAtt.setOrderId(vehiclePickReturn.getOrderId());
                vehiclePickReturnAtt.setPrType(vehiclePickReturn.getPrType());
                vehiclePickReturnAtt.setSource(VehiclePickReturnAttEnum.AttSourceEnum.OFFLINE.getSource());
                vehiclePickReturnAtt.setCreateTime(System.currentTimeMillis());
                vehiclePickReturnAtt.setAttType(attParam.getAttType());
                vehiclePickReturnAtt.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
                vehiclePickReturnAtt.setOpTime(System.currentTimeMillis());
                vehiclePickReturnAtt.setOpUserId(opUserId);
                insertList.add(vehiclePickReturnAtt);
            }
        }
        // 保存合同图片和验车单图片
        if (contractParam != null) {
            if (StringUtils.isNotBlank(contractParam.getContractUrl())) {
                VehiclePickReturnAtt contract = new VehiclePickReturnAtt();
                contract.setAttUrl(FileUploader.removeFilePrefix(contractParam.getContractUrl()));
                contract.setVehiclePickReturnId(vehiclePickReturn.getId());
                contract.setOrderId(vehiclePickReturn.getOrderId());
                contract.setPrType(vehiclePickReturn.getPrType());
                contract.setSource(VehiclePickReturnAttEnum.AttSourceEnum.OFFLINE.getSource());
                contract.setCreateTime(System.currentTimeMillis());
                contract.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.PIC.getType());
                contract.setOpTime(System.currentTimeMillis());
                contract.setOpUserId(opUserId);
                contract.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.CONTRACT.getType());
                insertList.add(contract);
            }
            if (StringUtils.isNotBlank(contractParam.getInspectionUrl())) {
                VehiclePickReturnAtt inspection = new VehiclePickReturnAtt();
                inspection.setAttUrl(FileUploader.removeFilePrefix(contractParam.getInspectionUrl()));
                inspection.setVehiclePickReturnId(vehiclePickReturn.getId());
                inspection.setOrderId(vehiclePickReturn.getOrderId());
                inspection.setPrType(vehiclePickReturn.getPrType());
                inspection.setSource(VehiclePickReturnAttEnum.AttSourceEnum.OFFLINE.getSource());
                inspection.setCreateTime(System.currentTimeMillis());
                inspection.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.PIC.getType());
                inspection.setOpTime(System.currentTimeMillis());
                inspection.setOpUserId(opUserId);
                inspection.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.INSPECTION.getType());
                insertList.add(inspection);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            vehiclePickReturnAttMapper.batchInsert(insertList);
        }
    }

    /**
     * 封装取还车附件
     */
    private void convertAtt(VehiclePickReturnVO pickReturn) {
        VehiclePickReturnAttExample attExample = new VehiclePickReturnAttExample();
        attExample.createCriteria().andOrderIdEqualTo(pickReturn.getOrderId()).andPrTypeEqualTo(pickReturn.getPrType());
        List<VehiclePickReturnAtt> attList = vehiclePickReturnAttMapper.selectByExample(attExample);

        PickReturnContractVO contract = new PickReturnContractVO();
        List<VehiclePickReturnAttVO> attVoList = new ArrayList<>();

        List<String> contractList = new ArrayList<>(2);
        List<String> inspectionUrls = new ArrayList<>(2);
        if (CollectionUtils.isNotEmpty(attList)) {
            for (VehiclePickReturnAtt att : attList) {
                // 验车视频/图片
                String completedUrl = FileUploader.addFilePrivatePrefix(att.getAttUrl());
                if (VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType().equals(att.getBusiType())) {
                    VehiclePickReturnAttVO vo = new VehiclePickReturnAttVO();
                    vo.setId(att.getId());
                    vo.setAttType(att.getAttType());
                    vo.setAttUrl(completedUrl);
                    vo.setVehiclePickReturnId(att.getVehiclePickReturnId());
                    attVoList.add(vo);
                } else if (VehiclePickReturnAttEnum.BusyTypeEnum.CONTRACT.getType().equals(att.getBusiType())) {
                    contractList.add(completedUrl);
                } else if (VehiclePickReturnAttEnum.BusyTypeEnum.INSPECTION.getType().equals(att.getBusiType())) {
                    inspectionUrls.add(completedUrl);
                }
            }
        }
        contract.setContractUrls(contractList);
        contract.setInspectionUrls(inspectionUrls);
        pickReturn.setContract(contract);
        pickReturn.setAttList(attVoList);
    }

    /**
     * 还车时，扣车损、违章
     */
    private void handlerDamageAndIllegalOrderWhenReturn(VehiclePickReturnParam param, OrderInfo orderInfo, DepositVO deposit, Long opUserId) {
        VehicleDamageOrderVO damageOrder = param.getDamageOrder();
        VehicleIllegalOrderParam illegalOrder = param.getIllegalOrder();
        if (damageOrder == null && illegalOrder == null) {
            return;
        }
        if (deposit == null) {
            throw new BizException("未查询到订单押金信息，无法扣除押金，请先还车。");
        }

        // 免押方式
        Integer freeDepositDegree = orderInfo.getFreeDepositDegree();
        if (damageOrder != null && this.calculatePayAmount(damageOrder) > 0) {
            boolean isOnlinePay = FreeDepositTypeEnum.FREE_ALL.getType().intValue() == freeDepositDegree || FreeDepositTypeEnum.FREE_RENT.getType().intValue() == freeDepositDegree;
            long maxDamageAmount = orderComponent.getVehicleDamageAmount(orderInfo, deposit);
            if (isOnlinePay && maxDamageAmount < this.calculatePayAmount(damageOrder)) {
                throw new BizException("扣款金额超出车辆押金，无法扣款。请联系客户/平台协商处理");
            }
            damageOrder.setDeductionType(isOnlinePay ? OrderDeductionTypeEnum.PLATFORM_PROXY.getType() : OrderDeductionTypeEnum.UNDERLINE.getType());
            damageOrder.setOrderId(orderInfo.getId());
            damageOrder.setDamageTime(new Date());
            List<VehiclePickReturnAttVO> attList = param.getAttList();
            if (CollectionUtils.isNotEmpty(attList)) {
                Byte picType = (byte)0;
                List<VehicleDamageProofVO> damageProofList = new ArrayList<>();
                for (VehiclePickReturnAttVO vehiclePickReturnAtt : attList) {
                    if (picType.equals(vehiclePickReturnAtt.getAttType())) {
                        VehicleDamageProofVO vehicleDamageProof = new VehicleDamageProofVO();
                        vehicleDamageProof.setProofUrl(vehiclePickReturnAtt.getAttUrl());
                        damageProofList.add(vehicleDamageProof);
                    }
                }
                damageOrder.setDamageProofList(damageProofList);
            }
            vehicleDamageOrderService.saveOrUpdate(damageOrder, opUserId);
        }
        if (illegalOrder != null) {
            boolean isOnlinePay = FreeDepositTypeEnum.FREE_ALL.getType().intValue() == freeDepositDegree || FreeDepositTypeEnum.FREE_ILLEGAL.getType().intValue() == freeDepositDegree;
            long illegalAmount = ObjectUtils.defaultIfNull(illegalOrder.getPenaltyAmount(), 0L) + ObjectUtils.defaultIfNull(illegalOrder.getAgencyFee(), 0L);
            if (isOnlinePay && orderComponent.getVehicleIllegalAmount(orderInfo, deposit) < illegalAmount) {
                throw new BizException("未查询到订单押金信息，无法扣除押金，请先还车。");
            }
            illegalOrder.setDeductionType(isOnlinePay ? OrderDeductionTypeEnum.PLATFORM_PROXY.getType() :
                OrderDeductionTypeEnum.UNDERLINE.getType());
            illegalOrder.setFromSource(IllegalOrderFromSourceEnum.ORDER.getSource());
            illegalOrder.setOrderId(param.getOrderId());
            vehicleIllegalOrderService.saveIllegalOrder(param.getIllegalOrder(), opUserId);
        }
    }

    /**
     * 后面删除
     */
    private long calculatePayAmount(VehicleDamageOrderVO damageOrder) {
        long payAmount = 0L;
        // 维修
        Long repairFee = damageOrder.getRepairFee();
        if (repairFee != null && repairFee > 0L) {
            payAmount += repairFee;
        }
        // 折旧
        Long depreciationFee = damageOrder.getDepreciationFee();
        if (depreciationFee != null && depreciationFee > 0) {
            payAmount += depreciationFee;
        }
        // 其他
        Long otherFee = damageOrder.getOtherFee();
        if (otherFee != null && otherFee > 0) {
            payAmount += otherFee;
        }
        // 停运
        Long outageFee = damageOrder.getOutageFee();
        if (outageFee != null && outageFee > 0) {
            payAmount += outageFee;
        }
        return payAmount;
    }

    /**
     * 允许取车
     */
    private void checkUpdateVehicleStatusWhenPick(OrderInfo orderInfo) {
        Result<VehicleInfoVO> vehicleInfoResult = vehicleInfoService.getBaseById(orderInfo.getVehicleId(), false);
        if (ResultUtil.isModelNull(vehicleInfoResult)) {
            throw new BizException("未查询到对应车辆信息");
        }
        VehicleInfoVO vehicleInfo = vehicleInfoResult.getModel();
        // 当前车辆是租赁中，check一下前面的订单是否到了预计还车时间
        if (VehicleStatusEnum.isLeased(vehicleInfo.getVehicleStatus())) {
            // 查询车辆对应状态是租赁中 且 未过预计还车时间的订单
//            OrderInfoExample orderInfoExample = new OrderInfoExample();
//            orderInfoExample.createCriteria().andVehicleIdEqualTo(orderInfo.getVehicleId())
//                .andIdNotEqualTo(orderInfo.getId()).andMerchantIdEqualTo(orderInfo.getMerchantId())
//                .andOrderStatusEqualTo(OrderStatusEnum.PICKED_UP.getStatus())
//                .andLastReturnDateGreaterThan(new Date());
//            List<OrderInfo> rentOrderList = orderInfoMapper.selectByExample(orderInfoExample);
//            if (CollectionUtils.isNotEmpty(rentOrderList)) {
//                String rentOrderIds =
//                    StringUtils.join(rentOrderList.stream().map(OrderInfo::getId).collect(Collectors.toList()), ",");
//                throw new BizException(String.format("当前车辆租赁中（订单号：%s），无法重复取车，请至订单详情页面操作还车。", rentOrderIds));
//            }
        } else {
            if (!VehicleStatusEnum.NOT_LEASED_OUT.getStatus().equals(vehicleInfo.getVehicleStatus())) {
                throw new BizException("当前车辆状态无法租赁");
            }
        }
    }

    /**
     * 保存验车单数据
     */
    private void saveVehicleInspection(VehicleInspectionVO inspection,
                                       Long orderId, Byte prType, Long opUserId) {
        if (inspection != null) {
            orderInspectionService.saveVehicleInspection(inspection, orderId, prType, opUserId);
        }
    }


    /**
     * check 三方平台对取车操作对限制
     *
     * @param orderInfo 订单
     * @param pickParam 取车参数
     */
    private void checkPickUpRuleForThird(OrderInfo orderInfo, VehiclePickReturnParam pickParam) {
        if (StringUtils.isBlank(orderInfo.getSourceOrderId())) {
            return;
        }

        if (OrderSourceEnum.FEIZHU.getSource().equals(orderInfo.getOrderSource())) {
            Date pickTime = pickParam.getPrTime();
            // 实际取车时间 + 7天 <= 订单预计时间才能取车，否则抛出错误
            if (pickTime.getTime() + 7L * 24 * 3600 * 1000 < orderInfo.getPickupDate().getTime()) {
                throw new BizException("飞猪线上订单：实际取车时间不能早于预计取车时间超过7天");
            }
        }
    }

    private void checkReturnRuleForThird(OrderInfo orderInfo, VehiclePickReturnParam param) {
        if (StringUtils.isBlank(orderInfo.getSourceOrderId())) {
            return;
        }
        if (OrderSourceEnum.transferCtripResale(orderInfo.getOrderSource()).equals(OrderSourceEnum.CTRIP.getSource())) {
            if (orderComponent.checkOrderEnableNotFulfillment(orderInfo)) {
                throw new BizException("该订单已在租车宝取车，请前往租车宝操作还车");
            }
        }

        if (OrderSourceEnum.DIDI.getSource().equals(orderInfo.getOrderSource())) {
            if (CollectionUtils.isNotEmpty(param.getReturnDeductionItemList())
                && OrderDeductionTypeEnum.isPlatformProxy(param.getDeductionPayType())) {
                List<Long> didiDeductionPropIds = Arrays.asList(1L, 4L, 6L);
                List<VehicleReturnExpenseItemVO> deductionItemList = param.getReturnDeductionItemList();
                if (deductionItemList.stream().anyMatch(e -> !didiDeductionPropIds.contains(e.getExpenseItemPropId()))) {
                    throw new BizException("滴滴订单线上扣费，仅支持扣ETC、油/电费和停车费");
                }
            }
        }
    }
}
