package com.ql.rent.provider.trade;


import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.ql.Constant;
import com.ql.dto.ApiResultResp;
import com.ql.enums.open.OpenVehicleEnum;
import com.ql.rent.api.aggregate.dto.OrderDetailExtraDTO;
import com.ql.rent.api.aggregate.dto.OrderExtraDTO;
import com.ql.rent.api.aggregate.remote.collector.vo.dto.OrderDetailResp;
import com.ql.rent.api.aggregate.remote.collector.vo.dto.OrderInventoryOccupyQueryResp;
import com.ql.rent.api.aggregate.remote.collector.vo.dto.OrderListResp;
import com.ql.rent.api.aggregate.remote.vo.request.OrderDetailReq;
import com.ql.rent.api.aggregate.remote.vo.request.OrderInventoryOccupyQueryRequest;
import com.ql.rent.api.aggregate.remote.vo.request.OrderListReq;
import com.ql.rent.api.aggregate.remote.vo.request.VehicleSettleReq;
import com.ql.rent.component.PlatformBiz;
import com.ql.rent.dao.trade.*;
import com.ql.rent.dto.trade.IllegalExtraDTO;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.HelloChannelInitStatusEnum;
import com.ql.rent.enums.WxMpTemplateEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.merchant.PushTypeEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.enums.vehicle.VehicleBusyEnum;
import com.ql.rent.param.price.RentKeyQuery;
import com.ql.rent.param.vehicle.VehicleBusyParam;
import com.ql.rent.param.vehicle.VehicleBusyQueryParam;
import com.ql.rent.service.common.IHelloChannelInitService;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.trade.IOrderSyncService;
import com.ql.rent.service.trade.IVehicleReturnExpenseItemService;
import com.ql.rent.service.vehicle.IThirdVehicleIdRelationService;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.common.TaskRecordVo;
import com.ql.rent.vo.login.WxMsgVo;
import com.ql.rent.vo.price.RentBaseVo;
import com.ql.rent.vo.trade.third.CtripHistoryRerentOrderDTO;
import com.ql.rent.vo.vehicle.VehicleBusyEntityVO;
import com.ql.rent.vo.vehicle.VehicleInfoTagVO;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ql.rent.enums.trade.OrderTypeEnum.RERENT_SERVICE;
import static com.ql.rent.service.trade.IVehicleReturnExpenseItemService.*;

@Slf4j
@Service
public class OrderSyncServiceImpl implements IOrderSyncService {
    @Resource
    private PlatformBiz platformBiz;

    @Resource
    private OrderComponent orderComponent;

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderMemberMapper orderMemberMapper;
    @Resource
    private OrderDetailMapper orderDetailMapper;
    @Resource
    private RerentOrderMapper rerentOrderMapper;
    @Resource
    private VehiclePickReturnMapper vehiclePickReturnMapper;
    @Resource
    private IThirdIdRelationService thirdIdRelationService;
    @Resource
    private IThirdVehicleIdRelationService thirdVehicleIdRelationService;
    @Resource
    private VehiclePickReturnAttMapper vehiclePickReturnAttMapper;

    @Resource
    private IVehicleBusyService vehicleBusyService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private OrderDepositMapper orderDepositMapper;
    @Resource
    private IRentMainService rentMainService;
    @Resource
    private OrderSnapshotMapper orderSnapshotMapper;
    @Resource
    private IHelloChannelInitService helloChannelInitService;
    @Resource
    private IPushMsgService pushMsgService;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;
    @Resource
    private VehicleReturnExpenseItemMapper vehicleReturnExpenseItemMapper;
    @Resource
    private VehicleDamageOrderMapper vehicleDamageOrderMapper;
    @Resource
    private VehicleIllegalOrderMapper vehicleIllegalOrderMapper;

    @Override
    @WithSpan("[hello]订单初始化")
    public void initOrder(Long merchantId, Long channelId) {
        log.info("订单初始化开始, merchantId={}, channelId={}", merchantId, channelId);
        List<String> orderIdList = orderList(merchantId, channelId);
        if (CollectionUtils.isEmpty(orderIdList)) {
            callBackSuccess(merchantId);
            return;
        }
        for (String orderId : orderIdList) {
            syncHelloOrder(merchantId, channelId, orderId, true);
        }
        callBackSuccess(merchantId);
    }

    private void callBackSuccess(Long merchantId) {
        TaskRecordVo taskRecordVo = new TaskRecordVo();
        taskRecordVo.setStatus(IHelloChannelInitService.TaskStatusEnum.SUCCESS.getStatus());
        taskRecordVo.setMerchantId(merchantId);
        taskRecordVo.setIndex(HelloChannelInitStatusEnum.INIT_ORDER.getIndex());
        taskRecordVo.setChannelId(Constant.ChannelId.HELLO);
        helloChannelInitService.statusCallBack(taskRecordVo);
    }

    @Override
    public List<String> orderList(Long merchantId, Long channelId) {
        try {
            if (merchantId == null || channelId == null) {
                return new ArrayList<>();
            }
            OrderListReq orderListReq = new OrderListReq();
            Integer pageIndex = 1;
            List<String> allOrders = new ArrayList<>(); // 用于存储所有订单数据
            while (true) {
                orderListReq.setMerchantId(merchantId);
                orderListReq.setPageIndex(pageIndex);
                orderListReq.setPageSize(50);
                ApiResultResp<OrderListResp> apiResultResp = platformBiz.getOrderList(merchantId, channelId, orderListReq);
                log.info("hello订单列表, apiResultResp={}", JSON.toJSONString(apiResultResp));
                // 检查是否有数据返回
                if (apiResultResp != null && apiResultResp.getData() != null && CollectionUtils.isNotEmpty(apiResultResp.getData().getRecords())) {

                    List<String> orderIdList = apiResultResp.getData().getRecords().stream().map(order -> order.getOutOrderNo()).collect(Collectors.toList());
                    // 处理当前页的数据
                    allOrders.addAll(orderIdList);
                    // 增加页码，准备下一次请求
                    pageIndex++;
                } else {
                    // 没有数据返回，退出循环
                    break;
                }
            }
            return allOrders; // 返回所有订单数据
        } catch (Exception e) {
            log.error("orderList error, merchantId:{}, channelId:{}", merchantId, channelId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean syncHelloOrder(Long merchantId, Long channelId, String sourceOrderId, boolean initialized) {
        try {
            log.info("syncHelloOrder start, merchantId:{}, channelId:{}, sourceOrderId:{}", merchantId, channelId, sourceOrderId);
            if (merchantId == null || channelId == null || StringUtils.isBlank(sourceOrderId)) {
                return false;
            }
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setThirdOrderId(sourceOrderId);
            orderDetailReq.setMerchantId(merchantId);
            ApiResultResp<OrderDetailResp> resp = platformBiz.getOrderDetail(merchantId, channelId, orderDetailReq);
            log.info("syncHelloOrder getOrderDetail, merchantId:{}, channelId:{}, orderDetailReq:{}, resp:{}", merchantId, channelId, JSON.toJSONString(orderDetailReq), JSON.toJSONString(resp));
            if (resp == null || resp.getData() == null) {
                return false;
            }
            OrderDetailResp orderDetailResp = resp.getData();

            Integer payStatus = orderDetailResp.getOrderInfo().getPayStatus();
            // 支付状态:0-无需支付,10-待支付,20-已支付,30-支付失败,40-取消支付
            if (payStatus == null || payStatus == 10 || payStatus == 30) {
                log.info("syncHelloOrder, 忽略未支付订单, merchantId:{}, channelId:{}, sourceOrderId:{}, payStatus:{}, payStatusDesc:{}",
                        merchantId, channelId, sourceOrderId, payStatus, orderDetailResp.getOrderInfo().getPayStatusDesc());
                return true;
            }

            OrderInfoExample example = new OrderInfoExample();
            example.createCriteria().andMerchantIdEqualTo(merchantId).andOrderSourceEqualTo(channelId.byteValue()).andSourceOrderIdEqualTo(sourceOrderId);
            List<OrderInfo> orderInfoList = orderInfoMapper.selectByExample(example);
            OrderInfo orderInfo = new OrderInfo();
            // 新增
            if (CollectionUtils.isNotEmpty(orderInfoList)) {
                orderInfo = orderInfoList.get(0);
                log.info("syncHelloOrder, orderInfo:{}", JSON.toJSONString(orderInfo));
            }

            saveMember(orderDetailResp, orderInfo);
            saveOrder(channelId, merchantId, orderDetailResp, orderInfo);
            saveOrderDrtail(orderDetailResp, orderInfo);
            saveRerentOrder(orderDetailResp, orderInfo);
            savePickupAndReturn(orderDetailResp, orderInfo);
            saveOrderDeposit(orderDetailResp, orderInfo);
            saveDamageAndIllegal(orderDetailResp, orderInfo);
            // 订单初始化忽略库存
            if (!initialized) {
                saveStock(orderDetailResp, channelId, merchantId, orderInfo);
            }
            saveOrderSnapshot(orderInfo);
            return true;
        } catch (Exception e) {
            log.error("syncHelloOrder error, merchantId:{}, channelId:{}, sourceOrderId:{}", merchantId, channelId, sourceOrderId, e);
            return false;
        }
    }

    private void saveDamageAndIllegal(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {
        if (orderDetailResp.getDepositInfo() == null || orderDetailResp.getDepositInfo().getDepositFlowInfoResponses() == null) {
            return;
        }
        VehicleDamageOrderExample delDamageExample = new VehicleDamageOrderExample();
        delDamageExample.createCriteria().andOrderIdEqualTo(orderInfo.getId());
        vehicleDamageOrderMapper.deleteByExample(delDamageExample);

        VehicleIllegalOrderExample delIllegalExample = new VehicleIllegalOrderExample();
        delIllegalExample.createCriteria().andOrderIdEqualTo(orderInfo.getId());
        vehicleIllegalOrderMapper.deleteByExample(delIllegalExample);

        List<OrderDetailResp.DepositInfo.DepositFlowInfo> depositFlowInfos = orderDetailResp.getDepositInfo().getDepositFlowInfoResponses();

        // 扣款
        for (OrderDetailResp.DepositInfo.DepositFlowInfo depositFlowInfo : depositFlowInfos) {
            if (depositFlowInfo.getStatus() == null || depositFlowInfo.getStatus() != 120 || depositFlowInfo.getIsAmountAllRefunded()) {
                continue;
            }
            if (depositFlowInfo.getAmount() != null && depositFlowInfo.getDepositBehave() != null && depositFlowInfo.getDepositBehave() == 1 && depositFlowInfo.getDepositBehaveType() != null) {
                long amount = (long) (depositFlowInfo.getAmount() * 100);
                // 车损
                if (depositFlowInfo.getDepositBehaveType() == 11) {
                    VehicleDamageOrder vehicleDamageOrder = new VehicleDamageOrder();
                    vehicleDamageOrder.setOrderId(orderInfo.getId());
                    vehicleDamageOrder.setVehicleId(orderInfo.getVehicleId());
                    vehicleDamageOrder.setDamageTime(DateUtil.getFormatDate(depositFlowInfo.getCreateTime(), DatePattern.UTC_SIMPLE_PATTERN));
                    vehicleDamageOrder.setRepairFee(amount);
                    vehicleDamageOrder.setDepreciationFee(0L);
                    vehicleDamageOrder.setOutageFee(0L);
                    vehicleDamageOrder.setOtherFee(0L);
                    vehicleDamageOrder.setSource(OrderSourceEnum.HELLO.getSource());
                    vehicleDamageOrder.setDeductionType((byte) 1);
                    vehicleDamageOrder.setLastVer(1);
                    vehicleDamageOrder.setDeleted(YesOrNoEnum.NO.getValue());
                    vehicleDamageOrder.setOpUserId(0L);
                    vehicleDamageOrder.setCreateTime(DateUtil.getFormatDate(depositFlowInfo.getCreateTime(), DatePattern.UTC_SIMPLE_PATTERN).getTime());
                    vehicleDamageOrder.setOpTime(vehicleDamageOrder.getCreateTime());
                    vehicleDamageOrder.setPartnerCreateTime(0L);
                    vehicleDamageOrder.setPartnerOpTime(0L);
                    vehicleDamageOrderMapper.insertSelective(vehicleDamageOrder);
                }
                // 违章
                if (depositFlowInfo.getDepositBehaveType() == 12) {
                    VehicleIllegalOrder illegalOrder = new VehicleIllegalOrder();
                    illegalOrder.setOrderId(orderInfo.getId());
                    illegalOrder.setHandleStoreId(orderInfo.getPickupStoreId());
                    illegalOrder.setIllegalTime(DateUtil.getFormatDate(depositFlowInfo.getCreateTime(), DatePattern.UTC_SIMPLE_PATTERN));
                    illegalOrder.setIllegalCityId(-1L);
                    illegalOrder.setIllegalAddr("-");
                    illegalOrder.setIllegalAction("-");
                    illegalOrder.setFraction(0);
                    illegalOrder.setPenaltyAmount(amount);
                    illegalOrder.setDeductionType((byte) 1);
                    illegalOrder.setRefundAmount(0L);
                    illegalOrder.setContractDamageAmount(amount);
                    illegalOrder.setSource(OrderSourceEnum.HELLO.getSource());
                    illegalOrder.setMerchantId(orderInfo.getMerchantId());
                    illegalOrder.setAgencyFee(0L);
                    illegalOrder.setFromSource((byte) 0);
                    illegalOrder.setIllegalType((byte) 0);
                    illegalOrder.setLastVer(1);
                    illegalOrder.setOpTime(DateUtil.getFormatDate(depositFlowInfo.getCreateTime(), DatePattern.UTC_SIMPLE_PATTERN).getTime());
                    illegalOrder.setOpUserId(0L);
                    illegalOrder.setCreateTime(illegalOrder.getOpTime());
                    illegalOrder.setPartnerCreateTime(0L);
                    illegalOrder.setPartnerOpTime(0L);
                    IllegalExtraDTO extraDTO = new IllegalExtraDTO();
                    extraDTO.setNote(depositFlowInfo.getNote());
                    illegalOrder.setExtra(JSON.toJSONString(extraDTO));
                    vehicleIllegalOrderMapper.insertSelective(illegalOrder);
                }
            }
        }
    }

    private void saveOrderSnapshot(OrderInfo orderInfo) {

        OrderSnapshotExample delSnapshotExample = new OrderSnapshotExample();
        delSnapshotExample.createCriteria().andOrderIdEqualTo(orderInfo.getId()).andOrderTypeEqualTo((byte) 0);
        orderSnapshotMapper.deleteByExample(delSnapshotExample);

        List<OrderSnapshot> recordList = new ArrayList<>();
        Result<List<VehicleInfoTagVO>> vehicleInfoTagVOResult =
                vehicleInfoService.listVehicleModelAndTag(Arrays.asList(orderInfo.getVehicleId()));
        log.info("创建订单, 保存订单快照, 获取车辆信息, vehicleInfoTagVOResult={}", JSON.toJSONString(vehicleInfoTagVOResult));
        if (vehicleInfoTagVOResult.isSuccess() && !CollectionUtils.isEmpty(vehicleInfoTagVOResult.getModel())) {
            VehicleInfoTagVO vehicleInfoTagVO = vehicleInfoTagVOResult.getModel().get(0);
            OrderSnapshot tagRecord = new OrderSnapshot();
            tagRecord.setOrderId(orderInfo.getId());
            tagRecord.setSnapshotType(OrderSnapshotTypeEnum.TAG.getType());
            tagRecord.setSourceId(0L);
            tagRecord.setOrderType((byte)0);
            tagRecord.setContent(JSON.toJSONString(vehicleInfoTagVO));
            tagRecord.setOpTime(System.currentTimeMillis());
            tagRecord.setCreateTime(tagRecord.getOpTime());
            recordList.add(tagRecord);
            OrderSnapshot vehicleRecord = new OrderSnapshot();
            vehicleRecord.setOrderId(orderInfo.getId());
            vehicleRecord.setSnapshotType(OrderSnapshotTypeEnum.ORIGINAL_VEHICLE.getType());
            vehicleRecord.setSourceId(0L);
            vehicleRecord.setOrderType((byte)0);
            vehicleRecord.setContent(JSON.toJSONString(vehicleInfoTagVO));
            vehicleRecord.setOpTime(System.currentTimeMillis());
            vehicleRecord.setCreateTime(vehicleRecord.getOpTime());
            recordList.add(vehicleRecord);
            if (orderSnapshotMapper.batchInsert(recordList) < 1) {
                throw new BizException("保存订单快照失败");
            }
        }
    }

    private void saveStock(OrderDetailResp orderDetailResp, Long channelId, Long merchantId, OrderInfo orderInfo) {
        if (orderDetailResp.getCarId() == null || "null".equals(orderDetailResp.getCarId()) || "0".equals(orderDetailResp.getCarId())) {
            log.info("syncHelloOrder 哈啰订单详情无排车, 不更新库存, orderId:{}", orderInfo.getId());
            return;
        }

        OrderInventoryOccupyQueryRequest orderDetailReq = new OrderInventoryOccupyQueryRequest();
        List<Long> helloOrderIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(orderInfo.getSourceInnerOrderId())) {
            try {
                helloOrderIdList.add(Long.valueOf(orderInfo.getSourceInnerOrderId()));
                orderDetailReq.setHelloOrderIdList(helloOrderIdList);
            } catch (NumberFormatException e) {
                log.error("syncHelloOrder 订单号无法转为Long类型, sourceInnerOrderId:{}", orderInfo.getSourceInnerOrderId(), e);
                return;
            }
        } else {
            log.warn("syncHelloOrder 订单sourceInnerOrderId为空, orderId:{}", orderInfo.getId());
            return;
        }
        
        // 查外部订单库存
        ApiResultResp<OrderInventoryOccupyQueryResp> apiResultResp = platformBiz.orderInventoryOccupyQuery(merchantId, channelId, orderDetailReq);
        log.info("syncHelloOrder orderInventoryOccupyQuery, merchantId:{}, channelId:{}, orderDetailReq:{}, resp:{}", merchantId, channelId, JSON.toJSONString(orderDetailReq), JSON.toJSONString(apiResultResp));
        if (apiResultResp == null || apiResultResp.getData() == null) {
            log.warn("没有找到外部订单库存数据, merchantId:{}, channelId:{}, orderId:{}", merchantId, channelId, orderInfo.getId());
            return;
        }
        List<OrderInventoryOccupyQueryResp.OccupyInfo> thirdOccupyList = apiResultResp.getData().getOccupyList();

        // 查saas订单库存
        VehicleBusyQueryParam param = new VehicleBusyQueryParam();
        param.setMerchantId(merchantId);
        param.setParentSourceId(orderInfo.getId());
        Result<List<VehicleBusyEntityVO>> listResult = vehicleBusyService.queryByParam(param);
        List<VehicleBusyEntityVO> saasOccupyList = listResult.isSuccess() ? listResult.getModel() : new ArrayList<>();
        
        try {
            // 预先准备三个列表，分别用于创建、更新和删除操作
            List<VehicleBusyEntityVO> createList = new ArrayList<>();
            List<VehicleBusyEntityVO> updateList = new ArrayList<>();
            List<Long> deleteList = new ArrayList<>();
            
            // 收集外部订单ID和时间范围，并按开始时间排序occupyList
            List<OrderInventoryOccupyQueryResp.OccupyInfo> sortedOccupyList = new ArrayList<>(thirdOccupyList);
            sortedOccupyList.sort((o1, o2) -> {
                Long t1 = DateUtil.getFormatDate(o1.getOccupiedStart(), DatePattern.UTC_SIMPLE_PATTERN).getTime();
                Long t2 = DateUtil.getFormatDate(o2.getOccupiedStart(), DatePattern.UTC_SIMPLE_PATTERN).getTime();
                return t1.compareTo(t2);
            });
            
            // SAAS库存按开始时间排序
            saasOccupyList.sort(Comparator.comparing(VehicleBusyEntityVO::getStartTime));
            
            // 标记已匹配的SAAS库存
            boolean[] saasMatched = new boolean[saasOccupyList.size()];
            
            // 遍历外部订单库存，与SAAS订单库存对比
            for (OrderInventoryOccupyQueryResp.OccupyInfo occupyInfo : sortedOccupyList) {
                // 得到的哈啰库存时间段有重复，过滤掉carId为0的库存
                if (occupyInfo.getCarId() == null || occupyInfo.getCarId() == 0) {
                    continue;
                }
                Long startTime = DateUtil.getFormatDate(occupyInfo.getOccupiedStart(), DatePattern.UTC_SIMPLE_PATTERN).getTime();
                Long actualReleased = StringUtils.isNotBlank(occupyInfo.getActualReleased()) ? 
                        DateUtil.getFormatDate(occupyInfo.getActualReleased(), DatePattern.UTC_SIMPLE_PATTERN).getTime() :
                        DateUtil.getFormatDate(occupyInfo.getOccupiedEnd(), DatePattern.UTC_SIMPLE_PATTERN).getTime();
                String thirdSourceId = occupyInfo.getGuid().toString();
                
                boolean matched = false;
                
                // 寻找匹配的SAAS库存
                for (int i = 0; i < saasOccupyList.size(); i++) {
                    if (saasMatched[i]) {
                        continue; // 跳过已匹配的库存
                    }
                    
                    VehicleBusyEntityVO saasStock = saasOccupyList.get(i);
                    
                    // 仅通过开始时间判断是否匹配
                    boolean isTimeMatch = Objects.equals(saasStock.getStartTime(), startTime); // 精确匹配，不允许误差
                    
                    if (isTimeMatch) {
                        // 找到匹配的库存记录，检查是否需要更新
                        boolean needUpdate = !Objects.equals(saasStock.getStartTime(), startTime)
                                || !Objects.equals(saasStock.getEndIntervalTime(), actualReleased)
                                || !Objects.equals(saasStock.getVehicleId(), orderInfo.getVehicleId());
                        
                        if (needUpdate) {
                            // 需要更新
                            log.debug("需要更新库存记录, id:{}, thirdSourceId:{}, 开始时间:{} -> {}, 结束时间:{} -> {}",
                                    saasStock.getId(),
                                    saasStock.getThirdSourceId(),
                                    saasStock.getStartTime(),
                                    startTime,
                                    saasStock.getEndTime(),
                                    actualReleased);
                            saasStock.setStartTime(startTime);
                            saasStock.setEndTime(actualReleased);
                            saasStock.setEndIntervalTime(actualReleased);
                            saasStock.setThirdSourceId(thirdSourceId);
                            saasStock.setOpTime(System.currentTimeMillis());
                            saasStock.setVehicleId(orderInfo.getVehicleId());
                            saasStock.setVehicleModelId(orderInfo.getVehicleModelId());
                            updateList.add(saasStock);
                        }
                        
                        saasMatched[i] = true;
                        matched = true;
                        break;
                    }
                }
                
                if (!matched) {
                    // 未找到匹配的库存记录，需要创建新记录
                    VehicleBusyEntityVO newStock = new VehicleBusyEntityVO();
                    newStock.setMerchantId(merchantId);
                    newStock.setChannelId(channelId);
                    newStock.setStoreId(orderInfo.getPickupStoreId());
                    newStock.setVehicleModelId(orderInfo.getVehicleModelId());
                    newStock.setVehicleId(orderInfo.getVehicleId());
                    newStock.setStartTime(startTime);
                    newStock.setEndTime(actualReleased);
                    newStock.setEndIntervalTime(actualReleased);
                    if (occupyInfo.getOccupiedType() == 11) {
                        newStock.setSourceType(VehicleBusyEnum.SUBORDER.getValueInt());
                    } else {
                        newStock.setSourceType(VehicleBusyEnum.ORDER.getValueInt());
                    }
                    newStock.setSourceId(orderInfo.getId());
                    newStock.setParentSourceId(orderInfo.getId());
                    newStock.setThirdSourceId(thirdSourceId);
                    newStock.setThirdParentSourceId(orderInfo.getSourceOrderId());
                    newStock.setLockFlg(Integer.valueOf(YesOrNoEnum.YES.getValue()));
                    newStock.setDeleted(Integer.valueOf(YesOrNoEnum.NO.getValue()));
                    newStock.setAutoSchedule(Integer.valueOf(YesOrNoEnum.NO.getValue()));
                    createList.add(newStock);
                    log.debug("需要创建新库存记录, thirdSourceId:{}, 开始时间:{}, 结束时间:{}",
                            thirdSourceId,
                            startTime,
                            actualReleased);
                }
            }
            
            // 未匹配的SAAS库存需要删除
            for (int i = 0; i < saasOccupyList.size(); i++) {
                if (!saasMatched[i]) {
                    VehicleBusyEntityVO saasStock = saasOccupyList.get(i);
                    deleteList.add(saasStock.getId());
                    log.debug("需要删除未匹配的库存记录, id:{}, thirdSourceId:{}, 开始时间:{}, 结束时间:{}", 
                            saasStock.getId(),
                            saasStock.getThirdSourceId(),
                            saasStock.getStartTime(),
                            saasStock.getEndTime());
                }
            }
            
            // 批量执行操作
            if (CollectionUtils.isNotEmpty(createList)) {
                List<VehicleBusyEntityVO> resultList = vehicleBusyService.busyPlatCreate(createList);
            }
            
            if (CollectionUtils.isNotEmpty(updateList)) {
                vehicleBusyService.busyPlatUpdate(updateList);
            }
            
            if (CollectionUtils.isNotEmpty(deleteList)) {
                vehicleBusyService.cancelPlatLocks(merchantId, deleteList, null);
            }
        } catch (Exception e) {
            log.error("同步订单库存异常, orderId:{}, sourceOrderId:{}", orderInfo.getId(), orderInfo.getSourceOrderId(), e);
        }
    }

    private void saveOrderDeposit(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {

        OrderDepositExample example = new OrderDepositExample();
        example.createCriteria().andOrderIdEqualTo(orderInfo.getId());
        List<OrderDeposit> list = orderDepositMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            for (OrderDeposit orderDeposit : list) {
                orderDepositMapper.deleteByPrimaryKey(orderDeposit.getId());
            }
        }

        RentKeyQuery storeModelKey = new RentKeyQuery();
        storeModelKey.setStoreId(orderInfo.getPickupStoreId());
        storeModelKey.setVehicleModelIdList(Arrays.asList(orderInfo.getVehicleModelId()));
        Result<RentBaseVo> rentMainVoResult = rentMainService.selectRentBase(storeModelKey);
        OrderDeposit orderDeposit = new OrderDeposit();
        orderDeposit.setOrderId(orderInfo.getId());
        orderDeposit.setVehicleInfoId(orderInfo.getVehicleModelId());

        orderDeposit.setCreateUserId(orderInfo.getOpUserId());
        orderDeposit.setOpUserId(orderInfo.getOpUserId());
        orderDeposit.setOrderSource(orderInfo.getOrderSource());
        orderDeposit.setOpTime(System.currentTimeMillis());

        OrderDetailResp.DepositInfo depositInfo = orderDetailResp.getDepositInfo();
        if (ResultUtil.isModelNotNull(rentMainVoResult)) {
            orderDeposit.setVehicleDepositAmount(rentMainVoResult.getModel().getRentDeposit());
            orderDeposit.setIllegalOrderDepositAmount(rentMainVoResult.getModel().getIllegalDeposit());
        } else {
            orderDeposit.setVehicleDepositAmount(BigDecimal.valueOf(depositInfo.getVehicleDeposit()).multiply(BigDecimal.valueOf(100)).intValue());
            orderDeposit.setIllegalOrderDepositAmount(BigDecimal.valueOf(depositInfo.getDepositAmount()).multiply(BigDecimal.valueOf(100)).intValue());
        }
        // 双免
        if (depositInfo != null && depositInfo.getFreeDepositType() != null && depositInfo.getFreeDepositType() == 3) {
            orderDeposit.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
            if (depositInfo.getVehicleDeposit()!= null) {
                orderDeposit.setVehicleDepositPayAmount(BigDecimal.valueOf(depositInfo.getVehicleDeposit()).multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                orderDeposit.setVehicleDepositPayAmount(orderDeposit.getVehicleDepositAmount());
            }
            if (depositInfo.getDepositAmount() != null) {
                orderDeposit.setIllegalOrderDepositPayAmount(BigDecimal.valueOf(depositInfo.getDepositAmount()).multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                orderDeposit.setIllegalOrderDepositPayAmount(orderDeposit.getIllegalOrderDepositAmount());
            }
            orderDeposit.setPayType(DepositPayTypeEnum.ZHIMA.getType());
        } else {
            orderDeposit.setPayStatus(PayStatusEnum.UNPAID.getStatus());
        }

        if (orderDepositMapper.insertSelective(orderDeposit) < 1) {
            throw new BizException("保存订单押金失败");
        }
    }


    private void savePickupAndReturn(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {
        try {
            VehiclePickReturnAttExample example = new VehiclePickReturnAttExample();
            example.createCriteria().andOrderIdEqualTo(orderInfo.getId());
            vehiclePickReturnAttMapper.deleteByExample(example);

            OrderDetailResp.PickUpCarInfo pickUpCarInfo = orderDetailResp.getPickUpCarInfo();
            OrderDetailResp.ReturnCarInfo returnCarInfo = orderDetailResp.getReturnCarInfo();
            if (pickUpCarInfo != null) {
                Long pickId = null;
                VehiclePickReturnExample pickExample = new VehiclePickReturnExample();
                pickExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                        .andPrTypeEqualTo(VehiclePickReturnEnum.PICK.getType());
                List<VehiclePickReturn> pickList = vehiclePickReturnMapper.selectByExample(pickExample);
                if (CollectionUtils.isNotEmpty(pickList)) {
                    VehiclePickReturn pick = pickList.get(0);
                    pickId= pick.getId();
                    pick.setMerchantId(orderInfo.getMerchantId());
                    if (pickUpCarInfo.getKilometres() != null) {
                        pick.setMileage(Integer.valueOf(pickUpCarInfo.getKilometres()));
                    }
                    pick.setOilLiter(Integer.valueOf(pickUpCarInfo.getOilAmount()));
                    pick.setMaxOilLiter(Integer.valueOf(pickUpCarInfo.getOilAll()));
                    pick.setPrTime(DateUtil.getFormatDate(pickUpCarInfo.getOperationTime(), DateUtil.yyyyMMddHHmmss));
                    vehiclePickReturnMapper.updateByPrimaryKeySelective(pick);
                } else {
                    VehiclePickReturn pick = new VehiclePickReturn();
                    pick.setOilLiter(Integer.valueOf(pickUpCarInfo.getOilAmount()));
                    pick.setMaxOilLiter(Integer.valueOf(pickUpCarInfo.getOilAll()));
                    pick.setOrderId(orderInfo.getId());
                    pick.setPrType(VehiclePickReturnEnum.PICK.getType());
                    pick.setPrTime(DateUtil.getFormatDate(pickUpCarInfo.getOperationTime(), DateUtil.yyyyMMddHHmmss));
                    pick.setVehicleId(orderInfo.getVehicleId());
                    pick.setLastVer(1);
                    pick.setOpUserId(0L);
                    pick.setDeductionPayType((byte) 0);
                    pick.setRefundPayType((byte) 0);
                    pick.setMerchantId(orderInfo.getMerchantId());
                    pick.setDeleted(YesOrNoEnum.NO.getValue());
                    pick.setCreateTime(System.currentTimeMillis());
                    pick.setOpTime(System.currentTimeMillis());
                    if (pickUpCarInfo.getKilometres() != null) {
                        pick.setMileage(Integer.valueOf(pickUpCarInfo.getKilometres()));
                    }
                    vehiclePickReturnMapper.insertSelective(pick);
                    pickId = pick.getId();
                }
                if (pickId != null) {
                    if (pickUpCarInfo.getCarAppearance() != null) {
                        log.info("保存取车附件");
                        OrderDetailResp.PickUpCarInfo.CarTrim carTrim = pickUpCarInfo.getCarTrim();
                        saveAtt(pickUpCarInfo.getCarAppearance(), pickId, orderInfo, VehiclePickReturnEnum.PICK.getType(), carTrim, pickUpCarInfo, returnCarInfo);
                    }
                }
            }

            if (returnCarInfo != null) {
                VehiclePickReturnExample returnExample = new VehiclePickReturnExample();
                returnExample.createCriteria().andOrderIdEqualTo(orderInfo.getId())
                        .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType());
                List<VehiclePickReturn> returnList = vehiclePickReturnMapper.selectByExample(returnExample);
                Long returnId = null;
                if (CollectionUtils.isNotEmpty(returnList)) {
                    VehiclePickReturn returnBack = returnList.get(0);
                    returnBack.setMerchantId(orderInfo.getMerchantId());
                    returnBack.setOilLiter(Integer.valueOf(returnCarInfo.getOilAmount()));
                    returnBack.setMaxOilLiter(Integer.valueOf(pickUpCarInfo.getOilAll()));
                    if (returnCarInfo.getKilometres() != null) {
                        returnBack.setMileage(Integer.valueOf(returnCarInfo.getKilometres()));
                    }
                    returnBack.setPrTime(DateUtil.getFormatDate(returnCarInfo.getOperationTime(), DateUtil.yyyyMMddHHmmss));
                    vehiclePickReturnMapper.updateByPrimaryKeySelective(returnBack);
                    returnId = returnBack.getId();
                } else {
                    VehiclePickReturn returnBack = new VehiclePickReturn();
                    if (returnCarInfo.getKilometres() != null) {
                        returnBack.setMileage(Integer.valueOf(returnCarInfo.getKilometres()));
                    }
                    returnBack.setOrderId(orderInfo.getId());
                    returnBack.setPrType(VehiclePickReturnEnum.RETURN_BACK.getType());
                    returnBack.setPrTime(DateUtil.getFormatDate(returnCarInfo.getOperationTime(), DateUtil.yyyyMMddHHmmss));
                    returnBack.setVehicleId(orderInfo.getVehicleId());
                    returnBack.setLastVer(1);
                    returnBack.setOpUserId(0L);
                    returnBack.setOilLiter(Integer.valueOf(returnCarInfo.getOilAmount()));
                    returnBack.setMaxOilLiter(Integer.valueOf(pickUpCarInfo.getOilAll()));
                    returnBack.setDeductionPayType((byte) 0);
                    returnBack.setRefundPayType((byte) 0);
                    returnBack.setMerchantId(orderInfo.getMerchantId());
                    returnBack.setDeleted(YesOrNoEnum.NO.getValue());
                    returnBack.setCreateTime(System.currentTimeMillis());
                    returnBack.setOpTime(System.currentTimeMillis());
                    vehiclePickReturnMapper.insertSelective(returnBack);
                    returnId = returnBack.getId();
                }
                if (returnId != null) {
                    OrderDetailResp.PickUpCarInfo.CarTrim carTrim = returnCarInfo.getCarTrim();
                    if (returnCarInfo.getCarAppearance() != null) {
                        saveAtt(returnCarInfo.getCarAppearance(), returnId, orderInfo, VehiclePickReturnEnum.RETURN_BACK.getType(), carTrim, pickUpCarInfo, returnCarInfo);
                    }
                    saveReturnFee(orderDetailResp, orderInfo, returnId);
                }
            }
        } catch (Exception e) {
            log.error("同步订单保存实际取还车时间异常, orderId={}, sourceOrderId={}", orderInfo.getId(), orderInfo.getSourceOrderId(), e);
        }

    }

    private void saveReturnFee(OrderDetailResp orderDetailResp, OrderInfo orderInfo, Long returnId) {
        try {
            VehicleReturnExpenseItemExample delExample = new VehicleReturnExpenseItemExample();
            delExample.createCriteria().andOrderIdEqualTo(orderInfo.getId());
            vehicleReturnExpenseItemMapper.deleteByExample(delExample);

            List<OrderDetailResp.DepositInfo.DepositFlowInfo> depositFlowInfos = new ArrayList<>();
            if (orderDetailResp.getDepositInfo() != null && CollectionUtils.isNotEmpty(orderDetailResp.getDepositInfo().getDepositFlowInfoResponses())) {
                depositFlowInfos = orderDetailResp.getDepositInfo().getDepositFlowInfoResponses();
            }


            if (StringUtils.isNotBlank(orderDetailResp.getReturnCarInfo().getRefundAmount())) {
                long returnAmount = (long) (Double.parseDouble(orderDetailResp.getReturnCarInfo().getRefundAmount()) * 100);
                if (returnAmount > 0) {
                    vehicleReturnExpenseItemService.saveExpense(orderInfo.getId(), returnId,
                            returnAmount, RETURN_EARLY_PROP, 0L);
                }
            }
            if (StringUtils.isNotBlank(orderDetailResp.getReturnCarInfo().getFuelRefundAmount())) {
                long returnAmount = (long) (Double.parseDouble(orderDetailResp.getReturnCarInfo().getFuelRefundAmount()) * 100);
                if (returnAmount > 0) {
                    vehicleReturnExpenseItemService.saveExpense(orderInfo.getId(), returnId,
                            returnAmount, RETURN_FUEL_PROP, 0L);
                }
            }
            if (StringUtils.isNotBlank(orderDetailResp.getReturnCarInfo().getElectricityRefund())) {
                long returnAmount = (long) (Double.parseDouble(orderDetailResp.getReturnCarInfo().getElectricityRefund()) * 100);
                if (returnAmount > 0) {
                    vehicleReturnExpenseItemService.saveExpense(orderInfo.getId(), returnId,
                            returnAmount, RETURN_ELECTRICITY_PROP, 0L);
                }
            }
            if (StringUtils.isNotBlank(orderDetailResp.getReturnCarInfo().getNightServeRefundAmount())) {
                long returnAmount = (long) (Double.parseDouble(orderDetailResp.getReturnCarInfo().getNightServeRefundAmount()) * 100);
                if (returnAmount > 0) {
                    vehicleReturnExpenseItemService.saveExpense(orderInfo.getId(), returnId,
                            returnAmount, OTHER_PROP, 0L);
                }
            }
            // 扣款
            for (OrderDetailResp.DepositInfo.DepositFlowInfo depositFlowInfo : depositFlowInfos) {
                if (depositFlowInfo.getStatus() == null || depositFlowInfo.getStatus() != 120) {
                    continue;
                }
                if (depositFlowInfo.getAmount() != null && depositFlowInfo.getDepositBehave() != null && depositFlowInfo.getDepositBehave() == 1
                        && depositFlowInfo.getDepositBehaveType() != 11 && depositFlowInfo.getDepositBehaveType() != 12) {
                    long amount = (long) (depositFlowInfo.getAmount() * 100);
                    if (amount > 0) {
                        vehicleReturnExpenseItemService.saveExpense(orderInfo.getId(), returnId, amount, OTHER_FEE_PROP, 0L);
                    }
                }
            }
        } catch (Exception e) {
            log.info("保存还车时退费扣款失败, orderId={}, thirdOrderId={}", orderInfo.getId(), orderInfo.getSourceOrderId(), JSON.toJSONString(orderDetailResp.getReturnCarInfo()), e);
        }
    }

    private void saveAtt(OrderDetailResp.PickUpCarInfo.CarAppearance appearance, Long pickId, OrderInfo orderInfo, byte prType,
                         OrderDetailResp.PickUpCarInfo.CarTrim carTrim, OrderDetailResp.PickUpCarInfo pickUpCarInfo, OrderDetailResp.ReturnCarInfo returnCarInfo) {
        log.info("保存取还车附件, prType={}, 请求参数appearance={}, carTrim={}", prType, JSON.toJSONString(appearance), JSON.toJSONString(carTrim));
        List<VehiclePickReturnAtt> attList = new ArrayList<>();
        OrderDetailResp.PickUpCarInfo.CarAppearance carAppearance = appearance;
        VehiclePickReturnAtt pickAtt = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt0 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt1 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt2 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt3 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt4 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt5 = new VehiclePickReturnAtt();
        VehiclePickReturnAtt pickAtt6 = new VehiclePickReturnAtt();

        pickAtt.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.PIC.getType());
        pickAtt.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
        pickAtt.setVehiclePickReturnId(pickId);
        pickAtt.setOrderId(orderInfo.getId());
        pickAtt.setPrType(prType);
        pickAtt.setSource(VehiclePickReturnAttEnum.AttSourceEnum.HELLO.getSource());
        pickAtt.setCreateTime(System.currentTimeMillis());
        pickAtt.setOpTime(System.currentTimeMillis());
        pickAtt.setOpUserId(0L);

        if (StringUtils.isNotBlank(carAppearance.getCarHeadImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt0);
            pickAtt0.setAttUrl(carAppearance.getCarHeadImage());
            attList.add(pickAtt0);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarTailImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt1);
            pickAtt1.setAttUrl(carAppearance.getCarTailImage());
            attList.add(pickAtt1);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarLeftRearImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt2);
            pickAtt2.setAttUrl(carAppearance.getCarLeftRearImage());
            attList.add(pickAtt2);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarRightRearImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt3);
            pickAtt3.setAttUrl(carAppearance.getCarRightRearImage());
            attList.add(pickAtt3);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarTailImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt4);
            pickAtt4.setAttUrl(carAppearance.getCarTailImage());
            attList.add(pickAtt4);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarRightFrontImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt5);
            pickAtt5.setAttUrl(carAppearance.getCarRightFrontImage());
            attList.add(pickAtt5);
        }

        if (StringUtils.isNotBlank(carAppearance.getCarLeftFrontImage())) {
            BeanUtils.copyProperties(pickAtt, pickAtt6);
            pickAtt6.setAttUrl(carAppearance.getCarLeftFrontImage());
            attList.add(pickAtt6);
        }

        if (CollectionUtils.isNotEmpty(carAppearance.getOthers())) {
            for (String other : carAppearance.getOthers()) {
                VehiclePickReturnAtt pickAtt7 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt7);
                pickAtt7.setAttUrl(other);
                attList.add(pickAtt7);
            }
        }
        if (carTrim != null) {
            if (StringUtils.isNotBlank(carTrim.getCarBackRowImage())) {
                VehiclePickReturnAtt pickAtt8 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt8);
                pickAtt8.setAttUrl(carTrim.getCarBackRowImage());
                attList.add(pickAtt8);
            }
            if (StringUtils.isNotBlank(carTrim.getCarCabImage())) {
                VehiclePickReturnAtt pickAtt9 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt9);
                pickAtt9.setAttUrl(carTrim.getCarCabImage());
                attList.add(pickAtt9);
            }
            if (StringUtils.isNotBlank(carTrim.getCarCentralControlImage())) {
                VehiclePickReturnAtt pickAtt10 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt10);
                pickAtt10.setAttUrl(carTrim.getCarCentralControlImage());
                attList.add(pickAtt10);
            }
            if (CollectionUtils.isNotEmpty(carTrim.getOthers())) {
                for (String other : carTrim.getOthers()) {
                    VehiclePickReturnAtt pickAtt11 = new VehiclePickReturnAtt();
                    BeanUtils.copyProperties(pickAtt, pickAtt11);
                    pickAtt11.setAttUrl(other);
                    attList.add(pickAtt11);
                }
            }
        }
        if (pickUpCarInfo != null && CollectionUtils.isNotEmpty(pickUpCarInfo.getRentContracts())) {
            for (String rentContract : pickUpCarInfo.getRentContracts()) {
                VehiclePickReturnAtt pickAtt12 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt12);
                pickAtt12.setAttUrl(rentContract);
                if (rentContract.endsWith(".pdf")) {
                    pickAtt12.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.CONTRACT.getType());
                } else {
                    pickAtt12.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
                }
                attList.add(pickAtt12);
            }
        }

        if (returnCarInfo != null && CollectionUtils.isNotEmpty(returnCarInfo.getVerificationSheet())) {
            for (String verificationSheet : returnCarInfo.getVerificationSheet()) {
                VehiclePickReturnAtt pickAtt12 = new VehiclePickReturnAtt();
                BeanUtils.copyProperties(pickAtt, pickAtt12);
                pickAtt12.setAttUrl(verificationSheet);
                if (verificationSheet.endsWith(".pdf")) {
                    pickAtt12.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.INSPECTION.getType());
                } else {
                    pickAtt12.setBusiType(VehiclePickReturnAttEnum.BusyTypeEnum.MEDIA.getType());
                }
                attList.add(pickAtt12);
            }
        }

        if (prType == VehiclePickReturnEnum.PICK.getType()) {
            VehiclePickReturnAtt pickAtt12 = new VehiclePickReturnAtt();
            if (pickUpCarInfo != null &&  StringUtils.isNotBlank(pickUpCarInfo.getFuelPhoto())) {
                BeanUtils.copyProperties(pickAtt, pickAtt12);
                pickAtt12.setAttUrl(pickUpCarInfo.getFuelPhoto());
                attList.add(pickAtt12);
            }

            VehiclePickReturnAtt pickAtt13 = new VehiclePickReturnAtt();
            if (pickUpCarInfo != null && StringUtils.isNotBlank(pickUpCarInfo.getVehicleVideo())) {
                BeanUtils.copyProperties(pickAtt, pickAtt13);
                pickAtt13.setAttUrl(pickUpCarInfo.getVehicleVideo());
                pickAtt13.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.VIDEO.getType());
                attList.add(pickAtt13);
            }
        }

        if (prType == VehiclePickReturnEnum.RETURN_BACK.getType()) {
            VehiclePickReturnAtt pickAtt14 = new VehiclePickReturnAtt();
            if (returnCarInfo != null && StringUtils.isNotBlank(returnCarInfo.getFuelPhoto())) {
                BeanUtils.copyProperties(pickAtt, pickAtt14);
                pickAtt14.setAttUrl(returnCarInfo.getFuelPhoto());
                attList.add(pickAtt14);
            }

            VehiclePickReturnAtt pickAtt15 = new VehiclePickReturnAtt();
            if (returnCarInfo != null && StringUtils.isNotBlank(returnCarInfo.getVehicleVideo())) {
                BeanUtils.copyProperties(pickAtt, pickAtt15);
                pickAtt15.setAttUrl(returnCarInfo.getVehicleVideo());
                pickAtt15.setAttType(VehiclePickReturnAttEnum.AttTypeEnum.VIDEO.getType());
                attList.add(pickAtt15);
            }
        }
        log.info("保存取车附件, attList={}", JSON.toJSONString(attList));
        if (CollectionUtils.isNotEmpty(attList)) {
            vehiclePickReturnAttMapper.batchInsert(attList);
        }
    }

    private void saveRerentOrder(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {
        RerentOrderExample delExample = new RerentOrderExample();
        delExample.createCriteria().andOrderIdEqualTo(orderInfo.getId());
        rerentOrderMapper.deleteByExample(delExample);

        OrderDetailExample example = new OrderDetailExample();
        example.createCriteria().andParentOrderIdEqualTo(orderInfo.getId());
        orderDetailMapper.deleteByExample(example);

        List<OrderDetailResp.PayRenewOrder> payRenewOrderList = orderDetailResp.getPayRenewOrderList();

        if (CollectionUtils.isNotEmpty(payRenewOrderList)) {
            payRenewOrderList.sort(Comparator.comparing(OrderDetailResp.PayRenewOrder::getRenewEndTime));
            Date lastDate = orderInfo.getLastReturnDate();
            for (OrderDetailResp.PayRenewOrder payRenewOrder : payRenewOrderList) {
                if (payRenewOrder.getPayStatus() != 20) {
                    continue;
                }

                RerentOrder rerentOrder = new RerentOrder();
                rerentOrder.setOrderId(orderInfo.getId());
                rerentOrder.setOrderStatus(RerentOrderStatusEnum.CONFIRMED.getStatus());
                rerentOrder.setStartTime(DateUtil.getFormatDate(payRenewOrder.getRenewStartTime(), DatePattern.UTC_SIMPLE_PATTERN));
                rerentOrder.setEndTime(DateUtil.getFormatDate(payRenewOrder.getRenewEndTime(), DatePattern.UTC_SIMPLE_PATTERN));
                rerentOrder.setStoreId(orderInfo.getPickupStoreId());
                rerentOrder.setSourceOrderId(payRenewOrder.getOrderRenewNo());
                rerentOrder.setPayAmount(BigDecimal.valueOf(payRenewOrder.getTotalPayAmount()).multiply(BigDecimal.valueOf(100)).intValue());
                rerentOrder.setReceivableAmount(BigDecimal.valueOf(payRenewOrder.getPayableAmount()).multiply(BigDecimal.valueOf(100)).intValue());
                rerentOrder.setPayMode(PayModeEnum.ONLINE_PAY.getMode());
                rerentOrder.setOrderSource(OrderSourceEnum.OFFLINE.getSource());
                rerentOrder.setOrderNo(orderComponent.generateOrderNo());
                Long orderTime = DateUtil.getFormatDate(payRenewOrder.getCreateTime(), DatePattern.UTC_SIMPLE_PATTERN).getTime();
                Long currTime = System.currentTimeMillis();
                rerentOrder.setOpUserId(0L);
                rerentOrder.setCreateTime(currTime);
                rerentOrder.setOpTime(currTime);
                rerentOrder.setOrderTime(orderTime);
                rerentOrder.setOrderOpTime(orderTime);
                rerentOrder.setLastVer(1);
                rerentOrder.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                rerentOrder.setParentOrderId(0L);
                if (lastDate.getTime() < rerentOrder.getEndTime().getTime()) {
                    lastDate = rerentOrder.getEndTime();
                }
                rerentOrderMapper.insert(rerentOrder);

                orderInfo.setLastReturnDate(lastDate);

                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setStoreId(orderInfo.getPickupStoreId() == null ? 0 : orderInfo.getPickupStoreId());
                orderDetail.setOrderId(rerentOrder.getId());
                orderDetail.setOrderType(RERENT_SERVICE.getType());
                orderDetail.setServiceId(0L);
                orderDetail.setName(RERENT_SERVICE.getName());
                orderDetail.setPrice(rerentOrder.getReceivableAmount());
                orderDetail.setAmount(orderDetail.getPrice());
                orderDetail.setQuantity(1.0);
                orderDetail.setPayKind(PayKindEnum.OTHER.getKind());
                orderDetail.setPayMode(PayModeEnum.ONLINE_PAY.getMode());
                orderDetail.setOrderStatus(OrderDetailStatusEnum.CONFIRMED.getStatus());
                orderDetail.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                if (orderInfo.getOrderStatus().intValue() == OrderStatusEnum.CANCELLED.getStatus().intValue()) {
                    orderDetail.setOrderStatus(OrderDetailStatusEnum.CANCELLED.getStatus());
                    orderDetail.setPayStatus(PayStatusEnum.ALL_REFUND.getStatus());
                }
                orderDetail.setParentOrderId(orderInfo.getId());
                OrderDetailExtraDTO orderDetailExtraDTO = new OrderDetailExtraDTO();
                orderDetailExtraDTO.setCalPer(1.0);
                orderDetail.setExtra(JSON.toJSONString(orderDetailExtraDTO));
                orderDetail.setSourceOrderId(rerentOrder.getSourceOrderId());
                orderDetail.setOrderSource(orderInfo.getOrderSource());
                orderDetail.setLastVer(1);
                orderDetail.setCreateTime(System.currentTimeMillis());
                orderDetail.setOpTime(orderDetail.getCreateTime());
                orderDetail.setOrderTime(orderInfo.getCreateTime());
                orderDetail.setOrderOpTime(orderInfo.getCreateTime());
                orderDetail.setOpUserId(0L);
                orderDetailMapper.insertSelective(orderDetail);
            }
            orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
            log.info("更新订单, orderId={}, lastReturnDate={}", orderInfo.getId(), lastDate);
        }

    }

    private void saveOrderDrtail(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {
        if (orderDetailResp.getChargeInfo().getChargeInfoList() != null) {
            OrderDetailExample delDetailExample = new OrderDetailExample();
            delDetailExample.createCriteria().andOrderIdEqualTo(orderInfo.getId()).andParentOrderIdEqualTo(0L);
            orderDetailMapper.deleteByExample(delDetailExample);
            List<OrderDetail> orderDetailList = new ArrayList<>();
            for (OrderDetailResp.ChargeInfo.ChargeInfoItem item : orderDetailResp.getChargeInfo().getChargeInfoList()) {
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setStoreId(orderInfo.getPickupStoreId() == null? 0 : orderInfo.getPickupStoreId());
                orderDetail.setOrderId(orderInfo.getId());
                orderDetail.setOrderType((byte) 0);
                orderDetail.setServiceId(0L);
                orderDetail.setName(item.getCode());
                orderDetail.setPrice(item.getPrice() == null ? 0 : BigDecimal.valueOf(item.getPrice()).multiply(BigDecimal.valueOf(100)).intValue());
//                orderDetail.setAmount(item.getStartingFee() == null ? 0 : BigDecimal.valueOf(item.getStartingFee()).multiply(BigDecimal.valueOf(100)).intValue());
                orderDetail.setAmount(orderDetail.getPrice());
                orderDetail.setQuantity(item.getNum() == null ? 0 : item.getNum().doubleValue());
                orderDetail.setPayKind(PayKindEnum.OTHER.getKind());
                orderDetail.setPayMode(PayModeEnum.ONLINE_PAY.getMode());
                orderDetail.setOrderStatus(OrderDetailStatusEnum.CONFIRMED.getStatus());
                orderDetail.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
                if (orderInfo.getOrderStatus().intValue() == OrderStatusEnum.CANCELLED.getStatus().intValue()) {
                    orderDetail.setOrderStatus(OrderDetailStatusEnum.CANCELLED.getStatus());
                    orderDetail.setPayStatus(PayStatusEnum.ALL_REFUND.getStatus());
                }
                orderDetail.setParentOrderId(0L);
                String serviceCode = "";
                if (item.getFeeCode() != null) {
                    if (item.getFeeCode() == 1) {
                        serviceCode = ServiceFeeTypeEnum.RENT_FEE_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 3) {
                        serviceCode = ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 4) {
                        serviceCode = ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 5) {
                        serviceCode = ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 6) {
                        serviceCode = ServiceFeeTypeEnum.STORE_PICKUP_NIGHT_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 7) {
                        serviceCode = ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 8) {
                        serviceCode = ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 9) {
                        serviceCode = ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 13 || item.getFeeCode() == 14) {
                        serviceCode = ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else if (item.getFeeCode() == 40) {
                        serviceCode = ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceCode();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }
                    else {
                        serviceCode = OrderTypeEnum.OTHER.getType().toString();
                        orderDetail.setOrderType(ServiceFeeTypeEnum.getTypeByCode(serviceCode));
                    }

                }
                OrderDetailExtraDTO orderDetailExtraDTO = new OrderDetailExtraDTO();
                orderDetailExtraDTO.setCode(serviceCode);
                orderDetailExtraDTO.setDesc(item.getDesc());
                orderDetailExtraDTO.setCalPer(1.0);
                orderDetail.setExtra(JSON.toJSONString(orderDetailExtraDTO));
                orderDetail.setSourceOrderId(orderInfo.getSourceOrderId());
                orderDetail.setOrderSource(orderInfo.getOrderSource());
                orderDetail.setLastVer(1);
                orderDetail.setCreateTime(System.currentTimeMillis());
                orderDetail.setOpTime(orderDetail.getCreateTime());
                orderDetail.setOrderTime(orderInfo.getCreateTime());
                orderDetail.setOrderOpTime(orderInfo.getCreateTime());
                orderDetail.setOpUserId(0L);
                orderDetailList.add(orderDetail);
            }
            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                orderDetailMapper.batchInsert(orderDetailList);
            }
        }
    }

    private void saveMember(OrderDetailResp orderDetailResp, OrderInfo orderInfo) {
        if (orderDetailResp.getDriverInfo() != null) {
            if (orderInfo.getId() == null) {
                OrderMember orderMember = new OrderMember();
                buildOrderMember(orderDetailResp.getDriverInfo(), orderInfo, orderMember);
                if (orderMemberMapper.insertSelective(orderMember) < 1) {
                    log.error("保存订单会员失败, reptileOrderDTO={}", JSON.toJSONString(orderDetailResp));
                    throw new BizException("保存订单会员失败");
                }
                orderInfo.setOrderMemberId(orderMember.getId());
            } else {
                OrderMember orderMember = orderMemberMapper.selectByPrimaryKey(orderInfo.getOrderMemberId());
                buildOrderMember(orderDetailResp.getDriverInfo(), orderInfo, orderMember);
                if (orderMemberMapper.updateByPrimaryKeySelective(orderMember) < 1) {
                    log.error("更新订单会员失败, reptileOrderDTO={}", JSON.toJSONString(orderDetailResp));
                    throw new BizException("更新订单会员失败");
                }
            }
        }
    }

    private void buildOrderMember(OrderDetailResp.DriverInfo driverInfo, OrderInfo orderInfo, OrderMember orderMember) {
        orderInfo.setMobile(driverInfo.getDriverPhone());
        orderInfo.setUserName(driverInfo.getDriverName());
        orderMember.setMobile(driverInfo.getDriverPhone());
        orderMember.setUserName(driverInfo.getDriverName());
        orderMember.setIdcardNo(driverInfo.getDriverIdCard());
    }


    private void saveOrder(Long channelId, Long merchantId, OrderDetailResp resp, OrderInfo orderInfo) {
        OrderDetailResp.OrderInfo respOrderInfo = resp.getOrderInfo();
        OrderDetailResp.DepositInfo respDepositInfo = resp.getDepositInfo();
        OrderDetailResp.DriverInfo driverInfo = resp.getDriverInfo();
        OrderDetailResp.ChargeInfo chargeInfo = resp.getChargeInfo();
        OrderDetailResp.OrderInfo.VehicleInfo vehicleInfo = resp.getOrderInfo().getVehicleInfo();
        OrderDetailResp.OrderInfo.SiteInfo pickSiteInfo = respOrderInfo.getPickUpCarSiteInfo();
        OrderDetailResp.OrderInfo.SiteInfo returnSiteInfo = respOrderInfo.getReturnCarSiteInfo();

        orderInfo.setOrderType((byte) 0);
        orderInfo.setSourceOrderId(respOrderInfo.getOutOrderNo());
        orderInfo.setOrderSource(channelId.byteValue());
        /** 订单状态:40-待取车,50-待还车,60-已完成,70-已取消 */
        orderInfo.setOrderStatus(OrderStatusEnum.UNSUBMITTED.getStatus());
        if (respOrderInfo.getStatus() != null) {
            if (respOrderInfo.getStatus() == 40) {
                orderInfo.setOrderStatus(OrderStatusEnum.SCHEDULED.getStatus());
            } else if (respOrderInfo.getStatus() == 50) {
                orderInfo.setOrderStatus(OrderStatusEnum.PICKED_UP.getStatus());
            } else if (respOrderInfo.getStatus() == 60) {
                orderInfo.setOrderStatus(OrderStatusEnum.RETURNED.getStatus());
            } else if (respOrderInfo.getStatus() == 70) {
                orderInfo.setOrderStatus(OrderStatusEnum.CANCELLED.getStatus());
            }
        }

        orderInfo.setSourceInnerOrderId(respOrderInfo.getGuid());
        orderInfo.setUserName(driverInfo.getDriverName());
        orderInfo.setMobile(driverInfo.getDriverPhone());

        orderInfo.setMerchantId(merchantId);
        orderInfo.setPickupDate(DateUtil.getFormatDate(respOrderInfo.getUseCarTimeStart(), DateUtil.yyyyMMddHHmm));
        orderInfo.setReturnDate(DateUtil.getFormatDate(respOrderInfo.getUseCarTimeEnd(), DateUtil.yyyyMMddHHmm));
        // 哈啰没有城市
        orderInfo.setPickupCityId(0L);
        orderInfo.setReturnCityId(0L);
        orderInfo.setPickupStoreId(0L);
        orderInfo.setReturnStoreId(0L);
        orderInfo.setOrderTime(System.currentTimeMillis());
        orderInfo.setOrderOpTime(orderInfo.getOrderTime());

        orderInfo.setPickupAddrType(PickupTypeEnum.CAR_SHOP.getType());
        /** 1 接用户到店取车、2 送车上门、3 自行取车 */
        if (respOrderInfo.getPickUpCarType() != null) {
            if (respOrderInfo.getPickUpCarType() == 1) {
                orderInfo.setPickupAddrType(PickupTypeEnum.PICK_UP.getType());
            } else if (respOrderInfo.getPickUpCarType() == 2) {
                orderInfo.setPickupAddrType(PickupTypeEnum.CAR_DOOR.getType());
            } else if (respOrderInfo.getPickUpCarType() == 3) {
                orderInfo.setPickupAddrType(PickupTypeEnum.CAR_SHOP.getType());
            }
        }
        orderInfo.setReturnAddrType(PickupTypeEnum.CAR_SHOP.getType());
        if (respOrderInfo.getReturnCarType() != null) {
            if (respOrderInfo.getReturnCarType() == 1) {
                orderInfo.setReturnAddrType(PickupTypeEnum.PICK_UP.getType());
            } else if (respOrderInfo.getReturnCarType() == 2) {
                orderInfo.setReturnAddrType(PickupTypeEnum.CAR_DOOR.getType());
            } else if (respOrderInfo.getReturnCarType() == 3) {
                orderInfo.setReturnAddrType(PickupTypeEnum.CAR_SHOP.getType());
            }
        }

        if (chargeInfo.getRealPayChargeInfoTotal() != null) {
            orderInfo.setPayAmount(BigDecimal.valueOf(chargeInfo.getRealPayChargeInfoTotal()).multiply(BigDecimal.valueOf(100)).intValue());
        } else {
            orderInfo.setPayAmount(BigDecimal.valueOf(chargeInfo.getChargeInfoTotal()).multiply(BigDecimal.valueOf(100)).intValue());
        }
        orderInfo.setReceivableAmount(BigDecimal.valueOf(chargeInfo.getChargeInfoTotal()).multiply(BigDecimal.valueOf(100)).intValue());
        orderInfo.setPickupAddr(respOrderInfo.getPickUpCarAddress());
        orderInfo.setReturnAddr(respOrderInfo.getReturnCarAddress());
        orderInfo.setLastReturnDate(orderInfo.getReturnDate());
        if (respDepositInfo != null) {
            orderInfo.setFreeDepositDegree(FreeDepositTypeEnum.NOT_SUPPORT.getType().intValue());
            if (respDepositInfo.getFreeDepositType() != null) {
                if (respDepositInfo.getFreeDepositType() == 0 || respDepositInfo.getFreeDepositType() == 3) {
                    orderInfo.setFreeDepositDegree(FreeDepositTypeEnum.FREE_ALL.getType().intValue());
                }
            }
        }

        orderInfo.setPayStatus(PayStatusEnum.ALL_PAID.getStatus());
        orderInfo.setOpTime(orderInfo.getCreateTime());
        OrderExtraDTO orderExtraDTO = new OrderExtraDTO();
        orderExtraDTO.setPickUpIdNo(driverInfo.getDriverIdCard());
        orderExtraDTO.setPickUpIdType(1);
        orderExtraDTO.setIsHelloReptile(1);
        orderInfo.setExtra(JSON.toJSONString(orderExtraDTO));

        orderInfo.setVehicleModelId(
                StringUtils.isNotBlank(resp.getSettleCarGoodsId()) && !"null".equals(resp.getSettleCarGoodsId()) ? Long.valueOf(resp.getSettleCarGoodsId()) :
                        StringUtils.isNotBlank(resp.getGoodsId()) && !"null".equals(resp.getGoodsId()) ? Long.valueOf(resp.getGoodsId()) : null);

        orderInfo.setVehicleNo(vehicleInfo.getVehicleLicense());
        orderInfo.setVehicleId(
                StringUtils.isBlank(resp.getCarId()) || "null".equals(resp.getCarId()) || "0".equals(resp.getCarId()) ? null : Long.valueOf(resp.getCarId()));
        orderInfo.setVehicleName(vehicleInfo.getVehicleName() + vehicleInfo.getVehicleModelName());

        if (pickSiteInfo != null && pickSiteInfo.getSiteId()!= null) {
            Long saasId = thirdIdRelationService.getMappingForSaas(channelId, IdRelationEnum.STORE.getType(), pickSiteInfo.getSiteId(), merchantId);
            if (saasId!= null) {
                orderInfo.setPickupStoreId(saasId);
            }
        }
        if (returnSiteInfo != null && returnSiteInfo.getSiteId()!= null) {
            Long saasId = thirdIdRelationService.getMappingForSaas(channelId, IdRelationEnum.STORE.getType(), returnSiteInfo.getSiteId(), merchantId);
            if (saasId!= null) {
                orderInfo.setReturnStoreId(saasId);
            }
        }
        if (orderInfo.getVehicleId() != null) {
            Long saasId = thirdVehicleIdRelationService.getMappingForSaas(channelId, OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), String.valueOf(orderInfo.getVehicleId()), merchantId, null);
            if (saasId!= null) {
                orderInfo.setVehicleId(saasId);
                Result<VehicleInfoVO> result = vehicleInfoService.getBaseById(saasId, false);
                log.info("syncHelloOrder, 更新的车辆信息={}", JSON.toJSONString(result));
                if (result.isSuccess() && result.getModel() != null) {
                    orderInfo.setVehicleNo(result.getModel().getLicense());
                    orderInfo.setVehicleName(result.getModel().getVehicleModelName());
                }
            }
        }
        if (orderInfo.getVehicleModelId() != null) {
            Long saasId = thirdVehicleIdRelationService.getVehicleForHelloGoodIds(String.valueOf(orderInfo.getVehicleModelId()), orderInfo.getPickupStoreId(), merchantId);
            if (saasId!= null) {
                orderInfo.setVehicleModelId(saasId);
            }
        }
        if (orderInfo.getId() == null) {
            orderInfo.setOrderNo(orderComponent.generateOrderNo());
            orderInfo.setCreateTime(System.currentTimeMillis());
            orderInfo.setLastVer(1);

            boolean needVehicleSettle = false;
            boolean needPushMessage = false;

            if (orderInfoMapper.insertSelective(orderInfo) < 1) {
                log.error("syncHelloOrder, 保存订单失败, orderDTO={}", JSON.toJSONString(resp));
                throw new BizException("保存订单失败");
            }

            if (orderInfo.getVehicleId() == null) {
                VehicleBusyParam parm = new VehicleBusyParam();
                parm.setEndTime(orderInfo.getReturnDate().getTime());
                parm.setStartTime(orderInfo.getPickupDate().getTime());
                parm.setFrcedScheduling((byte) 1);
                parm.setSourceType(VehicleBusyEnum.ORDER.getValueInt());
                parm.setMerchantId(merchantId);
                parm.setStoreId(orderInfo.getPickupStoreId());
                parm.setReturnStoreId(orderInfo.getReturnStoreId());
                parm.setSourceId(orderInfo.getId());
                parm.setVehicleModelId(orderInfo.getVehicleModelId());
                parm.setChannelId(orderInfo.getOrderSource().longValue());
                parm.setIsAllopatry(!Objects.equals(orderInfo.getPickupStoreId(), orderInfo.getReturnStoreId()));
                orderComponent.setCreateOffline(orderInfo.getSourceOrderId(), parm);
                Result<Long> vehicleIdResult = vehicleBusyService.getVehicleAndLock(parm);
                log.info("syncHelloOrder, 排车锁库存请求={}, result={}", JSON.toJSONString(parm), JSON.toJSONString(vehicleIdResult));
                if (vehicleIdResult.isSuccess() && vehicleIdResult.getModel() != null) {
                    needVehicleSettle = true;
                    orderInfo.setVehicleId(vehicleIdResult.getModel());
                    Result<VehicleInfoVO> result = vehicleInfoService.getBaseById(vehicleIdResult.getModel(), false);
                    log.info("syncHelloOrder, 排车锁库存车辆信息={}", vehicleIdResult.getModel());
                    if (result.isSuccess() && result.getModel() != null) {
                        orderInfo.setVehicleNo(result.getModel().getLicense());
                        orderInfo.setVehicleName(result.getModel().getVehicleModelName());
                    }
                } else {
                    needPushMessage = true;
                }
            }

            if (orderInfoMapper.updateByPrimaryKeySelective(orderInfo) < 1) {
                log.error("syncHelloOrder, 更新订单失败, orderDTO={}", JSON.toJSONString(resp));
                throw new BizException("更新订单失败");
            }

            if (needVehicleSettle) {
                VehicleBusyQueryParam VehicleBusyQueryParam = new VehicleBusyQueryParam();
                VehicleBusyQueryParam.setSourceId(orderInfo.getId());
                VehicleBusyQueryParam.setSourceType(VehicleBusyEnum.ORDER.getValueInt());
                VehicleBusyQueryParam.setMerchantId(merchantId);
                VehicleBusyEntityVO vehicleBusyEntityVO = vehicleBusyService.queryByParam(VehicleBusyQueryParam).getModel().stream().findFirst().orElse(null);
                if (vehicleBusyEntityVO == null) {
                    log.error("syncHelloOrder, 排车的库存不存在");
                } else {
                    VehicleSettleReq req = new VehicleSettleReq();
                    req.setOrderId(orderInfo.getId());
                    req.setSourceOrderId(orderInfo.getSourceOrderId());
                    req.setStoreId(orderInfo.getPickupStoreId());
                    req.setVehicleModelId(orderInfo.getVehicleModelId());
                    req.setVehicleId(orderInfo.getVehicleId());
                    req.setPickupDate(orderInfo.getPickupDate().getTime());
                    req.setReturnDate(orderInfo.getReturnDate().getTime());
                    req.setEndIntervalTime(vehicleBusyEntityVO.getEndIntervalTime());
                    req.setHelloOrderId(orderInfo.getSourceInnerOrderId());
                    ApiResultResp apiResultResp = platformBiz.vehicleSettle(orderInfo.getMerchantId(), orderInfo.getOrderSource().longValue(), req);
                    log.info("syncHelloOrder, 排车锁库存后调用hello接口请求={}, result={}", JSON.toJSONString(req), JSON.toJSONString(apiResultResp));
                    if (!apiResultResp.success()) {
                        needPushMessage = true;
                    }
                }
            }

            if (needPushMessage) {
                pushVehicleSettleErrorMsg(merchantId, orderInfo);
            }
        } else {
            orderInfo.setLastVer(orderInfo.getLastVer() + 1);
            if (orderInfoMapper.updateByPrimaryKeySelective(orderInfo) < 1) {
                log.error("更新订单失败, orderDTO={}", JSON.toJSONString(resp));
                throw new BizException("更新订单失败");
            }
        }
    }

    private void pushVehicleSettleErrorMsg(Long merchantId, OrderInfo orderInfo) {
        PushVO pushVO = new PushVO();
        pushVO.setPushTypeEnum(PushTypeEnum.TYPE_ORDER);
        pushVO.setMerchantId(merchantId);
        pushVO.setMpTemplate(WxMpTemplateEnum.VEHICLE_SETTLE_ERROR.getTemplateId());
        Map<String, WxMsgVo.Template> data = new HashMap<>();
        data.put("thing3", new WxMsgVo.Template("哈啰订单排车失败"));
        data.put("thing8", new WxMsgVo.Template(String.format("%s, %s用车订单, %s", orderInfo.getUserName(),
                DateUtil.getFormatDateStr(orderInfo.getPickupDate(), DateUtil.MMdd), orderInfo.getSourceOrderId()), WxMsgVo.TEXT_NORMAL_20));
        data.put("thing10", new WxMsgVo.Template("快去哈啰排车"));
        pushVO.setMpPushObj(data);
        pushVO.setStoreId(orderInfo.getPickupStoreId());
        pushMsgService.push(pushVO);
    }
}
