package com.ql.rent.provider.trade;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.dao.trade.TransferContractStatusMapper;
import com.ql.rent.dao.trade.TransferContractVersionMapper;
import com.ql.rent.dao.trade.VehicleIllegalOrderMapper;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.WxMpPageEnum;
import com.ql.rent.enums.WxMpTemplateEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.merchant.PushTypeEnum;
import com.ql.rent.enums.trade.MallServiceOrderStatusEnum;
import com.ql.rent.enums.trade.OrderStatusEnum;
import com.ql.rent.enums.trade.TransferContractStatusEnum;
import com.ql.rent.param.trade.*;
import com.ql.rent.remote.vehicle.enums.ViolationResultCodeEnum;
import com.ql.rent.remote.vehicle.enums.licenseTypeEnum;
import com.ql.rent.remote.vehicle.vo.request.AbortContractReq;
import com.ql.rent.remote.vehicle.vo.request.CancelContractReq;
import com.ql.rent.remote.vehicle.vo.request.SubmitContractReq;
import com.ql.rent.remote.vehicle.vo.response.SubmitContractResponse;
import com.ql.rent.remote.vehicle.vo.response.SyncContractResponse;
import com.ql.rent.service.common.IPushMsgService;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.vo.common.PushVO;
import com.ql.rent.vo.login.WxMsgVo;
import com.ql.rent.vo.merchant.TrafficManagementBindDTO;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TransferContractStatusServiceImpl implements ITransferContractStatusService {
    @Resource
    private TransferContractStatusMapper transferContractStatusMapper;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderMemberService orderMemberService;
    @Resource
    private TrafficManagementBindService trafficManagementBindService;
    @Resource
    private TransferContractVersionMapper transferContractVersionMapper;
    @Resource
    private VehicleIllegalOrderMapper vehicleIllegalOrderMapper;
    @Resource
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;
    @Resource
    private IMallServiceOrderInfoService mallServiceOrderInfoService;
    @Resource
    private RemoteViolationService remoteViolationService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehicleInfoService vehicleInfoService;
    @Resource
    private IOrderSlaveService orderSlaveService;
    @Resource
    private IPushMsgService pushMsgService;
    @Resource
    private IRedisService redisService;

    @Value("${transfer.contract.renewal.days}")
    private Integer renewalDays;

    @Override
    public Byte getTransferStatus(Long merchantId, Long vehicleId, Long orderId, Date illegalTime) {
        Byte transferStatus = TransferContractStatusEnum.UNKNOWN.getValue();
        if (orderId == null || orderId == 0L) {
            return transferStatus;
        }
        TransferContractStatusExample contractStatusExample = new TransferContractStatusExample();
        contractStatusExample.createCriteria().andVehicleIdEqualTo(vehicleId)
                .andOrderIdEqualTo(orderId)
                .andMerchantIdEqualTo(merchantId)
                .andBeginTimeLessThanOrEqualTo(illegalTime)
//                .andStatusNotEqualTo(TransferContractStatusEnum.CANCELLED.getValue())
                .andEndTimeGreaterThanOrEqualTo(illegalTime);
        contractStatusExample.setOrderByClause("id desc limit 1");
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(contractStatusExample);
        if (CollectionUtils.isNotEmpty(contractStatusList)) {
            transferStatus = contractStatusList.get(0).getStatus();
        }
        return transferStatus;
    }

    @Override
    public Result<Boolean> saveTransferContractStatus(TransferContractStatusParam param) {
        Long nowTime = System.currentTimeMillis();

        TransferContractStatus transferContractStatus = new TransferContractStatus();
        BeanUtils.copyProperties(param, transferContractStatus);
        transferContractStatus.setOpTime(nowTime);
        if (param.getId() == null) {
            transferContractStatus.setLastVer(1);
            transferContractStatus.setCreateTime(nowTime);
            transferContractStatusMapper.insertSelective(transferContractStatus);
        } else {
            TransferContractStatus oldTransferContract = transferContractStatusMapper.selectByPrimaryKey(param.getId());
            transferContractStatus.setLastVer(oldTransferContract.getLastVer() + 1);
            transferContractStatusMapper.updateByPrimaryKeySelective(transferContractStatus);
        }
        param.setId(transferContractStatus.getId());
        return ResultUtil.successResult(true);
    }

    @Override
    public Result<List<TransferContractStatusVo>> getTransferContractStatus(TransferContractStatusParam param) {
        if (param == null || param.getMerchantId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        TransferContractStatusExample example = new TransferContractStatusExample();
        TransferContractStatusExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(param.getMerchantId());
        if (param.getVehicleId() != null) {
            criteria.andVehicleIdEqualTo(param.getVehicleId());
        }
        if (param.getOrderId() != null) {
            criteria.andOrderIdEqualTo(param.getOrderId());
        }
        if (param.getStatus() != null) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (param.getStartBeginTime() != null) {
            criteria.andBeginTimeGreaterThanOrEqualTo(param.getStartBeginTime());
        }
        if (param.getEndBeginTime() != null) {
            criteria.andBeginTimeLessThanOrEqualTo(param.getEndBeginTime());
        }
        if (param.getStartEndTime() != null) {
            criteria.andEndTimeGreaterThanOrEqualTo(param.getStartEndTime());
        }
        if (param.getEndEndTime() != null) {
            criteria.andEndTimeLessThanOrEqualTo(param.getEndEndTime());
        }
        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            criteria.andStatusIn(param.getStatusList());
        }

        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
        List<TransferContractStatusVo> contractStatusVoList = new ArrayList<>();
        for (TransferContractStatus transferContractStatus : contractStatusList) {
            TransferContractStatusVo transferContractStatusVo = new TransferContractStatusVo();
            BeanUtils.copyProperties(transferContractStatus, transferContractStatusVo);
            contractStatusVoList.add(transferContractStatusVo);
        }

        return ResultUtil.successResult(contractStatusVoList);
    }

    @Override
    public TransferContractStatusVo getLatestContract(Long merchantId, Long vehicleId, Long orderId) {
        // 拉取更新最新合同
//        this.syncContractByJob();

        TransferContractStatusExample example = new TransferContractStatusExample();
        TransferContractStatusExample.Criteria criteria = example.createCriteria().andMerchantIdEqualTo(merchantId);
//        List<Byte> statusList = TransferContractStatusEnum.needAbort();
//        statusList.add(TransferContractStatusEnum.ABORTED.getValue());
        criteria.andStatusIn(TransferContractStatusEnum.maySuccessReport());
        if (vehicleId != null) {
            criteria.andVehicleIdEqualTo(vehicleId);
        }
        if (orderId != null) {
            criteria.andOrderIdEqualTo(orderId);
        }
        // 三方更新接口可能获取不到最新状态，加上id排序处理下
        example.setOrderByClause("end_time desc,id desc limit 1");
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(contractStatusList)) {
            return null;
        }
        TransferContractStatus contractStatus = contractStatusList.get(0);
        TransferContractStatusVo vo = new TransferContractStatusVo();
        BeanUtils.copyProperties(contractStatus, vo);
        return vo;
    }

    private Date truncateToMinutes(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.SECOND, 0);      // 清除秒
        calendar.set(Calendar.MILLISECOND, 0); // 清除毫秒
        return calendar.getTime();
    }

    @Override
    public Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime) {
        if (orderId == null || beginTime == null || endTime == null) {
            return ResultUtil.failResult("参数错误");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        // 合同开始时间需要在近24小时内
        if (beginTime.before(calendar.getTime())) {
            log.info("违章转移 参数check 合同开始时间需要在近24小时内, orderId:{}, beginTime:{}, endTime:{}", orderId, beginTime, endTime);
            return ResultUtil.failResult("合同开始时间需要在近24小时内");
        }
        if (truncateToMinutes(beginTime).compareTo(truncateToMinutes(endTime)) >= 0) {
            log.info("违章转移 参数check 合同结束时间必须晚于开始时间一分钟, orderId:{}, beginTime:{}, endTime:{}", orderId, beginTime, endTime);
            return ResultUtil.failResult("合同结束时间必须晚于开始时间一分钟");
        }

        Result<OrderInfoVo> orderInfoResult = orderService.getOrderInfo(orderId);
        if (ResultUtil.isModelNull(orderInfoResult)) {
            return ResultUtil.failResult("订单不存在");
        }
        OrderInfoVo orderInfo = orderInfoResult.getModel();
        Long merchantId = orderInfo.getMerchantId();

        // 查询该车辆是否允许被转移
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(merchantId);
        query.setVehicleId(orderInfo.getVehicleId());
        Result<List<VehicleIllegalTransferDetailVo>> transferDetailListResult =
                vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
        if (ResultUtil.isModelNull(transferDetailListResult) || CollectionUtils.isEmpty(transferDetailListResult.getModel())) {
            return ResultUtil.failResult("车辆未绑定转移订单");
        }

        // 判断订单是否可用
        MallServiceOrderParam param = new MallServiceOrderParam();
        param.setOrderIds(transferDetailListResult.getModel().stream()
                .map(VehicleIllegalTransferDetailVo::getOrderId).distinct().collect(Collectors.toList()));
        param.setOrderStatus(MallServiceOrderStatusEnum.ALL_PAID.getStatus().intValue());
        param.setStartExpirationDate(new Date());
        List<MallServiceOrderInfoDTO> serviceOrderInfoDTOS =
                mallServiceOrderInfoService.serviceOrderList(merchantId, param);
        if (CollectionUtils.isEmpty(serviceOrderInfoDTOS)) {
            return ResultUtil.failResult("该车辆不存在有效违章转移订单");
        }

        Result<UserCertificateVo> userCertificateVoResult = orderMemberService.getUserCertificate(orderId);
        if (ResultUtil.isModelNull(userCertificateVoResult)) {
            return ResultUtil.failResult("用户信息错误");
        }
        UserCertificateVo userCertificateVo = userCertificateVoResult.getModel();
        if (!IdcardUtil.isValidCard(userCertificateVo.getIdcardNo())) {
            log.info("违章转移 录入check 身份证信息错误, orderId:{}, idcardNo:{}", orderId, userCertificateVo.getIdcardNo());
            return ResultUtil.failResult("身份证信息错误 " + userCertificateVo.getIdcardNo());
        }

        // 根据车辆id&merchantid 查 122 账号
        TrafficManagementBindDTO trafficManagementBindDTO =
                trafficManagementBindService.selectIllegalTransferAccount(merchantId, orderInfo.getVehicleId());
        if (trafficManagementBindDTO == null) {
            return ResultUtil.failResult("该车辆未绑定122账号");
        }

        // 判断同一辆车是否录入过相同合同，并判断是否合同重叠
        TransferContractStatusParam contractStatusParam = new TransferContractStatusParam();
        contractStatusParam.setMerchantId(merchantId);
        contractStatusParam.setVehicleId(orderInfo.getVehicleId());
        contractStatusParam.setStartEndTime(beginTime);
        contractStatusParam.setEndBeginTime(endTime);
//        contractStatusParam.setStatusList(TransferContractStatusEnum.repeatReport());
        Result<List<TransferContractStatusVo>> transferContractStatus = this.getTransferContractStatus(contractStatusParam);
        if (transferContractStatus.isSuccess() && CollectionUtils.isNotEmpty(transferContractStatus.getModel())) {
            // 如果合同业务信息不变，并且合同状态为上报失败，不新增合同，而是重试旧合同， 如果合同为上报中状态，三方会重试，直接return；
            TransferContractStatusVo transferContractStatusVo = transferContractStatus.getModel().stream()
                    .filter(e -> e.getOrderId().equals(orderId)
                            && e.getVehicleId().equals(orderInfo.getVehicleId())
                            && e.getDriverName().equals(userCertificateVo.getUserName())
                            && e.getDriverPhone().equals(userCertificateVo.getMobile())
                            && e.getDriverId().equals(userCertificateVo.getIdcardNo())
                            && e.getAccount().equals(trafficManagementBindDTO.getUsername())
                            && e.getCarNumber().equals(orderInfo.getVehicleNo())
                            && e.getBeginTime().equals(beginTime)
                            && e.getEndTime().equals(endTime)
                            && e.getMerchantId().equals(merchantId)
                            && (e.getStatus().equals(TransferContractStatusEnum.REPORTING.getValue())
                                || e.getStatus().equals(TransferContractStatusEnum.REPORT_FAIL.getValue())))
                    .findFirst().orElse(null);

            if (transferContractStatusVo != null) {
                if (transferContractStatusVo.getStatus().equals(TransferContractStatusEnum.REPORTING.getValue())) {
                    log.info("违章转移 录取check 上报中的相同业务参数合同不重复上报, orderId={}, beginTime:{}, endTime:{}, transferContractStatusVo={}",
                            orderId, beginTime, endTime, transferContractStatusVo);
                    return ResultUtil.successResult(true);
                } else if (transferContractStatusVo.getStatus().equals(TransferContractStatusEnum.REPORT_FAIL.getValue())) {
                    log.info("违章转移 上报失败的相同业务参数合同以旧合同的参数上报, orderId={}, beginTime:{}, endTime:{}, transferContractStatusVo={}",
                            orderId, beginTime, endTime, transferContractStatusVo);
                    TransferContractStatus retryContract = new TransferContractStatus();
                    BeanUtils.copyProperties(transferContractStatusVo, retryContract);
                    this.retrySameContract(retryContract, false);
                    return ResultUtil.successResult(true);
                }
            }

            List<TransferContractStatusVo> checkList = transferContractStatus.getModel().stream()
                    .filter(e -> TransferContractStatusEnum.repeatReport().contains(e.getStatus())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(checkList)) {
                log.info("违章转移 录入check 合同时间段重叠, orderId:{}, beginTime:{}, endTime:{}, contractId:{}, transferContractStatus={}",
                        orderId, beginTime, endTime, transferContractStatus.getModel().get(0).getContractId(), JSON.toJSONString(transferContractStatus.getModel().get(0)));
                return ResultUtil.failResult("合同时间段重叠" + transferContractStatus.getModel().get(0).getContractId());
            }
        }

        Date tempEndTime = beginTime;
        calendar.setTime(tempEndTime);
        calendar.add(Calendar.MINUTE, -1);
        tempEndTime = calendar.getTime();

        // 合同上报时间段不可超过一个月
        while (tempEndTime.compareTo(endTime) < 0) {
            calendar.setTime(tempEndTime);
            calendar.add(Calendar.MINUTE, 1);
            Date tempBeginTime = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, renewalDays);
            tempEndTime = calendar.getTime();
            if (tempEndTime.after(endTime)) {
                tempEndTime = endTime;
            }

            this.submitOneContract(orderId, tempBeginTime, tempEndTime, orderInfo, trafficManagementBindDTO, userCertificateVo, merchantId);
        }
        return ResultUtil.successResult(true);
    }

    /**
     * 上传1份合同
     */
    private void submitOneContract(Long orderId, Date beginTime, Date endTime, OrderInfoVo orderInfo,
                                   TrafficManagementBindDTO trafficManagementBindDTO, UserCertificateVo userCertificateVo, Long merchantId) {
        SubmitContractReq submitContractReq = new SubmitContractReq();
        submitContractReq.setRentType(2);
        submitContractReq.setCarType(getCarType(orderInfo.getVehicleNo()));
        submitContractReq.setAccount(trafficManagementBindDTO.getUsername());
        submitContractReq.setDriverName(userCertificateVo.getUserName());
        submitContractReq.setDriverPhone(userCertificateVo.getMobile());
        submitContractReq.setDriverId(userCertificateVo.getIdcardNo());
        submitContractReq.setContractNo(generateContractNo(orderId));
        submitContractReq.setCarNumber(orderInfo.getVehicleNo());
        submitContractReq.setBeginTime(beginTime);
        submitContractReq.setEndTime(endTime);

        // 保存记录
        TransferContractStatusParam param = TransferContractStatusParam.builder()
                .orderId(orderId)
                .vehicleId(orderInfo.getVehicleId())
//                .contractId(contractResponse.getOrderId())
//                .status(contractResponse.getState().byteValue())
//                .message(contractResponse.getPostMsg())
                .driverName(submitContractReq.getDriverName())
                .driverPhone(submitContractReq.getDriverPhone())
                .driverId(submitContractReq.getDriverId())
                .carNumber(submitContractReq.getCarNumber())
                .contractNo(submitContractReq.getContractNo())
                .beginTime(submitContractReq.getBeginTime())
                .endTime(submitContractReq.getEndTime())
                .merchantId(merchantId)
                .carType(getCarType(orderInfo.getVehicleNo()))
                .account(submitContractReq.getAccount())
                .build();
        this.saveTransferContractStatus(param);

        // 调用三方录入合同
        Result<SubmitContractResponse> responseResult = remoteViolationService.submitContract(submitContractReq);
        Integer state;
        TransferContractStatusParam updParam = new TransferContractStatusParam();
        updParam.setId(param.getId());
        if (ResultUtil.isModelNotNull(responseResult)) {
            SubmitContractResponse contractResponse = responseResult.getModel();
            state = contractResponse.getState();
            updParam.setContractId(contractResponse.getOrderId());
            updParam.setStatus(contractResponse.getState().byteValue());
            updParam.setMessage(contractResponse.getPostMsg());
            updParam.setPostCode(contractResponse.getPostCode());
            this.pushTransferMsg(orderInfo, contractResponse, beginTime, endTime, submitContractReq.getAccount());
        } else {
            state = (int) TransferContractStatusEnum.REPORT_FAIL.getValue();
            updParam.setErrorMsg(responseResult.getMessage());
            updParam.setStatus(state.byteValue());
        }
        this.saveTransferContractStatus(updParam);

        // 更新违章的转移状态
        updIllegalTransferStatus(orderInfo.getVehicleId(), orderInfo.getId(), submitContractReq.getBeginTime(),
                submitContractReq.getEndTime(), state);

    }

    /**
     * 重试相同合同
     */
    public void retrySameContract(TransferContractStatus param, boolean isCheck) {
        Date beginTime = param.getBeginTime();
        Date endTime = param.getEndTime();

        if (isCheck) {
            // 合同开始时间需要在近24小时内
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            if (beginTime.before(calendar.getTime())) {
                log.info("违章转移 重试合同 参数check 合同开始时间需要在近24小时内, param:{}", JSON.toJSONString(param));
                return;
            }

            if (!IdcardUtil.isValidCard(param.getDriverId())) {
                log.info("违章转移 重试合同 参数check 合同身份证错误, param:{}", JSON.toJSONString(param));
                return;
            }

            if (!Validator.isMobile(param.getDriverPhone())) {
                log.info("违章转移 重试合同 参数check 合同手机号错误, param:{}", JSON.toJSONString(param));
                return;
            }

            if (truncateToMinutes(beginTime).compareTo(truncateToMinutes(endTime)) >= 0) {
                log.info("违章转移 重试合同 参数check 合同结束时间必须晚于开始时间一分钟, param:{}", JSON.toJSONString(param));
                return;
            }

            // 判断同一辆车的合同时间段不重叠
            TransferContractStatusParam contractStatusParam = new TransferContractStatusParam();
            contractStatusParam.setMerchantId(param.getMerchantId());
            contractStatusParam.setVehicleId(param.getVehicleId());
            contractStatusParam.setStartEndTime(beginTime);
            contractStatusParam.setEndBeginTime(endTime);
            contractStatusParam.setStatusList(TransferContractStatusEnum.repeatReport());
            Result<List<TransferContractStatusVo>> transferContractStatusList = this.getTransferContractStatus(contractStatusParam);
            if (transferContractStatusList.isSuccess() && CollectionUtils.isNotEmpty(transferContractStatusList.getModel())) {
                log.info("违章转移 重试合同 录入check 合同时间段重叠, beginTime:{}, endTime:{} contractId:{}, param:{}",
                        beginTime, endTime, transferContractStatusList.getModel().get(0).getContractId(), param);
                return;
            }
        }

        SubmitContractReq submitContractReq = new SubmitContractReq();
        submitContractReq.setAccount(param.getAccount());
        submitContractReq.setDriverName(param.getDriverName());
        submitContractReq.setDriverPhone(param.getDriverPhone());
        submitContractReq.setDriverId(param.getDriverId());
        submitContractReq.setCarNumber(param.getCarNumber());
        submitContractReq.setCarType(param.getCarType());
        submitContractReq.setContractNo(param.getContractNo());
        submitContractReq.setBeginTime(param.getBeginTime());
        submitContractReq.setEndTime(endTime);
        submitContractReq.setContractNo(param.getContractNo());
        submitContractReq.setRentType(2);

        // 调用三方重试合同
        log.info("违章转移 重试合同 orderId={}, isCheck={}, param={}", param.getOrderId(), isCheck, JSON.toJSONString(param));
        Result<SubmitContractResponse> responseResult = remoteViolationService.submitContract(submitContractReq);
        Integer state;
        TransferContractStatusParam updParam = new TransferContractStatusParam();
        updParam.setId(param.getId());
        if (ResultUtil.isModelNotNull(responseResult)) {
            SubmitContractResponse contractResponse = responseResult.getModel();
            state = contractResponse.getState();
            updParam.setContractId(contractResponse.getOrderId());
            updParam.setStatus(contractResponse.getState().byteValue());
            updParam.setMessage(contractResponse.getPostMsg());
            updParam.setPostCode(contractResponse.getPostCode());
        } else {
            state = (int) TransferContractStatusEnum.REPORT_FAIL.getValue();
            updParam.setErrorMsg(responseResult.getMessage());
            updParam.setStatus(state.byteValue());
        }
        this.saveTransferContractStatus(updParam);

        // 更新违章的转移状态
        updIllegalTransferStatus(param.getVehicleId(), param.getOrderId(), submitContractReq.getBeginTime(),
                submitContractReq.getEndTime(), state);

    }

    @Override
    public void testPushTransferMsg(Long orderId, String code, Date beginTime, Date endTime, String account) {
        Result<OrderInfoVo> orderInfoVoResult = orderService.getOrderInfo(orderId);
        if (ResultUtil.isModelNull(orderInfoVoResult)) {
            throw new BizException("订单不存在");
        }
        SubmitContractResponse contractResponse = new SubmitContractResponse();
        contractResponse.setPostCode(code);
        this.pushTransferMsg(orderInfoVoResult.getModel(), contractResponse, beginTime, endTime, account);
    }

    private void pushTransferMsg(OrderInfoVo orderInfo, SubmitContractResponse contractResponse, Date beginTime, Date endTime, String account) {
        try {
            ViolationResultCodeEnum.TransferContractEnum responseEnum = ViolationResultCodeEnum.TransferContractEnum.getByCode(contractResponse.getPostCode());
            if (responseEnum != null && responseEnum.getIsPushMessage()) {
                String rdKey = "pushTransferMsg:" + orderInfo.getId() + "_" + orderInfo.getVehicleId();
                // 24小时内 同一订单+车辆的上报不发消息
                long check = redisService.setnx(rdKey, 1, TimeUnit.DAYS);
                if (check > 1) {
                    log.info("违章转移 消息推送, 24小时内同一订单+车辆的上报错误不发消息 订单号:{}, 车牌号:{}, beginTime:{}, endTime:{}, contractResponse:{}",
                            orderInfo.getId(), orderInfo.getVehicleNo(), beginTime, endTime, JSON.toJSONString(contractResponse));
                    return;
                }
                // 公众号通知上报失败
                /*
                 * 违章错误码对接，当违章接口收到错误码时，通过微信公众号发送消息
                 * 错误码参考附件，触发条件：当不符合上报条件时触发消息/接口中获取到错误码消息触发消息
                 * 模板ID：bERQiHT1GEPzWXPhWSM7NaNR5WrfMxQWyVllClmZewQ
                 *
                 * 企业账号{{character_string1.DATA}}
                 * 租车订单号{{character_string2.DATA}}
                 * 车牌号{{car_number3.DATA}}
                 * 失败原因{{const4.DATA}} 管理枚举值
                 */
                PushVO pushVO = new PushVO();
                Map<String, WxMsgVo.Template> data = new HashMap<>();
                data.put("character_string1", new WxMsgVo.Template(account));
                data.put("character_string2", new WxMsgVo.Template(orderInfo.getId().toString()));
                data.put("car_number3", new WxMsgVo.Template(orderInfo.getVehicleNo()));
                data.put("const4", new WxMsgVo.Template(responseEnum.getSolution()));
                pushVO.setMpTemplate(WxMpTemplateEnum.ILLEGAL_REPORT_FAILURE.getTemplateId());
                pushVO.setMpPushObj(data);

                // ------通知对象参数--------
                pushVO.setMerchantId(orderInfo.getMerchantId());
                pushVO.setStoreId(orderInfo.getPickupStoreId());
                pushVO.setPushTypeEnum(PushTypeEnum.TYPE_ILLEGAL);
                if (ViolationResultCodeEnum.TransferContractEnum.isNeedLogin(contractResponse.getPostCode())) {
                    pushVO.setPagePath(WxMpPageEnum.VIOLATION_LOGIN.getPage() + account);
                }

                log.info("违章转移 消息推送;orderId={};beginTime={};endTime={};contractResponse={}",
                        orderInfo.getId(), beginTime, endTime, JSON.toJSONString(contractResponse));
                pushMsgService.push(pushVO);
            }
        } catch (Exception e) {
            log.error("违章转移 消息推送异常;orderId={};beginTime={};endTime={};contractResponse={}",
                    orderInfo.getId(), beginTime, endTime, JSON.toJSONString(contractResponse), e);
        }
    }

    private void updIllegalTransferStatus(Long vehicleId, Long orderId, Date illegalStartTime, Date illegalEndTime, Integer state) {
        List<VehicleIllegalOrderVO> illegalOrderList = vehicleIllegalOrderService.getBaseVehicleIllegalOrder(VehicleIllegalOrderInnerQuery.builder()
                .vehicleId(vehicleId)
                .orderId(orderId)
                .illegalStartTime(illegalStartTime)
                .illegalEndTime(illegalEndTime)
                .build());
        for (VehicleIllegalOrderVO illegalOrderVO : illegalOrderList) {
            VehicleIllegalOrder updIllegalOrder = new VehicleIllegalOrder();
            updIllegalOrder.setId(illegalOrderVO.getId());
            updIllegalOrder.setTransferStatus(state.byteValue());
            updIllegalOrder.setLastVer(illegalOrderVO.getLastVer() + 1);
            vehicleIllegalOrderMapper.updateByPrimaryKeySelective(updIllegalOrder);
        }
    }

    /**
     * 获取车辆类型
     * @param licenseNo
     * @return
     */
    private static String getCarType(String licenseNo) {
        String carType;
        if (licenseNo.length() == 7) {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        } else if (licenseNo.length() == 8) {
            carType = licenseTypeEnum.SMALL_NEW_ENERGY_CAR.getCode();
        } else {
            carType = licenseTypeEnum.SMALL_CAR.getCode();
        }
        return carType;
    }

    private String generateContractNo(Long orderId) {
        return UuidUtil.getUUID();
    }

    @Override
    public Boolean abortAllContract(Long orderId, Long vehicleId, Date abortDate, boolean needSubmit) {
        List<TransferContractStatus> contractStatusList = new ArrayList<>();

        boolean isAbort = false;

        // 先拉取更新合同
//        this.syncContractByJob();

        Date date = new Date();
        if (abortDate != null) {
            date = abortDate;
        }
        Date oneDayBefore = DateUtils.addDays(date, -1);
        if (orderId != null) {
            Result<OrderInfoVo> orderInfo = orderService.getOrderInfo(orderId);
            TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
            TransferContractStatusExample.Criteria criteria = transferContractStatusExample.createCriteria();
            // 补充合同要查出所有可能成功的合同
            criteria.andOrderIdEqualTo(orderId).andMerchantIdEqualTo(orderInfo.getModel().getMerchantId())
                    .andEndTimeGreaterThan(oneDayBefore).andStatusIn(TransferContractStatusEnum.maySuccessReport());
            contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);

            if (CollectionUtils.isEmpty(contractStatusList)) {
                return false;
            }
            // 按订单查合同需要补充合同
            // contractList根据endTime排序 从大到小排序
            contractStatusList.sort((o1, o2) -> o2.getEndTime().compareTo(o1.getEndTime()));
            TransferContractStatus lastContract = contractStatusList.get(0);
            if (lastContract.getEndTime().before(date) && needSubmit) {
                // 补充合同
                Result<Boolean> result = this.submitContract(lastContract.getOrderId(), DateUtils.addMinutes(lastContract.getEndTime(), 1), date);
                log.info("违章转移 补充录入合同 orderId={}, startTime={}, endTime={}, result={}",
                        lastContract.getOrderId(), DateUtils.addMinutes(lastContract.getEndTime(), 1), date, JSON.toJSONString(result));
                return false;
            }

            // 不需要补充合同再过滤出需要终止的合同
            contractStatusList = contractStatusList.stream().filter(contract ->
                    TransferContractStatusEnum.needAbort().contains(contract.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contractStatusList)) {
                return false;
            }
        } else if (vehicleId != null) {
            Result<VehicleInfoVO> vehicleInfoResult = vehicleInfoService.getBaseById(vehicleId, true);
            if (ResultUtil.isModelNotNull(vehicleInfoResult)) {
                TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
                TransferContractStatusExample.Criteria criteria = transferContractStatusExample.createCriteria();
                criteria.andVehicleIdEqualTo(vehicleId).andMerchantIdEqualTo(vehicleInfoResult.getModel().getMerchantId())
                        .andEndTimeGreaterThan(date).andStatusIn(TransferContractStatusEnum.needAbort());
                contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);
            }
        } else {
            throw new BizException("参数错误");
        }

        // 终止合同
        Date finalDate = date;
        contractStatusList = contractStatusList.stream().filter(e-> e.getEndTime().after(finalDate)).collect(Collectors.toList());
        for (TransferContractStatus transferContractStatus : contractStatusList) {
            if (StringUtils.isEmpty(transferContractStatus.getContractId())) {
                log.error("合同orderId获取失败 transferContractStatus={}", JSON.toJSONString(transferContractStatus));
                continue;
            }
            AbortContractReq abortContractReq = new AbortContractReq();
            abortContractReq.setOrderId(transferContractStatus.getContractId());
            Result<Boolean> booleanResult = remoteViolationService.abortContract(abortContractReq);
            isAbort = true;
            // 更新合同状态
            if (ResultUtil.isModelNotNull(booleanResult) && booleanResult.getModel()) {
                this.syncContractByJob();
            }
        }
        return isAbort;
    }

    @Override
    public void syncContractByJob() {
        // 获取本地合同最大版本
        TransferContractVersion oldVersion = transferContractVersionMapper.selectByPrimaryKey(1L);
        Long dbVersion = oldVersion.getLastVer();
        Result<List<SyncContractResponse>> listResult = remoteViolationService.syncContract(dbVersion);
        if (!listResult.isSuccess()) {
            log.error("违章转移 同步合同更新失败, dbVersion={}", dbVersion);
            return;
        }
        List<SyncContractResponse> list = listResult.getModel();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, SyncContractResponse> map = new HashMap<>();
        Long maxDbVersion = 0L;
        for (SyncContractResponse contractResponse : list) {
            if (contractResponse.getDbVersion() > maxDbVersion) {
                maxDbVersion = contractResponse.getDbVersion();
            }
            SyncContractResponse oldResponse = map.get(contractResponse.getContractNo());
            if (oldResponse == null) {
                map.put(contractResponse.getContractNo(), contractResponse);
            } else if (oldResponse.getDbVersion() < contractResponse.getDbVersion()) {
                map.put(contractResponse.getContractNo(), contractResponse);
            }
        }

        // 更新本地合同最大版本
        TransferContractVersion updVersion = new TransferContractVersion();
        updVersion.setId(oldVersion.getId());
        updVersion.setLastVer(maxDbVersion);
        updVersion.setOpTime(System.currentTimeMillis());
        transferContractVersionMapper.updateByPrimaryKey(updVersion);

        TransferContractStatusExample transferContractStatusExample = new TransferContractStatusExample();
        transferContractStatusExample.createCriteria().andContractNoIn(new ArrayList<>(map.keySet()));
        List<TransferContractStatus> contractStatusList = transferContractStatusMapper.selectByExample(transferContractStatusExample);

        for (TransferContractStatus transferContractStatus : contractStatusList) {
            SyncContractResponse syncContractResponse = map.get(transferContractStatus.getContractNo());
            // 已生效的合同只关注相同orderId的合同(避免重试的合同同步更新时覆盖成功结果)
            if (TransferContractStatusEnum.successReport().contains(transferContractStatus.getStatus())
                    && !String.valueOf(transferContractStatus.getContractId()).equals(syncContractResponse.getOrderId())) {
                continue;
            }
            // 更新合同状态
            this.saveTransferContractStatus(TransferContractStatusParam.builder()
                    .id(transferContractStatus.getId())
                    .status(syncContractResponse.getState().byteValue())
                    .message(syncContractResponse.getPostMsg())
                    .postCode(syncContractResponse.getPostCode())
//                    .driverPhone(syncContractResponse.getDriverPhone())
                    .endTime(syncContractResponse.getEndTime())
                    .contractId(syncContractResponse.getOrderId())
                    .build());

            // 更新违章的转移状态
            updIllegalTransferStatus(transferContractStatus.getVehicleId(), transferContractStatus.getOrderId(), transferContractStatus.getBeginTime(),
                    transferContractStatus.getEndTime(), syncContractResponse.getState());

            // 失败合同重试
            if (syncContractResponse.getState() == -1) {
                try {
                    this.retrySameContract(transferContractStatus, true);
                } catch (Exception e) {
                    log.error("合同重试失败, transferContractStatus={}", JSON.toJSONString(transferContractStatus), e);
                }
            }
        }
    }

    /***
     * 1.取车/取车后强排 如车辆有合同，终止生效的合同；录入合同 实际取车时间-订单最后结束时间 如最大时间大于1月 则实际取车时间-实际还车时间+1月
     * 2.还车 终止合同
     * 3.定时任务 每12小时执行一次
     *     扫描具有生效合同的订单，如订单未取车&&订单生效合同时间before当前时间24小时，录入合同 上一个合同结束时间-上一个订单结束时间+1月(为了减少表数据)
     */

    @Override
    public void submitDelayContractByJob() {
        /**
         * 每12小时执行一次
         * 扫描具有生效合同的订单，如订单为已取车&&订单生效合同时间before当前时间24小时 (转移订单需要有效)，录入合同 上一个合同结束时间+1分钟-上一个订单结束时间+x天
         * 续期时间不可小于1天 续期时间不可大于1月
         */

        Date nowDate = new Date();
        Date oneDayBefore = DateUtils.addDays(nowDate, -1);
        // 扫描已过期 但是不超过24小时的已上报合同
        TransferContractStatusExample contractStatusExample = new TransferContractStatusExample();
        contractStatusExample.createCriteria().andEndTimeGreaterThanOrEqualTo(oneDayBefore)
                .andStatusEqualTo(TransferContractStatusEnum.REPORTED.getValue());
        Map<Long, Map<Long, List<TransferContractStatus>>> contractMap =
                transferContractStatusMapper.selectByExample(contractStatusExample).stream()
                        .collect(Collectors.groupingBy(TransferContractStatus::getMerchantId,
                                Collectors.groupingBy(TransferContractStatus::getOrderId)));
        log.info("违章转移 合同续期 contractMap={}", JSON.toJSONString(contractMap));
        contractMap.forEach((merchantId, contractMapByOrder) -> {
            // 查询租车订单
            OrderInfoParam orderInfoParam = new OrderInfoParam();
            orderInfoParam.setIdList(new ArrayList<>(contractMapByOrder.keySet()));
            Result<List<OrderInfoVo>> orderListResult = orderSlaveService.getOrderBaseList(orderInfoParam);
            if (!orderListResult.isSuccess() || CollectionUtils.isEmpty(orderListResult.getModel())) {
                log.error("查询租车订单失败, merchantId={}", merchantId);
                return;
            }
            Map<Long, OrderInfoVo> orderMap = orderListResult.getModel().stream()
                    .collect(Collectors.toMap(OrderInfoVo::getId, e -> e, (v1, v2) -> v1));

            contractMapByOrder.forEach((orderId, contractList) -> {
                try {
                    OrderInfoVo orderInfoVo = orderMap.get(orderId);
                    // 过滤非已取车的订单和爬虫订单
                    if (orderInfoVo == null || !OrderStatusEnum.isPickedUp(orderInfoVo.getOrderStatus())
                            || CollectionUtils.isEmpty(contractList) || this.isReptileOrder(orderInfoVo.getExtra())) {
                        return;
                    }

                    // contractList根据endTime排序 从大到小排序
                    contractList.sort((o1, o2) -> o2.getEndTime().compareTo(o1.getEndTime()));
                    TransferContractStatus lastContract = contractList.get(0);

                    // 合同最后过期时间大于当前时间 不进行续期
                    if (lastContract.getEndTime().after(nowDate)) {
                        return;
                    }

                    // 按合同最后时间进行续期
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(lastContract.getEndTime());
                    calendar.add(Calendar.MINUTE, 1);
                    Date startTime = calendar.getTime();
                    calendar.add(Calendar.DAY_OF_MONTH, renewalDays);
                    Date endTime = calendar.getTime();
                    Result<Boolean> result = this.submitContract(orderId, startTime, endTime);
                    log.info("违章转移 合同续期 orderId={}, startTime={}, endTime={}, result={}", orderId, startTime, endTime, JSON.toJSONString(result));
                } catch (Exception e) {
                    // 可能是三方接口超时导致的异常
                    log.error("违章转移 合同续期异常 orderId={}, contractList={}", orderId, JSON.toJSONString(contractList), e);
                    return;
                }

            });
        });
    }

    @Override
    public Result<Boolean> cancelContract(String contractId) {
        log.info("违章转移 作废合同 contractId={}", contractId);
        CancelContractReq cancelContractReq = new CancelContractReq();
        cancelContractReq.setOrderId(contractId);
        Result<Boolean> result = remoteViolationService.cancelContract(cancelContractReq);
        this.syncContractByJob();
        return result;
    }

    public boolean isReptileOrder(String extra) {
        if (StringUtils.isBlank(extra)) {
            return false;
        }
        JSONObject jsonObject = JSON.parseObject(extra);
        Byte isReptile = jsonObject.getByte("isReptile");
        return YesOrNoEnum.isYes(isReptile);
    }

}
