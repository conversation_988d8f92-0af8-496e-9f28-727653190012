package com.ql.rent.provider.vehicle;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.ql.dto.ResultResp;
import com.ql.dto.open.request.stock.OpenStockMerchantCheckReq;
import com.ql.rent.client.IOpenThirdService;
import com.ql.rent.common.IRedisService;
import com.ql.rent.config.ShangQiProperties;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.service.common.OkHttpService;
import com.ql.rent.service.vehicle.IThirdCtripService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.third.vo.SaasResponse;
import com.ql.rent.third.vo.response.CtripTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.ql.rent.constant.MethodConstant.THIRD_STOCK_CHECK;
import static com.ql.rent.constant.RedisConstant.MsgTopic.STOCK_CHECK_TOPIC;

@Service
@Slf4j
public class IThirdCtripServiceImpl implements IThirdCtripService {

    @Resource
    private ShangQiProperties ctripProperties;

    @Resource
    private OkHttpService okHttpService;

    @Resource
    private IRedisService redisService;

    @Resource
    private IOpenThirdService openThirdService;

    /**
     * 获取access_token api名称
     * post
     */
    private static final String access_token_api = "getAccessToken";

    /**
     * 库存校验接口
     * post
     */
    private static final String check_inventory_api = "stock/qinglu/checkStock";


    @Override
    public String getAccessToken(String merchantId) {
        String postUrl = ctripProperties.getUrl() + access_token_api;

        String rKey = String.format(RedisConstant.CtripRedisKey.ACCESS_TOKEN, merchantId);
        String accessToken = (String) redisService.get(rKey);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        String clientId = ctripProperties.getClientId();
        String clientSecret = ctripProperties.getClientSecret();
        Map<String, Object> headers = Maps.newHashMap();
        headers.put("clientId", clientId);
        headers.put("clientSecret", clientSecret);

        String result = okHttpService.okPostJson(postUrl, Maps.newHashMap(), headers);
        log.info("result:{}", result);
        SaasResponse saasResponse = JSON.parseObject(result, SaasResponse.class);
        CtripTokenResponse ctripTokenResponse = JSON.parseObject(JSON.toJSONString(saasResponse.getData()), CtripTokenResponse.class);
        log.info("ctripTokenResponse:{}", JSON.toJSONString(ctripTokenResponse));
        if (Objects.isNull(ctripTokenResponse) || Objects.isNull(ctripTokenResponse.getAccessToken())) {
            return null;
        }
        accessToken = ctripTokenResponse.getAccessToken().getAccessToken();
        redisService.set(rKey, accessToken, 7000);
        return accessToken;
    }

    @Override
    public SaasResponse checkStock(Long merchantId, OpenStockMerchantCheckReq stockCheckRequest) {

        Map<String, Object> body = Maps.newHashMap();
        body.put("saasOrderId", stockCheckRequest.getSaasOrderId());
        body.put("storeId", stockCheckRequest.getStoreId());
        if (StringUtils.isNotBlank(stockCheckRequest.getVehicleModelId())) {
            body.put("vehicleModelId", String.valueOf(stockCheckRequest.getVehicleModelId()));
        }
        if (StringUtils.isNotBlank(stockCheckRequest.getVehicleId())) {
            body.put("vehicleId", String.valueOf(stockCheckRequest.getVehicleId()));
        }
        body.put("startTime", stockCheckRequest.getStartTime());
        body.put("endTime", stockCheckRequest.getEndTime());
        body.put("returnStoreId", stockCheckRequest.getReturnStoreId());
        body.put("platOrderNo", stockCheckRequest.getPlatOrderNo());
        body.put("packageId", stockCheckRequest.getPackageId());
        body.put("channelId", stockCheckRequest.getChannelId());
        body.put("sourceType", stockCheckRequest.getSourceType());
        body.put("saasStoreId", stockCheckRequest.getSaasStoreId());
        body.put("saasModelId", stockCheckRequest.getSaasModelId());
        body.put("saasVehicleId", stockCheckRequest.getSaasVehicleId());
        body.put("levelCorrEarliestRegisterTime", stockCheckRequest.getLevelCorrEarliestRegisterTime());
        if (Objects.nonNull(stockCheckRequest.getMainOrder())) {
            body.put("mainOrder", JSON.toJSONString(stockCheckRequest.getMainOrder()));
        }

        ResultResp result = openThirdService.pullDate(THIRD_STOCK_CHECK, merchantId, JSON.toJSONString(body));
        log.info("直连商家 merchantId:{},{},result:{}", merchantId, JSON.toJSONString(result));
        if (Objects.isNull(result) || !Objects.equals(result.getCode(), "1")) {
            // 先临时解决飞猪黑名单
            if (OrderSourceEnum.FEIZHU.getSource().longValue() == stockCheckRequest.getChannelId()) {
                throw new BizException(result.getCode(), result.getMessage());
            } else {
                throw new BizException("0", result.getMessage());
            }
        }
        return JSON.parseObject(JSON.toJSONString(result), SaasResponse.class);
//
//        if (merchantId != 58 && merchantId != 365) {
//            //log.info("直连商家 库存检查open merchantId:{} body:{}", merchantId, JSON.toJSONString(body));
//            ResultResp result = openThirdService.pullDate(THIRD_STOCK_CHECK, merchantId, JSON.toJSONString(body));
//            log.info("直连商家 merchantId:{} 非上汽 result:{}", merchantId, JSON.toJSONString(result));
//            if (Objects.isNull(result) || !Objects.equals(result.getCode(), "1")) {
//                throw new BizException(result.getCode(), result.getMessage());
//            }
//            return JSON.parseObject(JSON.toJSONString(result), SaasResponse.class);
//        }
//
//        String postUrl = ctripProperties.getUrl() + check_inventory_api;
//
//        String clientId = ctripProperties.getClientId();
//        String accessToken = this.getAccessToken(String.valueOf(merchantId));
//        Map<String, String> headers = Maps.newHashMap();
//        headers.put("clientId", clientId);
//        headers.put("token", accessToken);
//
//        body.put("startTime", DateUtil.getFormatDateStr(new Date(stockCheckRequest.getStartTime()), DateUtil.yyyyMMddHHmmss));
//        body.put("endTime", DateUtil.getFormatDateStr(new Date(stockCheckRequest.getEndTime()), DateUtil.yyyyMMddHHmmss));
//        String result = okHttpService.okPostJson(postUrl, headers, body);
//        if (Objects.isNull(result)) {
//            return null;
//        }
//        SaasResponse ctripStockResponse = JSON.parseObject(result, SaasResponse.class);
//        if (Objects.isNull(ctripStockResponse)) {
//            return null;
//        }
//        if (Integer.parseInt(ctripStockResponse.getCode()) != 0) {
//            throw new BizException(ctripStockResponse.getCode(), ctripStockResponse.getMessage());
//        }
//        return ctripStockResponse;
    }

}
