package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.api.aggregate.remote.wk.dto.SaasOrderCallbackResp;
import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.merchant.MerchantInfoMapper;
import com.ql.rent.dao.trade.*;
import com.ql.rent.entity.merchant.MerchantInfo;
import com.ql.rent.entity.merchant.MerchantInfoExample;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.param.BaseQuery;
import com.ql.rent.param.trade.OrderInfoParam;
import com.ql.rent.service.trade.IOrderFixDataService;
import com.ql.rent.service.trade.IOrderService;
import com.ql.rent.service.trade.IOrderSyncService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.UuidUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.OrderBillDetailVo;
import com.ql.rent.vo.trade.OrderInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @auther musi
 * @date 2023/5/7 20:43
 */
@Slf4j
@Service
public class OrderFixDataServiceImpl implements IOrderFixDataService {
    @Resource
    private OrderComponent orderComponent;
    @Resource
    private OrderSnapshotMapper orderSnapshotMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private VehiclePickReturnMapper vehiclePickReturnMapper;
    @Resource
    private OrderDetailMapper orderDetailMapper;
    @Resource
    private RerentOrderMapper rerentOrderMapper;
    @Resource
    private OrderMsgProducer orderMsgProducer;
    @Resource
    private IOrderService orderService;
    @Resource
    private IRedisService redisService;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private MerchantInfoMapper merchantInfoMapper;
    @Resource
    private FinanceStoreReportMapper financeStoreReportMapper;
    @Resource
    private IOrderSyncService orderSyncService;


    /**
     * 修复数据方法
     * 订单改排后，订单快照的车辆数据修复
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<Boolean> fixOrderSnapshot(Long orderId, Long vehicleId) {
        orderComponent.updateOrderSnapshot(orderId, vehicleId);
        return ResultUtil.successResult(true);
    }


    @Override
    public Result<Boolean> delOrderSnapshot() {
        RerentOrderExample rerentOrderExample = new RerentOrderExample();
        List<RerentOrder> list = rerentOrderMapper.selectByExample(rerentOrderExample);
        if (CollectionUtils.isEmpty(list)) {
            log.info("修复订单快照冗余数据, 续租数据为空");
            return ResultUtil.successResult(true);
        }
        log.info("修复订单快照冗余数据, 续租数据={}", list.size());
        List<Long> rerenOrderIdList = list.stream().map(RerentOrder::getId).collect(Collectors.toList());
        int index = 1;
        int delTotalCount = 0;
        while (true) {
            BaseQuery baseQuery = new BaseQuery();
            baseQuery.setPageSize(5000);
            baseQuery.setPageIndex(index);
            OrderSnapshotExample orderSnapshotExample = new OrderSnapshotExample();
            orderSnapshotExample.createCriteria().andSnapshotTypeEqualTo(OrderSnapshotTypeEnum.REPTILE_SERVICES.getType()).andOrderTypeEqualTo((byte) 1);
            orderSnapshotExample.setOrderByClause(String.format("id desc limit %s,%s", baseQuery.getStartPos(), baseQuery.getPageSize()));
            List<OrderSnapshot> snapshotList = orderSnapshotMapper.selectByExample(orderSnapshotExample);
            if (CollectionUtils.isEmpty(snapshotList)) {
                log.info("修复订单快照冗余数据, 订单快照为空");
                return ResultUtil.successResult(true);
            }
            List<Long> delList = new ArrayList<>();
            for (OrderSnapshot orderSnapshot : snapshotList) {
                if (rerenOrderIdList.contains(orderSnapshot.getOrderId())) {
                    continue;
                }
                delList.add(orderSnapshot.getId());
            }
            if (CollectionUtils.isNotEmpty(delList)) {
                OrderSnapshotExample delExample = new OrderSnapshotExample();
                delExample.createCriteria().andIdIn(delList);
                int delCount = orderSnapshotMapper.deleteByExample(delExample);
                delTotalCount = delTotalCount + delCount;
                log.info("修复订单快照冗余数据, 总计删除了={}条数据", delTotalCount);
            }
            try {
                Thread.sleep(500);
                log.info("修复订单快照冗余数据, 线程休眠500ms");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            index = index + 1;
            if (index >= 2000) {
                log.info("修复订单快照冗余数据, 查询了1000次OrderSnapshot表，自动结束");
                return ResultUtil.successResult(true);
            }
        }

    }

    /**
     * 添加订单快照
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<Boolean> addOrderSnapshot(Long orderId) {

        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        if (orderInfo == null) {
            log.info("添加订单快照, 订单不存在");
            return ResultUtil.failResult("订单不存在");
        }
        OrderDetailExample detailExample = new OrderDetailExample();
        detailExample.createCriteria().andOrderIdEqualTo(orderId).
                andOrderStatusEqualTo(OrderDetailStatusEnum.CONFIRMED.getStatus()).
                andParentOrderIdEqualTo(0L);

        List<OrderDetail> orderDetailList = orderDetailMapper.selectByExample(detailExample);

        OrderSnapshotExample orderSnapshotExample = new OrderSnapshotExample();
        orderSnapshotExample.createCriteria().andOrderIdEqualTo(orderId).andOrderTypeEqualTo((byte)0);
        List<OrderSnapshot> snapshotList = orderSnapshotMapper.selectByExample(orderSnapshotExample);
        List<Long> delList = new ArrayList<>();
        for (OrderSnapshot orderSnapshot : snapshotList) {
            delList.add(orderSnapshot.getId());
        }
        if (CollectionUtils.isNotEmpty(delList)) {
            OrderSnapshotExample delExample = new OrderSnapshotExample();
            delExample.createCriteria().andIdIn(delList);
            int delCount = orderSnapshotMapper.deleteByExample(delExample);
            log.info("添加订单快照, delList={}, 总计删除了={}条数据", JSON.toJSONString(delList), delCount);
        }

        orderComponent.saveOrderSnapshot(orderInfo, orderDetailList, null);

        log.info("添加订单快照成功");
        return ResultUtil.successResult(true);
    }

    /**
     * 订单消息推送
     *
     * @param orderId
     * @return
     */
    @Override
    public Result<Boolean> orderMsgPush(Long orderId) {
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        OrderMsgTypeEnum orderMsgTypeEnum = OrderMsgTypeEnum.CREATE;
        if (orderInfo.getOrderStatus().byteValue() == OrderStatusEnum.CANCELLED.getStatus().byteValue()) {
            orderMsgTypeEnum = OrderMsgTypeEnum.CANCEL;

        } else if (orderInfo.getOrderStatus().byteValue() == OrderStatusEnum.SCHEDULED.getStatus().byteValue()) {
            orderMsgTypeEnum = OrderMsgTypeEnum.CREATE;
        } else {
            return ResultUtil.successResult(true);
        }
        log.info("订单消息推送, orderId={}, orderMsgTypeEnum={}", orderId, orderMsgTypeEnum.getType());
        orderMsgProducer.sendOrderMsg(orderId, orderMsgTypeEnum);
        return ResultUtil.successResult(true);
    }


    @Override
    public Result<String> countOverTimeOriginOrder() {
        if (redisService.setnx("countOverTimeOriginOrder", 5L) > 1) {
            return ResultUtil.failResult("正在执行中");
        }

        String uuid = UuidUtil.getUUID();
        asyncPromiseExecutor.execute(() -> {
            try {
                this.countOverTimeOriginOrder(uuid);
            } catch (Exception e) {
                log.error(uuid + "20241108，开始计算满足超过30天未改排订单, error");
            }
        });
        return ResultUtil.successResult(uuid);
    }

    private void countOverTimeOriginOrder(String uuid) {
        log.info(uuid + ", 20241108，开始计算满足超过30天未改排订单, started");
        long startId = 0L;
        int pageSize = 1000;
        long timeInterval = 30L * 24 * 3600 * 1000;
        long apiOrderNum = 0;

        // 目标结果
        long targetNum = 0;
        try {
            out:
            while (true) {
                List<OrderInfo> orderInfoList = orderInfoMapper.selectPage(startId, pageSize);
                if (CollectionUtils.isEmpty(orderInfoList)) {
                    break;
                }
                for (OrderInfo orderInfo : orderInfoList) {
                    startId = orderInfo.getId();
                    if (orderInfo.getCreateTime() > System.currentTimeMillis() - timeInterval) {
                        break out;
                    }

                    boolean isApiOrder = StringUtils.isNotBlank(orderInfo.getSourceOrderId()) && !orderComponent.isReptileOrder(orderInfo);
                    if (!isApiOrder) {
                        continue;
                    }

                    apiOrderNum = apiOrderNum + 1;
                    Long compareTime = null;
                    // 分两种情况- 当前已还车根据预计还车时间和时间比较下。
                    if (OrderStatusEnum.isReturn(orderInfo.getOrderStatus())) {
                        VehiclePickReturnExample vehiclePickReturnExample = new VehiclePickReturnExample();
                        vehiclePickReturnExample.createCriteria().andOrderIdEqualTo(orderInfo.getId()).andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType());
                        vehiclePickReturnExample.setOrderByClause("order_id limit 1");
                        List<VehiclePickReturn> vehiclePickReturns = vehiclePickReturnMapper.selectByExample(vehiclePickReturnExample);
                        if (CollectionUtils.isEmpty(vehiclePickReturns)) {
                            continue;
                        }
                        compareTime = vehiclePickReturns.get(0).getCreateTime();
                    } else if (OrderStatusEnum.isPickedUp(orderInfo.getOrderStatus())) {
                        compareTime = System.currentTimeMillis();
                    } else {
                        continue;
                    }

                    if (compareTime - orderInfo.getLastReturnDate().getTime() > timeInterval && this.orderVehicleChanged(orderInfo.getId())) {
                        targetNum = targetNum + 1;
                    }
                }

                if (orderInfoList.size() < pageSize) {
                    break;
                }
            }
            log.info(uuid + "20241108，计算满足超过30天未改排订单, finished. apiOrderNum={}, targetNum={}", apiOrderNum, targetNum);
        } catch (Exception e) {
            log.error(uuid + "20241108，开始计算满足超过30天未改排订单, error");
        }
    }

    private boolean orderVehicleChanged(Long orderId) {
        OrderSnapshotExample orderSnapshotExample = new OrderSnapshotExample();
        orderSnapshotExample.createCriteria().andOrderIdEqualTo(orderId).andSnapshotTypeEqualTo(OrderSnapshotTypeEnum.TAG.getType());
        List<OrderSnapshot> orderSnapshots = orderSnapshotMapper.selectByExample(orderSnapshotExample);
        if (CollectionUtils.isEmpty(orderSnapshots)) {
            return false;
        }
        OrderSnapshot orderSnapshot = orderSnapshots.get(0);
        return !orderSnapshot.getCreateTime().equals(orderSnapshot.getOpTime());
    }


    @Override
    public Result<Integer> fixHistoryFinanceReport(FixHistoryParam param) {
        if (param == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }
        List<Long> merchantIds = param.getMerchantIds();
        List<Integer> monthList;
        if (CollectionUtils.isEmpty(param.getMonths())) {
            // 上月金额
            LocalDate localDate = LocalDate.now().minusMonths(1L).withDayOfMonth(1);
            monthList = Collections.singletonList(localDate.getYear() * 100 + localDate.getMonthValue());
        } else {
            monthList = param.getMonths();
        }

        String uuid = UuidUtil.getUUID();
        log.info("{}, fixHistoryFinanceReport committed", uuid);
        asyncPromiseExecutor.execute(() -> batchFixHistoryFinanceReport(merchantIds, monthList, uuid));
        return null;
    }

    @Override
    public Result<List<Long>> findWukongErrorAmountOrderIds(Long merchantId, Long start, Long end) {
        List<Long> list = new ArrayList<>();
        OrderInfoParam param = new OrderInfoParam();
        param.setMerchantId(merchantId);
        param.setOrderStatusList(Arrays.asList(OrderStatusEnum.RETURNED.getStatus()));
        param.setStartCreateTime(start);
        param.setEndCreateTime(end);
        param.setOrderSourceList(Arrays.asList(OrderSourceEnum.WUKONG.getSource()));
        LoginVo loginVo = new LoginVo();
        loginVo.setLoginName("musi-admin");
        loginVo.setMerchantId(merchantId);
        Result<List<OrderInfoVo>> orderListResult = orderService.getOrderBaseList(param, loginVo);


        if (CollectionUtils.isNotEmpty(orderListResult.getModel())) {
            for (OrderInfoVo orderInfoVo : orderListResult.getModel()) {
                Result<OrderBillDetailVo> billDetailVoResult = orderService.getOrderBillDetail(orderInfoVo.getId(), null);
                if (billDetailVoResult.getModel() != null) {
                    OrderBillDetailVo detailVo = billDetailVoResult.getModel();
                    if (detailVo.getOnlinePayAmount() + detailVo.getOfflinePayAmount() != detailVo.getPayAmount()) {
                        log.info("费用项实收不等于订单实收, orderId={}", orderInfoVo.getId());
                    }
                    OrderSnapshotExample orderSnapshotExample = new OrderSnapshotExample();
                    orderSnapshotExample.createCriteria().andOrderIdEqualTo(orderInfoVo.getId()).andSnapshotTypeEqualTo(OrderSnapshotTypeEnum.PLATFROM_BILL.getType());
                    List<OrderSnapshot> snapshotList = orderSnapshotMapper.selectByExample(orderSnapshotExample);
                    if (CollectionUtils.isNotEmpty(snapshotList)) {
                        OrderSnapshot orderSnapshot = snapshotList.get(0);
                        SaasOrderCallbackResp saasOrderCallbackResp = JSON.parseObject(orderSnapshot.getContent(), SaasOrderCallbackResp.class);
                        if (saasOrderCallbackResp != null && saasOrderCallbackResp.sumPayAmount().intValue() != detailVo.getPayAmount()/100) {
                            log.info("orderId={}, 悟空账单实收不等于订单实收, 悟空账单实收={}, 悟空账单对账V1={}, 悟空账单对账V3={}, 擎路订单实收={}",
                                    orderInfoVo.getId(), saasOrderCallbackResp.getTotalAmount(), saasOrderCallbackResp.sumPayAmount().intValue(), saasOrderCallbackResp.sumPayAmountV3().intValue(), detailVo.getPayAmount()/100);
                            list.add(orderInfoVo.getId());
                        }
                        log.info("orderId={}, 悟空账单实收={}, 悟空账单对账V1={}, 悟空账单对账V3={}, 擎路订单实收={}",
                                orderInfoVo.getId(), saasOrderCallbackResp.getTotalAmount(), saasOrderCallbackResp.sumPayAmount().intValue(), saasOrderCallbackResp.sumPayAmountV3().intValue(), detailVo.getPayAmount()/100);
                    }
                }
            }
        }
        return ResultUtil.successResult(list);
    }

    @Override
    public Result<Boolean> syncChannelOrder(Long merchantId, Long channelId, String sourceOrderId) {
        return ResultUtil.successResult(orderSyncService.syncHelloOrder(merchantId, channelId, sourceOrderId, false));
    }

    @Override
    public void fixTcOrderAmount(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            log.info("修复同程重复取还车订单数据, 没有订单ID");
            return;
        }

        for (String orderIdStr : orderIds) {
            if (StringUtils.isEmpty(orderIdStr)) {
                continue;
            }

            Long orderId = Long.valueOf(orderIdStr);

            OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
            if (orderInfo == null || orderInfo.getOrderSource().intValue() != OrderSourceEnum.TONGCHENG.getSource().intValue()) {
                log.info("修复同程重复取还车订单数据, 非同程订单, 直接跳过, orderId={}", orderId);
                continue;
            }

//            Integer amount = 0;

            OrderDetailExample example0 = new OrderDetailExample();
            example0.createCriteria().andOrderIdEqualTo(orderId).andParentOrderIdEqualTo(0L)
//                    .andExtraLike("01001")
                    .andNameEqualTo("送车上门服务费");
            List<OrderDetail> orderDetails0 = orderDetailMapper.selectByExample(example0);
            if (CollectionUtils.isEmpty(orderDetails0)) {
                log.info("修复同程重复取还车订单数据, 没用重复的送车上门服务费, 直接跳过, orderId={}", orderId);
                continue;
            }
            if (orderDetails0.size() == 2) {
                orderDetailMapper.deleteByPrimaryKey(orderDetails0.get(0).getId());
                log.info("修复同程重复取还车订单数据, 删掉重复的送车上门服务费, orderId={}, orderDetail={}", orderId, JSON.toJSONString(orderDetails0.get(0)));
//                amount = amount + orderDetails0.get(0).getAmount();
            }

            OrderDetailExample example1 = new OrderDetailExample();
            example1.createCriteria().andOrderIdEqualTo(orderId).andParentOrderIdEqualTo(0L)
//                    .andExtraLike("01002")
                    .andNameEqualTo("上门还车服务费");
            List<OrderDetail> orderDetails1 = orderDetailMapper.selectByExample(example1);
            if (CollectionUtils.isEmpty(orderDetails1)) {
                log.info("修复同程重复取还车订单数据, 没用重复的上门还车服务费, 直接跳过, orderId={}");
                continue;
            }
            if (orderDetails1.size() == 2) {
                orderDetailMapper.deleteByPrimaryKey(orderDetails1.get(0).getId());
                log.info("修复同程重复取还车订单数据, 删掉重复的上门还车服务费, orderId={}, orderDetail={}", orderId, JSON.toJSONString(orderDetails1.get(0)));
//                amount = amount + orderDetails1.get(0).getAmount();
            }

//            if (amount != 0) {
//                orderInfo.setReceivableAmount(orderInfo.getReceivableAmount() - amount);
//                orderInfo.setPayAmount(orderInfo.getPayAmount() - amount);
//                log.info("修复同程重复取还车订单数据, 更新订单金额, orderId={}, 需要减少的金额={}", orderId, amount);
//                orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
//            }

        }
    }

    @Override
    public void fixXcOrderDetail(Long merchantId) {
        OrderInfoExample orderInfoExample = new OrderInfoExample();
        orderInfoExample.createCriteria().andMerchantIdEqualTo(merchantId);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectByExample(orderInfoExample);
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            for (OrderInfo orderInfo : orderInfoList) {
                OrderDetailExample orderDetailExample = new OrderDetailExample();
                orderDetailExample.createCriteria().andOrderIdEqualTo(orderInfo.getId());
                List<OrderDetail> orderDetailList = orderDetailMapper.selectByExample(orderDetailExample);
                if (CollectionUtils.isNotEmpty(orderDetailList)) {
                    for (OrderDetail orderDetail : orderDetailList) {
                        if (Objects.equals(orderDetail.getName(), "租车押金") || Objects.equals(orderDetail.getName(), "违章押金")) {
                            orderDetailMapper.deleteByPrimaryKey(orderDetail.getId());
                            log.info("修复携程订单详情, 删掉重复的租车押金和违章押金, orderId={}, orderDetail={}", orderInfo.getId(), JSON.toJSONString(orderDetail));
                        }
                    }
                }
            }
        }
    }

    private void batchFixHistoryFinanceReport(List<Long> merchantIds, List<Integer> monthList, String uuid) {
        int merchantPageSize = 200;
        int pageIdx = 1;
        while (true) {
            BaseQuery baseQuery = new BaseQuery();
            baseQuery.setPageIndex(pageIdx);
            baseQuery.setPageSize(merchantPageSize);

            MerchantInfoExample merchantInfoExample = new MerchantInfoExample();
            MerchantInfoExample.Criteria criteria =
                merchantInfoExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            if (CollectionUtils.isNotEmpty(merchantIds)) {
                criteria.andIdIn(merchantIds);
            }

            merchantInfoExample.setOrderByClause("id limit " + baseQuery.getStartPos() + "," + baseQuery.getPageSize());
            List<MerchantInfo> merchantInfos = merchantInfoMapper.selectByExample(merchantInfoExample);
            if (CollectionUtils.isEmpty(merchantInfos))  {
                break;
            }
            for (MerchantInfo merchantInfo : merchantInfos) {
                try {
                    doFixHistoryFinanceReport(merchantInfo.getId(), monthList, uuid);
                } catch (Exception e) {
                    log.error("{}, fixHistoryFinanceReport error", uuid, e);
                }
            }
            if (merchantInfos.size() < merchantPageSize) {
                break;
            }

            pageIdx = pageIdx + 1;
        }
        log.info("{}, fixHistoryFinanceReport finished", uuid);
    }

    private void doFixHistoryFinanceReport(Long merchantId, List<Integer> monthList, String uuid) {
        log.info("{}, fixHistoryFinanceReport started, merchantId:{}, monthList:{}", uuid, merchantId, monthList);
        long millSendOfDay = 24L * 3600 * 1000;
        for (Integer monthInt : monthList) {
            int year = monthInt / 100;
            int month = (monthInt % 100) - 1;
            Calendar instance = Calendar.getInstance();
            instance.set(Calendar.YEAR, year);
            instance.set(Calendar.MONTH, month);
            instance.set(Calendar.DAY_OF_MONTH, 1);
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            long startTime = instance.getTimeInMillis();
            instance.set(Calendar.MONTH, month + 1);
            long endTime = instance.getTimeInMillis();

            int dayInt = 1;
            // 预估 30*10门店
            List<FinanceStoreReport> resultList = new ArrayList<>(300);
            while (startTime < endTime) {
                long tomorrowStart = startTime + millSendOfDay;
                VehiclePickReturnExample vehiclePickReturnExample = new VehiclePickReturnExample();
                vehiclePickReturnExample.createCriteria().andMerchantIdEqualTo(merchantId)
                    .andCreateTimeGreaterThanOrEqualTo(startTime).andCreateTimeLessThan(tomorrowStart)
                    .andPrTypeEqualTo(VehiclePickReturnEnum.RETURN_BACK.getType())
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
               List<VehiclePickReturn> returnList = vehiclePickReturnMapper.selectByExample(vehiclePickReturnExample);
               if (CollectionUtils.isNotEmpty(returnList)) {
                   // key为门店id，value为门店当日的收入
                   Map<Long, Long> storeAmountMap = new HashMap<>();
                   for (VehiclePickReturn vehiclePickReturn : returnList) {
                       Long orderId = vehiclePickReturn.getOrderId();
                       Result<OrderBillDetailVo> orderBillDetailResult = orderService.getOrderBillDetail(orderId, null);
                       if (ResultUtil.isModelNotNull(orderBillDetailResult)) {
                           OrderBillDetailVo orderBillDetail = orderBillDetailResult.getModel();
                           if (orderBillDetail.getIsReptile() != null && orderBillDetail.getIsReptile() == 0) {
                               Integer orderAmount = orderBillDetail.getReceivableAmount();
                               Long storeId = orderBillDetail.getPickupStoreId();
                               storeAmountMap.compute(storeId, (k, oldAmount) -> ObjectUtils.defaultIfNull(oldAmount, 0L) + orderAmount);
                           }
                       }
                   }

                   for (Map.Entry<Long, Long> storeAndAmount : storeAmountMap.entrySet()) {
                       long nowTime = System.currentTimeMillis();
                       FinanceStoreReport report = new FinanceStoreReport();
                       report.setStoreId(storeAndAmount.getKey());
                       report.setOutcomeAmount(0L);
                       report.setIncomeAmount(storeAndAmount.getValue());
                       report.setDate(monthInt * 100 + dayInt);
                       report.setMerchantId(merchantId);
                       report.setDeleted(YesOrNoEnum.NO.getValue());
                       report.setOpUserId(0L);
                       report.setCreateTime(nowTime);
                       report.setOpTime(nowTime);
                       resultList.add(report);
                   }
               }

               dayInt = dayInt + 1;
               startTime = tomorrowStart;
            }
            if (CollectionUtils.isNotEmpty(resultList)) {
                financeStoreReportMapper.batchInsert(resultList);
            }
            log.info("{}, fixHistoryFinanceReport end, merchantId:{}, month:{}", uuid, merchantId, monthInt);
        }
        log.info("{}, fixHistoryFinanceReport end, merchantId:{}, monthList:{}", uuid, merchantId, monthList);
    }
}
