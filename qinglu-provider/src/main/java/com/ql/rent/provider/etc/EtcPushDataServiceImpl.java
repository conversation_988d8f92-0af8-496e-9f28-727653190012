package com.ql.rent.provider.etc;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.ql.dto.ApiResultResp;
import com.ql.dto.mapping.RelationDTO;
import com.ql.dto.open.response.EtcOrderRespDetail;
import com.ql.dto.vehicle.VehicleEtcSyncReq;
import com.ql.enums.open.ResultCodeEnum;
import com.ql.rent.api.aggregate.model.request.etc.*;
import com.ql.rent.dao.trade.EtcFreeOrderConfigDBMapper;
import com.ql.rent.dao.trade.OrderInfoMapper;
import com.ql.rent.entity.trade.EtcFreeOrderConfigDB;
import com.ql.rent.entity.trade.EtcFreeOrderConfigDBExample;
import com.ql.rent.entity.trade.OrderInfo;
import com.ql.rent.entity.trade.OrderInfoExample;
import com.ql.rent.enums.EtcConstantsEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.param.etc.RelationParam;
import com.ql.rent.service.etc.IEtcPushDataService;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.etc.*;
import com.ql.rent.vo.store.LongLatVo;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class EtcPushDataServiceImpl implements IEtcPushDataService {

    private final IVehicleInfoService vehicleInfoService;
    private final EtcDeviceService etcDeviceService;
    private final IThirdIdRelationService thirdIdRelationService;
    private final EtcFreeOrderConfigDBMapper etcFreeOrderConfigDBMapper;
    private final Executor asyncPromiseExecutor;
    private final OrderInfoMapper orderInfoMapper;

    public boolean isNumeric(String str) {
        return str.matches("-?\\d+(\\.\\d+)?");
    }

    @Override
    public void orderCreate(OrderCreate orderNotifyReq, String merchantCode) {

        if ("saasEtc".equals(merchantCode)) {
            Long vehicleInfoId = Long.valueOf(orderNotifyReq.getVehId());
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoId, false);
            if(!baseById.isSuccess()||baseById.getModel()==null){
                //车辆 id是否发生变更  如变更 更新车辆关系·
                return;
            }
            VehicleInfoVO vehicleVo = baseById.getModel();
            Long vehicle = vehicleVo.getId();
            Long merchantId = vehicleVo.getMerchantId();
            Long storeId = vehicleVo.getStoreId();
            Long vehicleModelId = vehicleVo.getVehicleModelId();
//        匹配saas 订单 todo 根据 车辆 id 和 操作时间 获取 执行中订单   如果用户 再 取车前扫码  如何匹配
            //根据etc订单创建时间 和
            DateTime startTime = DateUtil.parse(orderNotifyReq.getStartTime());
            OrderInfoExample orderInfoExample = new OrderInfoExample();
            orderInfoExample.createCriteria()
                    .andVehicleIdEqualTo(vehicle).andMerchantIdEqualTo(merchantId)
                    .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)7,(byte)8))
                    .andPickupDateLessThanOrEqualTo(startTime)
                    .andReturnDateGreaterThanOrEqualTo(startTime);
            List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
            if(orderInfos.isEmpty()){
                //如果续租 根据续租时间 匹配
                OrderInfoExample orderInfoV1Example = new OrderInfoExample();
                orderInfoV1Example.createCriteria().andMerchantIdEqualTo(merchantId)
                        .andVehicleIdEqualTo(vehicle)
                        .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)7,(byte)8))
                        .andPickupDateLessThanOrEqualTo(startTime)
                        .andLastReturnDateGreaterThanOrEqualTo(startTime);

                orderInfos = orderInfoMapper.selectByExample(orderInfoV1Example);
            }
            Optional<Long> orderId = orderInfos.stream().map(OrderInfo::getId).findFirst();

            EtcOrderChargeVo vo = EtcOrderChargeVo.builder()
                    .orderId(orderId.map(Objects::toString).orElse(null))
                    .etcOrder(orderNotifyReq.getOrderNo())
                    .merchantId(merchantId)
                    .storeId(storeId)
                    .vehicleModelId(vehicleModelId)
                    .vehicleId(vehicle)
                    .amt(Optional.ofNullable(orderNotifyReq.getAmt()).map(Integer::longValue).orElse(0L))
                    .tenancyFee(Optional.ofNullable(orderNotifyReq.getAmt()).map(Integer::longValue).orElse(0L))
                    .orderStatus(orderNotifyReq.getStatus())
                    .orderStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime())
                    .orderEndTime(Optional.ofNullable(orderNotifyReq.getEndTime())
                            .map(DateUtil::parse)
                            .map(DateTime::getTime).orElse(null))
                    .source((byte) 1)
                    .build();
            etcDeviceService.saveOrderNotifySaasEtc(vo);
        } else {
            //获取 车辆牌照
            RelationParam licenseVO = RelationParam.builder()
                    .thirdIdList(Collections.singletonList(orderNotifyReq.getVehId()))
                    .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType())
                    .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
            Result<List<RelationVO>> licenseResp = thirdIdRelationService.relation(licenseVO);
            if (!licenseResp.isSuccess() && CollectionUtils.isEmpty(licenseResp.getModel())) {
                log.warn("未知车辆");
                return;
            }
            //车辆牌照
            for (RelationVO vehicleInfoVO : licenseResp.getModel()) {
                Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoVO.getSaasId(), false);
                if(!baseById.isSuccess()||baseById.getModel()==null){
                    //车辆 id是否发生变更  如变更 更新车辆关系·
                    continue;
                }
                VehicleInfoVO vehicleVo = baseById.getModel();
                Long vehicle = vehicleVo.getId();
                Long merchantId = vehicleVo.getMerchantId();
                Long storeId = vehicleVo.getStoreId();
                Long vehicleModelId = vehicleVo.getVehicleModelId();
//        匹配saas 订单 todo 根据 车辆 id 和 操作时间 获取 执行中订单   如果用户 再 取车前扫码  如何匹配
                //根据etc订单创建时间 和
                DateTime startTime = DateUtil.parse(orderNotifyReq.getStartTime());
                OrderInfoExample orderInfoExample = new OrderInfoExample();
                orderInfoExample.createCriteria()
                        .andVehicleIdEqualTo(vehicle).andMerchantIdEqualTo(merchantId)
                        .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                        .andPickupDateLessThanOrEqualTo(startTime)
                        .andReturnDateGreaterThanOrEqualTo(startTime);
                List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
                if(orderInfos.isEmpty()){
                    //如果续租 根据续租时间 匹配
                    OrderInfoExample orderInfoV1Example = new OrderInfoExample();
                    orderInfoV1Example.createCriteria().andMerchantIdEqualTo(merchantId)
                            .andVehicleIdEqualTo(vehicle)
                            .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                            .andPickupDateLessThanOrEqualTo(startTime)
                            .andLastReturnDateGreaterThanOrEqualTo(startTime)
                    ;

                    orderInfos = orderInfoMapper.selectByExample(orderInfoV1Example);
                }
                Optional<Long> orderId = orderInfos.stream().map(OrderInfo::getId).findFirst();

                EtcOrderChargeVo vo = EtcOrderChargeVo.builder()
                        .orderId(orderId.map(Objects::toString).orElse(null))
                        .etcOrder(orderNotifyReq.getOrderNo())
                        .merchantId(merchantId)
                        .storeId(storeId)
                        .vehicleModelId(vehicleModelId)
                        .vehicleId(vehicle)
                        .amt(Optional.ofNullable(orderNotifyReq.getAmt()).map(Integer::longValue).orElse(0L))
                        .orderStatus(orderNotifyReq.getStatus())
                        .orderStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime())
                        .orderEndTime(Optional.ofNullable(orderNotifyReq.getEndTime())
                                .map(DateUtil::parse)
                                .map(DateTime::getTime).orElse(null))
                        .source((byte) 2)
                        .build();
                etcDeviceService.saveOrderNotify(vo);
            }
        }

    }

    private void createEtcOrderCharge(OrderCreate orderNotifyReq, Long vehicleInfoId, String merchantCode) {
        Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoId, false);
        if(!baseById.isSuccess()||baseById.getModel()==null){
            //车辆 id是否发生变更  如变更 更新车辆关系·
            return;
        }
        VehicleInfoVO vehicleVo = baseById.getModel();
        Long vehicle = vehicleVo.getId();
        Long merchantId = vehicleVo.getMerchantId();
        Long storeId = vehicleVo.getStoreId();
        Long vehicleModelId = vehicleVo.getVehicleModelId();
//        匹配saas 订单 todo 根据 车辆 id 和 操作时间 获取 执行中订单   如果用户 再 取车前扫码  如何匹配
        //根据etc订单创建时间 和
        DateTime startTime = DateUtil.parse(orderNotifyReq.getStartTime());
        OrderInfoExample orderInfoExample = new OrderInfoExample();
        orderInfoExample.createCriteria()
                .andVehicleIdEqualTo(vehicle).andMerchantIdEqualTo(merchantId)
                .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                .andPickupDateLessThanOrEqualTo(startTime)
                .andReturnDateGreaterThanOrEqualTo(startTime);
        List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
        if(orderInfos.isEmpty()){
            //如果续租 根据续租时间 匹配
            OrderInfoExample orderInfoV1Example = new OrderInfoExample();
            orderInfoV1Example.createCriteria().andMerchantIdEqualTo(merchantId)
                    .andVehicleIdEqualTo(vehicle)
                    .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                    .andPickupDateLessThanOrEqualTo(startTime)
                    .andLastReturnDateGreaterThanOrEqualTo(startTime)
            ;

            orderInfos = orderInfoMapper.selectByExample(orderInfoV1Example);
        }
        Optional<Long> orderId = orderInfos.stream().map(OrderInfo::getId).findFirst();

        EtcOrderChargeVo vo = EtcOrderChargeVo.builder()
                .orderId(orderId.map(Objects::toString).orElse(null))
                .etcOrder(orderNotifyReq.getOrderNo())
                .merchantId(merchantId)
                .storeId(storeId)
                .vehicleModelId(vehicleModelId)
                .vehicleId(vehicle)
                .amt(Optional.ofNullable(orderNotifyReq.getAmt()).map(Integer::longValue).orElse(0L))
                .orderStatus(orderNotifyReq.getStatus())
                .orderStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime())
                .orderEndTime(Optional.ofNullable(orderNotifyReq.getEndTime())
                        .map(DateUtil::parse)
                        .map(DateTime::getTime).orElse(null))
                .source((byte)("saasEtc".equals(merchantCode) ? 1 : 2))
                .build();
        etcDeviceService.saveOrderNotify(vo);
    }

    @Override
    public void orderNotify(OrderNotifyReq orderNotifyReq, String merchantCode) {
        if ((StringUtils.isBlank(orderNotifyReq.getOrderNo())&&Objects.isNull(orderNotifyReq.getStartTime())) || StringUtils.isBlank(orderNotifyReq.getVehId())) {
            log.warn("缺少必要参数");
            return;
        }

        //获取 车辆牌照
        RelationParam licenseVO = RelationParam.builder()
                .thirdIdList(Collections.singletonList(orderNotifyReq.getVehId()))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> licenseResp = thirdIdRelationService.relation(licenseVO);
        if(!licenseResp.isSuccess()&&CollectionUtils.isEmpty(licenseResp.getModel())){
            log.warn("未知车辆");
            return;
        }

        for (RelationVO vehicleInfoVO : licenseResp.getModel()) {
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoVO.getSaasId(), false);
            if(!baseById.isSuccess()||baseById.getModel()==null){
                //车辆 id是否发生变更  如变更 更新车辆关系·
                continue;
            }
            VehicleInfoVO vehicleVo = baseById.getModel();
            Long vehicle = vehicleVo.getId();
            Long merchantId = vehicleVo.getMerchantId();
            Long storeId = vehicleVo.getStoreId();
            Long vehicleModelId = vehicleVo.getVehicleModelId();

            EtcOrderChargeVo vo = null;
            if(StringUtils.isNotBlank(orderNotifyReq.getOrderNo())){
                vo = etcDeviceService.orderQuery(merchantId, vehicle, orderNotifyReq.getOrderNo());
                if(vo.getOrderId()!=null){
                    vo.setAmt(NumberUtils.toLong(orderNotifyReq.getAmt()));
                    vo.setOrderStatus(orderNotifyReq.getStatus());
                    vo.setOrderStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime());
                    vo.setOrderEndTime(Optional.ofNullable(orderNotifyReq.getEndTime())
                                    .map(DateUtil::parse)
                                    .map(DateTime::getTime).orElse(null));
                    vo.setRealStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime());
                    vo.setRealEndTime(Optional.ofNullable(orderNotifyReq.getRealEndTime())
                                    .map(DateUtil::parse)
                                    .map(DateTime::getTime).orElse(null));
                }else {
                    vo = null;
                }
            }
            if(vo==null){
                //        匹配saas 订单 todo 根据 车辆 id 和 操作时间 获取 执行中订单   如果用户 再 取车前扫码  如何匹配
                //根据etc订单创建时间 和
                DateTime startTime = DateUtil.parse(orderNotifyReq.getStartTime());
                OrderInfoExample orderInfoExample = new OrderInfoExample();
                orderInfoExample.createCriteria().andMerchantIdEqualTo(merchantId)
                        .andVehicleIdEqualTo(vehicle)
                        .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                        .andPickupDateLessThanOrEqualTo(startTime)
                        .andReturnDateGreaterThanOrEqualTo(startTime);
                List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
                if(orderInfos.isEmpty()){
                    //如果续租 根据续租时间 匹配
                    OrderInfoExample orderInfoV1Example = new OrderInfoExample();
                    orderInfoV1Example.createCriteria().andMerchantIdEqualTo(merchantId)
                            .andVehicleIdEqualTo(vehicle)
                            .andOrderStatusNotIn(Lists.newArrayList((byte)0,(byte)6,(byte)7,(byte)8))
                            .andPickupDateLessThanOrEqualTo(startTime)
                            .andLastReturnDateGreaterThanOrEqualTo(startTime)
                    ;

                    orderInfos = orderInfoMapper.selectByExample(orderInfoV1Example);
                }
                Optional<Long> orderId = orderInfos.stream().map(OrderInfo::getId).findFirst();
                vo = EtcOrderChargeVo.builder()
                        .orderId(orderId.map(Objects::toString).orElse(null))
                        .etcOrder(orderNotifyReq.getEtcCode())
                        .merchantId(merchantId)
                        .vehicleId(vehicle)
                        .amt(NumberUtils.toLong(orderNotifyReq.getAmt()))
                        .orderStatus(orderNotifyReq.getStatus())

                        .orderStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime())
                        .orderEndTime(Optional.ofNullable(orderNotifyReq.getEndTime())
                                .map(DateUtil::parse)
                                .map(DateTime::getTime).orElse(null))
                        .realStartTime(DateUtil.parse(orderNotifyReq.getStartTime()).getTime())
                        .realEndTime(Optional.ofNullable(orderNotifyReq.getRealEndTime())
                                .map(DateUtil::parse)
                                .map(DateTime::getTime).orElse(null))
                        .build();
            }
            etcDeviceService.saveOrderNotify(vo);
            if(EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.getEnumeration().equals(vo.getOrderStatus())
            ||EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getEnumeration().equals(vo.getOrderStatus())){
                //订单结束时推送 etc订单数据
                EtcOrderChargeVo finalVo = vo;
                CompletableFuture.runAsync(() -> {
                    etcDeviceService.aSynPushETCInfoToTrip(finalVo.getEtcOrder(),finalVo.getVehicleId());
                },asyncPromiseExecutor).exceptionally(ex -> {
                    log.error("取消etc数据推送：merchantId={},etcOrder={},vehicleId={}",merchantId,finalVo.getEtcOrder(), ex);
                    return null;
                });

            }
        }
    }

    @Override
    public void vehicleNotify(VehicleNotifyReq vehicleNotifyReq, String merchantCode) {

        String vehId = vehicleNotifyReq.getVehId();
        if (StringUtils.isAnyBlank( vehId)) {
            log.error("缺少必要参数");
            throw new BizException("缺少必要参数");
        }

        //获取 车辆牌照
        RelationParam licenseVO = RelationParam.builder()
                .saasIdList(Collections.singletonList(Long.valueOf(vehicleNotifyReq.getVehId())))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE_LICENCE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> licenseResp = thirdIdRelationService.relation(licenseVO);
        if(!licenseResp.isSuccess()||CollectionUtils.isEmpty(licenseResp.getModel())){
            log.warn("未知车辆");
            return;
        }
        RelationVO relationVO = licenseResp.getModel().get(0);
        //车辆牌照
        String license = relationVO.getThirdId();

        //车辆id是否发生变更
        List<VehicleInfoVO> vehicleInfoVOS = vehicleInfoService.selectVehicleInfoVOS(license);
        Map<Long, VehicleInfoVO> voMap = vehicleInfoVOS.stream()
                .filter(f -> YesOrNoEnum.isNo(f.getDeleted()))
                .collect(Collectors.toMap(VehicleInfoVO::getMerchantId, Function.identity(), (k1, k2) -> k1));

        String longitude = vehicleNotifyReq.getLongitude();
        String latitude = vehicleNotifyReq.getLatitude();
        LongLatVo gis = new LongLatVo();
        if (StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude)) {
            gis.setLongitude(new BigDecimal(longitude).divide(BigDecimal.valueOf(1000000.0)).doubleValue());
            gis.setLatitude(new BigDecimal(latitude).divide(BigDecimal.valueOf(1000000.0)).doubleValue());
        }
        // 维护样本数据 只用于 备份
        EtcDeviceVo samplesVo = EtcDeviceVo.builder()
                .merchantId(0L)
                .storeId(0L)
                .vehicleModelId(0L)
                .vehicleId(0L)
                .etcNo(vehicleNotifyReq.getEtcNo())
                .icNo(vehicleNotifyReq.getIcNo())
                .online(EtcConstantsEnum.OnlineEnum.forByEnumeration(vehicleNotifyReq.getOnline()).getDesc())
                .hardLinkStatus(EtcConstantsEnum.HardNinkStatusEnum.forByEnumeration(vehicleNotifyReq.getHardLinkStatus()).getDesc())
                .workStatus(EtcConstantsEnum.WorkStatusEnum.forByEnumeration(vehicleNotifyReq.getWorkStatus()).getDesc())
                .activateStatus(EtcConstantsEnum.ActivateStatusEnum.forByEnumeration(vehicleNotifyReq.getActivateStatus()).getDesc())
                .gis(gis)
                .license(license)
                .service(EtcConstantsEnum.ServiceEnum.forByEnumeration(vehicleNotifyReq.getService()).getDesc()).build();
        EtcDeviceVo samples = etcDeviceService.addSamples(samplesVo);


        //查询saas车辆
        RelationParam vehicleRelationVO = RelationParam.builder()
                .thirdIdList(Collections.singletonList(vehId))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> vehicleRelationList = thirdIdRelationService.relation(vehicleRelationVO);
        if(!vehicleRelationList.isSuccess()||CollectionUtils.isEmpty(vehicleRelationList.getModel())){
            //绑定车辆关系
            log.warn("未知车辆");
            return;
        }

        for (RelationVO vehicleInfoVO : vehicleRelationList.getModel()) {
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoVO.getSaasId(), false);
            VehicleInfoVO vehicleVo = baseById.getModel();
            if(!baseById.isSuccess()||baseById.getModel()==null){
                //车辆 id是否发生变更  如变更 更新车辆关系·
                if(voMap.get(vehicleInfoVO.getMerchantId())==null||voMap.get(vehicleInfoVO.getMerchantId()).getId().equals(vehicleInfoVO.getSaasId())){
                    continue;
                }
                //更新车辆关系
                vehicleVo = voMap.get(vehicleInfoVO.getMerchantId());
                RelationVO vo = new RelationVO();
                vo.setMerchantId(vehicleInfoVO.getMerchantId());
                vo.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
                vo.setSource(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue());
                vo.setSaasId(vehicleVo.getId());
                vo.setThirdId(vehId);
                thirdIdRelationService.updateByThirdId(vo);

            }
            EtcDeviceVo etcDeviceVo = new EtcDeviceVo();
            BeanUtils.copyProperties(samples,etcDeviceVo);
            etcDeviceVo.setId(null);
            etcDeviceVo.setMerchantId(vehicleVo.getMerchantId());
            etcDeviceVo.setStoreId(vehicleVo.getStoreId());
            etcDeviceVo.setVehicleModelId(vehicleVo.getVehicleModelId());
            etcDeviceVo.setVehicleId(vehicleVo.getId());
            etcDeviceVo.setLicense(license);
            etcDeviceService.saveDevice(etcDeviceVo);
        }

    }

    @Override
    public void vehicleMessageNotify(VehicleEtcSyncReq vehicleNotifyReq, String merchantCode) {
        String plateNumber = vehicleNotifyReq.getPlateNumber();
        String vehId = vehicleNotifyReq.getVehId();
        if (StringUtils.isAnyBlank(plateNumber, vehId)) {
            log.error("缺少必要参数");
            throw new BizException("缺少必要参数");
        }

        //检索 车牌 绑定 关系  如果存在 并且 不相同 则更新
        //绑定车牌 和三方 id关系
        RelationVO updateVo = new RelationVO();
        updateVo.setMerchantId(0L);
        updateVo.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE_LICENCE.getType());
        updateVo.setSource(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue());
        updateVo.setSaasId(Long.valueOf(vehicleNotifyReq.getVehId()));
        updateVo.setThirdId(plateNumber);
        thirdIdRelationService.updateByThirdId(updateVo);
        // 维护样本数据 只用于 备份
        EtcDeviceVo samplesVo = EtcDeviceVo.builder()
                .merchantId(0L)
                .storeId(0L)
                .vehicleModelId(0L)
                .vehicleId(0L)
                .plateColor(EtcConstantsEnum.PlateColorEnum.forByCode(vehicleNotifyReq.getPlateColor()).getDesc())
                .axles(vehicleNotifyReq.getAxles())
                .length(vehicleNotifyReq.getLength())
                .width(vehicleNotifyReq.getWidth())
                .height(vehicleNotifyReq.getHeight())
                .totalWeight(vehicleNotifyReq.getTotalWeight())
                .grossWass(vehicleNotifyReq.getGrossMass())
                .registerDate(DateUtil.parse(vehicleNotifyReq.getRegisterDate()))
                .grantDate(DateUtil.parse(vehicleNotifyReq.getGrantDate()))
                .ownerName(vehicleNotifyReq.getOwnerName())
                .license(plateNumber)
                .build();
        EtcDeviceVo samples = etcDeviceService.addSamples(samplesVo);

        List<VehicleInfoVO> vehicleInfoVOS = vehicleInfoService.selectVehicleInfoVOS(plateNumber);
        if(vehicleInfoVOS.isEmpty()){
            log.info("车辆不存在");
            // 查询saas车辆，如果没找到,先落库保存样本数据。
            throw new BizException("车辆不存在");
        }

        // 查询saas车辆，基于样本数据 更新数据
        for (VehicleInfoVO vehicleInfoVO : vehicleInfoVOS) {
            if(YesOrNoEnum.isYes(vehicleInfoVO.getDeleted())){
                continue;
            }
            //绑定车辆关系
            RelationVO vo = new RelationVO();
            vo.setMerchantId(vehicleInfoVO.getMerchantId());
            vo.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
            vo.setSource(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue());
            vo.setSaasId(vehicleInfoVO.getId());
            vo.setThirdId(vehId);
            thirdIdRelationService.updateBySaasId(vo);

            // 基于样本数据 存储 车辆扩展信息
            EtcDeviceVo etcDeviceVo = new EtcDeviceVo();
            BeanUtils.copyProperties(samples,etcDeviceVo);
            etcDeviceVo.setId(null);
            etcDeviceVo.setMerchantId(vehicleInfoVO.getMerchantId());
            etcDeviceVo.setStoreId(vehicleInfoVO.getStoreId());
            etcDeviceVo.setVehicleModelId(vehicleInfoVO.getVehicleModelId());
            etcDeviceVo.setVehicleId(vehicleInfoVO.getId());
            etcDeviceVo.setLicense(plateNumber);
            etcDeviceService.saveDevice(etcDeviceVo);
        }
    }

    @Override
    public void orderTraffic(OrderTrafficReq orderTrafficReq, String merchantCode) {
        String orderNo = orderTrafficReq.getOrderNo();
        String tripId = orderTrafficReq.getTripId();
        if (StringUtils.isAnyBlank(orderNo, tripId)) {
            log.error("缺少必要参数");
        }
        //记录通行记录
        EtcOrderTollFeeVo vo = EtcOrderTollFeeVo
                .builder()
                .opUserId(1L)
                .amt(Optional.ofNullable(orderTrafficReq.getAmt()).map(amt->new BigDecimal(amt).multiply(BigDecimal.valueOf(100)).longValue()).orElse(0L))
                .etcOrder(orderTrafficReq.getOrderNo())
                .endStationName(orderTrafficReq.getEndStationName())
                .startStationName(orderTrafficReq.getStartStationName())
                .endTime(DateUtil.parse(orderTrafficReq.getEndTime()))
                .startTime(DateUtil.parse(orderTrafficReq.getStartTime()))
                .subScene(orderTrafficReq.getSubScene())
                .subType(orderTrafficReq.getSubType())
                .tripId(orderTrafficReq.getTripId())
                .build();
        etcDeviceService.saveEtcOrderTraffic(vo);
    }

    @Override
    public void etcActivate(EtcActivateReq orderTrafficReq, String merchantCode) {
        String vehId = orderTrafficReq.getVehId();
        if (StringUtils.isAnyBlank( vehId)) {
            log.error("缺少必要参数");
            throw new BizException("缺少必要参数");
        }

        //获取 车辆牌照
        RelationParam licenseVO = RelationParam.builder()
                .saasIdList(Collections.singletonList(Long.valueOf(orderTrafficReq.getVehId())))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE_LICENCE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> licenseResp = thirdIdRelationService.relation(licenseVO);
        if(!licenseResp.isSuccess()&&CollectionUtils.isEmpty(licenseResp.getModel())){
            log.warn("未知车辆");
            return;
        }
        RelationVO relationVO = licenseResp.getModel().get(0);
        //车辆牌照
        String license = relationVO.getThirdId();

        // 维护样本数据 只用于 备份
        EtcDeviceVo samplesVo = EtcDeviceVo.builder()
                .merchantId(0L)
                .storeId(0L)
                .vehicleModelId(0L)
                .vehicleId(0L)
                .availabilityStatus(EtcConstantsEnum.AvailabilityStatusEnum.forByEnumeration(orderTrafficReq.getStatus()).getDesc())
                .build();
        EtcDeviceVo samples = etcDeviceService.addSamples(samplesVo);

        List<VehicleInfoVO> vehicleInfoVOS = vehicleInfoService.selectVehicleInfoVOS(license);
        Map<Long, VehicleInfoVO> voMap = vehicleInfoVOS.stream()
                .filter(f -> YesOrNoEnum.isNo(f.getDeleted()))
                .collect(Collectors.toMap(VehicleInfoVO::getMerchantId, Function.identity(), (k1, k2) -> k1));

        if(CollectionUtils.isEmpty(vehicleInfoVOS)){
            //绑定车辆关系
            log.warn("未知车辆");
            return;
        }

        //查询saas车辆
        RelationParam vehicleRelationVO = RelationParam.builder()
                .thirdIdList(Collections.singletonList(vehId))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> vehicleRelationList = thirdIdRelationService.relation(vehicleRelationVO);
        if(!vehicleRelationList.isSuccess()||CollectionUtils.isEmpty(vehicleRelationList.getModel())){
            //绑定车辆关系
            log.warn("未知车辆");
            return;
        }

        for (RelationVO vehicleInfoVO : vehicleRelationList.getModel()) {
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleInfoVO.getSaasId(), false);
            VehicleInfoVO vehicleVo = baseById.getModel();
            if(!baseById.isSuccess()||baseById.getModel()==null){
                //车辆 id是否发生变更  如变更 更新车辆关系·
                if(voMap.get(vehicleInfoVO.getMerchantId())==null||voMap.get(vehicleInfoVO.getMerchantId()).getId().equals(vehicleInfoVO.getSaasId())){
                    continue;
                }
                //更新车辆关系
                vehicleVo = voMap.get(vehicleInfoVO.getMerchantId());
                RelationVO vo = new RelationVO();
                vo.setMerchantId(vehicleInfoVO.getMerchantId());
                vo.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
                vo.setSource(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue());
                vo.setSaasId(vehicleVo.getId());
                vo.setThirdId(vehId);
                thirdIdRelationService.updateByThirdId(vo);

            }
            // 存储 车辆扩展信息
            EtcDeviceVo etcDeviceVo = new EtcDeviceVo();
            BeanUtils.copyProperties(samples,etcDeviceVo);
            etcDeviceVo.setId(null);
            etcDeviceVo.setMerchantId(vehicleVo.getMerchantId());
            etcDeviceVo.setStoreId(vehicleVo.getStoreId());
            etcDeviceVo.setVehicleModelId(vehicleVo.getVehicleModelId());
            etcDeviceVo.setVehicleId(vehicleVo.getId());
            etcDeviceVo.setLicense(license);
            etcDeviceService.saveDevice(etcDeviceVo);
            if(Objects.equals(EtcConstantsEnum.AvailabilityStatusEnum.activated.getDesc(),etcDeviceVo.getAvailabilityStatus())) {
                //发行状态已激活 sku打上 etc标签
//            vehicleTagService.enableEtcTags(storeId, vehicleModelId, vehicleId);
                //跟新车辆表 etc标签
                vehicleInfoService.updateTiIncludeEtc(vehicleVo.getMerchantId(), vehicleVo.getId(), 1);
            }
        }
    }

    @Override
    public Object orderQuery(OrderQuery query, String merchantCode) {

        RelationParam vehicleRelationVO = RelationParam.builder()
                .thirdIdList(Collections.singletonList(query.getVehId()))
                .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType())
                .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
        Result<List<RelationVO>> vehicleRelationList = thirdIdRelationService.relation(vehicleRelationVO);
        if(vehicleRelationList.isSuccess()&&vehicleRelationList.getModel().isEmpty()){
            //绑定车辆关系
            throw new BizException("未知车辆");
        }
        if(CollectionUtils.isEmpty(vehicleRelationList.getModel())){
            EtcOrderRespDetail build = EtcOrderRespDetail.builder()
                    .bizCode("USERNOTALLOW")
                    .build();
            return build;
        }

        RelationVO vehicleRelation = vehicleRelationList.getModel().get(0);

        DateTime startTime = DateUtil.parse(query.getStartTime());
        OrderInfoExample orderInfoExample = new OrderInfoExample();
        orderInfoExample.createCriteria()
            .andMerchantIdEqualTo(vehicleRelation.getMerchantId())
                .andVehicleIdEqualTo(vehicleRelation.getSaasId())
                .andOrderStatusNotIn(Lists.newArrayList((byte)0, (byte)6, (byte)7, (byte)8))
                .andPickupDateLessThanOrEqualTo(startTime)
                .andReturnDateGreaterThanOrEqualTo(startTime);

        List<OrderInfo> orderInfos = orderInfoMapper.selectByExample(orderInfoExample);
        if (orderInfos.isEmpty()) {
            //如果续租 根据续租时间 匹配
            OrderInfoExample orderInfoV1Example = new OrderInfoExample();
            orderInfoV1Example.createCriteria()
                .andMerchantIdEqualTo(vehicleRelation.getMerchantId())
                    .andVehicleIdEqualTo(vehicleRelation.getSaasId())
                    .andOrderStatusNotIn(Lists.newArrayList((byte)0, (byte)6, (byte)7, (byte)8))
                    .andPickupDateLessThanOrEqualTo(startTime)
                    .andLastReturnDateGreaterThanOrEqualTo(startTime);
            orderInfos = orderInfoMapper.selectByExample(orderInfoV1Example);
        }

        //获取saas订单信息
        if (orderInfos.size() != 1) {
            EtcOrderRespDetail build = EtcOrderRespDetail.builder()
                    .bizCode("USERNOTALLOW")
                    .build();
            return build;
        }

        OrderInfo orderInfo = orderInfos.get(0);
        EtcFreeOrderConfigDBExample etcFreeOrderConfigDBExample = new EtcFreeOrderConfigDBExample();
        etcFreeOrderConfigDBExample.createCriteria()
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andMerchantIdEqualTo(orderInfo.getMerchantId())
                .andCityIdEqualTo(orderInfo.getPickupCityId());
        List<EtcFreeOrderConfigDB> etcFreeOrderConfigDBS = etcFreeOrderConfigDBMapper.selectByExample(etcFreeOrderConfigDBExample);

        Date lastReturnDate = orderInfo.getLastReturnDate();
        if(lastReturnDate == null){
            lastReturnDate = orderInfo.getReturnDate();
        }
        Byte orderSource = orderInfo.getOrderSource();
        if(StringUtils.isBlank(orderInfo.getSourceOrderId())){
            orderSource = (byte)1;
        }
        if(etcFreeOrderConfigDBS.isEmpty()&&(orderSource==2)){
            EtcOrderRespDetail build = EtcOrderRespDetail.builder()
                    .bizCode("USERNOTALLOW")
                    .build();
            return build;
        }
        EtcOrderRespDetail build = EtcOrderRespDetail.builder()
//                .orderNo(query.getMobile())
                .bizCode("SUCCESS")
                .channel(orderSource.toString())
                .endTime(DateUtil.format(lastReturnDate, DatePattern.NORM_DATETIME_PATTERN))
                .build();
        return build;
    }

    public ApiResultResp<List<RelationDTO>> getThirdId(Long source, List<Long> saasId, Long merchantId,Byte type) {
        Result<List<RelationVO>> relation =
            thirdIdRelationService.relation(RelationParam.builder().merchantId(merchantId)
                .type(type).source(source).saasIdList(saasId).build());
        if (!relation.isSuccess()) {
            return ApiResultResp.failResult(ResultCodeEnum.CommonResultCode.e004);
        }
        List<RelationVO> model = relation.getModel();
        if (Objects.isNull(model)) {
            return ApiResultResp.failResult(ResultCodeEnum.CommonResultCode.e004);
        }
        return ApiResultResp.successResult(model.stream().map(x -> {
            RelationDTO relationDTO = new RelationDTO();
            BeanUtils.copyProperties(x, relationDTO);
            return relationDTO;
        }).collect(Collectors.toList()));
    }

    public void saveStoreMapping(Long source, Long saasId, String thirdId, Long merchantId) {
        if (Objects.isNull(saasId) || Objects.isNull(source) || StringUtils.isBlank(thirdId) || Objects.isNull(
            merchantId)) {
            log.warn("缺少必要参数");
            return;
        }
        RelationVO relationVO = new RelationVO();
        relationVO.setSaasId(saasId);
        relationVO.setSource(source);
        relationVO.setMerchantId(merchantId);
        relationVO.setThirdId(thirdId);
        relationVO.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_MERCHANT.getType());
        thirdIdRelationService.save(relationVO);
    }
}
