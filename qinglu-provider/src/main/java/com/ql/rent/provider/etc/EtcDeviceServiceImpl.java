package com.ql.rent.provider.etc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.dto.ApiResultResp;
import com.ql.dto.mapping.RelationDTO;
import com.ql.dto.open.response.EtcOrderRespDetail;
import com.ql.dto.open.response.EtcRespDetail;
import com.ql.dto.store.OrganCreate;
import com.ql.dto.vehicle.VehicleUnmountReq;
import com.ql.enums.open.ResultCodeEnum;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.SignRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.PushEtcOrderReq;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.PushEtcOrderTripReq;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.BaseCtripResponse;
import com.ql.rent.api.aggregate.model.request.etc.CancellationOrder;
import com.ql.rent.api.aggregate.remote.ctrip.api.CtripV2SpApiClient;
import com.ql.rent.api.aggregate.remote.etc.api.EtcApiClient;
import com.ql.rent.api.aggregate.remote.etc.api.SaasSelfEtcApiClient;
import com.ql.rent.api.aggregate.remote.etc.api.dto.EtcEndOrderRequest;
import com.ql.rent.api.aggregate.remote.etc.api.dto.EtcVehicleInfo;
import com.ql.rent.api.aggregate.remote.etc.api.dto.QueryEtcVehicleResponse;
import com.ql.rent.common.FileUploader;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.CtripRequestSignBuilder;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.trade.*;
import com.ql.rent.dao.vehicle.EtcDeviceExtMapper;
import com.ql.rent.dao.vehicle.EtcDeviceMapper;
import com.ql.rent.entity.trade.*;
import com.ql.rent.entity.vehicle.EtcDevice;
import com.ql.rent.entity.vehicle.EtcDeviceExample;
import com.ql.rent.entity.vehicle.EtcDeviceExt;
import com.ql.rent.entity.vehicle.EtcDeviceExtExample;
import com.ql.rent.enums.EtcConstantsEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.trade.MallOrderItemEnum;
import com.ql.rent.enums.vehicle.VehicleMediaEnum;
import com.ql.rent.enums.vehicle.VehicleStatusEnum;
import com.ql.rent.event.EventPublisher;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.etc.EtcDeviceReq;
import com.ql.rent.param.etc.EtcOrderChargeReq;
import com.ql.rent.param.etc.EtcOrderChargeResp;
import com.ql.rent.param.etc.RelationParam;
import com.ql.rent.param.trade.EtcOrderBaseQueryParam;
import com.ql.rent.param.trade.RefundEmailParam;
import com.ql.rent.param.vehicle.VehicleMediaQueryParam;
import com.ql.rent.param.vehicle.VehicleModelInnerQuery;
import com.ql.rent.provider.store.StoreInfoServiceImpl;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.service.merchant.MerchantInfoService;
import com.ql.rent.service.merchant.SysRoleService;
import com.ql.rent.service.merchant.SysUserService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.trade.IEtcOrderService;
import com.ql.rent.service.trade.IMallServiceOrderInfoService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.service.vehicle.IVehicleMediaService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.CurrencyUtils;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.etc.*;
import com.ql.rent.vo.merchant.MerchantInfoVo;
import com.ql.rent.vo.merchant.SysUserBoxVo;
import com.ql.rent.vo.store.LongLatVo;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.trade.EtcOrderVO;
import com.ql.rent.vo.trade.MallServiceOrderInfoDTO;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import com.ql.rent.vo.vehicle.VehicleInfoVO;
import com.ql.rent.vo.vehicle.VehicleMediaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNumeric;

@Service
@Slf4j
public class EtcDeviceServiceImpl implements EtcDeviceService {
    @Resource
    private EtcDeviceMapper etcDeviceMapper;

    @Resource
    private EtcOrderChargeMapper etcOrderChargeMapper;
    @Resource
    private WithdrawalRecordInfoMapper withdrawalRecordInfoMapper;

    @Autowired
    private EventPublisher eventPublisher;

    @Resource
    private EtcOrderTollFeeMapper etcOrderTollFeeMapper;

    @Resource
    IStoreInfoService storeInfoService;

    @Resource
    IVehicleModelService vehicleModelService;

    @Resource
    IVehicleInfoService vehicleInfoService;

    @Resource
    private IMallServiceOrderInfoService mallServiceOrderInfoService;

    @Resource
    private  EtcApiClient etcApiClient;

    @Resource
    private SaasSelfEtcApiClient saasSelfEtcApiClient;

    @Resource
    private  IThirdIdRelationService thirdIdRelationService;

    @Resource
    private  MerchantInfoService merchantInfoService;

    @Resource
    MallServiceBillInfoMapper mallServiceBillInfoMapper;

    @Resource
    private Executor asyncPromiseExecutor;

    @Resource
    private CtripV2SpApiClient ctripV2SpApiClient;

    @Resource
    private CtripRequestSignBuilder signBuilder;
    @Resource
    private SysUserService userService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private IVehicleMediaService vehicleMediaService;

    @Resource
    private IEtcOrderService iEtcOrderService;

    @Resource
    EtcDeviceQRCodeServiceImpl selfEtcDeviceService;

    @Resource
    EtcDeviceExtMapper etcDeviceExtMapper;

    @Resource
    FileUploader fileUploader;

    @Resource
    EtcOrderMapper etcOrderMapper;

    @Override
    public Result<PageListVo<EtcDeviceVo>> obtainDeviceList(EtcDeviceReq req) {
        EtcDeviceExample example = this.convertToExample(req);
        Page<EtcDevice> page = PageHelper.startPage(req.getPageIndex(), req.getPageSize());
        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(example);

        List<Long> vehicleIdList = etcDevices.stream().map(EtcDevice::getVehicleId).distinct().collect(Collectors.toList());
        Map<Long, VehicleInfoVO> vehiclelByIdMap = Maps.newHashMap();
        Result<Map<Long,VehicleInfoVO>> vehicleResult = vehicleInfoService.obtainVehicleInfo(req.getMerchantId(),vehicleIdList);
        if(vehicleResult.isSuccess()&&vehicleResult.getModel()!=null){
            vehiclelByIdMap = vehicleResult.getModel();
        }
        Map<Long, VehicleInfoVO> finalVehiclelByIdMap = vehiclelByIdMap;
        List<EtcDeviceVo> voList = etcDevices.stream().map(db -> {
            EtcDeviceVo vo = this.convertToVo(db);
            vo.setLicense(Optional.ofNullable(finalVehiclelByIdMap.get(vo.getVehicleId()))
                .map(VehicleInfoVO::getLicense).orElse(""));
            return vo;
        }).collect(Collectors.toList());
        return ResultUtil.successResult(PageListVo.buildPageList(page.getTotal(),voList));
    }

    @Override
    public Result<List<EtcDeviceVo>> listDevice(EtcDeviceReq req) {
        if (req == null || req.getMerchantId() == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        EtcDeviceExample example = this.convertToExample(req);
        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(etcDevices)) {
            return ResultUtil.successResult(Collections.emptyList());
        }

        List<EtcDeviceVo> resultList = etcDevices.stream().map(this::convertToVo).collect(Collectors.toList());
        return ResultUtil.successResult(resultList);
    }

    private EtcDeviceVo convertToVo(EtcDevice db) {
        EtcDeviceVo vo = new EtcDeviceVo();
        vo.setId(db.getId());
        vo.setMerchantId(db.getMerchantId());
        vo.setStoreId(db.getStoreId());
        vo.setVehicleModelId(db.getVehicleModelId());
        vo.setVehicleId(db.getVehicleId());
        vo.setIcNo(db.getIcNo());
        vo.setEtcNo(db.getEtcNo());
        vo.setOnline(EtcConstantsEnum.OnlineEnum.forByCode(db.getOnline()).getDesc());
        vo.setHardLinkStatus(EtcConstantsEnum.HardNinkStatusEnum.forByCode(db.getHardNinkStatus()).getDesc());
        vo.setWorkStatus(EtcConstantsEnum.WorkStatusEnum.forByCode(db.getWorkStatus()).getDesc());
        vo.setService(EtcConstantsEnum.ServiceEnum.forByCode(db.getService()).getDesc());
        vo.setActivateStatus(EtcConstantsEnum.ActivateStatusEnum.forByCode(db.getActivateStatus()).getDesc());
        vo.setAvailabilityStatus(EtcConstantsEnum.AvailabilityStatusEnum.forByCode(db.getAvailabilityStatus()).getDesc());
        vo.setRemark(db.getRemark());
        if (db.getGis() != null) {
            Geometry geometry = null;
            try {
                geometry = StoreInfoServiceImpl.getGeometryByBytes(db.getGis());
                LongLatVo gis = new LongLatVo();
                gis.setLatitude(geometry.getCentroid().getX());
                gis.setLongitude(geometry.getCentroid().getY());
                vo.setGis(gis);
            } catch (Exception e) {
            }
        }
        vo.setEtcSourceStr(EtcConstantsEnum.ThirdSourceEnum.forByCode(db.getEtcSource()).getName());
        return vo;
    }

    private EtcDeviceExample convertToExample(EtcDeviceReq req) {
        EtcDeviceExample example = new EtcDeviceExample();
        EtcDeviceExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(req.getMerchantId())
            .andSampleIsEqualTo(YesOrNoEnum.NO.getValue())
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        if (req.getVehicleId() != null) {
            criteria.andVehicleIdEqualTo(req.getVehicleId());
        }
        if (req.getStoreId() != null) {
            criteria.andStoreIdEqualTo(req.getStoreId());
        }
        if (req.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(req.getVehicleModelId());
        }
        if (CollectionUtils.isNotEmpty(req.getVehicleIdList())) {
            criteria.andVehicleIdIn(req.getVehicleIdList());
        }
        if (req.getService() != null) {
            criteria.andServiceEqualTo(req.getService());
        }
        return example;
    }

    @Override
    public Result<PageListVo<EtcOrderChargeVo>> obtainRevenueDetails(EtcDeviceReq req) {
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria
                .andMerchantIdEqualTo(req.getMerchantId());

        if (req.getStoreId() != null) {
            criteria.andStoreIdEqualTo(req.getStoreId());
        }
        if (req.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(req.getVehicleModelId());
        }
        if (CollectionUtils.isNotEmpty(req.getVehicleModelIdList())) {
            criteria.andVehicleModelIdIn(req.getVehicleModelIdList());
        }
        if (req.getOrderId() != null) {
            criteria.andOrderIdEqualTo(req.getOrderId());
        }
        if (req.getEtcOrderId() != null) {
            criteria.andEtcOrderEqualTo(req.getEtcOrderId());
        }
        if(req.getOrderLock()!=null){
            criteria.andOrderLockEqualTo(req.getOrderLock());
        }
        if (req.getOrderStatus() != null) {
            criteria.andOrderStatusEqualTo(req.getOrderStatus());
        }

        if (req.getRealStartTime() != null) {
            criteria.andRealEndTimeGreaterThanOrEqualTo(new Date(req.getRealStartTime()));
        }
        if (req.getRealEndTime() != null) {
            criteria.andRealEndTimeLessThanOrEqualTo(new Date(req.getRealEndTime()));
        }
        if(ObjectUtils.isNotEmpty(req.getVehicleId())){
            criteria .andVehicleIdEqualTo(req.getVehicleId());
        }

        Page<EtcOrderCharge> page = PageHelper.startPage(req.getPageIndex(), req.getPageSize());
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);

        List<Long> storeIdList = etcOrderCharges.stream().map(EtcOrderCharge::getStoreId).distinct().collect(Collectors.toList());
        List<Long> vehicleModelIdList = etcOrderCharges.stream().map(EtcOrderCharge::getVehicleModelId).distinct().collect(Collectors.toList());
        List<Long> vehicleIdList = etcOrderCharges.stream().map(EtcOrderCharge::getVehicleId).distinct().collect(Collectors.toList());
        Result<List<StoreSimpleVo>> storeResult = storeInfoService.storeSampleForUnion(storeIdList);

        Map<Long, String> storeNameById = Maps.newHashMap();
        Map<Long, BaseVehicleModelVO> vehicleModelByIdMap = Maps.newHashMap();
        Map<Long, VehicleInfoVO> vehiclelByIdMap = Maps.newHashMap();

        if(storeResult.isSuccess()&&storeResult.getModel()!=null){
            storeNameById = storeResult.getModel().stream().collect(Collectors.toMap(StoreSimpleVo::getStoreId, StoreSimpleVo::getStoreName));
        }
        Result<Map<Long, BaseVehicleModelVO>> mapResult = vehicleModelService.obtainVehicleModeInfo(req.getMerchantId(), vehicleModelIdList);
        if(mapResult.isSuccess()&&mapResult.getModel()!=null){
            vehicleModelByIdMap = mapResult.getModel();
        }
        Result<Map<Long,VehicleInfoVO>> vehicleResult = vehicleInfoService.obtainVehicleInfo(req.getMerchantId(),vehicleIdList);
        if(vehicleResult.isSuccess()&&vehicleResult.getModel()!=null){
            vehiclelByIdMap = vehicleResult.getModel();
        }

        Map<Long, String> finalStoreNameById = storeNameById;
        Map<Long, BaseVehicleModelVO> finalVehicleModelByIdMap = vehicleModelByIdMap;
        Map<Long, VehicleInfoVO> finalVehiclelByIdMap = vehiclelByIdMap;
        List<EtcOrderChargeVo> voList = etcOrderCharges.stream().map(db -> {
            EtcOrderChargeVo vo = new EtcOrderChargeVo();
            BeanUtils.copyProperties(db, vo);
            vo.setOrderLock(EtcConstantsEnum.OrderLockEnum.forByCode(db.getOrderLock()).getDesc());
            //车型 门店 , 车辆
            vo.setStoreName(finalStoreNameById.getOrDefault(vo.getStoreId(),""));
            vo.setVehicleSeryName(Optional.ofNullable(finalVehicleModelByIdMap.get(vo.getVehicleModelId()))
                    .map(BaseVehicleModelVO::getVehicleSeryName).orElse(""));
            vo.setVehicleName(Optional.ofNullable(finalVehiclelByIdMap.get(vo.getVehicleId()))
                    .map(VehicleInfoVO::getLicense).orElse(""));
            vo.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus()).getDesc());
            if(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus())== EtcConstantsEnum.EtcOrderStatusEnum.ended_oder){
                vo.setEndPaymentTime(db.getOpTime());
            }
            vo.setOrderStartTime(Optional.ofNullable(db.getOrderStartTime()).map(Date::getTime).orElse(null));
            vo.setOrderEndTime(Optional.ofNullable(db.getOrderEndTime()).map(Date::getTime).orElse(null));

            vo.setRealStartTime(Optional.ofNullable(db.getRealStartTime()).map(Date::getTime).orElse(null));
            vo.setRealEndTime(Optional.ofNullable(db.getRealEndTime()).map(Date::getTime).orElse(null));

            String sourceName = EtcConstantsEnum.EtcOrderChargeSource.forByCode(db.getSource()).getName();
            vo.setEtcSourceStr(sourceName);
            return vo;
        }).collect(Collectors.toList());
        return ResultUtil.successResult(PageListVo.buildPageList(page.getTotal(),voList));
    }


    @Override
    public Result<EtcOrderChargeResp> obtainWithdrawalAmount(EtcOrderChargeReq req) {
        //获取商家下所有 结算订单 分润汇总
        Integer actualIncome = etcOrderChargeMapper.obtainWithdrawalAmount(req.getMerchantId(), Lists.newArrayList(EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getCode()));
        //预计总收益
        Integer projectedRevenue = etcOrderChargeMapper.obtainWithdrawalAmount(req.getMerchantId(),
                Arrays.stream(EtcConstantsEnum.EtcOrderStatusEnum.values())
                .filter(f->EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.getCode()!=f.getCode())
                .map(EtcConstantsEnum.EtcOrderStatusEnum::getCode).collect(Collectors.toList()));

        EtcOrderChargeExample example = new EtcOrderChargeExample();
        example.createCriteria()
                .andOrderStatusNotIn(Collections.singletonList(EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.getCode()))
                .andMerchantIdEqualTo(req.getMerchantId());
        long orderTotal = etcOrderChargeMapper.countByExample(example);

        EtcDeviceExample deviceExample = new EtcDeviceExample();
        deviceExample.createCriteria()
                .andMerchantIdEqualTo(req.getMerchantId());
        long installedDevicesNumber = etcDeviceMapper.countByExample(deviceExample);
        //查询商家所有已支付 订单
        List<MallServiceOrderInfoDTO> serviceOrderList = mallServiceOrderInfoService.availableMallOrderList(req.getMerchantId(), MallOrderItemEnum.ETC_SERVICE_ITEM.getItemType().byteValue());

        // 押金和 订单金额需要计算了
        int purchasedDeviceCount = serviceOrderList.stream().mapToInt(MallServiceOrderInfoDTO::getRemainingCount).sum();
        OptionalInt duration = serviceOrderList.stream().mapToInt(MallServiceOrderInfoDTO::getDuration).max();

        int orderDeposit = serviceOrderList.stream().mapToInt(MallServiceOrderInfoDTO::getOrderDeposit).sum();
        int orderPrice = serviceOrderList.stream().mapToInt(MallServiceOrderInfoDTO::getOrderPrice).sum();

        EtcOrderChargeResp build = EtcOrderChargeResp
                .builder()
                .withdrawalAmount(actualIncome)
                .actualIncome(actualIncome)
                .projectedRevenue(projectedRevenue)
                .orderTotal(orderTotal)
                .installedDevicesNumber(installedDevicesNumber)
                .purchasedDeviceCount(purchasedDeviceCount)
                .itemPrice(orderPrice)
                .deposit(orderDeposit)
                .duration(duration.orElse(0))
                .build();
        return ResultUtil.successResult(build);
    }
    @Resource
    private IRedisService redisService;

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public Result<Boolean> withdrawal(EtcOrderChargeReq req) {
        String lockLey = "EtcDeviceServiceImpl:withdrawal:merchantId:"+req.getMerchantId();
        try {
            long check = redisService.setnx(lockLey, RedisConstant.RedisExpireTime.MINUTE_1);
            if (check > 1) {
                throw new BizException("请勿频繁操作");
            }
            ArrayList<Byte> orderStatus = Lists.newArrayList(EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getCode());
            //查询可提现订单
            EtcOrderChargeExample example = new EtcOrderChargeExample();
            example.createCriteria()
                    .andMerchantIdEqualTo(req.getMerchantId())
    //                .andStoreIdEqualTo(req.getStoreId())
                    .andOrderStatusIn(orderStatus)
                    .andOrderLockEqualTo((byte)0);
            List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
            List<Long> orderIdList = etcOrderCharges.stream().map(EtcOrderCharge::getId)
                    .collect(Collectors.toList());

            EtcOrderChargeExample countExample = new EtcOrderChargeExample();
            countExample.createCriteria()
                    .andMerchantIdEqualTo(req.getMerchantId());

            long orderTotal = etcOrderChargeMapper.countByExample(countExample);

            Integer withdrawalAmount = etcOrderCharges.stream().map(EtcOrderCharge::getMerchantProfit)
                    .mapToInt(Long::intValue)
                    .sum();

            //校验可提现金额
            if(orderTotal<1000){
                throw new BizException("可提现订单量1000才可以 提现");
            }

            // 生成提现 明细单
            long l = System.currentTimeMillis();
            WithdrawalRecordInfo withdrawalRecordInfo = new WithdrawalRecordInfo();
            withdrawalRecordInfo.setMerchantId(req.getMerchantId());
            withdrawalRecordInfo.setWithdrawalMethod(req.getWithdrawalMethod().byteValue());
            withdrawalRecordInfo.setWithdrawalAmount(withdrawalAmount.longValue());
            withdrawalRecordInfo.setReceivingAccount(req.getMerchantId().toString());
            withdrawalRecordInfo.setStatus(EtcConstantsEnum.WithdrawalStatusEnum.submitted.getCode());
            withdrawalRecordInfo.setCreateTime(l);
            withdrawalRecordInfo.setOpTime(l);
            withdrawalRecordInfo.setDeleted(YesOrNoEnum.NO.getValue());
            withdrawalRecordInfo.setLastVer(1);
            withdrawalRecordInfo.setOpUserId(req.getOpUserId());
            withdrawalRecordInfoMapper.insert(withdrawalRecordInfo);

            // 锁住 提现订单 防止二次提现
            //进行锁单
            EtcOrderCharge updateEtcOrderCharge = new EtcOrderCharge();
            updateEtcOrderCharge.setOrderLock((byte)2);
            updateEtcOrderCharge.setWithdrawalId(withdrawalRecordInfo.getId());
            EtcOrderChargeExample updateExample = new EtcOrderChargeExample();
            updateExample.createCriteria()
                    .andMerchantIdEqualTo(req.getMerchantId())
    //                .andStoreIdEqualTo(req.getStoreId())
                    .andIdIn(orderIdList);
            etcOrderChargeMapper.updateByExampleSelective(updateEtcOrderCharge,updateExample);

            //查询 采购商城 订单号
            //发送 邮件
            RefundEmailParam refundEmailParam = new RefundEmailParam();
            refundEmailParam.setMerchantId(req.getMerchantId());
            refundEmailParam.setWithdrawalMethod(EtcConstantsEnum.WithdrawalMethodEnum.forByCode(req.getWithdrawalMethod().byteValue()).getDesc());
            refundEmailParam.setType(1);
            refundEmailParam.setRefundAmount(CurrencyUtils.saas2Ctrip(withdrawalAmount,2).stripTrailingZeros().toPlainString());
            eventPublisher.publishEvent(refundEmailParam);
        } catch (BizException e) {
            throw new RuntimeException(e);
        }finally {
            redisService.remove(lockLey);
        }

        return ResultUtil.successResult(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> saveDevice(EtcDeviceVo vo) {
        EtcDeviceExample example = new EtcDeviceExample();
        EtcDeviceExample.Criteria criteria = example.createCriteria();
        criteria
                .andSampleIsEqualTo(YesOrNoEnum.NO.getValue())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andMerchantIdEqualTo(vo.getMerchantId())
                .andLicenseEqualTo(vo.getLicense());

        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(example);
        if(!etcDevices.isEmpty()){
            EtcDevice etcDevice = etcDevices.get(0);
            if(vo.getPlateColor()!=null){
                etcDevice.setPlateColor(EtcConstantsEnum.PlateColorEnum.forByDesc(vo.getPlateColor()).getCode());
            }
            etcDevice.setStoreId(vo.getStoreId());
            etcDevice.setVehicleModelId(vo.getVehicleModelId());
            etcDevice.setVehicleId(vo.getVehicleId());
            etcDevice.setAxles(vo.getAxles());
            etcDevice.setLength(vo.getLength());
            etcDevice.setWidth(vo.getWidth());
            etcDevice.setHeight(vo.getHeight());
            etcDevice.setTotalWeight(vo.getTotalWeight());
            etcDevice.setGrossWass(vo.getGrossWass());
            etcDevice.setRegisterDate(vo.getRegisterDate());
            etcDevice.setGrantDate(vo.getGrantDate());
            etcDevice.setOwnerName(vo.getOwnerName());
            etcDevice.setHardNinkStatus(ObjectUtils.isEmpty(vo.getHardLinkStatus())?etcDevice.getHardNinkStatus():EtcConstantsEnum.HardNinkStatusEnum.forByDesc(vo.getHardLinkStatus()).getCode());
            etcDevice.setOnline(ObjectUtils.isEmpty(vo.getOnline())?etcDevice.getOnline():EtcConstantsEnum.OnlineEnum.forByDesc(vo.getOnline()).getCode());
            etcDevice.setActivateStatus(ObjectUtils.isEmpty(vo.getActivateStatus())?etcDevice.getActivateStatus():EtcConstantsEnum.ActivateStatusEnum.forByDesc(vo.getActivateStatus()).getCode());
            etcDevice.setAvailabilityStatus(ObjectUtils.isEmpty(vo.getAvailabilityStatus())?etcDevice.getAvailabilityStatus():EtcConstantsEnum.AvailabilityStatusEnum.forByDesc(vo.getAvailabilityStatus()).getCode());
            etcDevice.setService(ObjectUtils.isEmpty(vo.getService())?etcDevice.getService():EtcConstantsEnum.ServiceEnum.forByDesc(vo.getService()).getCode());
            etcDevice.setEtcNo(vo.getEtcNo());
            etcDevice.setIcNo(vo.getIcNo());

            long l = System.currentTimeMillis();
            etcDevice.setOpTime(l);
            etcDevice.setOpUserId(1L);
            etcDevice.setLastVer(etcDevice.getLastVer()+1);

            etcDeviceMapper.updateByPrimaryKeySelective(etcDevice);
            if(vo.getGis()!=null){
                String gis = String.format("point(%s %s)", vo.getGis().getLongitude().toString(),vo.getGis().getLatitude().toString());
                etcDeviceMapper.updateGis(etcDevice.getId(),gis);
            }
        }else {
            EtcDevice etcDevice = convertToEtcDevice(vo,YesOrNoEnum.NO.getValue());
            etcDeviceMapper.insertSelective(etcDevice);
            if(vo.getGis()!=null){
                String gis = String.format("point(%s %s)", vo.getGis().getLongitude().toString(),vo.getGis().getLatitude().toString());
                etcDeviceMapper.updateGis(etcDevice.getId(),gis);
            }
        }

        return ResultUtil.successResult(Boolean.TRUE);
    }


    @Override
    public Result<Boolean> saveEtcOrderTraffic(EtcOrderTollFeeVo vo) {
        EtcOrderTollFeeExample example = new EtcOrderTollFeeExample();
        example.createCriteria()
                .andEtcOrderEqualTo(vo.getEtcOrder())
                .andTripIdEqualTo(vo.getTripId())
        ;
        List<EtcOrderTollFee> etcOrderTollFees = etcOrderTollFeeMapper.selectByExample(example);

        if(!etcOrderTollFees.isEmpty()){
            long l = System.currentTimeMillis();
            EtcOrderTollFee db = etcOrderTollFees.get(0);
            db.setStartTime(vo.getStartTime());
            db.setEndTime(vo.getEndTime());
            db.setStartStationName(vo.getStartStationName());
            db.setEndStationName(vo.getEndStationName());
            String subType = vo.getSubType();
            if (StringUtils.isNotBlank(subType)) {
                db.setSubType(EtcConstantsEnum.EtcSubTypeEnum.forByEnumeration(subType).getCode());
            }
            String subScene = vo.getSubScene();
            if (StringUtils.isNotBlank(subScene)) {
                db.setSubScene(EtcConstantsEnum.EtcSubSceneEnum.forByEnumeration(subScene).getCode());
            }
            db.setAmt(vo.getAmt());
            db.setOpTime(l);
            etcOrderTollFeeMapper.updateByPrimaryKeySelective(db);

        }else {
            EtcOrderTollFee db = convertToEtcOrderTollFee(vo);
            etcOrderTollFeeMapper.insertSelective(db);
        }

        return ResultUtil.successResult(Boolean.TRUE);
    }

    @Override
    public void saveOrderNotify(EtcOrderChargeVo vo) {
        //

        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(vo.getMerchantId())
                .andEtcOrderEqualTo((vo.getEtcOrder()))
        ;
        if (vo.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(vo.getVehicleModelId());
        }
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        if(!etcOrderCharges.isEmpty()){
            EtcOrderCharge etcOrderCharge = etcOrderCharges.get(0);
            etcOrderCharge.setOrderId(vo.getOrderId());
            etcOrderCharge.setAmt(vo.getAmt());
            etcOrderCharge.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByEnumeration(vo.getOrderStatus()).getCode());
            //todo 租车费 和 商家分润
            if(vo.getAmt()!=0){
                long merchantProfit = BigDecimal.valueOf(vo.getAmt())
                        .multiply(BigDecimal.valueOf(0.94))
                        .multiply(BigDecimal.valueOf(0.35))
                        .setScale(0, BigDecimal.ROUND_UP).longValue();
                etcOrderCharge.setMerchantProfit(merchantProfit);
            }
            if(vo.getOrderStartTime()!=null){
                etcOrderCharge.setOrderStartTime(new Date(vo.getOrderStartTime()));
            }
            if(vo.getOrderEndTime()!=null){
                etcOrderCharge.setOrderEndTime(new Date(vo.getOrderEndTime()));
            }
            if(vo.getRealStartTime()!=null){
                etcOrderCharge.setRealStartTime(new Date(vo.getRealStartTime()));
            }
            if(vo.getRealEndTime()!=null){
                etcOrderCharge.setRealEndTime(new Date(vo.getRealEndTime()));
            }
            etcOrderCharge.setTenancyFee(vo.getAmt());
            etcOrderCharge.setLastVer(etcOrderCharge.getLastVer()+1);
            etcOrderCharge.setOpTime(System.currentTimeMillis());
            //更新
            etcOrderChargeMapper.updateByPrimaryKeySelective(etcOrderCharge);
        }else {
            vo.setSource(vo.getSource());
            vo.setOpUserId(1L);
            vo.setLastVer(1);
            vo.setOpTime(System.currentTimeMillis());
            vo.setCreateTime(System.currentTimeMillis());
            if(vo.getAmt()!=0){
                long merchantProfit = BigDecimal.valueOf(vo.getAmt())
                        .multiply(BigDecimal.valueOf(0.94))
                        .multiply(BigDecimal.valueOf(0.35))
                        .setScale(0, BigDecimal.ROUND_UP).longValue();
                vo.setMerchantProfit(merchantProfit);
            }
            EtcOrderCharge etcOrderCharge = convertToEtcOrderCharge(vo);
            etcOrderChargeMapper.insertSelective(etcOrderCharge);
        }
    }

    @Override
    public void saveOrderNotifySaasEtc(EtcOrderChargeVo vo) {

        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(vo.getMerchantId())
                .andEtcOrderEqualTo((vo.getEtcOrder()));
        if (vo.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(vo.getVehicleModelId());
        }
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        if(!etcOrderCharges.isEmpty()){
            EtcOrderCharge etcOrderCharge = etcOrderCharges.get(0);
            etcOrderCharge.setOrderId(vo.getOrderId());
            etcOrderCharge.setAmt(vo.getAmt());
            etcOrderCharge.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByEnumeration(vo.getOrderStatus()).getCode());
            //todo 租车费 和 商家分润
            if(vo.getAmt()!=0){
                long merchantProfit = BigDecimal.valueOf(vo.getAmt())
                        .multiply(BigDecimal.valueOf(0.94))
                        .multiply(BigDecimal.valueOf(0.35))
                        .setScale(0, BigDecimal.ROUND_UP).longValue();
                etcOrderCharge.setMerchantProfit(merchantProfit);
                etcOrderCharge.setTenancyFee(vo.getAmt());
            }
            if(vo.getOrderStartTime()!=null){
                etcOrderCharge.setOrderStartTime(new Date(vo.getOrderStartTime()));
            }
            if(vo.getOrderEndTime()!=null){
                etcOrderCharge.setOrderEndTime(new Date(vo.getOrderEndTime()));
            }
            if(vo.getRealStartTime()!=null){
                etcOrderCharge.setRealStartTime(new Date(vo.getRealStartTime()));
            }
            if(vo.getRealEndTime()!=null){
                etcOrderCharge.setRealEndTime(new Date(vo.getRealEndTime()));
            }
            etcOrderCharge.setTenancyFee(vo.getAmt());
            etcOrderCharge.setLastVer(etcOrderCharge.getLastVer()+1);
            etcOrderCharge.setOpTime(System.currentTimeMillis());
            //更新
            etcOrderChargeMapper.updateByPrimaryKeySelective(etcOrderCharge);
        }else {
            vo.setSource(vo.getSource());
            vo.setOpUserId(1L);
            vo.setLastVer(1);
            vo.setOpTime(System.currentTimeMillis());
            vo.setCreateTime(System.currentTimeMillis());
            if(vo.getAmt()!=0){
                long merchantProfit = BigDecimal.valueOf(vo.getAmt())
                        .multiply(BigDecimal.valueOf(0.94))
                        .multiply(BigDecimal.valueOf(0.35))
                        .setScale(0, BigDecimal.ROUND_UP).longValue();
                vo.setMerchantProfit(merchantProfit);
                vo.setTenancyFee(vo.getAmt());
            }
            EtcOrderCharge etcOrderCharge = convertToEtcOrderCharge(vo);
            etcOrderChargeMapper.insertSelective(etcOrderCharge);
        }
    }

    @Override
    public Result<PageListVo<WithdrawalRecordInfoVo>> obtainWithdrawal(EtcOrderChargeReq req) {
        WithdrawalRecordInfoExample example = new WithdrawalRecordInfoExample();
        example.createCriteria()
                .andMerchantIdEqualTo(req.getMerchantId())

        ;
        example.setOrderByClause("create_time desc");
        Page<WithdrawalRecordInfo> page = PageHelper.startPage(req.getPageIndex(), req.getPageSize());
        List<WithdrawalRecordInfo> dbList = withdrawalRecordInfoMapper.selectByExample(example);

        List<Long> opUserIds = dbList.stream().map(WithdrawalRecordInfo::getOpUserId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, SysUserBoxVo> userIdInfoMapById = Maps.newHashMap();
        List <Long> roleIdList = Lists.newArrayList();
        Result<List<SysUserBoxVo>> userIdInfoListReq = null;
        if(!opUserIds.isEmpty()){
            userIdInfoListReq = userService.findByIds(opUserIds);
        }

        if (userIdInfoListReq!=null&&userIdInfoListReq.isSuccess() && userIdInfoListReq.getModel() != null) {
            userIdInfoMapById = userIdInfoListReq.getModel().stream().collect(Collectors.toMap(SysUserBoxVo::getId, Function.identity()));
            roleIdList = userIdInfoListReq.getModel().stream().map(SysUserBoxVo::getRoleId).distinct().collect(Collectors.toList());
        }
        Map<Long, String> namesByIds = sysRoleService.findNamesByIds(roleIdList);
        Map<Long, SysUserBoxVo> finalUserIdInfoMapById = userIdInfoMapById;
        List<WithdrawalRecordInfoVo> voList = dbList.stream().map(db -> {
            WithdrawalRecordInfoVo vo = new WithdrawalRecordInfoVo();
            BeanUtils.copyProperties(db,vo);
            vo.setOpUserName(Optional.ofNullable(finalUserIdInfoMapById.get(db.getOpUserId())).map(SysUserBoxVo::getName).orElse(""));
            if(namesByIds!=null){
                vo.setOpRoleName(Optional.ofNullable(namesByIds.get(Optional.ofNullable(finalUserIdInfoMapById.get(db.getOpUserId()))
                        .map(SysUserBoxVo::getRoleId).orElse(9999999L))).orElse(""));
            }
            return vo;
        }).collect(Collectors.toList());

        return ResultUtil.successResult(PageListVo.buildPageList(page.getTotal(),voList));
    }

    @Override
    public Result<List<String>> searchEtcOrderList(EtcOrderChargeReq req) {
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(req.getMerchantId())
                .andEtcOrderLike("%"+req.getEtcOrder()+"%");
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        List<String> voList = etcOrderCharges.stream().map(EtcOrderCharge::getEtcOrder).distinct().collect(Collectors.toList());
        return ResultUtil.successResult(voList);
    }

    @Override
    public EtcOrderChargeVo orderQuery(Long merchantId,Long vehicle, String etcOrderId) {
        //匹配saas订单
        // 返回渠道  和 预计还车时间
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria.andMerchantIdEqualTo(merchantId)
                .andVehicleIdEqualTo(vehicle)
                .andEtcOrderEqualTo(etcOrderId);

        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        Optional<EtcOrderChargeVo> first = etcOrderCharges.stream().map(db->{
            EtcOrderChargeVo vo = new EtcOrderChargeVo();
            BeanUtils.copyProperties(db,vo);
            return vo;
        }).findFirst();

        return first.orElse(new EtcOrderChargeVo());
    }

    @Override
    public List<EtcOrderChargeVo> exportWithdrawalIncome(Long merchantId,  Long id) {
        WithdrawalRecordInfo withdrawalRecordInfo = withdrawalRecordInfoMapper.selectByPrimaryKey(id);

        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria
                .andMerchantIdEqualTo(merchantId)
                .andWithdrawalIdEqualTo(withdrawalRecordInfo.getId());
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);

        List<Long> storeIdList = etcOrderCharges.stream().map(EtcOrderCharge::getStoreId).distinct().collect(Collectors.toList());
        List<Long> vehicleModelIdList = etcOrderCharges.stream().map(EtcOrderCharge::getVehicleModelId).distinct().collect(Collectors.toList());
        List<Long> vehicleIdList = etcOrderCharges.stream().map(EtcOrderCharge::getVehicleId).distinct().collect(Collectors.toList());
        Result<List<StoreSimpleVo>> storeResult = storeInfoService.storeSampleForUnion(storeIdList);

        Map<Long, String> storeNameById = Maps.newHashMap();
        Map<Long, BaseVehicleModelVO> vehicleModelByIdMap = Maps.newHashMap();
        Map<Long, VehicleInfoVO> vehiclelByIdMap = Maps.newHashMap();

        if(storeResult.isSuccess()&&storeResult.getModel()!=null){
            storeNameById = storeResult.getModel().stream().collect(Collectors.toMap(StoreSimpleVo::getStoreId, StoreSimpleVo::getStoreName));
        }
        Result<Map<Long, BaseVehicleModelVO>> mapResult = vehicleModelService.obtainVehicleModeInfo(merchantId, vehicleModelIdList);
        if(mapResult.isSuccess()&&mapResult.getModel()!=null){
            vehicleModelByIdMap = mapResult.getModel();
        }
        Result<Map<Long,VehicleInfoVO>> vehicleResult = vehicleInfoService.obtainVehicleInfo(merchantId,vehicleIdList);
        if(vehicleResult.isSuccess()&&vehicleResult.getModel()!=null){
            vehiclelByIdMap = vehicleResult.getModel();
        }

        Map<Long, String> finalStoreNameById = storeNameById;
        Map<Long, BaseVehicleModelVO> finalVehicleModelByIdMap = vehicleModelByIdMap;
        Map<Long, VehicleInfoVO> finalVehiclelByIdMap = vehiclelByIdMap;
        List<EtcOrderChargeVo> voList = etcOrderCharges.stream().map(db -> {
            EtcOrderChargeVo vo = new EtcOrderChargeVo();
            BeanUtils.copyProperties(db, vo);
            //车型 门店 , 车辆
            vo.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus()).getDesc());
            vo.setStoreName(finalStoreNameById.getOrDefault(vo.getStoreId(),""));
            vo.setVehicleSeryName(Optional.ofNullable(finalVehicleModelByIdMap.get(vo.getVehicleModelId()))
                    .map(BaseVehicleModelVO::getVehicleSeryName).orElse(""));
            vo.setVehicleName(Optional.ofNullable(finalVehiclelByIdMap.get(vo.getVehicleId()))
                    .map(VehicleInfoVO::getLicense).orElse(""));
            if(ObjectUtils.equals(EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getCode(),db.getOrderStatus())){
                vo.setEndPaymentTime(db.getOpTime());
            }

            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    //获取需要释放的库存
    private List<OrderCostDetailsVo> toReleasedEtcOrderDetails(Long merchantId, Long saasOrderId){
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria
                .andMerchantIdEqualTo(merchantId)
                .andOrderIdEqualTo(saasOrderId.toString());
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        List<String> etcOrderList = etcOrderCharges.stream().map(EtcOrderCharge::getEtcOrder).distinct().collect(Collectors.toList());

        //过滤 调关联多个订单号的 etc单号
        List<Byte> orderStatus = Arrays.stream(EtcConstantsEnum.EtcOrderStatusEnum.values())
                .filter(f->!EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.equals(f))
                .filter(f->!EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.equals(f))
                .map(EtcConstantsEnum.EtcOrderStatusEnum::getCode)
                .collect(Collectors.toList());

        EtcOrderChargeExample exampleByEtcOrder = new EtcOrderChargeExample();
        exampleByEtcOrder.createCriteria()
                .andOrderStatusIn(orderStatus)
                .andEtcOrderIn(etcOrderList);
        List<EtcOrderCharge> etcOrderByEtcOrder = etcOrderChargeMapper.selectByExample(exampleByEtcOrder);
        Map<String, List<EtcOrderCharge>> etcOrderMapByEtcOrder = etcOrderByEtcOrder.stream().collect(Collectors.groupingBy(EtcOrderCharge::getEtcOrder));

        List<OrderCostDetailsVo> voList = etcOrderCharges.stream()
                .filter(db->etcOrderMapByEtcOrder.get(db.getEtcOrder())!=null&&etcOrderMapByEtcOrder.get(db.getEtcOrder()).size()==1)
                .map(db -> {
            OrderCostDetailsVo vo = new OrderCostDetailsVo();
            BeanUtils.copyProperties(db, vo);
            vo.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus()).getDesc());
            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    @Override
    public List<OrderCostDetailsVo> etcOrderDetails(Long merchantId, Long saasOrderId) {
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria
                .andMerchantIdEqualTo(merchantId)
                .andOrderIdEqualTo(saasOrderId.toString());
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        List<String> etcOrderList = etcOrderCharges.stream().map(EtcOrderCharge::getEtcOrder).distinct().collect(Collectors.toList());
        //查询通行费
        EtcOrderTollFeeExample etcOrderTollFeeExample = new EtcOrderTollFeeExample();
        example.createCriteria()
                .andEtcOrderIn(etcOrderList);
        List<EtcOrderTollFee> etcOrderTollFees = etcOrderTollFeeMapper.selectByExample(etcOrderTollFeeExample);
        Map<String, List<EtcOrderTollFee>> orderTollFeesByEtcOrder = etcOrderTollFees.stream().collect(Collectors.groupingBy(EtcOrderTollFee::getEtcOrder));

        List<OrderCostDetailsVo> voList = etcOrderCharges.stream().map(db -> {
            etcOrderList.add(db.getEtcOrder());
            OrderCostDetailsVo vo = new OrderCostDetailsVo();
            BeanUtils.copyProperties(db, vo);
            vo.setOrderStartTime(Optional.ofNullable(db.getOrderStartTime()).map(Date::getTime).orElse(null));
            vo.setOrderEndTime(Optional.ofNullable(db.getOrderEndTime()).map(Date::getTime).orElse(null));
            vo.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus()).getDesc());
            List<EtcOrderTollFee> etcOrderTollFeesList = orderTollFeesByEtcOrder.get(db.getEtcOrder());
            if(etcOrderTollFeesList!=null){
                List<EtcOrderTollFeeVo> expresswayTolls = etcOrderTollFeesList.stream().map(m -> {
                    EtcOrderTollFeeVo feeVo = new EtcOrderTollFeeVo();
                    BeanUtils.copyProperties(m, feeVo);
                    return feeVo;
                }).collect(Collectors.toList());
                vo.setEtcOrderTollFeesList(expresswayTolls);
            }
            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    @Override
    public boolean cancelEtcOrder(Long merchantId, Long id) {
        log.info("取消etc订单,merchantId{},id{}", merchantId, id);
        try {
            if (Objects.isNull(id) || Objects.isNull(merchantId)) {
                log.warn("缺少必要参数");
                throw new BizException("缺少必要参数");
            }
            EtcOrderCharge etcOrderCharge = etcOrderChargeMapper.selectByPrimaryKey(id);
            Long vehicleId = etcOrderCharge.getVehicleId();
            String outOrderNumber = etcOrderCharge.getEtcOrder();
            CancellationOrder orderCreate = new CancellationOrder();
            ApiResultResp<List<RelationDTO>> thirdIdBySaasId =
                    getThirdId(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue(),
                            Collections.singletonList(vehicleId), merchantId,
                            EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
            if (Objects.isNull(thirdIdBySaasId) || !thirdIdBySaasId.success()) {
                log.warn("找不到对应的映射关系：saasID：{}", vehicleId);
                throw new BizException("找不到对应的映射关系：saasID："+ vehicleId);
            }
            thirdIdBySaasId.getData().stream().findFirst().map(RelationDTO::getThirdId)
                    .orElseThrow(() -> new RuntimeException("找不到对应的映射关系"));
            orderCreate.setOrderNumber(outOrderNumber);
            ApiResultResp<EtcOrderRespDetail> result = etcApiClient.etcOrderComplete(orderCreate);
            log.info("第三方返回结果{}", JSONObject.toJSONString(result));
            String resultCode =
                    Optional.ofNullable(result).map(ApiResultResp::getData).map(EtcOrderRespDetail::getBizCode)
                            .orElse(null);
            if (!"SUCCESS".equals(resultCode)) {
                log.error("同步失败,请求参数：{}",
                        Arrays.asList(String.valueOf(vehicleId), outOrderNumber, String.valueOf(merchantId)));
                throw new BizException("订单取消失败");
            }
        } catch (Exception e) {
            log.error("pushOrderCreate", e);
            throw new BizException(e.getMessage());
        }
        return true;
    }

    @Override
    public void pushStore(Long merchantId) {
        log.info("同步门店,merchantId{}", merchantId);
        try {
            if (Objects.isNull(merchantId)) {
                log.warn("缺少必要参数");
                return;
            }
            Result<MerchantInfoVo> resultResp = merchantInfoService.findById(merchantId);
            if (!resultResp.isSuccess()) {
                return;
            }
            MerchantInfoVo merchantInfo = resultResp.getModel();
            if (merchantInfo == null) {
                return;
            }
            String name = merchantInfo.getName();
            String mobile = merchantInfo.getPhone();
            String regSource = merchantInfo.getRegSource();
            if (StringUtils.isBlank(mobile) && isNumeric(regSource)) {
                mobile = regSource;
            }
            if (StringUtils.isAnyBlank(name, mobile)) {
                log.warn("缺少必要参数");
                return;
            }
            String linkName = merchantInfo.getLinkName();
            OrganCreate request = new OrganCreate();
            request.setName(name);
            request.setBrandName(merchantInfo.getNameShort());
            request.setMobile(mobile);
            request.setContactsName(linkName);
            request.setEnterpriseAddress(merchantInfo.getAddress());
            request.setEnterpriseName(name);
            request.setEnterpriseCode(merchantInfo.getUscc());
            ApiResultResp<List<RelationDTO>> thirdStoreId =
                    getThirdId(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue(),
                            Collections.singletonList(merchantId), merchantId,
                            EtcConstantsEnum.ThirdRelationTypeEnum.ETC_MERCHANT.getType());
            ApiResultResp<EtcRespDetail> result;
            if (Objects.isNull(thirdStoreId) || !thirdStoreId.success()) {
                log.warn("获取映射关系失败");
                return;
            }
            if (CollectionUtils.isEmpty(thirdStoreId.getData())) {
                request.setCode(merchantId.toString());
                result = etcApiClient.organCreate(request);
            } else {
                request.setOrganCode(thirdStoreId.getData().get(0).getThirdId());
                result = etcApiClient.updateOrgan(request);
            }
            log.info("organCreate或updateOrgan第三方返回{}", result);
            String resultCode =
                    Optional.ofNullable(result).map(ApiResultResp::getData).map(EtcRespDetail::getBizCode).orElse(null);
            if (!"SUCCESS".equals(resultCode)) {
                log.error("同步失败,请求参数：{}", request);
                return;
            }
            if (!CollectionUtils.isEmpty(thirdStoreId.getData())) {
                return;
            }
            //保存映射关系
            String thirdId = result.getData().getOrganCode();
            saveStoreMapping(1L, merchantId, thirdId, merchantId);
        } catch (Exception e) {
            log.error("开通etc商家异常：merchantId={}",merchantId, e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public void pushVehicleUnmount(Long vehicleId, Boolean upFlag, Long merchantId) {
        log.info("etc车辆上下架,vehicleId{},upFlag{},merchantId{}", vehicleId, upFlag, merchantId);
        if (Objects.isNull(vehicleId) || Objects.isNull(merchantId)) {
            log.warn("缺少必要参数");
            return;
        }
        asyncPromiseExecutor.execute(() -> {
            VehicleUnmountReq vehicleUnmountReq = new VehicleUnmountReq();
            ApiResultResp<List<RelationDTO>> thirdIdBySaasId =
                getThirdId(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue(),
                    Collections.singletonList(vehicleId), merchantId,
                    EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
            if (Objects.isNull(thirdIdBySaasId) || !thirdIdBySaasId.success()) {
                log.warn("找不到对应的映射关系：saasID：{}", vehicleId);
                return;
            }
            vehicleUnmountReq.setVehId(thirdIdBySaasId.getData().stream().findFirst().map(RelationDTO::getThirdId)
                .orElseThrow(() -> new RuntimeException("找不到对应的映射关系")));
            vehicleUnmountReq.setFlag(upFlag);
            ApiResultResp<EtcRespDetail> result = etcApiClient.vehicleOpcontrol(vehicleUnmountReq);
            log.info("vehicleOpcontrol第三方返回{}", result);
            String resultCode =
                Optional.ofNullable(result).map(ApiResultResp::getData).map(EtcRespDetail::getBizCode).orElse(null);
            if (!"SUCCESS".equals(resultCode)) {
                log.error("同步失败,请求参数：{}", vehicleUnmountReq);
            }
        });
    }

    @Override
    public EtcOrderChargeVo obtainOrderChargeInfo(Long id) {
        EtcOrderCharge db = etcOrderChargeMapper.selectByPrimaryKey(id);
        if(db==null){
            return null;
        }
        EtcOrderChargeVo vo = new EtcOrderChargeVo();
        BeanUtils.copyProperties(db,vo);
        vo.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByCode(db.getOrderStatus()).getDesc());
        return vo;
    }

    @Override
    public void asynchronousCancelEtcOrderTask(Long merchantId, Long saasOrder, Long vehicleId) {
        CompletableFuture.runAsync(() -> {
            //校验 车辆 标识
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(vehicleId, false);
            if(!baseById.isSuccess()||baseById.getModel()==null){
                log.warn("车辆获取失败,vehicleId{}",vehicleId);
                return;
            }
            if(baseById.getModel().getTiIncludeEtc()!=1){
                log.warn("车辆不包含etc,vehicleId{}",vehicleId);
                return;
            }
            //执行推送任务
            List<OrderCostDetailsVo> etcOrderChargeVos = this.toReleasedEtcOrderDetails(merchantId, saasOrder);
            if (etcOrderChargeVos != null) {
                etcOrderChargeVos.stream()
                        .filter(f->vehicleId.equals(f.getVehicleId()))
                        .filter(f -> !EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.getDesc().equals(f.getOrderStatus()))
                        .filter(f -> !EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getDesc().equals(f.getOrderStatus()))
//                        .map(OrderCostDetailsVo::getId)
                        .forEach(vo -> {
                            Boolean aBoolean = this.cancelEtcOrder(merchantId, vo.getId());
                            this.aSynPushETCInfoToTrip(vo.getEtcOrder(),vo.getVehicleId());
                        });
            }
        },asyncPromiseExecutor).exceptionally(ex -> {
            log.error("取消etc订单异常：merchantId={},saasOrder={},vehicleId={}",merchantId,saasOrder,vehicleId, ex);
            return null;
        });
    }

    @Override
    public Map<Byte,String> obtainPaymentMethod(Long merchantId) {
        //查询商家所有已支付 订单
        List<MallServiceOrderInfoDTO> serviceOrderList = mallServiceOrderInfoService.availableMallOrderList(merchantId, MallOrderItemEnum.ETC_SERVICE_ITEM.getItemType().byteValue());
        List<String> orderNoList = serviceOrderList.stream().map(MallServiceOrderInfoDTO::getOrderNo).distinct().collect(Collectors.toList());
        //获取已支付订单的 支付信息
        List<MallServiceBillInfo> mallServiceBillInfos = Lists.newArrayList();
        if(!orderNoList.isEmpty()){
            MallServiceBillInfoExample mallServiceBillInfoExample = new MallServiceBillInfoExample();
            mallServiceBillInfoExample.createCriteria()
                    .andOrderNoIn(orderNoList);
             mallServiceBillInfos = mallServiceBillInfoMapper.selectByExample(mallServiceBillInfoExample);
        }
        return mallServiceBillInfos.stream().map(MallServiceBillInfo::getPayMethod).distinct()
                .map(EtcConstantsEnum.WithdrawalMethodEnum::forByCode)
                .collect(Collectors.toMap(EtcConstantsEnum.WithdrawalMethodEnum::getCode,EtcConstantsEnum.WithdrawalMethodEnum::getDesc));
    }

    @Override
    public EtcDeviceVo addSamples(EtcDeviceVo samplesVo) {
        //根据车牌检索样本数据    样本数据 车牌需要唯一
        EtcDeviceExample example = new EtcDeviceExample();
        EtcDeviceExample.Criteria criteria = example.createCriteria();
        criteria
                .andLicenseEqualTo(samplesVo.getLicense())
                .andSampleIsEqualTo(YesOrNoEnum.YES.getValue())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());

        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(example);
        if(!etcDevices.isEmpty()){
            long l = System.currentTimeMillis();
            for (int i = 0; i < etcDevices.size(); i++) {
                EtcDevice etcDevice = etcDevices.get(i);
                if(i>0){
                    //样本数据 一个车牌号只需要 一条   多余的 删除
                    etcDevice.setOpTime(l);
                    etcDevice.setOpUserId(1L);
                    etcDevice.setLastVer(etcDevice.getLastVer()+1);
                    etcDevice.setDeleted(YesOrNoEnum.YES.getValue());
                    etcDeviceMapper.updateByPrimaryKeySelective(etcDevice);
                    continue;
                }

                if(samplesVo.getPlateColor()!=null){
                    etcDevice.setPlateColor(EtcConstantsEnum.PlateColorEnum.forByDesc(samplesVo.getPlateColor()).getCode());
                }
                etcDevice.setStoreId(samplesVo.getStoreId());
                etcDevice.setVehicleModelId(samplesVo.getVehicleModelId());
                etcDevice.setAxles(samplesVo.getAxles());
                etcDevice.setLength(samplesVo.getLength());
                etcDevice.setWidth(samplesVo.getWidth());
                etcDevice.setHeight(samplesVo.getHeight());
                etcDevice.setTotalWeight(samplesVo.getTotalWeight());
                etcDevice.setGrossWass(samplesVo.getGrossWass());
                etcDevice.setRegisterDate(samplesVo.getRegisterDate());
                etcDevice.setGrantDate(samplesVo.getGrantDate());
                etcDevice.setOwnerName(samplesVo.getOwnerName());
                etcDevice.setHardNinkStatus(ObjectUtils.isEmpty(samplesVo.getHardLinkStatus())?etcDevice.getHardNinkStatus():EtcConstantsEnum.HardNinkStatusEnum.forByDesc(samplesVo.getHardLinkStatus()).getCode());
                etcDevice.setOnline(ObjectUtils.isEmpty(samplesVo.getOnline())?etcDevice.getOnline():EtcConstantsEnum.OnlineEnum.forByDesc(samplesVo.getOnline()).getCode());
                etcDevice.setActivateStatus(ObjectUtils.isEmpty(samplesVo.getActivateStatus())?etcDevice.getActivateStatus():EtcConstantsEnum.ActivateStatusEnum.forByDesc(samplesVo.getActivateStatus()).getCode());
                etcDevice.setAvailabilityStatus(ObjectUtils.isEmpty(samplesVo.getAvailabilityStatus())?etcDevice.getAvailabilityStatus():EtcConstantsEnum.AvailabilityStatusEnum.forByDesc(samplesVo.getAvailabilityStatus()).getCode());
                etcDevice.setService(ObjectUtils.isEmpty(samplesVo.getService())?etcDevice.getService():EtcConstantsEnum.ServiceEnum.forByDesc(samplesVo.getService()).getCode());
                etcDevice.setEtcNo(samplesVo.getEtcNo());
                etcDevice.setIcNo(samplesVo.getIcNo());


                etcDevice.setOpTime(l);
                etcDevice.setOpUserId(1L);
                etcDevice.setLastVer(etcDevice.getLastVer()+1);

                etcDeviceMapper.updateByPrimaryKeySelective(etcDevice);
                if(samplesVo.getGis()!=null){
                    String gis = String.format("point(%s %s)", samplesVo.getGis().getLongitude().toString(),samplesVo.getGis().getLatitude().toString());
                    etcDeviceMapper.updateGis(etcDevice.getId(),gis);
                }
            }

        }else {
            EtcDevice etcDevice = convertToEtcDevice(samplesVo,YesOrNoEnum.YES.getValue());
            etcDeviceMapper.insertSelective(etcDevice);
            if(samplesVo.getGis()!=null){
                String gis = String.format("point(%s %s)", samplesVo.getGis().getLongitude().toString(),samplesVo.getGis().getLatitude().toString());
                etcDeviceMapper.updateGis(etcDevice.getId(),gis);
            }
        }
        List<EtcDevice> dbList = etcDeviceMapper.selectByExample(example);
        List<EtcDeviceVo> voList = dbList.stream().map(db -> {
            EtcDeviceVo vo = new EtcDeviceVo();
            vo.setId(db.getId());
            vo.setMerchantId(db.getMerchantId());
            vo.setStoreId(db.getStoreId());
            vo.setVehicleModelId(db.getVehicleModelId());
            vo.setVehicleId(db.getVehicleId());
            vo.setLicense(db.getLicense());
            vo.setEtcNo(db.getEtcNo());
            vo.setIcNo(db.getIcNo());
            vo.setOnline(EtcConstantsEnum.OnlineEnum.forByCode(db.getOnline()).getDesc());
            vo.setHardLinkStatus(EtcConstantsEnum.HardNinkStatusEnum.forByCode(db.getHardNinkStatus()).getDesc());
            vo.setWorkStatus(EtcConstantsEnum.WorkStatusEnum.forByCode(db.getWorkStatus()).getDesc());
            vo.setService(EtcConstantsEnum.ServiceEnum.forByCode(db.getService()).getDesc());
            vo.setActivateStatus(EtcConstantsEnum.ActivateStatusEnum.forByCode(db.getActivateStatus()).getDesc());
            vo.setAvailabilityStatus(EtcConstantsEnum.AvailabilityStatusEnum.forByCode(db.getAvailabilityStatus()).getDesc());

            vo.setRemark(db.getRemark());
            if (db.getGis() != null) {
                Geometry geometry = null;
                try {
                    geometry = StoreInfoServiceImpl.getGeometryByBytes(db.getGis());
                    LongLatVo gis = new LongLatVo();
                    gis.setLatitude(geometry.getCentroid().getX());
                    gis.setLongitude(geometry.getCentroid().getY());
                    vo.setGis(gis);
                } catch (Exception e) {
                }
            }
            vo.setPlateColor(EtcConstantsEnum.PlateColorEnum.forByCode(db.getPlateColor()).getDesc());
            vo.setAxles(db.getAxles());
            vo.setLength(db.getLength());
            vo.setWidth(db.getWidth());
            vo.setHeight(db.getHeight());
            vo.setTotalWeight(db.getTotalWeight());
            vo.setGrossWass(db.getGrossWass());
            vo.setRegisterDate(db.getRegisterDate());
            vo.setGrantDate(db.getGrantDate());
            vo.setOwnerName(db.getOwnerName());
            return vo;
        }).collect(Collectors.toList());
        return voList.get(0);
    }

    @Override
    public Result<List<EtcDeviceVo>> retrieveSampleEquipment(EtcDeviceVo samplesVo) {
        if (StringUtils.isAnyBlank( samplesVo.getLicense())||samplesVo.getStoreId()==null) {
            throw new BizException("缺少必要参数");
        }
        //根据车牌检索样本数据    样本数据 车牌需要唯一
        EtcDeviceExample example = new EtcDeviceExample();
        EtcDeviceExample.Criteria criteria = example.createCriteria();
        criteria
                .andLicenseEqualTo(samplesVo.getLicense())
                .andSampleIsEqualTo(YesOrNoEnum.YES.getValue())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());

        List<EtcDevice> samplesList = etcDeviceMapper.selectByExample(example);

        //当前商家 是否已经存在设备
        EtcDeviceExample dbExample = new EtcDeviceExample();
        EtcDeviceExample.Criteria dbCriteria = dbExample.createCriteria();
        dbCriteria
                .andMerchantIdEqualTo(samplesVo.getMerchantId())
                .andLicenseEqualTo(samplesVo.getLicense())
                .andSampleIsEqualTo(YesOrNoEnum.NO.getValue())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());

        List<EtcDevice> dbList = etcDeviceMapper.selectByExample(dbExample);
        Set<String> dbLicenseSet = dbList.stream().map(EtcDevice::getLicense).collect(Collectors.toSet());

        List<EtcDeviceVo> samplesVoList = samplesList.stream()
                .filter(f->!dbLicenseSet.contains(f.getLicense()))
                .map(db -> {
            EtcDeviceVo vo = new EtcDeviceVo();
            vo.setId(db.getId());
            vo.setMerchantId(db.getMerchantId());
            vo.setStoreId(samplesVo.getStoreId());
            vo.setVehicleModelId(samplesVo.getVehicleModelId());
            vo.setVehicleId(samplesVo.getVehicleId());
            vo.setLicense(db.getLicense());
            vo.setEtcNo(db.getEtcNo());
            vo.setIcNo(db.getIcNo());
            vo.setOnline(EtcConstantsEnum.OnlineEnum.forByCode(db.getOnline()).getDesc());
            vo.setHardLinkStatus(EtcConstantsEnum.HardNinkStatusEnum.forByCode(db.getHardNinkStatus()).getDesc());
            vo.setWorkStatus(EtcConstantsEnum.WorkStatusEnum.forByCode(db.getWorkStatus()).getDesc());
            vo.setService(EtcConstantsEnum.ServiceEnum.forByCode(db.getService()).getDesc());
            vo.setActivateStatus(EtcConstantsEnum.ActivateStatusEnum.forByCode(db.getActivateStatus()).getDesc());
            vo.setAvailabilityStatus(EtcConstantsEnum.AvailabilityStatusEnum.forByCode(db.getAvailabilityStatus()).getDesc());

            vo.setRemark(db.getRemark());
            if (db.getGis() != null) {
                Geometry geometry = null;
                try {
                    geometry = StoreInfoServiceImpl.getGeometryByBytes(db.getGis());
                    LongLatVo gis = new LongLatVo();
                    gis.setLatitude(geometry.getCentroid().getX());
                    gis.setLongitude(geometry.getCentroid().getY());
                    vo.setGis(gis);
                } catch (Exception e) {
                }
            }
            vo.setPlateColor(EtcConstantsEnum.PlateColorEnum.forByCode(db.getPlateColor()).getDesc());
            vo.setAxles(db.getAxles());
            vo.setLength(db.getLength());
            vo.setWidth(db.getWidth());
            vo.setHeight(db.getHeight());
            vo.setTotalWeight(db.getTotalWeight());
            vo.setGrossWass(db.getGrossWass());
            vo.setRegisterDate(db.getRegisterDate());
            vo.setGrantDate(db.getGrantDate());
            vo.setOwnerName(db.getOwnerName());
            return vo;
        }).collect(Collectors.toList());

        return ResultUtil.successResult(samplesVoList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.VEHICLE)
    public Result<Boolean> addCopySample(EtcDeviceVo samplesVo,Long opUserId) {
        if (StringUtils.isAnyBlank( samplesVo.getLicense())
                ||samplesVo.getStoreId()==null
                ||samplesVo.getId()==null) {
            throw new BizException("缺少必要参数");
        }
        log.info("EtcDeviceServiceImpl addCopySample samplesVo={},opUserId={}", JSONUtil.toJsonStr(samplesVo),opUserId);
        String lockLey = "EtcDeviceServiceImpl:addCopySample:merchantId:"+samplesVo.getMerchantId();
        try {
            long check = redisService.setnx(lockLey, RedisConstant.RedisExpireTime.MINUTE_1);
            if (check > 1) {
                throw new BizException("请勿频繁操作");
            }
            EtcDevice etcDevice = etcDeviceMapper.selectByPrimaryKey(samplesVo.getId());
            if(etcDevice== null||!YesOrNoEnum.isYes(etcDevice.getSampleIs())){
                throw new BizException("设备不存在");
            }
            //根据车牌号 寻找当前
            List<VehicleInfoVO> vehicleInfoVOS = vehicleInfoService.selectVehicleInfoVOS(samplesVo.getLicense());
            Map<Long, VehicleInfoVO> voMap = vehicleInfoVOS.stream()
                    .filter(f -> YesOrNoEnum.isNo(f.getDeleted()))
                    .collect(Collectors.toMap(VehicleInfoVO::getMerchantId, Function.identity(), (k1, k2) -> k1));
            if(Objects.isNull(voMap.get(samplesVo.getMerchantId()))){
                throw new BizException("车辆不存在");
            }
            VehicleInfoVO vehicleInfoVO = voMap.get(samplesVo.getMerchantId());
            //当前商家 是否已经存在设备
            EtcDeviceExample dbExample = new EtcDeviceExample();
            EtcDeviceExample.Criteria dbCriteria = dbExample.createCriteria();
            dbCriteria
                    .andMerchantIdEqualTo(samplesVo.getMerchantId())
                    .andLicenseEqualTo(samplesVo.getLicense())
                    .andSampleIsEqualTo(YesOrNoEnum.NO.getValue())
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue());

            List<EtcDevice> dbList = etcDeviceMapper.selectByExample(dbExample);
            if(CollectionUtils.isNotEmpty(dbList)){
                throw new BizException("设备已经存在【"+dbList.get(0).getStoreId()+"】门店");
            }

            //获取 车辆牌照
            RelationParam licenseVO = RelationParam.builder()
                    .thirdIdList(Collections.singletonList(samplesVo.getLicense()))
                    .type(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE_LICENCE.getType())
                    .source(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue()).build();
            Result<List<RelationVO>> licenseResp = thirdIdRelationService.relation(licenseVO);
            if(!licenseResp.isSuccess()||CollectionUtils.isEmpty(licenseResp.getModel())){
                throw new BizException("未知车辆");
            }
            Long thirdId = licenseResp.getModel().get(0).getSaasId();
            RelationVO vo = new RelationVO();
            vo.setMerchantId(vehicleInfoVO.getMerchantId());
            vo.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_VEHICLE.getType());
            vo.setSource(EtcConstantsEnum.ThirdSourceEnum.ETC_MERCHANT.getType().longValue());
            vo.setSaasId(vehicleInfoVO.getId());
            vo.setThirdId(thirdId.toString());
            thirdIdRelationService.updateByThirdId(vo);
            long l = System.currentTimeMillis();
            etcDevice.setId(null);
            etcDevice.setMerchantId(samplesVo.getMerchantId());
            etcDevice.setStoreId(samplesVo.getStoreId());
            etcDevice.setVehicleModelId(vehicleInfoVO.getVehicleModelId());
            etcDevice.setVehicleId(vehicleInfoVO.getId());
            etcDevice.setCreateTime(l);
            etcDevice.setOpTime(l);
            etcDevice.setOpUserId(opUserId);
            etcDeviceMapper.insertSelective(etcDevice);
        } catch (BizException e) {
            throw new RuntimeException(e);
        }finally {
            redisService.remove(lockLey);
        }
        return  ResultUtil.successResult(true);
    }

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Override
    public void aSynPushETCInfoToTrip(String etcOrder,Long vehicleId) {
        log.info("推送携程etc订单信息,etcOrder{},vehicleId{}", etcOrder,vehicleId);
        // 根据 订单 查询 携程订单 信息
        EtcOrderChargeExample example = new EtcOrderChargeExample();
        EtcOrderChargeExample.Criteria criteria = example.createCriteria();
        criteria.andEtcOrderEqualTo(etcOrder)
                .andVehicleIdEqualTo(vehicleId)
        ;
        List<EtcOrderCharge> etcOrderCharges = etcOrderChargeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(etcOrderCharges)) {
            log.warn("未查询到etc订单信息,etcOrder{},vehicleId{}", etcOrder,vehicleId);
            return;
        }
        for (EtcOrderCharge etcOrderCharge : etcOrderCharges) {
            Long merchantId = etcOrderCharge.getMerchantId();
            String orderId = etcOrderCharge.getOrderId();
            if(StringUtils.isBlank(orderId)){
                log.info("订单号为空,etcOrderCharge{}", JSONUtil.toJsonStr(etcOrderCharge));
                continue;
            }
            OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(Long.valueOf(orderId));
            Byte orderSource = orderInfo.getOrderSource();
            if(StringUtils.isBlank(orderInfo.getSourceOrderId())){
                orderSource = (byte)1;
            }
            if(orderSource!=2){
                log.info("订单来源不是携程,orderInfo{}", JSONUtil.toJsonStr(orderInfo));
                return;
            }
            //组装参数
            Result<VehicleInfoVO> baseById = vehicleInfoService.getBaseById(etcOrderCharge.getVehicleId(), true);
            String license = Optional.ofNullable(baseById.getModel()).map(VehicleInfoVO::getLicense).orElse("");

            // 推送 订单信息
            PushEtcOrderReq req = new PushEtcOrderReq();
            req.setEtcOrderId(etcOrderCharge.getEtcOrder());
            req.setCtripOrderld(Long.valueOf(orderInfo.getSourceOrderId()));
            req.setOrderTime(etcOrderCharge.getCreateTime());
            req.setLicensePlate(license);
            req.setStartTime(Optional.ofNullable(etcOrderCharge.getRealStartTime()).map(Date::getTime).orElse(null));
            req.setEndTime(Optional.ofNullable(etcOrderCharge.getRealEndTime()).map(Date::getTime).orElse(null));
            //计算租期
            if(etcOrderCharge.getRealStartTime()!=null&&etcOrderCharge.getRealEndTime()!=null){
                long differenceInMilliSeconds
                        = Math.abs(etcOrderCharge.getRealStartTime().getTime() - etcOrderCharge.getRealEndTime().getTime());
                long differenceInMinute
                        = differenceInMilliSeconds / (60 * 60 * 1000);
                Long period = differenceInMinute / (24 * 60);


                Long delivery = differenceInMinute % (24 * 60);
                if(delivery>0){
                    period +=1;
                }
                req.setPeriod(period);
            }

            req.setAmount(CurrencyUtils.saas2Ctrip(etcOrderCharge.getAmt(), 2));
            SignRequest signRequest = signBuilder.build(merchantId, "pushETCRentalDataToTrip",req);
            BaseCtripResponse response = ctripV2SpApiClient.pushETCInfoToTrip(signRequest);
            log.info("推送etc订单信息 response={},signRequest={}",JSONUtil.toJsonStr(response),JSONUtil.toJsonStr(signRequest));
            // 推送行程信息
            EtcOrderTollFeeExample tollFeeExample = new EtcOrderTollFeeExample();
            tollFeeExample.createCriteria()
                    .andEtcOrderEqualTo(etcOrderCharge.getEtcOrder());
            List<EtcOrderTollFee> etcOrderTollFees = etcOrderTollFeeMapper.selectByExample(tollFeeExample);
            for (EtcOrderTollFee etcOrderTollFee : etcOrderTollFees) {
                PushEtcOrderTripReq tripReq = new PushEtcOrderTripReq();
                tripReq.setEtcOrderId(etcOrderCharge.getEtcOrder());
                tripReq.setCtripOrderId(Long.valueOf(orderInfo.getSourceOrderId()));
                tripReq.setItineraryId(etcOrderTollFee.getTripId());
                tripReq.setAmount(CurrencyUtils.saas2Ctrip(etcOrderTollFee.getAmt(),2));
                tripReq.setItineraryCreateTime(etcOrderTollFee.getCreateTime());
                SignRequest toTrip = signBuilder.build(merchantId, "pushETCTravelRecordDataToTrip",tripReq);
                BaseCtripResponse tripResponse = ctripV2SpApiClient.pushETCInfoToTrip(toTrip);
                log.info("推送etc订单通行费用信息 response={},signRequest={}",JSONUtil.toJsonStr(tripResponse),JSONUtil.toJsonStr(toTrip));
            }
        }

    }

    @Override
    public void asyncEndSaasEtcOrder(Long saasOrder) {
        CompletableFuture.runAsync(() -> {
            log.info("结束ETC订单，租车单号：{}", saasOrder);
            EtcEndOrderRequest etcEndOrderRequest = new EtcEndOrderRequest();
            etcEndOrderRequest.setEndType(EtcConstantsEnum.EtcOrderEndTypeEnum.FORCED_END.getCode());
            etcEndOrderRequest.setRentOrderId(saasOrder);
            ApiResultResp apiResultResp = saasSelfEtcApiClient.endOrder(etcEndOrderRequest);
            if (!apiResultResp.success()) {
                log.error("结束ETC订单失败，{}", apiResultResp.getMessage());
            }
        },asyncPromiseExecutor).exceptionally(ex -> {
            log.error("结束etc订单异常：saasOrder={}",saasOrder, ex);
            return null;
        });
    }


    public void saveStoreMapping(Long source, Long saasId, String thirdId, Long merchantId) {
        if (Objects.isNull(saasId) || Objects.isNull(source) || StringUtils.isBlank(thirdId) || Objects.isNull(
                merchantId)) {
            log.warn("缺少必要参数");
            return;
        }
        RelationVO relationVO = new RelationVO();
        relationVO.setSaasId(saasId);
        relationVO.setSource(source);
        relationVO.setMerchantId(merchantId);
        relationVO.setThirdId(thirdId);
        relationVO.setType(EtcConstantsEnum.ThirdRelationTypeEnum.ETC_MERCHANT.getType());
        thirdIdRelationService.save(relationVO);
    }

    private ApiResultResp<List<RelationDTO>> getThirdId(Long source, List<Long> saasId, Long merchantId,Byte type) {
        Result<List<RelationVO>> relation =
                thirdIdRelationService.relation(RelationParam.builder().merchantId(merchantId)
                        .type(type).source(source).saasIdList(saasId).build());
        if (!relation.isSuccess() || CollectionUtils.isEmpty(relation.getModel())) {
            return ApiResultResp.failResult(ResultCodeEnum.CommonResultCode.e004);
        }
        List<RelationVO> model = relation.getModel();
        return ApiResultResp.successResult(model.stream().map(x -> {
            RelationDTO relationDTO = new RelationDTO();
            BeanUtils.copyProperties(x, relationDTO);
            return relationDTO;
        }).collect(Collectors.toList()));
    }

    private EtcOrderCharge convertToEtcOrderCharge(EtcOrderChargeVo vo){
        EtcOrderCharge etcOrderCharge = new EtcOrderCharge();
        etcOrderCharge.setId(vo.getId());
        etcOrderCharge.setMerchantId(vo.getMerchantId());
        etcOrderCharge.setStoreId(vo.getStoreId());
        etcOrderCharge.setVehicleModelId(vo.getVehicleModelId());
        etcOrderCharge.setVehicleId(vo.getVehicleId());
        etcOrderCharge.setTenancyFee(vo.getTenancyFee());
        etcOrderCharge.setMerchantProfit(vo.getMerchantProfit());
        etcOrderCharge.setOrderId(vo.getOrderId());
        etcOrderCharge.setEtcOrder(vo.getEtcOrder());
        etcOrderCharge.setSource(vo.getSource());
        etcOrderCharge.setOrderStatus(EtcConstantsEnum.EtcOrderStatusEnum.forByEnumeration(vo.getOrderStatus()).getCode());
        etcOrderCharge.setLastVer(vo.getLastVer());
        etcOrderCharge.setOpUserId(vo.getOpUserId());
        etcOrderCharge.setCreateTime(vo.getCreateTime());
        etcOrderCharge.setOpTime(vo.getOpTime());
        etcOrderCharge.setAmt(vo.getAmt());
        if(vo.getOrderStartTime()!=null){
            etcOrderCharge.setOrderStartTime(new Date(vo.getOrderStartTime()));
        }
        if(vo.getOrderEndTime()!=null){
            etcOrderCharge.setOrderEndTime(new Date(vo.getOrderEndTime()));
        }
        if(vo.getRealStartTime()!=null){
            etcOrderCharge.setRealStartTime(new Date(vo.getRealStartTime()));
        }
        if(vo.getRealEndTime()!=null){
            etcOrderCharge.setRealEndTime(new Date(vo.getRealEndTime()));
        }
        return etcOrderCharge;

    }

    private EtcOrderTollFee convertToEtcOrderTollFee(EtcOrderTollFeeVo vo){
        long l = System.currentTimeMillis();
        EtcOrderTollFee db = new EtcOrderTollFee();
        db.setId(vo.getId());
        db.setEtcOrder(vo.getEtcOrder());
        db.setTripId(vo.getTripId());
        db.setStartTime(vo.getStartTime());
        db.setEndTime(vo.getEndTime());
        db.setStartStationName(vo.getStartStationName());
        db.setEndStationName(vo.getEndStationName());
        String subType = vo.getSubType();
        if (StringUtils.isNotBlank(subType)) {
            db.setSubType(EtcConstantsEnum.EtcSubTypeEnum.forByEnumeration(subType).getCode());
        }
        String subScene = vo.getSubScene();
        if (StringUtils.isNotBlank(subScene)) {
            db.setSubScene(EtcConstantsEnum.EtcSubSceneEnum.forByEnumeration(subScene).getCode());
        }
        db.setAmt(vo.getAmt());
        db.setOpUserId(vo.getOpUserId());
        db.setCreateTime(l);
        db.setOpTime(l);
        return db;

    }

    private EtcDevice convertToEtcDevice(EtcDeviceVo vo,Byte SampleIs){
        EtcDevice db = new EtcDevice();
        db.setId(vo.getId());
        db.setMerchantId(vo.getMerchantId());
        db.setStoreId(vo.getStoreId());
        db.setVehicleModelId(vo.getVehicleModelId());
        db.setVehicleId(vo.getVehicleId());
        db.setEtcNo(vo.getEtcNo());
        db.setIcNo(vo.getIcNo());
        db.setAvailabilityStatus(EtcConstantsEnum.AvailabilityStatusEnum.forByDesc(vo.getAvailabilityStatus()).getCode());
        db.setOnline(ObjectUtils.isEmpty(vo.getOnline())?null:EtcConstantsEnum.OnlineEnum.forByDesc(vo.getOnline()).getCode());
        db.setHardNinkStatus(EtcConstantsEnum.HardNinkStatusEnum.forByDesc(vo.getHardLinkStatus()).getCode());
        db.setWorkStatus(ObjectUtils.isEmpty(vo.getWorkStatus())?null:EtcConstantsEnum.WorkStatusEnum.forByDesc(vo.getWorkStatus()).getCode());
        db.setService(EtcConstantsEnum.ServiceEnum.forByDesc(vo.getService()).getCode());
        db.setAvailabilityStatus(ObjectUtils.isEmpty(vo.getAvailabilityStatus())?null:EtcConstantsEnum.AvailabilityStatusEnum.forByDesc(vo.getAvailabilityStatus()).getCode());
        db.setRemark(vo.getRemark());
        db.setPlateColor(ObjectUtils.isEmpty(vo.getPlateColor())?null:EtcConstantsEnum.PlateColorEnum.forByDesc(vo.getPlateColor()).getCode());
        db.setAxles(vo.getAxles());
        db.setLength(vo.getLength());
        db.setWidth(vo.getWidth());
        db.setHeight(vo.getHeight());
        db.setTotalWeight(vo.getTotalWeight());
        db.setGrossWass(vo.getGrossWass());
        db.setRegisterDate(vo.getRegisterDate());
        db.setGrantDate(vo.getGrantDate());
        db.setOwnerName(vo.getOwnerName());
        db.setLicense(vo.getLicense());

        long l = System.currentTimeMillis();
        db.setSampleIs(SampleIs);
        db.setCreateTime(l);
        db.setOpTime(l);
        db.setOpUserId(1L);
        db.setLastVer(1);
        db.setDeleted(YesOrNoEnum.NO.getValue());
        if (db.getGis() != null) {
            Geometry geometry = null;
            try {
                geometry = StoreInfoServiceImpl.getGeometryByBytes(db.getGis());
                LongLatVo gis = new LongLatVo();
                gis.setLatitude(geometry.getCentroid().getX());
                gis.setLongitude(geometry.getCentroid().getY());
                vo.setGis(gis);
            } catch (Exception e) {
            }
        }
        return db;

    }

    @Override
    public Result<EtcVehicleVo> getEtcVehicleById(Long vehicleId, Long merchantId){

        if (vehicleId == null || merchantId == null) {
            return ResultUtil.failResult("参数错误");
        }

        EtcDeviceExample etcDeviceExample = new EtcDeviceExample();
        etcDeviceExample.createCriteria().andVehicleIdEqualTo(vehicleId);
        etcDeviceExample.createCriteria().andMerchantIdEqualTo(merchantId);
        etcDeviceExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(etcDeviceExample);
        if (CollectionUtil.isEmpty(etcDevices)) {
            return ResultUtil.successResult("未查到数据");
        }
        EtcDevice etcDevice = etcDevices.get(0);
        if (!etcDevice.getMerchantId().equals(merchantId)) {
            return ResultUtil.failResult("越权查看");
        }

        EtcVehicleVo etcVehicleVo = new EtcVehicleVo();
        // 车牌号
        etcVehicleVo.setLicense(etcDevice.getLicense());
        // 发行方
        etcVehicleVo.setPublisherStr(EtcConstantsEnum.PublisherEnum.forByCode(etcDevice.getPublisher().intValue()).getDesc());
        // ETC发行状态
        etcVehicleVo.setPublisherStatusStr(etcDevice.getAvailabilityStatus()== EtcConstantsEnum.AvailabilityStatusEnum.activated.getCode()?
                EtcConstantsEnum.PublishStatusEnum.PUBLISHED.getDesc():EtcConstantsEnum.PublishStatusEnum.UN_PUBLISH.getDesc());
        // 租赁状态
        etcVehicleVo.setRentStatusStr(Objects.equals(etcDevice.getActivateStatus(),(byte)1)?
                EtcConstantsEnum.EtcRentStatusEnum.USED.getDesc():EtcConstantsEnum.EtcRentStatusEnum.UN_USED.getDesc());
        // ETC激活状态
        etcVehicleVo.setActivateStatusStr(EtcConstantsEnum.ActivateStatusEnum.forByCode(etcDevice.getActivateStatus()).getDesc());
        // ETC激活状态
        etcVehicleVo.setAvailabilityStatusStr(EtcConstantsEnum.AvailabilityStatusEnum.forByCode(etcDevice.getAvailabilityStatus()).getDesc());
        // ETCSN
        etcVehicleVo.setEtcNo(etcDevice.getEtcNo());

        etcVehicleVo.setBlacklistStatusStr(EtcConstantsEnum.BlacklistStatusEnum.forByCode(etcDevice.getEtcSource()).getDesc());

        EtcOrderExample etcOrderExample = new EtcOrderExample();
        EtcOrderExample.Criteria orderExampleCriteria = etcOrderExample.createCriteria();
        orderExampleCriteria.andEtcNoEqualTo(etcDevice.getEtcNo());
        orderExampleCriteria.andMerchantIdEqualTo(merchantId);
        List<EtcOrder> etcOrders = etcOrderMapper.selectByExample(etcOrderExample);
        if (CollectionUtil.isNotEmpty(etcOrders)) {
            EtcOrder etcOrder = etcOrders.get(0);
            int platformColorCode = EtcConstantsEnum.SaasSelftPlateColorEnum.forByCode(etcDevice.getPlateColor()).getCode();
            ApiResultResp<EtcVehicleInfo> queryEtcVehicleResponseApiResultResp = saasSelfEtcApiClient.etcVehicle(etcDevice.getThirdVehicleId(), etcDevice.getLicense(), platformColorCode, etcOrder.getOrderNo());
            if (queryEtcVehicleResponseApiResultResp.success()) {
                Optional.ofNullable(queryEtcVehicleResponseApiResultResp.getData()).map(EtcVehicleInfo::getDeductSignStatus).ifPresent(
                        item->{
                            EtcConstantsEnum.AutoDeductionAgreementEnum byCode = EtcConstantsEnum.AutoDeductionAgreementEnum.getByCode(item);
                            etcVehicleVo.setDeductSignStatusStr(byCode.getDesc());
                        }
                );
            }
        } else {
            etcVehicleVo.setDeductSignStatusStr(EtcConstantsEnum.SignStatusEnum.WAIT_SIGN.getDescription());
        }
        return ResultUtil.successResult(etcVehicleVo);
    }


    @Override
    public Result<PageListVo<EtcVehicleVo>> obtainEtcVehicleList(EtcDeviceReq req) {
        EtcDeviceExample etcDeviceExample = buildObtainEtcVehicleListExample(req);
        Page<EtcDevice> page = PageHelper.startPage(req.getPageIndex(), req.getPageSize());

        List<EtcDevice> etcDevices = etcDeviceMapper.selectByExample(etcDeviceExample);

        List<Long> vehicleIdList = etcDevices.stream().map(EtcDevice::getVehicleId).distinct().collect(Collectors.toList());
        List<String> licenseList = etcDevices.stream().map(EtcDevice::getLicense).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(etcDevices)) {
            return ResultUtil.successResult(PageListVo.buildEmptyPage());
        }
        Map<Long, VehicleInfoVO> finalVehiclelByIdMap = Maps.newHashMap();
        Result<Map<Long,VehicleInfoVO>> vehicleResult = vehicleInfoService.obtainVehicleInfo(req.getMerchantId(),vehicleIdList);
        if(vehicleResult.isSuccess()&&vehicleResult.getModel()!=null){
            finalVehiclelByIdMap = vehicleResult.getModel();
        }

//        Map<String, EtcOrderVO> etcOrderHashMap = Maps.newHashMap();
//        Result<Map<String, EtcOrderVO>> etcOrderMapByLicenses = iEtcOrderService.getEtcOrderMapByLicenses(licenseList);
//        if(vehicleResult.isSuccess()&&vehicleResult.getModel()!=null){
//            etcOrderHashMap = etcOrderMapByLicenses.getModel();
//        }

        // 查询etc二维码信息
        EtcDeviceExtExample etcDeviceExtExample = new EtcDeviceExtExample();
        EtcDeviceExtExample.Criteria deviceExtExampleCriteria = etcDeviceExtExample.createCriteria();
//        deviceExtExampleCriteria.andMerchantIdEqualTo(req.getMerchantId());
        deviceExtExampleCriteria.andEtcNoIn(etcDevices.stream().map(EtcDevice::getEtcNo).collect(Collectors.toList()));
        deviceExtExampleCriteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<EtcDeviceExt> etcDeviceExts = etcDeviceExtMapper.selectByExample(etcDeviceExtExample);
        Map<String, String> etcNoToQrCodeMap = etcDeviceExts.stream()
                .collect(Collectors.toMap(
                        EtcDeviceExt::getEtcNo, // 键：EtcNo
                        EtcDeviceExt::getQrCodeUrl, // 值：QrCodeUrl
                        (existing, replacement) -> replacement // 合并逻辑：保留新值
                ));

        // 填充基本信息、并且转换成vo
        List<EtcVehicleVo> voList = assembleBasicEtcVehicleInfo(etcDevices, finalVehiclelByIdMap,etcNoToQrCodeMap);
        // 2、填充车型名称、车辆url、门店等信息
        assembleVehicleInfo(voList);
        // 3、填充费用单信息
        assembleEtcOrderInfo(voList,req.getMerchantId());

        return ResultUtil.successResult(PageListVo.buildPageList(page.getTotal(),voList));
    }

    private EtcDeviceExample buildObtainEtcVehicleListExample(EtcDeviceReq pageQuery) {
        EtcDeviceExample example = new EtcDeviceExample();
        EtcDeviceExample.Criteria criteria = example.createCriteria();

        // 设置基本查询条件
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());  // 未删除记录

        // 商户ID过滤
        if (pageQuery.getMerchantId() != null) {
            criteria.andMerchantIdEqualTo(pageQuery.getMerchantId());
        }

        // etc编号
        if (pageQuery.getEtcNo() != null) {
            criteria.andEtcNoEqualTo(pageQuery.getEtcNo());
        }

        // 车牌号过滤
        if(StringUtils.isNotBlank(pageQuery.getLicense())){
            criteria.andLicenseLike("%"+pageQuery.getLicense()+"%");
        }

        // 归属门店
        if(pageQuery.getStoreId() != null){
            criteria.andStoreIdEqualTo(pageQuery.getStoreId());
        }

        // 激活状态
        if(pageQuery.getEtcActiveStatus()!=null){
            if(pageQuery.getEtcActiveStatus()==1){
                criteria.andActivateStatusEqualTo((byte)1);
            } else {
                criteria.andActivateStatusNotEqualTo((byte)1);
            }
        }

        // 发行方
        if(pageQuery.getPublisher()!=null){
            criteria.andPublisherEqualTo(pageQuery.getPublisher().byteValue());
        }

        if(pageQuery.getEtcRentStatus()!=null){
            if(pageQuery.getEtcRentStatus()==1){
                criteria.andActivateStatusEqualTo((byte)1);
            } else {
                criteria.andActivateStatusNotEqualTo((byte)1);
            }
        }

        // 工作状态
        if(pageQuery.getEtcWorkStatus()!=null){
            if(pageQuery.getEtcWorkStatus()==1){
                criteria.andActivateStatusEqualTo(EtcConstantsEnum.ActivateStatusEnum.active.getCode());
            } else if(pageQuery.getEtcWorkStatus()==2){
                criteria.andActivateStatusEqualTo(EtcConstantsEnum.ActivateStatusEnum.not_active.getCode());
            } else  {
                criteria.andActivateStatusEqualTo(EtcConstantsEnum.ActivateStatusEnum.unknown.getCode());
            }
        }

        if(pageQuery.getBlacklistStatus()!=null){
            criteria.andBlacklistStatusEqualTo(pageQuery.getBlacklistStatus().byteValue());
        }

        if(StringUtils.isNotEmpty(pageQuery.getFrameNum())){
            example.setFrameNum("%"+pageQuery.getFrameNum()+"%");
        }

        if(pageQuery.getVehicleStatus()!=null){
            example.setVehicleStatus(pageQuery.getVehicleStatus());
        }

        if(CollectionUtil.isNotEmpty(pageQuery.getVehicleModelIdList())){
            example.setVehicleModelIdList(pageQuery.getVehicleModelIdList());
        }

        return example;
    }


    private List<EtcVehicleVo> assembleBasicEtcVehicleInfo(List<EtcDevice> etcDevices, Map<Long, VehicleInfoVO> finalVehiclelByIdMap, Map<String, String> etcNoToQrCodeMap) {
        return etcDevices.stream().map(db -> {
            VehicleInfoVO vehicleInfoVO = finalVehiclelByIdMap.get(db.getVehicleId());
            if (vehicleInfoVO == null) {
                log.error("obtainEtcVehicleList:vehicleInfoVO is null,vehicleId:{}, its from vehicleInfoService.obtainVehicleInfo", db.getVehicleId());
                return null;
            }
            // 1、获取一些基础信息
            EtcVehicleVo etcDeviceVo = this.etcVehicleListVOConvert(db);
            etcDeviceVo.setLicense(vehicleInfoVO.getLicense());
            etcDeviceVo.setRentStatusStr(Objects.equals(db.getActivateStatus(),(byte)1)?EtcConstantsEnum.EtcRentStatusEnum.USED.getDesc():EtcConstantsEnum.EtcRentStatusEnum.UN_USED.getDesc());
            etcDeviceVo.setRentStatus(Objects.equals(db.getActivateStatus(),(byte)1)?1:0);
            etcDeviceVo.setVehicleStatusStr(VehicleStatusEnum.getNameByStatus(vehicleInfoVO.getVehicleStatus()));
            etcDeviceVo.setVehicleStatus(vehicleInfoVO.getVehicleStatus().intValue());
            etcDeviceVo.setEtcWorkStatusStr(EtcConstantsEnum.EtcDeviceWorkStatusEnum.getByCode(db.getActivateStatus().intValue()).getDesc());
            etcDeviceVo.setEtcSourceStr(EtcConstantsEnum.ThirdSourceEnum.forByCode(db.getEtcSource()).getName());
            etcDeviceVo.setFrameNum(vehicleInfoVO.getFrameNum());
            etcDeviceVo.setBlacklistStatusStr(EtcConstantsEnum.BlacklistStatusEnum.forByCode(db.getBlacklistStatus()).getDesc());
            // ETC激活状态
            etcDeviceVo.setActivateStatusStr(EtcConstantsEnum.ActivateStatusEnum.forByCode(db.getActivateStatus()).getDesc());
            // qrcode
            String originQrCode = etcNoToQrCodeMap.get(db.getEtcNo());
            if(StringUtils.isNotBlank(etcNoToQrCodeMap.get(db.getEtcNo()))){
                etcDeviceVo.setEtcQrCodeUrl(FileUploader.addFilePrivatePrefix(originQrCode));
            }
            return etcDeviceVo;
        }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void assembleEtcOrderInfo(List<EtcVehicleVo> voList,Long merchantId) {
        EtcOrderBaseQueryParam etcOrderBaseQueryParam = new EtcOrderBaseQueryParam();
        etcOrderBaseQueryParam.setLicenseList(voList.stream().map(EtcVehicleVo::getLicense).collect(Collectors.toList()));
        etcOrderBaseQueryParam.setMerchantId(merchantId);
        Result<List<EtcOrderVO>> etcOrderAndAmountInfo = iEtcOrderService.getEtcOrderAndAmountInfo(etcOrderBaseQueryParam);
        if(!ResultUtil.isResultSuccess(etcOrderAndAmountInfo)) {
            log.warn("查询ETC订单和营收金额失败");
            return;
        }
        List<EtcOrderVO> etcOrderVOList = etcOrderAndAmountInfo.getModel();
        for (EtcOrderVO etcOrderVO : etcOrderVOList) {
            EtcVehicleVo etcVehicleVo = voList.stream().filter(it -> it.getLicense().equals(etcOrderVO.getLicense())).findFirst().orElse(null);
            if (etcVehicleVo != null) {
                etcVehicleVo.setTotalEtcOrderCount(etcOrderVO.getTotalEtcOrderCount());
                etcVehicleVo.setEtcOrderTotalActualAmount(etcOrderVO.getEtcOrderTotalActualAmount());
            }
        }
    }

    private void assembleVehicleInfo(List<EtcVehicleVo> voList) {

        List<Long> modes = voList.stream().map(it -> it.getVehicleModelId()).collect(Collectors.toList());

        VehicleModelInnerQuery vehicleModelInnerQuery = new VehicleModelInnerQuery();
        vehicleModelInnerQuery.setIdList(modes);
        Result<List<BaseVehicleModelVO>> modelResult = vehicleModelService.listBaseVehicleModel(vehicleModelInnerQuery);
        Map<Long, BaseVehicleModelVO> mapModel = new HashMap<>();
        if (modelResult.isSuccess() && modelResult.getModel() != null) {
            mapModel = modelResult.getModel().stream()
                    .collect(Collectors.toMap(BaseVehicleModelVO::getId, modelVo -> modelVo));
        }

        // 查询门店信息
        List<Long> storeIdList = voList.stream().map(it -> it.getStoreId()).collect(Collectors.toList());

        Result<List<StoreSimpleVo>> storeResult = storeInfoService.storeSampleForUnion(storeIdList);
        // key为门店id，value为门店信息
        Map<Long, StoreSimpleVo> idToStoreMap = Optional.ofNullable(storeResult).map(Result::getModel).map(List::stream)
                .map(e -> e.collect(Collectors.toMap(
                        StoreSimpleVo::getStoreId, storeSimpleVo -> storeSimpleVo, (k1, k2) -> k1)))
                .orElse(Collections.emptyMap());

        // 查询车辆图片
        Result<List<VehicleMediaVO>> mediaListResult = vehicleMediaService.listByParam(
                VehicleMediaQueryParam.builder().
                        vehicleModelIdList(modes).mediaType(VehicleMediaEnum.THUMBNAIL.getValue()).build());
        Map<Long, String> modelId2PathMap = Optional.ofNullable(mediaListResult)
                .map(Result::getModel).map(List::stream)
                .map(e -> e.collect(Collectors.toMap(
                        VehicleMediaVO::getVehicleModelId, VehicleMediaVO::getMediaPath, (k1, k2) -> k1)))
                .orElse(Collections.emptyMap());

        for (EtcVehicleVo etcVehicleVo : voList) {
            // 门店名称
            StoreSimpleVo storeSimpleVo = idToStoreMap.get(etcVehicleVo.getStoreId());
            if (storeSimpleVo != null) {
                etcVehicleVo.setStoreName(storeSimpleVo.getStoreName());
                etcVehicleVo.setStoreNameUnion(storeSimpleVo.getStoreNameUnion());
                etcVehicleVo.setBelongCityName(storeSimpleVo.getCityName());
            }
            // 图片
            etcVehicleVo.setVehicleImgUrl(modelId2PathMap.get(etcVehicleVo.getVehicleModelId()));
            BaseVehicleModelVO modelVo = mapModel.get(etcVehicleVo.getVehicleModelId());
            // 车型名称
            if (modelVo != null) {
                etcVehicleVo.setLicenseType(modelVo.getLicenseType());
                if (StringUtils.isBlank(modelVo.getVehicleModelGroup())) {
                    etcVehicleVo.setVehicleModelName(modelVo.getVehicleUnionName());
                } else {
                    etcVehicleVo.setVehicleModelName(StringUtils.defaultString(modelVo.getVehicleModelGroup()) + " " + modelVo.getVehicleUnionName());
                }
            }
        }
    }

    private EtcVehicleVo etcVehicleListVOConvert(EtcDevice db) {
        EtcVehicleVo vo = new EtcVehicleVo();
        BeanUtils.copyProperties(db,vo);
        vo.setPlateColor(db.getPlateColor().intValue());
        vo.setPlateColorStr(EtcConstantsEnum.SaasSelftPlateColorEnum.forByCode(db.getPlateColor()).getDesc());
        vo.setActivateStatus(db.getActivateStatus().intValue());
        vo.setActivateStatusStr(EtcConstantsEnum.ActivateStatusEnum.forByCode(db.getActivateStatus()).getDesc());
        vo.setAvailabilityStatus(db.getAvailabilityStatus().intValue());
        vo.setAvailabilityStatusStr(EtcConstantsEnum.AvailabilityStatusEnum.forByCode(db.getAvailabilityStatus()).getDesc());
        vo.setPublisher(db.getPublisher().intValue());
        vo.setPublisherStr(EtcConstantsEnum.PublisherEnum.forByCode(db.getPublisher().intValue()).getDesc());
        return vo;
    }


}
