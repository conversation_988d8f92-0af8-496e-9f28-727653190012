package com.ql.rent.provider.store;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.request.StoreSearchReq;
import com.ql.rent.api.aggregate.model.request.StoreServiceSearchReq;
import com.ql.rent.api.aggregate.model.response.StoreSearchResp;
import com.ql.rent.api.aggregate.model.response.StoreServiceSearchResp;
import com.ql.rent.api.aggregate.model.vo.store.ServicePolicyVO;
import com.ql.rent.api.aggregate.remote.ctrip.api.CtripApiClient;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.SignRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.GetStoreListForSaasRequest;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.GetDiffStoreConfigListForSaas;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.GetHourlyChargeForSaasResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.GetStoreListForSaasResponse;
import com.ql.rent.common.FileUploader;
import com.ql.rent.common.IRedisService;
import com.ql.rent.component.CtripRequestSignBuilder;
import com.ql.rent.constant.AllopatryRuleConstant;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.store.*;
import com.ql.rent.dao.store.ex.StoreMapper;
import com.ql.rent.entity.store.*;
import com.ql.rent.enums.TimeUnitEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.store.MobileTypeEnum;
import com.ql.rent.enums.store.PickupTypeEnum;
import com.ql.rent.enums.store.RuleTypeEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.enums.trade.OrderTypeEnum;
import com.ql.rent.enums.trade.ServiceFeeTypeEnum;
import com.ql.rent.event.EventPublisher;
import com.ql.rent.multipledatasource.TransactionManagerName;
import com.ql.rent.param.common.GaodeDriverParam;
import com.ql.rent.param.common.GaodeDriverVo;
import com.ql.rent.param.store.IdRelationParam;
import com.ql.rent.param.store.StoreThirdParam;
import com.ql.rent.param.sync.TransactionalRedisParam;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.common.IAreaService;
import com.ql.rent.service.store.IAllopatryRuleService;
import com.ql.rent.service.store.ICancelRuleService;
import com.ql.rent.service.store.IServicePolicyService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.vehicle.IVehicleBusyService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.util.GisUtils;
import com.ql.rent.vo.common.ApiConnMoreVo;
import com.ql.rent.vo.common.AreaVo;
import com.ql.rent.vo.price.RentMainVo;
import com.ql.rent.vo.store.*;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripChargeDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.CtripStoreDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.CtripStoreVO;
import com.ql.rent.vo.store.thirdSync.*;
import com.ql.rent.vo.store.xunjia.*;
import com.ql.rent.vo.vehicle.VehicleBusyFreeVO;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ql.Constant.ChannelId.FEIZHU;
import static com.ql.Constant.ChannelId.OFFLINE;

/**
 * <AUTHOR>
 * @Date 2022/10/29 13:58
 */
@Service
@Slf4j
public class ThirdStoreServiceImpl extends ThirdStoreServiceBase implements IThirdStoreService {
    @Resource
    private StoreInfoMapper storeInfoMapper;
    @Resource
    private StoreGuideMapper storeGuideMapper;
    @Resource
    private GuidePicMapper guidePicMapper;
    @Resource
    private StoreInfoChannelMapper storeInfoChannelMapper;
    @Resource
    private StoreMapper storeMapper;
    @Resource
    private ServicePickupMapper servicePickupMapper;
    @Resource
    private ServicePolicyStoreMapper servicePolicyStoreMapper;
    @Resource
    private ICancelRuleService cancelRuleService;
    @Resource
    private IVehicleBusyService vehicleBusyService;
    @Resource
    private IServicePolicyService servicePolicyService;
    @Resource
    private IThirdIdRelationService thirdIdRelationService;
    @Resource
    private ThirdIdRelationMapper thirdIdRelationMapper;
    @Resource
    private StoreContactMapper storeContactMapper;
    @Resource
    private BusinessTimeMapper businessTimeMapper;
    @Resource
    private NightServiceMapper nightServiceMapper;
    @Resource
    private RestTimeMapper restTimeMapper;
    @Resource
    private StoreHourlyChargeMapper storeHourlyChargeMapper;
    @Resource
    private IAreaService areaService;
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private IApiConnService apiConnService;
    @Resource
    private CtripApiClient ctripApiClient;
    @Resource
    private CtripRequestSignBuilder ctripRequestSignBuilder;
    @Value("${gaode.lbs.web.key}")
    private String gaodeWebKey;
    @Value("${gaode.lbs.web.url}")
    private String gaodeWebUrl;
    @Resource
    private IRedisService redisService;

    @Resource
    private IAllopatryRuleService allopatryRuleService;

    @Resource
    private EventPublisher eventPublisher;

    @Resource
    private ServiceRestTimeMapper serviceRestTimeMapper;

    @Override
    public StoreSearchResp storeSearch(Long merchantId, StoreSearchReq req) {
        Long channelId = req.getChannelId();
        long startExceTime = System.currentTimeMillis();
        StoreSearchResp resp = new StoreSearchResp();
        boolean isSameStore = false;
        if (null != req.getPickUpStoreId() && null != req.getReturnStoreId()
                && req.getPickUpStoreId().equals(req.getReturnStoreId())) {
            isSameStore = true;
        } else {
            if (req.getPickUpPoint().getLongitude().equals(req.getReturnPoint().getLongitude()) &&
                    req.getPickUpPoint().getLatitude().equals(req.getReturnPoint().getLatitude())) {
                isSameStore = true;
            }
        }
        // 获取门店
        List<StoreDistanceVo> pickupStores = selectByDistance(null, merchantId, req.getPickUpStoreId(),
                channelId, req.getPickUpPoint().getLongitude(), req.getPickUpPoint().getLatitude(), null);
        pickupStores = filterStoreByChannel(pickupStores, channelId);
        List<StoreDistanceVo> returnStores = pickupStores.stream().collect(Collectors.toList());
        if (!isSameStore) {
            returnStores = selectByDistance(null, merchantId, req.getReturnStoreId(),
                    channelId, req.getReturnPoint().getLongitude(), req.getReturnPoint().getLatitude(), null);
            returnStores = filterStoreByChannel(returnStores, channelId);
        }
        List<Long> channelStores = getChannelExiteStore(channelId, pickupStores, returnStores);
        // 获取所有门店IDS
        List<Long> pickupStoreIds = pickupStores.stream().map(e -> e.getStoreId()).collect(Collectors.toList());
        List<Long> returnStoreIds = returnStores.stream().map(e -> e.getStoreId()).collect(Collectors.toList());
        List<Long> allStoreIds = new ArrayList<>();
        Stream.of(pickupStoreIds, returnStoreIds).forEach(allStoreIds::addAll);
        allStoreIds = allStoreIds.stream().distinct().collect(Collectors.toList());
        // 查询服务圈
        List<StorePickupVo> circlePickupList = new ArrayList<>();
        List<StorePickupVo> circleReturnList = new ArrayList<>();
        if (CollectionUtils.isEmpty(pickupStoreIds)) {
            log.info("==ThirdStoreService.storeSearch pickupStoreIds date empty!");
            log.info("==ThirdStoreService.storeSearch execTime=" + (System.currentTimeMillis() - startExceTime));
            return resp;
        }
        if (CollectionUtils.isEmpty(returnStoreIds)) {
            log.info("==ThirdStoreService.storeSearch returnStoreIds date empty!");
            log.info("==ThirdStoreService.storeSearch execTime=" + (System.currentTimeMillis() - startExceTime));
            return resp;
        }
        circlePickupList = storeMapper.selectByIsinCircle(pickupStoreIds, channelId, req.getPickUpPoint().getLongitude(), req.getPickUpPoint().getLatitude());
        circlePickupList = filterCircleByChannel(channelStores, circlePickupList, channelId);
        circleReturnList = circlePickupList.stream().collect(Collectors.toList());
        if (!req.getPickUpPoint().getLongitude().equals(req.getReturnPoint().getLongitude()) ||
                !req.getPickUpPoint().getLatitude().equals(req.getReturnPoint().getLatitude())) {
            circleReturnList = storeMapper.selectByIsinCircle(returnStoreIds, channelId, req.getReturnPoint().getLongitude(), req.getReturnPoint().getLatitude());
            circleReturnList = filterCircleByChannel(channelStores, circleReturnList, channelId);
        }
        // 过滤营业时间圈
        try {
            DateFormat toDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            DateFormat dateFormat = new SimpleDateFormat("HHmm");
            Date pickupDate = toDateFormat.parse(req.getPickUpDate());
            Date returnDate = toDateFormat.parse(req.getReturnDate());
            int pickupTime = Integer.parseInt(dateFormat.format(pickupDate));
            int returnTime = Integer.parseInt(dateFormat.format(returnDate));
            //if (pickupTime != 0) {
            circlePickupList = circlePickupList.stream()
                    .filter(e -> (pickupTime >= e.getBusinessFrom() && pickupTime <= e.getBusinessTo()) ||
                            (e.getBusinessFrom() == 0 && e.getBusinessTo() == 0))
                    .collect(Collectors.toList());
            //}
            //if (returnTime != 0) {
            circleReturnList = circleReturnList.stream()
                    .filter(e -> (returnTime >= e.getBusinessFrom() && returnTime <= e.getBusinessTo()) ||
                            (e.getBusinessFrom() == 0 && e.getBusinessTo() == 0))
                    .collect(Collectors.toList());
            //}

            // 过滤休息时间
            List<StorePickupVo> tempList = new ArrayList<>(circlePickupList);
            tempList.addAll(circleReturnList);
            List<Long> circleIds = tempList.stream().map(StorePickupVo::getId).distinct().collect(Collectors.toList());
            List<ServiceRestTime> serviceRestTimeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(circleIds)) {
                ServiceRestTimeExample serviceRestTimeExample = new ServiceRestTimeExample();
                serviceRestTimeExample.createCriteria().andServicePickupIdIn(circleIds).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                serviceRestTimeList = serviceRestTimeMapper.selectByExample(serviceRestTimeExample);
            }
            //      休息时间目前只会有一段
            Map<Long, ServiceRestTime> serviceRestTimeMap = serviceRestTimeList.stream().collect(Collectors.toMap(ServiceRestTime::getServicePickupId, e -> e, (v1, v2) -> v1));

            circlePickupList = circlePickupList.stream().filter(e ->
                    checkServiceRestTime(serviceRestTimeMap.get(e.getId()), pickupDate)).collect(Collectors.toList());

            circleReturnList = circleReturnList.stream().filter(e ->
                    checkServiceRestTime(serviceRestTimeMap.get(e.getId()), returnDate)).collect(Collectors.toList());

        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("日期转货错误");
        }

        // 过滤服务圈及到店数据
        List<Long> circlePickStoreIdList = circlePickupList.stream().map(e -> e.getStoreId()).collect(Collectors.toList());
        pickupStores = pickupStores.stream().filter(e -> req.getDistance() == null || (e.getDistance() != null &&  req.getDistance() != null && e.getDistance() < req.getDistance()) || circlePickStoreIdList.contains(e.getStoreId())).collect(Collectors.toList());
        List<Long> circleReturnStoreIdList = circleReturnList.stream().map(e -> e.getStoreId()).collect(Collectors.toList());
        returnStores = returnStores.stream().filter(e -> req.getDistance() == null || (e.getDistance() != null &&  req.getDistance() != null && e.getDistance() < req.getDistance()) || circleReturnStoreIdList.contains(e.getStoreId())).collect(Collectors.toList());


        // 联系电话
        List<StoreContact> storeContactList = filterContactByChannel(allStoreIds, channelStores, channelId);
        List<StorePairWithCircleDTO> storePairList = new ArrayList<>();

        // 获取异门店信息
        String returnDate = req.getReturnDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        long timestamp = 0;
        try {
            Date date = dateFormat.parse(returnDate);
            timestamp = date.getTime();
        } catch (Exception e) {
            log.error("req.getPickUpDate() is error", e);
        }
        if (timestamp <= 0) {
            throw new BizException("还车时间不合理");
        }

        List<AllopatryRuleVO> allopatryRuleVOS = Lists.newArrayList();
        if (!isSameStore){
            long finalTimestamp = timestamp;
            allopatryRuleVOS = allopatryRuleService.allopatryRuleList(merchantId, channelId, new Date(timestamp))
                    .stream()
                    .filter(r -> new Date(finalTimestamp).getTime() >= new Date(finalTimestamp).getTime())
                    .collect(Collectors.toList());
        }



        Map<String, List<AllopatryRuleVO>> allopatryStoreMap = Maps.newHashMap();
        Map<String, List<AllopatryRuleVO>> allopatryCityMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(allopatryRuleVOS)) {
            List<AllopatryRuleVO> allopatryStoreList = Lists.newArrayList();
            List<AllopatryRuleVO> allopatryCityList = Lists.newArrayList();
            for (AllopatryRuleVO allopatryRuleVO : allopatryRuleVOS) {
                Long pickUpId = allopatryRuleVO.getPickUpId();
                Long returnId = allopatryRuleVO.getReturnId();
                String key = String.format(AllopatryRuleConstant.RULE_FORMAT, pickUpId, returnId);
                if (1 == allopatryRuleVO.getRuleType()) {
                    allopatryStoreList.add(allopatryRuleVO);
                    allopatryStoreMap.put(key, allopatryStoreList);
                } else if (2 == allopatryRuleVO.getRuleType()) {
                    allopatryCityList.add(allopatryRuleVO);
                    allopatryCityMap.put(key, allopatryCityList);
                }
            }
        }

        // 取车门店列表
        for (StoreDistanceVo pickupStore : pickupStores) {
//            if (!checkRentTime(pickupStore, req)) {
//                log.warn("==ThirdStoreService.storeSearch merchantId={},pickupStoreId={},pickUpDate={},"
//                        + "returnDate={},rent time error"
//                    , merchantId, pickupStore.getStoreId(), req.getPickUpDate(), req.getReturnDate());
//                continue;
//            }
            if (YesOrNoEnum.isNo(pickupStore.getStoreStatus())) {
                // 非营业
                continue;
            }
            StoreCircleDTO storePickupCircleDTO = setCircleAndPhone(pickupStore, circlePickupList, storeContactList,
                    req.getPickUpDeliveryServiceType(), req.getPickUpDate(), channelId, req.getDistance());
            if (storePickupCircleDTO == null) {
//                log.warn("==ThirdStoreService.storeSearch merchantId={},pickupStoreId={},"
//                                + "pickUpDeliveryServiceType={},minAdvanceBookingTime error or circle not in gis"
//                        , merchantId, pickupStore.getStoreId(), req.getPickUpDeliveryServiceType());
                continue;
            }
            if (CollectionUtils.isEmpty(storePickupCircleDTO.getServiceCircleDTOList())) {
//                log.warn("==ThirdStoreService.storeSearch merchantId={},pickupStoreId={},"
//                                + "pickUpDeliveryServiceType={},circle not in gis"
//                        , merchantId, pickupStore.getStoreId(), req.getPickUpDeliveryServiceType());
                continue;
            }
            // 还车门店列表
            if (isSameStore) {
                StorePairWithCircleDTO dto = new StorePairWithCircleDTO();
                dto.setPickUpStore(storePickupCircleDTO);
                StoreCircleDTO storeReturnCircleDTO =
                        setCircleAndPhone(pickupStore, circleReturnList, storeContactList, req.getReturnDeliveryServiceType(), null, channelId, req.getDistance());
                dto.setReturnStore(storeReturnCircleDTO);
                if (storeReturnCircleDTO == null || CollectionUtils.isEmpty(storeReturnCircleDTO.getServiceCircleDTOList())) {
//                    log.warn("==ThirdStoreService.storeSearch merchantId={},returnStoreId={},"
//                                    + "returnDeliveryServiceType={},circle not inside gis"
//                            , merchantId, pickupStore.getStoreId(), req.getReturnDeliveryServiceType());
                    continue;
                }
                storePairList.add(dto);
            } else {
                if (CollectionUtils.isEmpty(allopatryRuleVOS)) {
                    Span.current().setAttribute("是否命中异门店规则", false);
                    continue;
                }
                for (StoreDistanceVo returnStore : returnStores) {
                    if (YesOrNoEnum.isNo(returnStore.getStoreStatus())) {
                        // 非营业
                        continue;
                    }
                    // 获取异门店规则，判断是否异门店id， 城市id，是否在异门店规则内
                    Long pickupStoreId = pickupStore.getStoreId();
                    Long returnStoreId = returnStore.getStoreId();
                    String storeKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickupStoreId, returnStoreId);

                    Long pickupCityId = pickupStore.getCityId();
                    Long returnCityId = returnStore.getCityId();

                    String cityKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickupCityId, returnCityId);
                    List<AllopatryRuleVO> storeRuleVOS = allopatryStoreMap.get(storeKey);
                    List<AllopatryRuleVO> cityRuleVOS = allopatryCityMap.get(cityKey);

                    if (CollectionUtils.isEmpty(storeRuleVOS) && CollectionUtils.isEmpty(cityRuleVOS)) {
                        continue;
                    }

                    StorePairWithCircleDTO dto = new StorePairWithCircleDTO();
                    StoreCircleDTO storeReturnCircleDTO =
                            setCircleAndPhone(returnStore, circleReturnList, storeContactList, req.getReturnDeliveryServiceType(), null, channelId, req.getDistance());
                    if (Objects.isNull(storeReturnCircleDTO)||CollectionUtils.isEmpty(storeReturnCircleDTO.getServiceCircleDTOList())) {
//                        log.warn("==ThirdStoreService.storeSearch merchantId={},returnStoreId={},"
//                                        + "returnDeliveryServiceType={},circle not inside gis"
//                                , merchantId, pickupStore.getStoreId(), req.getReturnDeliveryServiceType());
                        continue;
                    }
                    /*if (merchantId == 31L) {
                        String allowIds = ","
                            + "200-201,"
                            + "201-200,"
                            + "203-204,"
                            + "204-203,"
                            + "203-205,"
                            + "205-203,"
                            + "204-205,"
                            + "205-204,"
                            + "98-97,";
                        if (allowIds.indexOf("," + storePickupCircleDTO.getId() + "-" + storeReturnCircleDTO.getId() + ",") < 0){
                            continue;
                        }
                    }*/
                    /**
                     200-201 双向可还
                     203-204 双向可还
                     203-205 双向可还
                     204-205 双向可还
                     98-97  单向可还
                     */
                    dto.setPickUpStore(storePickupCircleDTO);
                    dto.setReturnStore(storeReturnCircleDTO);
                    storePairList.add(dto);
                }
            }
        }
        resp.setStorePairList(storePairList);
        return resp;
    }

    private List<Long> getChannelExiteStore(Long channelId, List<StoreDistanceVo> list1, List<StoreDistanceVo> list2) {
        List<Long> channelStores = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list1)) {
            channelStores.addAll(
                    list1.stream().filter(
                                    e -> e.getChannelId().equals(channelId) &&
                                            !e.getChannelId().equals(OrderSourceEnum.OFFLINE.getSource().longValue())).
                            map(e -> e.getStoreId()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(list2)) {
            channelStores.addAll(
                    list2.stream().filter(e -> e.getChannelId().equals(channelId) &&
                                    !e.getChannelId().equals(OrderSourceEnum.OFFLINE.getSource().longValue())).
                            map(e -> e.getStoreId()).collect(Collectors.toList()));
        }
        channelStores = channelStores.stream().distinct().collect(Collectors.toList());
        return channelStores;
    }

    private boolean checkRentTime(StoreInfoChannel pickupStore, Date pickUpTime, Date returnTime) {
        if (pickUpTime == null) {
            throw new BizException("取车时间不能为空");
        } else if (returnTime == null) {
            throw new BizException("还车时间不能为空");
        }
        long rentTime = returnTime.getTime() - pickUpTime.getTime();
        String[] minAry = pickupStore.getMinRentTerm().toString().split("\\.");
        long minTime = Long.valueOf(minAry[0]) * 24 * 60 * 60 * 1000 + Long.valueOf(minAry[1]) * 60 * 60 * 1000;
        String[] maxAry = pickupStore.getMaxRentTerm().toString().split("\\.");
        long maxTime = Long.valueOf(maxAry[0]) * 24 * 60 * 60 * 1000 + Long.valueOf(maxAry[1]) * 60 * 60 * 1000;
        if (minTime != 0 && rentTime < minTime) {
            //log.info("==ThirdStoreService.checkRentTime rentTime < minRentTime {}day", pickupStore.getMinRentTerm());
            return false;
        } else if (maxTime != 0 && rentTime > maxTime) {
            //log.info("==ThirdStoreService.checkRentTime rentTime > maxRentTime {}day", pickupStore.getMaxRentTerm());
            return false;
        }
        long nowTime = System.currentTimeMillis();
        long minBook = pickupStore.getMinAdvanceBookingTime().multiply(new BigDecimal(1000)).longValue() * 60 * 60;
        if (minBook != 0 && pickUpTime.getTime() < nowTime + minBook) {
            //log.info("==ThirdStoreService.checkBookTime pickupTime {} < minBookTime {}hour", pickUpTime, pickupStore.getMinAdvanceBookingTime());
            return false;
        }
        long maxBook = pickupStore.getMaxAdvanceBookingTime().multiply(new BigDecimal(1000)).longValue() * 30 * 24 * 60 * 60;
        if (maxBook != 0 && pickUpTime.getTime() > nowTime + maxBook) {
            //log.info("==ThirdStoreService.checkBookTime pickupTime {} > maxBookTime {}month", pickupStore.getMaxAdvanceBookingTime());
            return false;
        }
        return true;
    }

    private boolean checkRentTimeV2(XjStoreInfoChannelVO pickupStore, Date pickUpTime, Date returnTime, String debugInfo, List<String> outMsgMap) {
        if (pickUpTime == null) {
            throw new BizException("取车时间不能为空");
        } else if (returnTime == null) {
            throw new BizException("还车时间不能为空");
        }
        long rentTime = returnTime.getTime() - pickUpTime.getTime();
        String[] minAry = pickupStore.getMinRentTerm().toString().split("\\.");
        long minTime = Long.valueOf(minAry[0]) * 24 * 60 * 60 * 1000 + Long.valueOf(minAry[1]) * 60 * 60 * 1000;
        String[] maxAry = pickupStore.getMaxRentTerm().toString().split("\\.");
        long maxTime = Long.valueOf(maxAry[0]) * 24 * 60 * 60 * 1000 + Long.valueOf(maxAry[1]) * 60 * 60 * 1000;
        if (minTime != 0 && rentTime < minTime) {
            //log.info("==ThirdStoreService.checkRentTime rentTime < minRentTime {}day", pickupStore.getMinRentTerm());
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("取车门店[" + pickupStore.getStoreId() + "],未满足门店最小租期" + pickupStore.getMinRentTerm().toString().replace(".", "天") + "小时");
            }
            return false;
        } else if (maxTime != 0 && rentTime > maxTime) {
            //log.info("==ThirdStoreService.checkRentTime rentTime > maxRentTime {}day", pickupStore.getMaxRentTerm());
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("取车门店[" + pickupStore.getStoreId() + "],超过门店最大租期" + pickupStore.getMaxRentTerm().toString().replace(".", "天") + "小时");
            }
            return false;
        }
        long nowTime = System.currentTimeMillis();
        long minBook = TimeUnitEnum.MinAdvanceBookingUnitEnum.convertBookingByUnit(pickupStore.getMinAdvanceBookingUnit(), pickupStore.getMinAdvanceBookingTime());
        if (minBook != 0 && pickUpTime.getTime() < nowTime + minBook) {
            //log.info("==ThirdStoreService.checkBookTime pickupTime {} < minBookTime {}hour", pickUpTime, pickupStore.getMinAdvanceBookingTime());
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("取车门店[" + pickupStore.getStoreId() + "],未满足门店最小提前预定时间" + pickupStore.getMinAdvanceBookingTime()
                        + TimeUnitEnum.MinAdvanceBookingUnitEnum.forUnitByValue(pickupStore.getMinAdvanceBookingUnit()));
            }
            return false;
        }
        long maxBook = TimeUnitEnum.MaxAdvanceBookingUnitEnum.convertBookingByUnit(pickupStore.getMaxAdvanceBookingUnit(), pickupStore.getMaxAdvanceBookingTime());
        if (maxBook != 0 && pickUpTime.getTime() > nowTime + maxBook) {
            //log.info("==ThirdStoreService.checkBookTime pickupTime {} > maxBookTime {}month", pickupStore.getMaxAdvanceBookingTime());
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("取车门店[" + pickupStore.getStoreId() + "],超过门店最大提前预定时间" + pickupStore.getMaxAdvanceBookingTime()
                        + TimeUnitEnum.MaxAdvanceBookingUnitEnum.forUnitByValue(pickupStore.getMaxAdvanceBookingUnit()));
            }
            return false;
        }
        return true;
    }

    private StoreCircleDTO setCircleAndPhone(StoreDistanceVo store, List<StorePickupVo> circleList,
                                             List<StoreContact> contactList, Integer deliveryServiceType,
                                             String pickupDate, Long channelId, Integer distance) {
        StoreCircleDTO storeCircleDTO = new StoreCircleDTO();
        storeCircleDTO.setId(store.getStoreId());
        storeCircleDTO.setCityCode(store.getCityId().toString());
        storeCircleDTO.setMinAdvanceBookingTime(store.getMinAdvanceBookingTime());
        List<ServiceCircleDTO> circleDTOReturnList = new ArrayList<>();
        ServiceCircleDTO dto = new ServiceCircleDTO();
        if (deliveryServiceType == null || deliveryServiceType == -1) {
            dto.setId(-1L);
            dto.setName("到店");
            dto.setPrice(0);
            dto.setDeliveryServiceType(-1);
            /**
             用户到门店距离超过5km，过滤到店（默认）
             */
            int defaultDistance = 5000;
            // 飞猪30公里内出到店
            if (channelId.compareTo(OrderSourceEnum.FEIZHU.longValue()) == 0) {
                defaultDistance = 30000;
            }
            boolean addDdFlg = false;
            if (distance == null) {
                if (store.getDistance() <= defaultDistance) {
                    addDdFlg = true;
                }
            } else {
                if (store.getDistance() <= distance) {
                    addDdFlg = true;
                }
            }
            if (addDdFlg) {
                circleDTOReturnList.add(dto);
            }
        }
        List<ServiceCircleDTO> circleTmpList = new ArrayList<>();
        List<StorePickupVo> storeCirclePickupList = circleList.stream().filter(
                e -> store.getStoreId().equals(e.getStoreId())).collect(Collectors.toList());
        for (StorePickupVo gis : storeCirclePickupList) {
            if ((gis.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue()) && YesOrNoEnum.isYes(store.getFreeShuttleEnabled())) ||
                    gis.getPickupType().equals(PickupTypeEnum.PICKUP.getValue()) && YesOrNoEnum.isYes(store.getPickupEnabled())) {
                dto = new ServiceCircleDTO();
                dto.setId(gis.getId());
                dto.setName(gis.getName());
                dto.setPrice(gis.getFee());
                dto.setDeliveryServiceType(Integer.parseInt(gis.getPickupType().toString()));
                dto.setMinAdvanceBookingTime(gis.getMinAdvanceBookingTime());
//                if (gis.getPickupType().equals(PickupTypeEnum.PICKUP.getValue())) {
//                    storeCircleDTO.setMinAdvanceBookingTime(gis.getMinAdvanceBookingTime());
//                    if (pickupDate != null && !checkMinAdvanceBookingTime(pickupDate, gis.getMinAdvanceBookingTime())) {
//                        continue;
//                    }
//                }
                if (gis.getPickupType().equals(PickupTypeEnum.PICKUP.getValue())) {
                    circleTmpList.add(dto);
                    continue;
                }
                circleDTOReturnList.add(dto);
            }
        }
        if (CollectionUtils.isNotEmpty(circleTmpList)) {
            circleTmpList = circleTmpList.stream().sorted(Comparator.comparing(ServiceCircleDTO::getPrice)).collect(Collectors.toList());
            circleDTOReturnList.add(circleTmpList.get(0));
        }
        if (null != deliveryServiceType) {
            circleDTOReturnList = circleDTOReturnList.stream()
                    .filter(e -> e.getDeliveryServiceType().equals(deliveryServiceType))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(circleDTOReturnList)) {
            return null;
        }
        storeCircleDTO.setServiceCircleDTOList(circleDTOReturnList);
        // 联系电话
        List<StoreContact> storeContactList = contactList.stream().filter(
                e -> store.getStoreId().equals(e.getStoreId())).collect(Collectors.toList());
        List<String> phones = storeContactList.stream().map(e -> e.getMobile()).collect(Collectors.toList());
        storeCircleDTO.setPhones(phones);
        storeCircleDTO.setDistance(store.getDistance());
        return storeCircleDTO;
    }

    private boolean checkMinAdvanceBookingTime(Date pickUpTime, BigDecimal minAdvanceBookingTime) {
        long nowTime = System.currentTimeMillis();
        long minTime = minAdvanceBookingTime.multiply(new BigDecimal(1000)).longValue() * 60 * 60;
        if (nowTime + minTime > pickUpTime.getTime()) {
            //log.info("==ThirdStoreService.checkMinAdvanceBookingTime Circle bookTime < minBookTime {}hour", minAdvanceBookingTime);
            return false;
        }
        return true;
    }

    @Override
    public CancelPolicyDTO getCancelRule(Long merchantId, Date date) {
        Result<List<CancelRuleVo>> listResult = cancelRuleService.ruleList(merchantId);
        List<CancelRuleVo> list = listResult.getModel();
        List<CancelRuleVo> tmpList = list.stream()
                .filter(e -> e.getStartDate().getTime() <= date.getTime() && e.getEndDate().getTime() + 24 * 60 * 60 * 1000 > date.getTime())
                .filter(e -> YesOrNoEnum.isYes(e.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tmpList)) {
            tmpList = list.stream().filter(e -> e.getRuleType().equals(RuleTypeEnum.NORMAL.getValue())).collect(
                    Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(tmpList)) {
            CancelRuleVo ruleVo = tmpList.get(0);
            CancelPolicyDTO cancelPolicyDTO = new CancelPolicyDTO();
            cancelPolicyDTO.setId(ruleVo.getId());
            cancelPolicyDTO.setRuleType(ruleVo.getRuleType());
            cancelPolicyDTO.setStartDate(ruleVo.getStartDate());
            cancelPolicyDTO.setEndDate(ruleVo.getEndDate());
            cancelPolicyDTO.setName(ruleVo.getRuleName());
            cancelPolicyDTO.setFreeCancel(ruleVo.getFreeCancel());
            cancelPolicyDTO.setCostRule(ruleVo.getCostRule());
            cancelPolicyDTO.setCostRulePer(ruleVo.getCostRulePer());
            cancelPolicyDTO.setTimeoutPer(ruleVo.getTimeoutPer());
            cancelPolicyDTO.setStatus(ruleVo.getStatus());
            cancelPolicyDTO.setRuleList(ruleVo.getRuleTimeList().stream().map(cancelRuleTimeVo -> {
                CancelRuleDTO cancelRuleDTO = new CancelRuleDTO();
                cancelRuleDTO.setId(cancelRuleTimeVo.getId());
                cancelRuleDTO.setCancelRuleId(cancelRuleTimeVo.getCancelRuleId());
                cancelRuleDTO.setTimeType(cancelRuleTimeVo.getTimeType());
                cancelRuleDTO.setBeforeHour(cancelRuleTimeVo.getBeforeHour());
                cancelRuleDTO.setBeforeHourPer(cancelRuleTimeVo.getBeforeHourPer());
                return cancelRuleDTO;
            }).collect(Collectors.toList()));
            return cancelPolicyDTO;
        }
        return null;
    }

    @Override
    public ServicePolicyVO getServicePolicy(Long channelId, Long merchantId, Long storeId) {
        StoreInfoChannelExample storeInfoChannelExample = new StoreInfoChannelExample();
        storeInfoChannelExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andChannelIdEqualTo(channelId);
        // count小于等于0 表示商家未设置分渠道。数据取线下渠道
        if (storeInfoChannelMapper.countByExample(storeInfoChannelExample) <= 0) {
            channelId = 1L;
        }

        ServicePolicyStoreExample example = new ServicePolicyStoreExample();
        example.createCriteria().andChanneIdEqualTo(channelId)
                .andMerchantIdEqualTo(merchantId)
                .andStoreIdEqualTo(storeId);
        List<ServicePolicyStore> servicePolicyStores = servicePolicyStoreMapper.selectByExample(example);
        if (null == servicePolicyStores || servicePolicyStores.isEmpty()) {
            return null;
        }
        Long id = servicePolicyStores.get(0).getPolicyId();
        Result<ServicePolicyVO> servicePolicyRes = servicePolicyService.getServicePolicy(id, merchantId);
        if (servicePolicyRes.isSuccess()) {
            return servicePolicyRes.getModel();
        }
        return null;
    }

    @Override
    public List<StoreServiceSearchResp> getServicePolicyById(Long merchantId, StoreServiceSearchReq req) {
        if (Objects.isNull(req.getChannelId())) {
            return Collections.emptyList();
        }
        if (req.getChannelId().equals(FEIZHU)) {
            return getFeiZhuServicePolicy(merchantId,req);
        }
        return null;
    }

    private List<StoreServiceSearchResp> getFeiZhuServicePolicy(Long merchantId, StoreServiceSearchReq req) {
        if (Objects.isNull(req.getMerchantId())) {
            return Collections.emptyList();
        }
        Long policyId = req.getPolicyId();
        if (Objects.nonNull(policyId)) {
            return getFeiZhuServicePolicyById(merchantId, policyId);
        }
        List<Long> storeIds = req.getStoreIds();
        if (CollectionUtils.isNotEmpty(storeIds)) {
            return getFeiZhuServicePolicyByStoreIds(merchantId, storeIds);
        }
        return getAllFeiZhuServicePolicy(merchantId);
    }

    private List<StoreServiceSearchResp> getAllFeiZhuServicePolicy(Long merchantId) {
        ServicePolicyStoreExample example = new ServicePolicyStoreExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ServicePolicyStore> servicePolicyStores = servicePolicyStoreMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(servicePolicyStores)) {
            return Collections.emptyList();
        }
        List<ServicePolicyStore> storeMappings = new ArrayList<>();
        Map<Long, List<ServicePolicyStore>> storePolicyMap =
            servicePolicyStores.stream().collect(Collectors.groupingBy(ServicePolicyStore::getStoreId));
        storePolicyMap.forEach((storeId, policyStoresMapping) -> {
            ServicePolicyStore feizhuChannelData = getChannelData(policyStoresMapping,FEIZHU);
            if (feizhuChannelData == null) {
                return;
            }
            storeMappings.add(feizhuChannelData);
        });
        if (CollectionUtils.isEmpty(storeMappings)) {
            return Collections.emptyList();
        }

        Map<Long, List<Long>> collect =
            storeMappings.stream().collect(Collectors.groupingBy(ServicePolicyStore::getPolicyId,
                Collectors.mapping(ServicePolicyStore::getStoreId, Collectors.toList())));

        List<StoreServiceSearchResp> storeServiceSearchRespList = new ArrayList<>();
        collect.forEach((k, v) -> {
            Result<ServicePolicyVO> servicePolicy = servicePolicyService.getServicePolicy(k, merchantId);
            if (!servicePolicy.isSuccess()) {
                return;
            }
            ServicePolicyVO servicePolicyVO = servicePolicy.getModel();
            StoreServiceSearchResp storeServiceSearchResp = new StoreServiceSearchResp();
            storeServiceSearchResp.setServicePolicy(servicePolicyVO);
            storeServiceSearchResp.setStoreIds(v);
            storeServiceSearchRespList.add(storeServiceSearchResp);
        });
        return storeServiceSearchRespList;
    }

    private List<StoreServiceSearchResp> getFeiZhuServicePolicyByStoreIds(Long merchantId, List<Long> storeIds) {
        //获取所有的服务政策与门店mapping
        ServicePolicyStoreExample example = new ServicePolicyStoreExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ServicePolicyStore> servicePolicyStores = servicePolicyStoreMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(servicePolicyStores)) {
            return Collections.emptyList();
        }
        List<ServicePolicyStore> storeMappings = this.getFeiZhuServicePolicyStoreMapping(storeIds, servicePolicyStores);
        if (CollectionUtils.isEmpty(storeMappings)) {
            return Collections.emptyList();
        }
        Map<Long, List<Long>> collect =
            storeMappings.stream().collect(Collectors.groupingBy(ServicePolicyStore::getPolicyId,
                Collectors.mapping(ServicePolicyStore::getStoreId, Collectors.toList())));

        List<StoreServiceSearchResp> storeServiceSearchRespList = new ArrayList<>();
        collect.forEach((k, v) -> {
            Result<ServicePolicyVO> servicePolicy = servicePolicyService.getServicePolicy(k, merchantId);
            if (!servicePolicy.isSuccess()) {
                return;
            }
            ServicePolicyVO servicePolicyVO = servicePolicy.getModel();
            StoreServiceSearchResp storeServiceSearchResp = new StoreServiceSearchResp();
            storeServiceSearchResp.setServicePolicy(servicePolicyVO);
            storeServiceSearchResp.setStoreIds(v);
            storeServiceSearchRespList.add(storeServiceSearchResp);
        });
        return storeServiceSearchRespList;
    }

    private List<StoreServiceSearchResp> getFeiZhuServicePolicyById(Long merchantId, Long policyId) {
        // 查询服务政策
        Result<ServicePolicyVO> servicePolicyRes = servicePolicyService.getServicePolicy(policyId, merchantId);
        if (!servicePolicyRes.isSuccess()) {
            return Collections.emptyList();
        }
        ServicePolicyVO servicePolicyVO = servicePolicyRes.getModel();
        if (Objects.isNull(servicePolicyVO)) {
            return Collections.emptyList();
        }
        List<StoreServiceSearchResp> storeServiceSearchRespList = new ArrayList<>();
        // 查询服务政策门店
        ServicePolicyStoreExample example = new ServicePolicyStoreExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId).andPolicyIdEqualTo(policyId)
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ServicePolicyStore> servicePolicyStores = servicePolicyStoreMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(servicePolicyStores)) {
            return Collections.emptyList();
        }
        List<Long> storeIdList =
            servicePolicyStores.stream().map(ServicePolicyStore::getStoreId).collect(Collectors.toList());
        // 查询服务政策门店
        ServicePolicyStoreExample storeExample = new ServicePolicyStoreExample();
        storeExample.createCriteria().andMerchantIdEqualTo(merchantId).andStoreIdIn(storeIdList)
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ServicePolicyStore> storeMappings = servicePolicyStoreMapper.selectByExample(storeExample);
        List<ServicePolicyStore> mappingList = getFeiZhuServicePolicyStoreMapping(storeIdList, storeMappings).stream()
            .filter(x -> Objects.equals(x.getPolicyId(), policyId)).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(mappingList)) {
            return Collections.emptyList();
        }
        StoreServiceSearchResp storeServiceSearchResp = new StoreServiceSearchResp();
        storeServiceSearchResp.setServicePolicy(servicePolicyVO);
        storeServiceSearchResp.setStoreIds(
            mappingList.stream().map(ServicePolicyStore::getStoreId).collect(Collectors.toList()));
        storeServiceSearchRespList.add(storeServiceSearchResp);
        return storeServiceSearchRespList;
    }

    private List<ServicePolicyStore> getFeiZhuServicePolicyStoreMapping(List<Long> storeIds,
                                                                        List<ServicePolicyStore> mappingList) {
        List<ServicePolicyStore> feiZhuMapping = new ArrayList<>();
        Map<Long, List<ServicePolicyStore>> storePolicyMap =
            mappingList.stream().collect(Collectors.groupingBy(ServicePolicyStore::getStoreId));
        for (Long storeId : storeIds) {
            List<ServicePolicyStore> servicePolicyStoreMapping = storePolicyMap.get(storeId);
            if (servicePolicyStoreMapping == null) {
                continue;
            }
            ServicePolicyStore feizhuChannelData = getChannelData(servicePolicyStoreMapping, FEIZHU);
            if (Objects.isNull(feizhuChannelData)) {
                continue;
            }
            feiZhuMapping.add(feizhuChannelData);
        }
        return feiZhuMapping;
    }

    private ServicePolicyStore getChannelData(List<ServicePolicyStore> policyStoreMapping,Long channeId) {
        return policyStoreMapping.stream().filter(x -> Objects.equals(x.getChanneId(), channeId)).findFirst()
            .orElseGet(() -> policyStoreMapping.stream().filter(x -> Objects.equals(x.getChanneId(), OFFLINE)).findFirst()
                .orElse(null));
    }

//    @Override
//    public List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> selectOptionalVehicleModels(Long merchantId, Long paramChannelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime, List<RentMainVo> rentSellList) {
//        ;
////        Tracer tracer = GlobalOpenTelemetry.getTracer(Span.current().getSpanContext().getTraceId());
////        Span span = tracer.spanBuilder("可用车辆")
////            .setParent(Context.current().with(Span.current()))
////            .setSpanKind(SpanKind.SERVER)
////            .startSpan();
//        Long channelId = CommStore.getParentChannelId(paramChannelId);
//        long t = System.currentTimeMillis();
////        log.info("==ThirdStoreService.selectOptionalVehicleModels merchantId={},channelId={},pairDTOList={},pickUpGis={},returnGis={},pickUpTime={},returnTime={},",
////                merchantId, channelId, JSON.toJSONString(pairDTOList), JSON.toJSONString(pickUpGis), JSON.toJSONString(returnGis), pickUpTime, returnTime);
//        List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> retList = new ArrayList<>();
//        Set<Long> setAllStoreIds = Sets.newHashSet();
//        pairDTOList.forEach(p -> {
//            StoreCircleIdDTO pickUpStore = p.getPickUpStore();
//            StoreCircleIdDTO returnStore = p.getReturnStore();
//            setAllStoreIds.add(pickUpStore.getId());
//            setAllStoreIds.add(returnStore.getId());
//        });
//        // 查询门店
//        List<Long> allStoreIds = Lists.newArrayList(setAllStoreIds);
//        List<StoreInfoChannel> storeList = filterStoreChannelByChannel(allStoreIds, channelId);
//        List<Long> channelStoreIds = getChannelExiteStore(channelId, storeList);
//        if (CollectionUtils.isEmpty(storeList)) {
//            log.warn("==ThirdStoreService.selectOptionalVehicleModels store not exist, merchantId={}, storeIds={}", merchantId, allStoreIds);
//            return retList;
//        }
//        Map<Long, StoreInfoChannel> storeMap = storeList.stream().collect(Collectors.toMap(StoreInfoChannel::getStoreId, vo -> vo));
//        Span.current().setAttribute("　aa查询门店耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 查询车型
//        VehicleModelInnerQuery vehicleModelInnerQuery = new VehicleModelInnerQuery();
//        vehicleModelInnerQuery.setMerchantId(merchantId);
//        List<InquiryVehicleModel> modelList = vehicleModelService.listInquiryVehicleModels(merchantId);
//        Span.current().setAttribute("　aa查询车型耗时", System.currentTimeMillis() - t);
//        if (CollectionUtils.isEmpty(modelList)) {
//            log.warn("==ThirdStoreService.selectOptionalVehicleModels model not exist, merchantId={}", merchantId);
//            return retList;
//        }
//        t = System.currentTimeMillis();
//        // 转换参数
//        List<VehicleModelAbbrDTO> models = modelList.stream().map(source -> {
//            VehicleModelAbbrDTO target = new VehicleModelAbbrDTO();
//            target.setVehicleModelId(source.getId());
//            target.setLicenseType(source.getLicenseType());
//            return target;
//        }).collect(Collectors.toList());
//
//
//        // 支持异地还车设置
//        Map<String, List<AllopatryRuleDTO>> allopatryRuleDTOMap = new HashMap<>();
//        boolean diffFlg = pairDTOList.stream().anyMatch(e -> e.getPickUpStore().getId().compareTo(e.getReturnStore().getId()) != 0);
//        if (diffFlg) {
//            allopatryRuleDTOMap = allopatryRuleService
//                .selectAllopatryRule(merchantId, paramChannelId, allStoreIds, pairDTOList, returnTime)
//                .stream().collect(Collectors.groupingBy(dto -> String
//                    .format(AllopatryRuleConstant.RULE_FORMAT, dto.getPickUpStoreId(), dto.getReturnStoreId())));
//        }
//        t = System.currentTimeMillis();
//        // 营业时间
//        List<BusinessTime> businessTimeList = filterBusinessTimeByChannel(allStoreIds, channelStoreIds, channelId);
//        Span.current().setAttribute("　aa查询营业时间耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 休息时间
//        List<RestTime> restTimeList = filterRestTimeByChannel(allStoreIds, channelStoreIds, channelId);
//        Span.current().setAttribute("　aa查询休息时间耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 数据组装
//        List<Long> allowIds = storeList.stream().map(StoreInfoChannel::getStoreId).collect(Collectors.toList());
//        for (StorePairDTO storePairDTO : pairDTOList) {
//            // 店铺不存在 或 未营业
//            if (!allowIds.contains(storePairDTO.getPickUpStore().getId()) && !allowIds.contains(storePairDTO.getReturnStore().getId())) {
//                continue;
//            }
//            // 营业时间&休息时间check
//            if (!checkBusinessAndRestTime(businessTimeList, restTimeList, storePairDTO, pickUpTime, returnTime)) {
//                continue;
//            }
//            List<VehicleModelAbbrDTO> tempModels = new ArrayList<>(models);
//            StorePairUniDTO<List<VehicleModelAbbrDTO>> pairUniDTO = new StorePairUniDTO<>();
//            BeanUtils.copyProperties(storePairDTO, pairUniDTO);
//            StoreInfoChannel pickupStore = storeMap.get(storePairDTO.getPickUpStore().getId());
//            if (pickupStore == null) {
//                continue;
//            }
//            if (!checkRentTime(pickupStore, pickUpTime, returnTime)) {
//                continue;
//            }
//            // 过滤可用车型
//            List<VehicleBusyFreeVO> feeModelIds = vehicleBusyService.getStockFeeModels(merchantId, pairUniDTO.getPickUpStore().getId(), pickUpTime.getTime(), returnTime.getTime());
//            Span.current().setAttribute("　aa查询可用车型(门店" + pairUniDTO.getPickUpStore().getId() + ")耗时", System.currentTimeMillis() - t);
//            t = System.currentTimeMillis();
//            if (feeModelIds.size() == 0) {
//                log.warn("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={} free models is empty", pairUniDTO.getPickUpStore().getId());
//                continue;
//            }
//            // 门店
//            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
//            Long returnStoreId = pairUniDTO.getReturnStore().getId();
//            if (storeMap.get(pickupStoreId) == null) {
//                log.warn("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId not exist, pickupStoreId={}", pickupStoreId);
//                continue;
//            }
//            List<Long> storeIds = new ArrayList<>();
//            storeIds.add(pickupStoreId);  // 还车门店先不check
//            if (!pickupStoreId.equals(returnStoreId)) {
//                String ruleKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickupStoreId, returnStoreId);
//                List<AllopatryRuleDTO> allopatryRuleDTOList = allopatryRuleDTOMap.get(ruleKey);
//                if (Objects.isNull(allopatryRuleDTOList)) {
//                    continue;
//                }
//                List<Long> modelIds = Lists.newArrayList();
//                allopatryRuleDTOList.forEach(rule -> {
//                    modelIds.addAll(rule.getVehicleModelIds());
//                });
//                if (CollectionUtils.isEmpty(modelIds)) {
//                    continue;
//                }
//                if (modelIds.contains(0L)) {
//                    tempModels = models;
//                } else {
//                    tempModels = models.stream()
//                            .filter(m -> (modelIds.contains(m.getVehicleModelId())))
//                            .collect(Collectors.toList());
//                }
//            }
//            List<Long> rentMainIds = rentSellList.stream().filter(e -> e.getStoreId().equals(pickupStoreId))
//                    .map(e -> e.getVehicleModelId()).collect(Collectors.toList());
//            tempModels = tempModels.stream().filter(e -> rentMainIds.contains(e.getVehicleModelId())).collect(Collectors.toList());
////            List<Long> priceModelIds = listPrice.stream().filter(e -> e.getStoreId().equals(pickupStoreId))
////                    .map(e -> e.getVehicleModelId()).collect(Collectors.toList());
////            tempModels = tempModels.stream().filter(e -> priceModelIds.contains(e.getVehicleModelId())).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(tempModels)) {
//                log.warn("==ThirdStoreService.selectOptionalVehicleModels price not setting, pickupStoreId={}", pickupStoreId);
//            }
//            tempModels = tempModels.stream().filter(e -> feeModelIds.contains(e.getVehicleModelId())).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(tempModels)) {
//                log.warn("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={} store price setting models is empty", pickupStoreId);
//            }
//            pairUniDTO.setData(tempModels);
//            retList.add(pairUniDTO);
//        }
////        Span.current().setAttribute("selectOptionalVehicleModels return", JSON.toJSONString(retList));
////        Span.current().setAttribute("selectOptionalVehicleModels耗时", (System.currentTimeMillis() - startExceTime));
////        span.setStatus(StatusCode.OK);
////        span.end();
//        return retList;
//    }

    @Override
    public List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> selectOptionalVehicleModelsV2(Long merchantId, Long paramChannelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime, List<RentMainVo> rentSellList, List<XjStoreInfoChannelVO> storeList, List<XjBusinessTimeVO> businessTimeList, String debugInfo, List<String> outMsgMap) {
        Long channelId = CommStore.getParentChannelId(paramChannelId);
        long t = System.currentTimeMillis();

        List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> retList = new ArrayList<>();
        Set<Long> setAllStoreIds = Sets.newHashSet();
        pairDTOList.forEach(p -> {
            StoreCircleIdDTO pickUpStore = p.getPickUpStore();
            StoreCircleIdDTO returnStore = p.getReturnStore();
            setAllStoreIds.add(pickUpStore.getId());
            setAllStoreIds.add(returnStore.getId());
        });
        // 查询门店
        List<Long> allStoreIds = Lists.newArrayList(setAllStoreIds);
        if (storeList == null) {
            storeList = filterStoreChannelByChannelV2(allStoreIds, channelId);
            Span.current().setAttribute("　aa查询门店耗时", System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }
        if (CollectionUtils.isEmpty(storeList)) {
            return retList;
        }
        // 已开通的渠道门店
        List<Long> openStoreChannelIds = getOpenChannelStoreIds(channelId, storeList);
        // 门店map
        Map<Long, XjStoreInfoChannelVO> storeMap = storeList.stream().collect(Collectors.toMap(XjStoreInfoChannelVO::getStoreId, vo -> vo));
//        Span.current().setAttribute("　aa查询门店耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();

        // 支持异地还车设置
        Map<String, List<AllopatryRuleDTO>> allopatryRuleDTOMap = new HashMap<>();
        boolean diffFlg = pairDTOList.stream().anyMatch(e -> e.getPickUpStore().getId().compareTo(e.getReturnStore().getId()) != 0);
        if (diffFlg) {
            allopatryRuleDTOMap = allopatryRuleService
                    .selectAllopatryRule(merchantId, paramChannelId, allStoreIds, pairDTOList, returnTime)
                    .stream().collect(Collectors.groupingBy(dto -> String
                            .format(AllopatryRuleConstant.RULE_FORMAT, dto.getPickUpStoreId(), dto.getReturnStoreId())));
        }

        t = System.currentTimeMillis();
        // 营业时间
        if (businessTimeList == null) {
            businessTimeList = filterBusinessTimeByChannelV2(allStoreIds, openStoreChannelIds, channelId);
            Span.current().setAttribute("　aa查询营业时间耗时", System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }
        Span.current().setAttribute("　aa查询营业时间耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        // 休息时间
        List<XjRestTimeVO> restTimeList = filterRestTimeByChannelV2(allStoreIds, openStoreChannelIds, channelId);
        Span.current().setAttribute("　aa查询休息时间耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        // 数据组装
        List<Long> allowIds = storeList.stream().map(XjStoreInfoChannelVO::getStoreId).collect(Collectors.toList());
        for (StorePairDTO storePairDTO : pairDTOList) {
            // 店铺不存在 或 未营业
            if (!allowIds.contains(storePairDTO.getPickUpStore().getId())) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("取车门店[" + storePairDTO.getPickUpStore().getId() + "],不存在或未营业");
                }
                continue;
            }
            if (!allowIds.contains(storePairDTO.getReturnStore().getId())) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("还车门店[" + storePairDTO.getReturnStore().getId() + "],不存在或未营业");
                }
                continue;
            }
            // 营业时间&休息时间check
            if (!checkBusinessAndRestTimeV2(businessTimeList, restTimeList, storePairDTO, pickUpTime, returnTime, debugInfo, outMsgMap)) {
                continue;
            }
            StorePairUniDTO<List<VehicleModelAbbrDTO>> pairUniDTO = new StorePairUniDTO<>();
            BeanUtils.copyProperties(storePairDTO, pairUniDTO);
            XjStoreInfoChannelVO pickupStore = storeMap.get(storePairDTO.getPickUpStore().getId());
            if (pickupStore == null) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("取车门店[" + storePairDTO.getPickUpStore().getId() + "],不存在或未营业");
                }
                continue;
            }
            if (!checkRentTimeV2(pickupStore, pickUpTime, returnTime, debugInfo, outMsgMap)) {
                continue;
            }
            // 询价过滤订单间隔；如非全部车型，逻辑比较复杂，等有时间再开发(目前针对上汽)
            long orderInterval = -1;
            String orderIntervalRule = (String) redisService.get("orderIntervalRule"); //  ",1,"  or "all"  格式
            if (StringUtils.isEmpty(orderIntervalRule)) {
                orderIntervalRule = ",58,365,";
            }
            if (orderIntervalRule.indexOf(",all,") > -1 || orderIntervalRule.indexOf("," + merchantId + ",") > -1) {
            //if (Arrays.asList(58L, 365L).contains(merchantId)) {
                if (diffFlg) {
                    String ruleKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pairUniDTO.getPickUpStore().getId(), pairUniDTO.getReturnStore().getId());
                    List<AllopatryRuleDTO> allopatryRuleDTOList = allopatryRuleDTOMap.getOrDefault(ruleKey, new ArrayList<>());
                    List<Long> modelIds = Lists.newArrayList();
                    allopatryRuleDTOList.forEach(rule -> {
                        modelIds.addAll(rule.getVehicleModelIds());
                    });
                    if (modelIds.contains(0L)) {
                        // 先匹配所有车型，否则先使用门店规则
                        AllopatryRuleDTO dto = allopatryRuleDTOList.stream().filter(e -> e.getVehicleModelIds().contains(0L)).collect(Collectors.toList()).get(0);
                        // 订单间隔时间单位 1:分钟，2:小时
                        if (dto.getDuration() == -1) {
                            // 未设置以门店规则
                        } else if (dto.getDurationUnit() == null || dto.getDurationUnit() == 2) {
                            orderInterval = dto.getDuration() * 3600 * 1000;
                        } else {
                            orderInterval = dto.getDuration() * 60 * 1000;
                        }
                    } else {
                        orderInterval = 0;
                    }
                }
                // 门店规则
                if (orderInterval == -1) {
                    // 订单间隔时间单位 1:分钟，2:小时
                    if (pickupStore.getOrderIntervalUnit() == null || pickupStore.getOrderIntervalUnit().intValue() == 2) {
                        orderInterval = pickupStore.getOrderInterval() * 3600 * 1000;
                    } else {
                        orderInterval = pickupStore.getOrderInterval() * 60 * 1000;
                    }
                }
            }
            // 过滤可用车型
            List<VehicleBusyFreeVO> feeModelSelfServices = vehicleBusyService.getStockFeeModels(merchantId, pairUniDTO.getPickUpStore().getId(), pickUpTime.getTime(), returnTime.getTime() + orderInterval, channelId);
            if (feeModelSelfServices.size() == 0) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("可用车型,门店[" + storePairDTO.getPickUpStore().getId() + "->" + storePairDTO.getReturnStore().getId() + "],库存可用车型为空");
                }
                continue;
            }

            // 转换参数
            List<VehicleModelAbbrDTO> models = feeModelSelfServices.stream().map(source -> {
                VehicleModelAbbrDTO target = new VehicleModelAbbrDTO();
                target.setVehicleModelId(source.getVehicleModelId());
                target.setLicenseType(source.getLicenseType());
                target.setSelfServiceReturn(source.getSelfServiceReturn());
                target.setStockNum(source.getStockNum());
                return target;
            }).collect(Collectors.toList());
            List<VehicleModelAbbrDTO> tempModels = new ArrayList<>(models);
            Span.current().setAttribute("　aa查询可用车型(门店" + pairUniDTO.getPickUpStore().getId() + ")耗时", System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
            if (!StringUtils.isEmpty(debugInfo)) {
                Set<Long> feeModelIds = feeModelSelfServices.stream().map(VehicleBusyFreeVO::getVehicleModelId).collect(Collectors.toSet());
                outMsgMap.add("可用车型,门店[" + storePairDTO.getPickUpStore().getId() + "->" + storePairDTO.getReturnStore().getId() + "],库存可用车型为" + JSON.toJSONString(feeModelIds));
            }

            // 门店
            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
            Long returnStoreId = pairUniDTO.getReturnStore().getId();
            if (storeMap.get(pickupStoreId) == null) {
                log.warn("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId not exist, pickupStoreId={}", pickupStoreId);
                continue;
            }

            // 还车门店先不check
            if (!pickupStoreId.equals(returnStoreId)) {
                String ruleKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickupStoreId, returnStoreId);
                List<AllopatryRuleDTO> allopatryRuleDTOList = allopatryRuleDTOMap.get(ruleKey);
                if (Objects.isNull(allopatryRuleDTOList)) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("异地还车,门店[" + pickupStoreId + "->" + returnStoreId + "],门店未配置异地取还规则");
                    }
                    continue;
                }
                List<Long> modelIds = Lists.newArrayList();
                allopatryRuleDTOList.forEach(rule -> {
                    modelIds.addAll(rule.getVehicleModelIds());
                });
                if (CollectionUtils.isEmpty(modelIds)) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("异地还车,门店[" + pickupStoreId + "->" + returnStoreId + "],未配置异地取还车型规则");
                    }
                    continue;
                }
                if (modelIds.contains(0L)) {
                    tempModels = models;
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("异地还车,门店[" + pickupStoreId + "->" + returnStoreId +  "],支持所有车型异地还车");
                    }
                } else {
                    tempModels = models.stream()
                            .filter(m -> (modelIds.contains(m.getVehicleModelId())))
                            .collect(Collectors.toList());
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("异地还车,门店[" + pickupStoreId + "->" + returnStoreId + "],支持异地还车车型" + JSON.toJSONString(modelIds));
                    }
                }
            }
            List<Long> rentMainIds = rentSellList.stream().filter(e -> e.getStoreId().equals(pickupStoreId))
                    .map(e -> e.getVehicleModelId()).collect(Collectors.toList());
            tempModels = tempModels.stream().filter(e -> rentMainIds.contains(e.getVehicleModelId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempModels)) {
                //log.warn("==ThirdStoreService.selectOptionalVehicleModels price not setting, pickupStoreId={}", pickupStoreId);
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("车型过滤,门店[" + pickupStoreId + "->" + returnStoreId +  "],与所有车型匹配后，返回车型为空");
                }
                continue;
            }
            pairUniDTO.setData(tempModels);
            retList.add(pairUniDTO);
        }
        return retList;
    }

    private boolean checkBusinessAndRestTime(List<BusinessTime> businessTimeList, List<RestTime> restTimeList,
                                             StorePairDTO storePairDTO, Date pickUpTime, Date returnTime) {
        Long pickupStoreId = storePairDTO.getPickUpStore().getId();
        Long returnStoreId = storePairDTO.getPickUpStore().getId();

        List<BusinessTime> businessTimeTmpList = businessTimeList.stream()
                .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());
//        if (!checkWeekTimeSingle(businessTimeTmpList, pickUpTime)) {
//            log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间不在营业时间内", pickupStoreId);
//            return false;
//        }
//        if (!checkBusinessTimeSingle(businessTimeTmpList, pickUpTime)) {
//            log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间不在营业时间内", pickupStoreId);
//            return false;
//        }
        if (!checkWeekAndBusinessTime(businessTimeTmpList, pickUpTime)) {
            log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间不在营业时间内", pickupStoreId);
            return false;
        }
        businessTimeTmpList = businessTimeList.stream()
                .filter(e -> e.getStoreId().equals(returnStoreId)).collect(Collectors.toList());
//        if (!checkWeekTimeSingle(businessTimeTmpList, returnTime)) {
//            log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，还车时间不在营业时间内", pickupStoreId);
//            return false;
//        }
//        if (!checkBusinessTimeSingle(businessTimeTmpList, returnTime)) {
//            log.info("==ThirdStoreService.selectOptionalVehicleModels returnStoreId={}，还车时间不在营业时间内", returnStoreId);
//            return false;
//        }
        if (!checkWeekAndBusinessTime(businessTimeTmpList, returnTime)) {
            log.info("==ThirdStoreService.selectOptionalVehicleModels returnStoreId={}，还车时间不在营业时间内", returnStoreId);
            return false;
        }

        List<RestTime> restTimeTmpList = restTimeList.stream()
                .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());
        if (!checkRestTimeSingle(restTimeTmpList, pickUpTime)) {
            log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间在休息时间", returnStoreId);
            return false;
        }
        restTimeTmpList = restTimeList.stream()
                .filter(e -> e.getStoreId().equals(returnStoreId)).collect(Collectors.toList());
        if (!checkRestTimeSingle(restTimeTmpList, returnTime)) {
            log.info("==ThirdStoreService.selectOptionalVehicleModels returnStoreId={}，还车时间在休息时间", returnStoreId);
            return false;
        }

        return true;
    }

    private boolean checkBusinessAndRestTimeV2(List<XjBusinessTimeVO> businessTimeList, List<XjRestTimeVO> restTimeList,
                                               StorePairDTO storePairDTO, Date pickUpTime, Date returnTime, String debugInfo, List<String> outMsgMap) {
        Long pickupStoreId = storePairDTO.getPickUpStore().getId();
        Long returnStoreId = storePairDTO.getReturnStore().getId();

        List<XjBusinessTimeVO> businessTimeTmpList = businessTimeList.stream()
                .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());

        if (!checkWeekAndBusinessTimeV2(businessTimeTmpList, pickUpTime)) {
            //log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间不在营业时间内", pickupStoreId);
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("营业时间,门店[" + pickupStoreId + "->" + returnStoreId + "],取车门店[" + pickupStoreId + "],不在营业时间内");
            }
            return false;
        }
        businessTimeTmpList = businessTimeList.stream()
                .filter(e -> e.getStoreId().equals(returnStoreId)).collect(Collectors.toList());

        if (!checkWeekAndBusinessTimeV2(businessTimeTmpList, returnTime)) {
            //log.info("==ThirdStoreService.selectOptionalVehicleModels returnStoreId={}，还车时间不在营业时间内", returnStoreId);
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("营业时间,门店[" + pickupStoreId + "->" + returnStoreId + "],还车车门店[" + returnStoreId + "],不在营业时间内");
            }
            return false;
        }

        List<XjRestTimeVO> restTimeTmpList = restTimeList.stream()
                .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());
        if (!checkRestTimeSingleV2(restTimeTmpList, pickUpTime)) {
            //log.info("==ThirdStoreService.selectOptionalVehicleModels pickupStoreId={}，取车时间在休息时间", returnStoreId);
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("营业时间,门店[" + pickupStoreId + "->" + returnStoreId + "],取车门店[" + pickupStoreId + "],不能在休息时间");
            }
            return false;
        }
        restTimeTmpList = restTimeList.stream()
                .filter(e -> e.getStoreId().equals(returnStoreId)).collect(Collectors.toList());
        if (!checkRestTimeSingleV2(restTimeTmpList, returnTime)) {
            //log.info("==ThirdStoreService.selectOptionalVehicleModels returnStoreId={}，还车时间在休息时间", returnStoreId);
            if (!StringUtils.isEmpty(debugInfo)) {
                outMsgMap.add("营业时间,门店[" + pickupStoreId + "->" + returnStoreId + "],取车门店[" + returnStoreId + "],不能在休息时间");
            }
            return false;
        }

        return true;
    }

    private boolean checkWeekAndBusinessTime(List<BusinessTime> businessTimeList, Date dateTime) {
        int weekIndex = getWeekIndex(dateTime);
        if (weekIndex == 0) {
            weekIndex = 7;
        }
        for (BusinessTime businessTime : businessTimeList) {
            //在这一天营业
            boolean weekFlg = businessTime.getBusinessPeriod().charAt(weekIndex - 1) == '1';
            if (weekFlg) {
                int time = Integer.valueOf(DateUtil.getFormatDateStr(dateTime, "HHmm"));
                //在营业时间段内
                if (time >= businessTime.getBusinessFrom() && time <= businessTime.getBusinessTo()) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkWeekAndBusinessTimeV2(List<XjBusinessTimeVO> businessTimeList, Date dateTime) {
        int weekIndex = getWeekIndex(dateTime);
        if (weekIndex == 0) {
            weekIndex = 7;
        }
        for (XjBusinessTimeVO businessTime : businessTimeList) {
            //在这一天营业
            boolean weekFlg = businessTime.getBusinessPeriod().charAt(weekIndex - 1) == '1';
            if (weekFlg) {
                int time = Integer.valueOf(DateUtil.getFormatDateStr(dateTime, "HHmm"));
                //在营业时间段内
                if (time >= businessTime.getBusinessFrom() && time <= businessTime.getBusinessTo()) {
                    return true;
                }
            }
        }
        return false;
    }


    private boolean checkWeekTimeSingle(List<BusinessTime> businessTimeList, Date dateTime) {
        int weekIndex = getWeekIndex(dateTime);
        if (weekIndex == 0) {
            weekIndex = 7;
        }
        for (BusinessTime businessTime : businessTimeList) {
            boolean weekFlg = businessTime.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
            if (weekFlg) {
                return true;
            }
        }
        return false;
    }

    private boolean checkBusinessTimeSingle(List<BusinessTime> businessTimeList, Date dateTime) {
        int time = Integer.valueOf(DateUtil.getFormatDateStr(dateTime, "HHmm"));
        for (BusinessTime businessTime : businessTimeList) {
            if (time >= businessTime.getBusinessFrom() && time <= businessTime.getBusinessTo()) {
                return true;
            }
        }
        return false;
    }

    private boolean checkRestTimeSingle(List<RestTime> restTimeList, Date dateTime) {
        Date date = DateUtil.getFormatDate(DateUtil.getFormatDateStr(dateTime, DateUtil.yyyyMMdd), DateUtil.yyyyMMdd);
        int time = Integer.valueOf(DateUtil.getFormatDateStr(dateTime, "HHmm"));
        for (RestTime restTime : restTimeList) {
            if (date.compareTo(restTime.getStartDate()) >= 0 && date.compareTo(restTime.getEndDate()) <= 0) {
                if (time >= restTime.getStartTime() && time <= restTime.getEndTime()) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean checkRestTimeSingleV2(List<XjRestTimeVO> restTimeList, Date dateTime) {
        Date date = DateUtil.getFormatDate(DateUtil.getFormatDateStr(dateTime, DateUtil.yyyyMMdd), DateUtil.yyyyMMdd);
        int time = Integer.valueOf(DateUtil.getFormatDateStr(dateTime, "HHmm"));
        for (XjRestTimeVO restTime : restTimeList) {
            if (date.compareTo(restTime.getStartDate()) >= 0 && date.compareTo(restTime.getEndDate()) <= 0) {
                if (time >= restTime.getStartTime() && time <= restTime.getEndTime()) {
                    return false;
                }
            }
        }
        return true;
    }

//    @Override
//    public List<StorePairServiceCircleDTO> selectServiceCirclePrice(Long channelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTim) {
////        log.info("==ThirdStoreService.selectServiceCirclePrice pairDTOList={},pickUpGis={},returnGis={}",
////                JSON.toJSONString(pairDTOList), JSON.toJSONString(pickUpGis), JSON.toJSONString(returnGis));
//        List<StorePairServiceCircleDTO> retList = new ArrayList<>();
//        Long startExceTime = System.currentTimeMillis();
//        List<Long> circleIds = new ArrayList<>();
//        List<Long> pickupCircleIds = new ArrayList<>();
//        List<Long> returnCircleIds = new ArrayList<>();
//        for (StorePairDTO storePairDTO : pairDTOList) {
//            pickupCircleIds = storePairDTO.getPickUpStore().getServiceCircleIdList();
//            returnCircleIds = storePairDTO.getReturnStore().getServiceCircleIdList();
//            if (pickupCircleIds != null && returnCircleIds != null) {
//                Stream.of(pickupCircleIds, returnCircleIds).forEach(circleIds::addAll);
//            } else {
//                if (pickupCircleIds == null) {
//                    log.warn("==ThirdStoreService.selectServiceCirclePrice pickupCircleIds is null");
//                } else if (returnCircleIds == null) {
//                    log.warn("==ThirdStoreService.selectServiceCirclePrice returnCircleIds is null");
//                }
//                continue;
//            }
//        }
//        if (CollectionUtils.isEmpty(circleIds)) {
//            log.error("==ThirdStoreService.selectServiceCirclePrice circleIds is null");
//            return retList;
//        }
//        // 查询服务圈
//        circleIds = circleIds.stream().filter(e -> e.longValue() != -1).distinct().collect(
//                Collectors.toList());
//        List<ServicePickup> pickupList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(circleIds)) {
//            ServicePickupExample pickupExample = new ServicePickupExample();
//            pickupExample.createCriteria().andIdIn(circleIds)
//                    //.andChannelIdIn(Arrays.asList((long) OrderSourceEnum.OFFLINE.getSource(), channelId))
//                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
//                    .andEnabledEqualTo(YesOrNoEnum.YES.getValue());
//            pickupList = servicePickupMapper.selectByExample(pickupExample);
//            //pickupList = filterPickupByChannel(pickupList, channelId);
//            //Span.current().setAttribute("　查询服务圈耗时：", System.currentTimeMillis() - startExceTime);
//        }
//        // 数据组装
//        for (StorePairDTO storePairDTO : pairDTOList) {
//            StorePairServiceCircleDTO pairUniDTO = new StorePairServiceCircleDTO();
//            pairUniDTO.setId(storePairDTO.getId());
//            StoreCircleDTO pickUpStore = new StoreCircleDTO();
//            BeanUtils.copyProperties(storePairDTO.getPickUpStore(), pickUpStore);
//            pairUniDTO.setPickUpStore(pickUpStore);
//            StoreCircleDTO returnStore = new StoreCircleDTO();
//            BeanUtils.copyProperties(storePairDTO.getReturnStore(), returnStore);
//            pairUniDTO.setReturnStore(returnStore);
//            // 取车门店
//            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
//            List<ServicePickup> storeCirclePickupList = pickupList.stream().filter(
//                    e -> pickupStoreId.equals(e.getStoreId())).collect(Collectors.toList());
//            List<ServiceCircleDTO> circleDTOPickupList = getPicekPrice(storeCirclePickupList,
//                    storePairDTO.getPickUpStore().getServiceCircleIdList(), pickUpTime, true, null, null);
//            if (CollectionUtils.isEmpty(circleDTOPickupList)) {
//                continue;
//            }
//            pairUniDTO.getPickUpStore().setServiceCircleDTOList(circleDTOPickupList);
//            // 还车门店
//            Long returneStoreId = pairUniDTO.getReturnStore().getId();
//            List<ServicePickup> storeReturnCircleList = pickupList.stream().filter(
//                    e -> returneStoreId.equals(e.getStoreId())).collect(Collectors.toList());
//            List<ServiceCircleDTO> circleDTOReturnList = getPicekPrice(storeReturnCircleList,
//                    storePairDTO.getReturnStore().getServiceCircleIdList(), pickUpTime, false, null, null);
//            pairUniDTO.getReturnStore().setServiceCircleDTOList(circleDTOReturnList);
//            retList.add(pairUniDTO);
//        }
////        log.info("==ThirdStoreService.selectServiceCirclePrice return={}", JSON.toJSONString(retList));
////        log.info("==ThirdStoreService.selectServiceCirclePrice execTime=" + (System.currentTimeMillis() - startExceTime));
//        return retList;
//    }

    @Override
    public List<StorePairServiceCircleDTO> selectServiceCirclePriceV2(Long channelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTim, String debugInfo, List<String> outMsgMap) {
        List<StorePairServiceCircleDTO> retList = new ArrayList<>();
        List<Long> circleIds = new ArrayList<>();
        List<Long> pickupCircleIds = new ArrayList<>();
        List<Long> returnCircleIds = new ArrayList<>();
        for (StorePairDTO storePairDTO : pairDTOList) {
            pickupCircleIds = storePairDTO.getPickUpStore().getServiceCircleIdList();
            returnCircleIds = storePairDTO.getReturnStore().getServiceCircleIdList();
            if (pickupCircleIds != null && returnCircleIds != null) {
                Stream.of(pickupCircleIds, returnCircleIds).forEach(circleIds::addAll);
            }
        }
        if (CollectionUtils.isEmpty(circleIds)) {
            log.error("==ThirdStoreService.selectServiceCirclePrice circleIds is null");
            return retList;
        }
        // 查询服务圈
        circleIds = circleIds.stream().filter(e -> e.longValue() != -1).distinct().collect(
                Collectors.toList());
        List<ServicePickup> pickupList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(circleIds)) {
            ServicePickupExample pickupExample = new ServicePickupExample();
            pickupExample.createCriteria().andIdIn(circleIds)
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andEnabledEqualTo(YesOrNoEnum.YES.getValue());
            pickupList = servicePickupMapper.selectByExample(pickupExample);
        }
        // 查服务圈休息时间
        List<ServiceRestTime> serviceRestTimeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pickupList)) {
            ServiceRestTimeExample serviceRestTimeExample = new ServiceRestTimeExample();
            serviceRestTimeExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                    .andServicePickupIdIn(pickupList.stream().map(ServicePickup::getId).collect(Collectors.toList()));
            serviceRestTimeList = serviceRestTimeMapper.selectByExample(serviceRestTimeExample);
        }
        //      服务圈休息时间目前只有一段
        Map<Long, ServiceRestTime> serviceRestTimeMap = serviceRestTimeList.stream()
                .collect(Collectors.toMap(ServiceRestTime::getServicePickupId, e -> e, (v1, v2) -> v1));
        // 数据组装
        for (StorePairDTO storePairDTO : pairDTOList) {
            StorePairServiceCircleDTO pairUniDTO = new StorePairServiceCircleDTO();
            pairUniDTO.setId(storePairDTO.getId());
            StoreCircleDTO pickUpStore = new StoreCircleDTO();
            BeanUtils.copyProperties(storePairDTO.getPickUpStore(), pickUpStore);
            pairUniDTO.setPickUpStore(pickUpStore);
            StoreCircleDTO returnStore = new StoreCircleDTO();
            BeanUtils.copyProperties(storePairDTO.getReturnStore(), returnStore);
            pairUniDTO.setReturnStore(returnStore);
            // 取车门店
            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
            List<ServicePickup> storeCirclePickupList = pickupList.stream().filter(
                    e -> pickupStoreId.equals(e.getStoreId())).collect(Collectors.toList());
            List<ServiceCircleDTO> circleDTOPickupList = getPicekPrice(storeCirclePickupList,
                    storePairDTO.getPickUpStore().getServiceCircleIdList(), pickUpTime, returnTim, true, serviceRestTimeMap, debugInfo, outMsgMap);
            if (CollectionUtils.isEmpty(circleDTOPickupList)) {
                continue;
            }
            pairUniDTO.getPickUpStore().setServiceCircleDTOList(circleDTOPickupList);
            // 还车门店
            Long returneStoreId = pairUniDTO.getReturnStore().getId();
            List<ServicePickup> storeReturnCircleList = pickupList.stream().filter(
                    e -> returneStoreId.equals(e.getStoreId())).collect(Collectors.toList());
            List<ServiceCircleDTO> circleDTOReturnList = getPicekPrice(storeReturnCircleList,
                    storePairDTO.getReturnStore().getServiceCircleIdList(), pickUpTime, returnTim, false, serviceRestTimeMap, debugInfo, outMsgMap);
            pairUniDTO.getReturnStore().setServiceCircleDTOList(circleDTOReturnList);
            if (CollectionUtils.isEmpty(circleDTOReturnList)) {
                continue;
            }
            retList.add(pairUniDTO);
        }
        return retList;
    }

    private List<ServiceCircleDTO> getPicekPrice(List<ServicePickup> storeCircleList, List<Long> circleIds, Date pickUpTime, Date returnTime, boolean isPickup, Map<Long, ServiceRestTime> serviceRestTimeMap, String debugInfo, List<String> outMsgMap) {
        List<ServiceCircleDTO> circleDTOReturnList = new ArrayList<>();
        Map<Long, ServicePickup> mapPickup = storeCircleList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
        for (Long circleId : circleIds) {
            ServiceCircleDTO dto = new ServiceCircleDTO();
            if (circleId == -1) {
                dto.setId(-1L);
                dto.setName("到店");
                dto.setPrice(0);
                dto.setDeliveryServiceType(-1);
                circleDTOReturnList.add(dto);
            } else {
                ServicePickup pickup = mapPickup.get(circleId);
                if (pickup != null) {
                    if (isPickup && pickup.getPickupType().equals(PickupTypeEnum.PICKUP.getValue())) {
                        if (pickUpTime != null && !checkMinAdvanceBookingTime(pickUpTime, pickup.getMinAdvanceBookingTime())) {
                            if (!StringUtils.isEmpty(debugInfo)) {
                                outMsgMap.add("取车门店[" + pickup.getStoreId() + "],不满足服务圈最小提前时间" + pickup.getMinAdvanceBookingTime() + "小时");
                            }
                            continue;
                        }
                    }

                    // 目前只有上门送取支持服务圈休息时间
                    if (pickup.getPickupType().equals(PickupTypeEnum.PICKUP.getValue())) {
                        if (isPickup) {
                            ServiceRestTime serviceRestTime = serviceRestTimeMap.get(circleId);
                            if (serviceRestTime != null && !checkServiceRestTime(serviceRestTime, pickUpTime)) {
                                if (!StringUtils.isEmpty(debugInfo)) {
                                    outMsgMap.add("取车门店[" + pickup.getStoreId() + "],服务圈[" + pickup.getId() + "],不满足服务圈休息时间");
                                }
                                continue;
                            }
                        } else {
                            ServiceRestTime serviceRestTime = serviceRestTimeMap.get(circleId);
                            if (serviceRestTime != null && !checkServiceRestTime(serviceRestTime, returnTime)) {
                                if (!StringUtils.isEmpty(debugInfo)) {
                                    outMsgMap.add("还车门店[" + pickup.getStoreId() + "],服务圈[" + pickup.getId() + "],不满足服务圈休息时间");
                                }
                                continue;
                            }
                        }
                    }

                    dto.setMinAdvanceBookingTime(pickup.getMinAdvanceBookingTime());
                    dto.setId(pickup.getId());
                    dto.setName(pickup.getName());
                    int fee = pickup.getFee();
                    if (YesOrNoEnum.isNo(pickup.getFeeType())) {
                        fee = 0;
                    }
                    dto.setPrice(fee);
                    // 1：免费接送，2：上门取送 待判断；把免费接送服务器添加到围栏
                    dto.setDeliveryServiceType(Integer.parseInt(pickup.getPickupType().toString()));
                    circleDTOReturnList.add(dto);
                } else {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("不存在的服务圈id=" + circleId);
                    }
                }
            }
        }
        return circleDTOReturnList;
    }

    private boolean checkServiceRestTime(ServiceRestTime serviceRestTime, Date dateTime) {
        // 无休息时间配置
        if (serviceRestTime == null) {
            return true;
        }

        // 检查日期是否在休息日期范围内
        if (DateUtil.getStartTime(serviceRestTime.getStartDate()).compareTo(dateTime) <= 0
                && DateUtil.getEndTime(serviceRestTime.getEndDate()).compareTo(dateTime) >= 0) {
            // 获取时间点(HHmm格式)
            DateFormat dateFormat = new SimpleDateFormat("HHmm");
            int time = Integer.parseInt(dateFormat.format(dateTime));
            
            // 检查时间是否在休息时间段内
            if (time >= serviceRestTime.getStartTime() && time <= serviceRestTime.getEndTime()) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    public StorePairServiceCircleDTO selectServiceCirclePrice(Long channelId, StorePairDTO pairDTO, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime) {
        List<StorePairServiceCircleDTO> serviceCircleDTOS =
                this.selectServiceCirclePriceV2(channelId, Lists.newArrayList(pairDTO), pickUpGis, returnGis, pickUpTime, returnTime, null, null);
        if (null == serviceCircleDTOS || serviceCircleDTOS.isEmpty()) {
            return null;
        }
        return serviceCircleDTOS.get(0);
    }

//    @Override
//    public void selectStoreService(Map<Integer, List<StorePairUniDTO<List<ServiceItemDTO>>>> serviceItemsMap, Long merchantId, Long channelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime) {
//        List<StorePairUniDTO<List<ServiceItemDTO>>> retList = selectStoreService(merchantId, channelId, pairDTOList, pickUpGis, returnGis, pickUpTime, returnTime);
//        serviceItemsMap.put(0, retList);
//    }

    /**
     * 获取门店夜间服务费（for orde 算价）
     *
     * @param storeId
     * @param paramChannelId
     * @param serviceTime
     * @return
     */
    @Override
    public Integer selectStoreNightFee(Long storeId, Long paramChannelId, Date serviceTime) {
        Long channelId = CommStore.getParentChannelId(paramChannelId);
        List<Long> allStoreIds = Arrays.asList(storeId);

        List<StoreInfoChannel> storeList = filterStoreChannelByChannel(allStoreIds, channelId);
        List<Long> channelStores = getChannelExiteStore(channelId, storeList);

        // 营业时间
        List<BusinessTime> businessTimeList = filterBusinessTimeByChannel(allStoreIds, channelStores, channelId);
        List<Long> businessTimeIds = businessTimeList.stream().filter(e -> YesOrNoEnum.isYes(e.getNightService())).map(e -> e.getId()).collect(
                Collectors.toList());
        // 夜间服务
        List<NightService> nightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessTimeIds)) {
            nightList = filterNightByChannel(allStoreIds, businessTimeIds, channelStores, channelId);
        }
        /**
         * 夜间服务费
         */
        int weekIndex = getWeekIndex(serviceTime);
        if (weekIndex == 0) weekIndex = 7;
        DateFormat dateFormat = new SimpleDateFormat("HHmm");
        // 取车
        int pickTime = Integer.parseInt(dateFormat.format(serviceTime));
        // 取车营业时间check
//        List<BusinessTime> tempBusinessTimeList = fiterDateBusiTimeList(businessTimeList, storeId, weekIndex);
        List<BusinessTime> tempBusinessTimeList = businessTimeList.stream()
                .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tempBusinessTimeList)) {
//            BusinessTime businessTime = tempBusinessTimeList.get(0);
//            if (pickTime < businessTime.getBusinessFrom() || pickTime > businessTime.getBusinessTo()) {
//                //是否需要check是否营业时间内
//                throw new BizException("取还车时间不在门店营业时间范围");
//            }
            if (!checkWeekAndBusinessTime(businessTimeList, serviceTime)) {
                throw new BizException("取还车时间不在门店营业时间范围");
            }
        } else {
            // 不存在营业时间;
            throw new BizException("门店营业时间未设置");
        }
        // 取车夜间服务费
        List<NightService> tmpNightList = nightList.stream().filter(
                e -> e.getStoreId().equals(storeId) &&
                        pickTime >= e.getBusinessFrom() && pickTime <= e.getBusinessTo()
        ).collect(Collectors.toList());
        for (NightService night : tmpNightList) {
            boolean weekFlg = night.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
            if (weekFlg) {
                return night.getFee();
            }
        }
        return 0;
    }

    /**
     * 获取门店夜间服务费（for 滴滴）
     *
     * @param storeId
     * @param paramChannelId
     * @return
     */
    @Override
    public List<StoreNightVo> selectStoreNightFee(Long storeId, Long paramChannelId) {
        Long channelId = CommStore.getParentChannelId(paramChannelId);
        List<Long> allStoreIds = Arrays.asList(storeId);

        List<StoreInfoChannel> storeList = filterStoreChannelByChannel(allStoreIds, channelId);
        List<Long> channelStores = getChannelExiteStore(channelId, storeList);

        // 营业时间
        List<BusinessTime> businessTimeList = filterBusinessTimeByChannel(allStoreIds, channelStores, channelId);
        List<Long> businessTimeIds = businessTimeList.stream().filter(e -> YesOrNoEnum.isYes(e.getNightService())).map(e -> e.getId()).collect(
                Collectors.toList());
        // 夜间服务
        List<NightService> nightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessTimeIds)) {
            nightList = filterNightByChannel(allStoreIds, businessTimeIds, channelStores, channelId);
        }

        List<StoreNightVo> list = new ArrayList<>();
        for (NightService night : nightList) {
            StoreNightVo vo = new StoreNightVo();
            BeanUtils.copyProperties(night, vo);
            list.add(vo);
        }
        return list;
    }



//    @Override
//    public List<StorePairUniDTO<List<ServiceItemDTO>>> selectStoreService(Long merchantId, Long paramChannelId, List<StorePairDTO> pairDTOList, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime) {
//        //Span.current().updateName("门店服务型");
//        Long channelId = CommStore.getParentChannelId(paramChannelId);
//        long t = System.currentTimeMillis();
////        log.info("==ThirdStoreService.selectStoreService pairDTOList={},pickUpGis={},returnGis={},pickUpTime={},returnTime={}",
////                JSON.toJSONString(pairDTOList), JSON.toJSONString(pickUpGis), JSON.toJSONString(returnGis), pickUpTime, returnTime);
//        Long startExceTime = System.currentTimeMillis();
//        List<Long> pickupStoreIds = pairDTOList.stream().map(e -> e.getPickUpStore().getId()).collect(Collectors.toList());
//        List<Long> returnStoreIds = pairDTOList.stream().map(e -> e.getReturnStore().getId()).collect(Collectors.toList());
//        List<Long> allStoreIds = new ArrayList<>();
//        Stream.of(pickupStoreIds, returnStoreIds).forEach(allStoreIds::addAll);
//        allStoreIds = allStoreIds.stream().distinct().collect(Collectors.toList());
//        List<StoreInfoChannel> storeList = filterStoreChannelByChannel(allStoreIds, channelId);
//        List<Long> channelStores = getChannelExiteStore(channelId, storeList);
//        // Map<Long, StoreInfoChannel> storeMap = storeList.stream().collect(Collectors.toMap(StoreInfoChannel::getStoreId, vo -> vo));
//        // 查询服务圈
//        List<ServicePickup> pickupList = filterPickupByChannel(allStoreIds, channelStores, channelId, true);
//        Map<Long, ServicePickup> pickupMap = pickupList.stream().collect(Collectors.toMap(ServicePickup::getId, vo -> vo));
//        //Span.current().setAttribute("　ac查询查询服务圈耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 支持异地还车设置
//        //  List<DiffplaceService> diffList = filterDiffByChannel(allStoreIds, channelStores, channelId);
//        //  Map<Long, DiffplaceService> diffMap = diffList.stream().collect(Collectors.toMap(DiffplaceService::getStoreId, vo -> vo));
//        // 获取异地取还规则
//        // 数据结构: (取方id - 还方id) ->> 异门店规则
//        /*Map<String, AllopatryRuleDTO> allopatryRuleDTOMap = allopatryRuleService.selectAllopatryRule(merchantId, paramChannelId, allStoreIds, pairDTOList, returnTime)
//                .stream()
//                .collect(Collectors.toMap(dto ->
//                        String.format(AllopatryRuleConstant.RULE_FORMAT, dto.getPickUpStoreId(), dto.getReturnStoreId()), Function.identity(), (k1, k2) -> k1));*/
//        Span.current().setAttribute("　ac查询支持异地还车设置耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 营业时间
//        List<BusinessTime> businessTimeList = filterBusinessTimeByChannel(allStoreIds, channelStores, channelId);
//        List<Long> businessTimeIds = businessTimeList.stream().filter(e -> YesOrNoEnum.isYes(e.getNightService())).map(e -> e.getId()).collect(
//                Collectors.toList());
//        Span.current().setAttribute("　ac查询营业时间耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//        // 夜间服务
//        List<NightService> nightList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(businessTimeIds)) {
//            nightList = filterNightByChannel(allStoreIds, businessTimeIds, channelStores, channelId);
//        }
//        Span.current().setAttribute("　ac查询夜间服务耗时", System.currentTimeMillis() - t);
//        t = System.currentTimeMillis();
//       /* boolean distanceFlg = false;
//        for (StorePairDTO storePairDTO : pairDTOList) {
//            if (!storePairDTO.getPickUpStore().getId().equals(storePairDTO.getReturnStore().getId())) {
//                distanceFlg = true;
//                break;
//            }
//        }
//        List<StoreDistance> distanceList = new ArrayList<>();
//        if (distanceFlg) {
//            distanceList = getStoreDistance(merchantId);
//            Span.current().setAttribute("　ac查询门店对距离", System.currentTimeMillis() - t);
//            t = System.currentTimeMillis();
//        }*/
//        // 数据组装
//        List<StorePairUniDTO<List<ServiceItemDTO>>> retList = new ArrayList<>();
//        for (StorePairDTO storePairDTO : pairDTOList) {
//            StorePairUniDTO<List<ServiceItemDTO>> pairUniDTO = new StorePairUniDTO<>();
//            pairUniDTO.setId(storePairDTO.getId());
//            BeanUtils.copyProperties(storePairDTO, pairUniDTO);
//            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
//            Long returneStoreId = pairUniDTO.getReturnStore().getId();
//            // 服务项组装
//            List<ServiceItemDTO> dtoList = new ArrayList<>();
//            /**
//             * 门店取车服务
//             */
//            addCircleService(true, storePairDTO.getPickUpStore().getServiceCircleIdList(), pickupMap, dtoList);
//            /**
//             * 门店还车服务
//             */
//            addCircleService(false, storePairDTO.getReturnStore().getServiceCircleIdList(), pickupMap, dtoList);
//            // 异门店还车费
//            ServiceItemDTO dto;
//            /*if (!pickupStoreId.equals(returneStoreId)) {
//             *//*StoreInfoChannel pickupStoreChannel = storeMap.get(returneStoreId);
//                if (YesOrNoEnum.isNo(pickupStoreChannel.getAllopatryReturnEnabled())) {
//                    Span.current().setAttribute("　ac门店未开启异地还车，storeId=" + pickupStoreId + "返回", 0);
//                    continue;
//                }*//*
//                if (MapUtils.isEmpty(allopatryRuleDTOMap)){
//                    continue;
//                }
//                String ruleKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickupStoreId, returneStoreId);
//                // 异门店还车费
//                AllopatryRuleDTO allopatryRuleDTO = allopatryRuleDTOMap.get(ruleKey);
//             //   DiffplaceService diffplace = diffMap.get(pickupStoreId);
//                if (allopatryRuleDTO == null) {
//                    // 未设置异地还车设置
//                    continue;
//                } else {
//                    // 支持异地还车车型怎么返回 ？ todo
//                    Long startExceTimeDistance = System.currentTimeMillis();
//
//                    // 根据高德计算最短路线
//                    String storeKey = getStoreKey(pickupStoreId, returneStoreId);
//                    List<StoreDistance> tmpDistance = distanceList.stream().filter(e -> e.getStoreKeys().equals(storeKey)).collect(Collectors.toList());
//                    Integer distance;
//                    if (CollectionUtils.isEmpty(tmpDistance)) {
//                        StoreInfo pickupStoreInfo = storeInfoMapper.selectByPrimaryKey(pickupStoreId);
//                        StoreInfo returnStoreInfo = storeInfoMapper.selectByPrimaryKey(returneStoreId);
//                        try {
//                            // 20230606 沟通计算采用门店经纬度
//                            Geometry geometry = StoreInfoServiceImpl.getGeometryByBytes(pickupStoreInfo.getGis());
//                            pickUpGis.setLongitude(geometry.getCentroid().getX());
//                            pickUpGis.setLatitude(geometry.getCentroid().getY());
//                            geometry = StoreInfoServiceImpl.getGeometryByBytes(returnStoreInfo.getGis());
//                            returnGis.setLongitude(geometry.getCentroid().getX());
//                            returnGis.setLatitude(geometry.getCentroid().getY());
//                            distance = caseDistance(pickUpGis, returnGis);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                            throw new BizException("高德计算距离异常:" + e.getMessage());
//                        }
//                    } else {
//                        distance = new BigDecimal(tmpDistance.get(0).getDistance()).divide(new BigDecimal("1000"))
//                            .setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
//                    }
//
//                    // 向上取整到元
//                    Integer diffPrice = (Objects.nonNull(allopatryRuleDTO.getFixedPrice()) && allopatryRuleDTO.getFixedPrice() >= 0) ? allopatryRuleDTO.getFixedPrice() : (allopatryRuleDTO.getPriceKm() * distance);
//                    diffPrice = diffPrice % 100 == 0 ? diffPrice : ((int) (diffPrice / 100) + 1) * 100;
//                    dto = setStoreServiceItem(allopatryRuleDTO.getId(),
//                            ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceCode(),
//                            ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceName(),
//                            OrderTypeEnum.PICK_SERVICE.getType(), diffPrice,
//                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
//                    log.info("==caseDistance execTime=" + (System.currentTimeMillis() - startExceTimeDistance));
//                    Span.current().setAttribute("　ac高德计算最短路线耗时", System.currentTimeMillis() - t);
//                    dtoList.add(dto);
//                }
//            }*/
//            /**
//             * 夜间服务费
//             */
//            int weekIndex = getWeekIndex(pickUpTime);
//            if (weekIndex == 0) weekIndex = 7;
//            DateFormat dateFormat = new SimpleDateFormat("HHmm");
//            // 取车
//            int pickTime = Integer.parseInt(dateFormat.format(pickUpTime));
//            // 取车营业时间check
////            List<BusinessTime> tempBusinessTimeList = fiterDateBusiTimeList(businessTimeList, pickupStoreId, weekIndex);
////            if (CollectionUtils.isNotEmpty(tempBusinessTimeList)) {
////                BusinessTime businessTime = tempBusinessTimeList.get(0);
////                if (pickTime < businessTime.getBusinessFrom() || pickTime > businessTime.getBusinessTo()) {
////                    continue;
////                }
////            } else {
////                continue;
////            }
//            List<BusinessTime> businessTimeTmpList = businessTimeList.stream()
//                    .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());
//            if (!checkWeekAndBusinessTime(businessTimeTmpList, pickUpTime)) {
//                continue;
//            }
//            // 取车夜间服务费
//            List<NightService> tmpNightList = nightList.stream().filter(
//                    e -> e.getStoreId().equals(pickupStoreId) &&
//                            pickTime >= e.getBusinessFrom() && pickTime <= e.getBusinessTo()
//            ).collect(Collectors.toList());
//            for (NightService night : tmpNightList) {
//                boolean weekFlg = night.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
//                if (weekFlg) {
//                    dto = setStoreServiceItem(night.getId(), ServiceFeeTypeEnum.STORE_PICKUP_NIGHT_SERVICE.getServiceCode(),
//                            ServiceFeeTypeEnum.STORE_PICKUP_NIGHT_SERVICE.getServiceName(),
//                            OrderTypeEnum.NIGHT_PICKUP_SERVICE.getType(), night.getFee(),
//                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
//                    dtoList.add(dto);
//                    break;
//                }
//            }
//            // 还车
//            weekIndex = getWeekIndex(returnTime);
//            if (weekIndex == 0) weekIndex = 7;
//            int retTime = Integer.parseInt(dateFormat.format(returnTime));
//            // 还车营业时间check
////            tempBusinessTimeList = fiterDateBusiTimeList(businessTimeList, pickupStoreId, weekIndex);
////            if (CollectionUtils.isNotEmpty(tempBusinessTimeList)) {
////                BusinessTime businessTime = tempBusinessTimeList.get(0);
////                if (retTime < businessTime.getBusinessFrom() || retTime > businessTime.getBusinessTo()) {
////                    continue;
////                }
////            } else {
////                continue;
////            }
//            businessTimeTmpList = businessTimeList.stream()
//                    .filter(e -> e.getStoreId().equals(returneStoreId)).collect(Collectors.toList());
//            if (!checkWeekAndBusinessTime(businessTimeTmpList, returnTime)) {
//                continue;
//            }
//            // 还车夜间服务费
//            tmpNightList = nightList.stream().filter(
//                    e -> e.getStoreId().equals(returneStoreId) &&
//                            retTime >= e.getBusinessFrom() && retTime <= e.getBusinessTo()
//            ).collect(Collectors.toList());
//            for (NightService night : tmpNightList) {
//                boolean weekFlg = night.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
//                if (weekFlg) {
//                    dto = setStoreServiceItem(night.getId(), ServiceFeeTypeEnum.STORE_RETURN_NIGHT_SERVICE.getServiceCode(),
//                            ServiceFeeTypeEnum.STORE_RETURN_NIGHT_SERVICE.getServiceName(),
//                            OrderTypeEnum.NIGHT_RETURN_SERVICE.getType(), night.getFee(),
//                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
//                    dtoList.add(dto);
//                    break;
//                }
//            }
//            // 设置是否全天营业
//            pairUniDTO.getPickUpStore().setAllDay(getIsAllDate(businessTimeList, pairUniDTO.getPickUpStore().getId()));
//            pairUniDTO.getReturnStore().setAllDay(getIsAllDate(businessTimeList, pairUniDTO.getReturnStore().getId()));
//            // 返回
//            pairUniDTO.setData(dtoList);
//            retList.add(pairUniDTO);
//        }
////        log.info("==ThirdStoreService.selectStoreService return={}", JSON.toJSONString(retList));
////        log.info("==ThirdStoreService.selectStoreService execTime=" + (System.currentTimeMillis() - startExceTime));
//        //Span.current().setStatus(StatusCode.OK);
//        return retList;
//    }

    @Override
    public List<StorePairUniDTO<List<ServiceItemDTO>>> selectStoreServiceV2(Long merchantId, Long paramChannelId, List<StorePairDTO> pairDTOList,
                                                                            PointDTO pickUpGis, PointDTO returnGis,
                                                                            Date pickUpTime, Date returnTime,
                                                                            List<XjStoreInfoChannelVO> storeList, List<XjBusinessTimeVO> businessTimeList) {
        Long channelId = CommStore.getParentChannelId(paramChannelId);
        long t = System.currentTimeMillis();
        List<Long> pickupStoreIds = pairDTOList.stream().map(e -> e.getPickUpStore().getId()).collect(Collectors.toList());
        List<Long> returnStoreIds = pairDTOList.stream().map(e -> e.getReturnStore().getId()).collect(Collectors.toList());
        List<Long> allStoreIds = new ArrayList<>();
        Stream.of(pickupStoreIds, returnStoreIds).forEach(allStoreIds::addAll);
        allStoreIds = allStoreIds.stream().distinct().collect(Collectors.toList());
        if (storeList == null) {
            storeList = filterStoreChannelByChannelV2(allStoreIds, channelId);
        }
        // 已开通的渠道门店
        List<Long> openStoreChannelIds = getOpenChannelStoreIds(channelId, storeList);
        // 查询服务圈
        List<XjServicePickupVO> pickupList = filterPickupByChannelV2(allStoreIds, openStoreChannelIds, channelId, true);
        Map<Long, XjServicePickupVO> pickupMap = pickupList.stream().collect(Collectors.toMap(XjServicePickupVO::getId, vo -> vo));
        Span.current().setAttribute("　ac查询查询服务圈耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        t = System.currentTimeMillis();
        // 营业时间
        if (businessTimeList == null) {
            businessTimeList = filterBusinessTimeByChannelV2(allStoreIds, openStoreChannelIds, channelId);
        }
        List<Long> businessTimeIds = businessTimeList.stream().filter(e -> YesOrNoEnum.isYes(e.getNightService())).map(e -> e.getId()).collect(
                Collectors.toList());
        Span.current().setAttribute("　ac查询营业时间耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        // 夜间服务
        List<XjNightServiceVO> nightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessTimeIds)) {
            nightList = filterNightByChannelV2(allStoreIds, businessTimeIds, openStoreChannelIds, channelId);
        }
        Span.current().setAttribute("　ac查询夜间服务耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        // 数据组装
        List<StorePairUniDTO<List<ServiceItemDTO>>> retList = new ArrayList<>();
        for (StorePairDTO storePairDTO : pairDTOList) {
            StorePairUniDTO<List<ServiceItemDTO>> pairUniDTO = new StorePairUniDTO<>();
            pairUniDTO.setId(storePairDTO.getId());
            BeanUtils.copyProperties(storePairDTO, pairUniDTO);
            Long pickupStoreId = pairUniDTO.getPickUpStore().getId();
            Long returnStoreId = pairUniDTO.getReturnStore().getId();
            // 服务项组装
            List<ServiceItemDTO> dtoList = new ArrayList<>();
            /**
             * 门店取车服务
             */
            addCircleServiceV2(true, storePairDTO.getPickUpStore().getServiceCircleIdList(), pickupMap, dtoList, paramChannelId);
            /**
             * 门店还车服务
             */
            addCircleServiceV2(false, storePairDTO.getReturnStore().getServiceCircleIdList(), pickupMap, dtoList, paramChannelId);
            ServiceItemDTO dto;
            /**
             * 夜间服务费
             */
            int weekIndex = getWeekIndex(pickUpTime);
            if (weekIndex == 0) weekIndex = 7;
            DateFormat dateFormat = new SimpleDateFormat("HHmm");
            // 取车
            int pickTime = Integer.parseInt(dateFormat.format(pickUpTime));
            // 取车营业时间check
            List<XjBusinessTimeVO> businessTimeTmpList = businessTimeList.stream()
                    .filter(e -> e.getStoreId().equals(pickupStoreId)).collect(Collectors.toList());
            if (!checkWeekAndBusinessTimeV2(businessTimeTmpList, pickUpTime)) {
                continue;
            }
            // 取车夜间服务费
            List<XjNightServiceVO> tmpNightList = nightList.stream().filter(
                    e -> e.getStoreId().equals(pickupStoreId) &&
                            pickTime >= e.getBusinessFrom() && pickTime <= e.getBusinessTo()
            ).collect(Collectors.toList());
            for (XjNightServiceVO night : tmpNightList) {
                boolean weekFlg = night.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
                if (weekFlg) {
                    dto = setStoreServiceItem(night.getId(), ServiceFeeTypeEnum.STORE_PICKUP_NIGHT_SERVICE.getServiceCode(),
                            ServiceFeeTypeEnum.STORE_PICKUP_NIGHT_SERVICE.getServiceName(),
                            OrderTypeEnum.NIGHT_PICKUP_SERVICE.getType(), night.getFee(),
                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
                    dtoList.add(dto);
                    break;
                }
            }
            // 还车
            weekIndex = getWeekIndex(returnTime);
            if (weekIndex == 0) weekIndex = 7;
            int retTime = Integer.parseInt(dateFormat.format(returnTime));
            // 还车营业时间check
            businessTimeTmpList = businessTimeList.stream()
                    .filter(e -> e.getStoreId().equals(returnStoreId)).collect(Collectors.toList());
            if (!checkWeekAndBusinessTimeV2(businessTimeTmpList, returnTime)) {
                continue;
            }
            // 还车夜间服务费
            tmpNightList = nightList.stream().filter(
                    e -> e.getStoreId().equals(returnStoreId) &&
                            retTime >= e.getBusinessFrom() && retTime <= e.getBusinessTo()
            ).collect(Collectors.toList());
            for (XjNightServiceVO night : tmpNightList) {
                boolean weekFlg = night.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1");
                if (weekFlg) {
                    dto = setStoreServiceItem(night.getId(), ServiceFeeTypeEnum.STORE_RETURN_NIGHT_SERVICE.getServiceCode(),
                            ServiceFeeTypeEnum.STORE_RETURN_NIGHT_SERVICE.getServiceName(),
                            OrderTypeEnum.NIGHT_RETURN_SERVICE.getType(), night.getFee(),
                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
                    dtoList.add(dto);
                    break;
                }
            }
            // 设置是否全天营业
            pairUniDTO.getPickUpStore().setAllDay(getIsAllDateV2(businessTimeList, pairUniDTO.getPickUpStore().getId()));
            pairUniDTO.getReturnStore().setAllDay(getIsAllDateV2(businessTimeList, pairUniDTO.getReturnStore().getId()));
            // 返回
            pairUniDTO.setData(dtoList);
            retList.add(pairUniDTO);
        }
        Span.current().setAttribute("　ac其他处理耗时", System.currentTimeMillis() - t);
        return retList;
    }

    @Override
    public List<StorePairVehicleModelUniDTO> selectVehicleModelUni(Long merchantId, Long paramChannelId, List<StorePairAndVehicleModelDTO> pairDTOList,
                                                                   Date pickUpTime, Date returnTime) {

        long t = System.currentTimeMillis();
        List<StorePairDTO> pairDTOParamList = Lists.newArrayList();
        Set<Long> allStoreIds = Sets.newHashSet();
        for (StorePairAndVehicleModelDTO storePairDTO : pairDTOList) {
            Long pickUpStoreId = storePairDTO.getPickUpStoreId();
            Long returnStoreId = storePairDTO.getReturnStoreId();
            allStoreIds.add(pickUpStoreId);
            allStoreIds.add(returnStoreId);
            StorePairDTO pairDTO = new StorePairDTO();
            StoreCircleIdDTO pickUpstoreCircleIdDTO = new StoreCircleIdDTO();
            pickUpstoreCircleIdDTO.setId(pickUpStoreId);
            StoreCircleIdDTO returnStoreCircleIdDTO = new StoreCircleIdDTO();
            returnStoreCircleIdDTO.setId(returnStoreId);
            pairDTO.setPickUpStore(pickUpstoreCircleIdDTO);
            pairDTO.setReturnStore(returnStoreCircleIdDTO);
            pairDTOParamList.add(pairDTO);
        }
        boolean distanceFlg = false;
        for (StorePairAndVehicleModelDTO storePairDTO : pairDTOList) {
            if (!storePairDTO.getPickUpStoreId().equals(storePairDTO.getReturnStoreId())) {
                distanceFlg = true;
                break;
            }
        }
        List<StoreDistance> distanceList = new ArrayList<>();
        if (distanceFlg) {
            distanceList = getStoreDistance(merchantId);
            Span.current().setAttribute("　ac查询门店对距离", System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }

        Map<String, List<AllopatryRuleDTO>> allopatryRuleDTOMap = Maps.newHashMap();
        if (BooleanUtils.isTrue(distanceFlg)){
            // 数据结构: (取方id - 还方id) ->> 异门店规则列表
            allopatryRuleDTOMap = allopatryRuleService.selectAllopatryRule(merchantId, paramChannelId, allStoreIds, pairDTOParamList, returnTime)
                    .stream()
                    .collect(Collectors.groupingBy(dto ->
                            String.format(AllopatryRuleConstant.RULE_FORMAT, dto.getPickUpStoreId(), dto.getReturnStoreId())));
        }


        List<StorePairVehicleModelUniDTO> result = Lists.newArrayList();
        for (StorePairAndVehicleModelDTO storePairAndVehicleModelDTO : pairDTOList) {
            Long pickUpStoreId = storePairAndVehicleModelDTO.getPickUpStoreId();
            Long returnStoreId = storePairAndVehicleModelDTO.getReturnStoreId();
            Long id = storePairAndVehicleModelDTO.getId();

            List<Long> storeVehicleModels = storePairAndVehicleModelDTO.getVehicleModelIds();
            // 同门店不触发规则
            if (pickUpStoreId.equals(returnStoreId)) {
                continue;
            }
            if (MapUtils.isEmpty(allopatryRuleDTOMap)) {
                continue;
            }
            String ruleKey = String.format(AllopatryRuleConstant.RULE_FORMAT, pickUpStoreId, returnStoreId);
            List<AllopatryRuleDTO> allopatryRuleDTOList = allopatryRuleDTOMap.get(ruleKey);
            if (CollectionUtils.isEmpty(allopatryRuleDTOList)) {
                continue;
            }

            Comparator<AllopatryRuleDTO> modelComparator = (obj1, obj2) -> {
                List<Long> list1 = obj1.getVehicleModelIds();
                List<Long> list2 = obj2.getVehicleModelIds();

                Long number1 = CollectionUtils.isEmpty(list1) ? 0L : list1.get(0);
                Long number2 = CollectionUtils.isEmpty(list2) ? 0L : list2.get(0);
                // 按降序排序
                return number2.compareTo(number1);
            };

            Comparator<AllopatryRuleDTO> channelComparator = (obj1, obj2) -> {
                List<Long> list1 = obj1.getChannelIds();
                List<Long> list2 = obj2.getChannelIds();

                Long number1 = CollectionUtils.isEmpty(list1) ? 0L : list1.get(0);
                Long number2 = CollectionUtils.isEmpty(list2) ? 0L : list2.get(0);
                // 按降序排序
                return number2.compareTo(number1);
            };

            Comparator<AllopatryRuleDTO> permanentTagComparator = (obj1, obj2) -> {
                Integer permanentTag1 = obj1.getPermanentTag();
                Integer permanentTag2 = obj2.getPermanentTag();

                Long number1 = Objects.isNull(permanentTag1) ? 0L : permanentTag1;
                Long number2 = Objects.isNull(permanentTag2) ? 0L : permanentTag2;
                // 按升序序排序
                return number1.compareTo(number2);
            };
            allopatryRuleDTOList.sort(Comparator.comparing(AllopatryRuleDTO::getRuleType)
                    .thenComparing(permanentTagComparator)
                    .thenComparing(channelComparator)
                    .thenComparing(modelComparator));
            // 根据高德计算最短路线
            String storeKey = getStoreKey(pickUpStoreId, returnStoreId);
            List<StoreDistance> tmpDistance = distanceList.stream().filter(e -> e.getStoreKeys().equals(storeKey)).collect(Collectors.toList());
            Integer distance = 0;
            if (CollectionUtils.isNotEmpty(tmpDistance)) {
                distance = new BigDecimal(tmpDistance.get(0).getDistance()).divide(new BigDecimal("1000")).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
            }
            Map<Long, StorePairVehicleModelUniDTO> modelUniDTOMap = Maps.newHashMap();
            // 服务项组装，规则以排序，全部车型会在最后面。部分车型优先级最高
            for (AllopatryRuleDTO allopatryRuleDTO : allopatryRuleDTOList) {
                // 异门店车型服务项数据处理
                List<Long> allopatryRuleModelIds = allopatryRuleDTO.getVehicleModelIds();
                for (Long modelId : storeVehicleModels) {
                    List<ServiceItemDTO> storeModelItems = new ArrayList<>();
                    // 1.规则支持全部车型 2.传过来的车型在规则内才会有异门店服务项
                    if (!allopatryRuleModelIds.contains(0L) && !allopatryRuleModelIds.contains(modelId)) {
                        continue;
                    }
                    StorePairVehicleModelUniDTO vehicleModelAbbrDTO = new StorePairVehicleModelUniDTO();
                    vehicleModelAbbrDTO.setId(id);
                    vehicleModelAbbrDTO.setPickUpStoreId(pickUpStoreId);
                    vehicleModelAbbrDTO.setReturnStoreId(returnStoreId);
                    vehicleModelAbbrDTO.setVehicleModelId(modelId);
                    // 向上取整到元
                    Integer diffPrice = (Objects.nonNull(allopatryRuleDTO.getFixedPrice()) && allopatryRuleDTO.getFixedPrice() >= 0) ? allopatryRuleDTO.getFixedPrice() : (allopatryRuleDTO.getPriceKm() * distance);
                    diffPrice = diffPrice % 100 == 0 ? diffPrice : ((int) (diffPrice / 100) + 1) * 100;
                    ServiceItemDTO dto = setStoreServiceItem(allopatryRuleDTO.getId(),
                            ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceCode(),
                            ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceName(),
                            OrderTypeEnum.PICK_SERVICE.getType(), diffPrice,
                            YesOrNoEnum.YES.getValue(), YesOrNoEnum.YES.getValue());
                    storeModelItems.add(dto);
                    vehicleModelAbbrDTO.setVehicleModelItems(storeModelItems);
                    StorePairVehicleModelUniDTO uniDTO = modelUniDTOMap.get(modelId);
                    if (Objects.isNull(uniDTO)) {
                        modelUniDTOMap.put(modelId, vehicleModelAbbrDTO);
                        result.add(vehicleModelAbbrDTO);
                    }
                }
            }
        }
        Span.current().setAttribute("　ac异门店车型数据处理", System.currentTimeMillis() - t);
        return result;
    }

    private String getStoreKey(long storeId1, Long storeId2) {
        String key = storeId1 + ":" + storeId2;
        if (storeId1 > storeId2) {
            key = storeId2 + ":" + storeId1;
        }
        return key;
    }

    private List<BusinessTime>  fiterDateBusiTimeList(List<BusinessTime> list, Long storeId, int weekIndex) {
        return list.stream().filter(e -> e.getStoreId().equals(storeId) &&
                e.getBusinessPeriod().substring(weekIndex - 1, weekIndex).equals("1")).collect(Collectors.toList());
    }

    private void addCircleService(Boolean isPicck, List<Long> circleIds, Map<Long, ServicePickup> pickupMap, List<ServiceItemDTO> dtoList) {
        ServiceItemDTO arriveService = null;
        ServiceItemDTO tranceService = null;
        ServiceItemDTO circleService = null;
        if (CollectionUtils.isEmpty(circleIds)) {
            throw new BizException("circleIds cannot be empty");
        }
        for (Long serviceType : circleIds) {
            if (serviceType == null || serviceType == -1) {
                // 到店
                String code = ServiceFeeTypeEnum.STORE_PICKUIP_ARRIVE_SERVICE.getServiceCode();
                String name = ServiceFeeTypeEnum.STORE_PICKUIP_ARRIVE_SERVICE.getServiceName();
                if (!isPicck) {
                    code = ServiceFeeTypeEnum.STORE_RETURN_ARRIVE_SERVICE.getServiceCode();
                    name = ServiceFeeTypeEnum.STORE_RETURN_ARRIVE_SERVICE.getServiceName();
                }
                arriveService = setStoreServiceItem(serviceType, code, name,
                        OrderTypeEnum.PICK_SERVICE.getType(), 0,
                        YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
            } else {
                ServicePickup pickup = pickupMap.get(serviceType);
                if (pickup != null) {
                    String code = ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceCode();
                    String name = ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceName();
                    if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                        code = ServiceFeeTypeEnum.STORE_PICKUIP_TRANCS_SERVICE.getServiceCode();
                        name = ServiceFeeTypeEnum.STORE_PICKUIP_TRANCS_SERVICE.getServiceName();
                    }
                    if (!isPicck) {
                        code = ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceCode();
                        name = ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceName();
                        if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                            code = ServiceFeeTypeEnum.STORE_RETURN_TRANCS_SERVICE.getServiceCode();
                            name = ServiceFeeTypeEnum.STORE_RETURN_TRANCS_SERVICE.getServiceName();
                        }
                    }
                    int fee = YesOrNoEnum.isYes(pickup.getFeeType()) ? pickup.getFee() : 0;
                    if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                        tranceService = setStoreServiceItem(serviceType, code, name,
                                OrderTypeEnum.PICK_SERVICE.getType(), fee,
                                YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
                    } else {
                        circleService = setStoreServiceItem(serviceType, code, name,
                                OrderTypeEnum.PICK_SERVICE.getType(), fee,
                                YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
                    }
                }
            }
        }
        boolean required = false;
        if (tranceService != null) {
            if (!required) {
                tranceService.setRequired(YesOrNoEnum.YES.getValue());
                required = true;
            }
            dtoList.add(tranceService);
        }
        if (arriveService != null) {
            if (!required) {
                arriveService.setRequired(YesOrNoEnum.YES.getValue());
                required = true;
            }
            dtoList.add(arriveService);
        }
        if (circleService != null) {
            if (!required) {
                circleService.setRequired(YesOrNoEnum.YES.getValue());
                required = true;
            }
            dtoList.add(circleService);
        }
    }

    private void addCircleServiceV2(Boolean isPick, List<Long> circleIds, Map<Long, XjServicePickupVO> pickupMap, List<ServiceItemDTO> dtoList, Long channelId) {
        ServiceItemDTO arriveService = null;
        ServiceItemDTO tranceService = null;
        ServiceItemDTO circleService = null;
        List<ServiceItemDTO> circleServiceList = new ArrayList<>();
        if (CollectionUtils.isEmpty(circleIds)) {
            throw new BizException("circleIds cannot be empty");
        }
        for (Long serviceType : circleIds) {
            if (serviceType == null || serviceType == -1) {
                // 到店
                String code = ServiceFeeTypeEnum.STORE_PICKUIP_ARRIVE_SERVICE.getServiceCode();
                String name = ServiceFeeTypeEnum.STORE_PICKUIP_ARRIVE_SERVICE.getServiceName();
                if (!isPick) {
                    code = ServiceFeeTypeEnum.STORE_RETURN_ARRIVE_SERVICE.getServiceCode();
                    name = ServiceFeeTypeEnum.STORE_RETURN_ARRIVE_SERVICE.getServiceName();
                }
                arriveService = setStoreServiceItem(serviceType, code, name,
                        OrderTypeEnum.PICK_SERVICE.getType(), 0,
                        YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
            } else {
                XjServicePickupVO pickup = pickupMap.get(serviceType);
                if (pickup != null) {
                    String code = ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceCode();
                    String name = ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceName();
                    if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                        code = ServiceFeeTypeEnum.STORE_PICKUIP_TRANCS_SERVICE.getServiceCode();
                        name = ServiceFeeTypeEnum.STORE_PICKUIP_TRANCS_SERVICE.getServiceName();
                    }
                    if (!isPick) {
                        code = ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceCode();
                        name = ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceName();
                        if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                            code = ServiceFeeTypeEnum.STORE_RETURN_TRANCS_SERVICE.getServiceCode();
                            name = ServiceFeeTypeEnum.STORE_RETURN_TRANCS_SERVICE.getServiceName();
                        }
                    }
                    int fee = YesOrNoEnum.isYes(pickup.getFeeType()) ? pickup.getFee() : 0;
                    if (pickup.getPickupType().equals(PickupTypeEnum.FREE_SHUTTLE.getValue())) {
                        tranceService = setStoreServiceItem(serviceType, code, name,
                                OrderTypeEnum.PICK_SERVICE.getType(), fee,
                                YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
                    } else {
//                        circleService = setStoreServiceItem(serviceType, code, name,
//                                OrderTypeEnum.PICK_SERVICE.getType(), fee,
//                                YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue());
                        circleServiceList.add(setStoreServiceItem(serviceType, code, name,
                                OrderTypeEnum.PICK_SERVICE.getType(), fee,
                                YesOrNoEnum.YES.getValue(), YesOrNoEnum.NO.getValue()));
                    }
                }
            }
        }
        // 如上门命中 3个圈，费用分别为 0，100，200; 返回最优的一个
        if (CollectionUtils.isNotEmpty(circleServiceList)) {
            circleServiceList = circleServiceList.stream().sorted(Comparator.comparing(ServiceItemDTO::getPrice)).collect(Collectors.toList());
            circleService = circleServiceList.get(0);
        }
        boolean required = false;
        if (channelId != null && channelId == 3) {
            if (circleService != null) {
                if (!required) {
                    circleService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(circleService);
            }
            if (tranceService != null) {
                if (!required) {
                    tranceService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(tranceService);
            }
            if (arriveService != null) {
                if (!required) {
                    arriveService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(arriveService);
            }
        } else {
            if (tranceService != null) {
                if (!required) {
                    tranceService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(tranceService);
            }
            if (arriveService != null) {
                if (!required) {
                    arriveService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(arriveService);
            }
            if (circleService != null) {
                if (!required) {
                    circleService.setRequired(YesOrNoEnum.YES.getValue());
                    required = true;
                }
                dtoList.add(circleService);
            }
        }
    }

    private Integer getIsAllDate(List<BusinessTime> businessTimeList, Long storeId) {
        List<BusinessTime> tempList = businessTimeList.stream().filter(e -> e.getStoreId().equals(storeId)).collect(
                Collectors.toList());
        int chkPeriod = 0;
        for (BusinessTime time : tempList) {
            if (time.getBusinessFrom() == 0 && time.getBusinessTo() == 2359) {
            } else {
                return 0;
            }
            chkPeriod = chkPeriod + Integer.valueOf(time.getBusinessPeriod());
        }
        String chkPeriodStr = String.valueOf(chkPeriod);
        for (int i = 0; i < chkPeriodStr.length(); i++) {
            if (chkPeriodStr.substring(i, i + 1).equals("0")) {
                return 0;
            }
        }
        return 1;
    }

    private Integer getIsAllDateV2(List<XjBusinessTimeVO> businessTimeList, Long storeId) {
        List<XjBusinessTimeVO> tempList = businessTimeList.stream().filter(e -> e.getStoreId().equals(storeId)).collect(
                Collectors.toList());
        int chkPeriod = 0;
        for (XjBusinessTimeVO time : tempList) {
            if (time.getBusinessFrom() == 0 && time.getBusinessTo() == 2359) {
            } else {
                return 0;
            }
            chkPeriod = chkPeriod + Integer.valueOf(time.getBusinessPeriod());
        }
        String chkPeriodStr = String.valueOf(chkPeriod);
        for (int i = 0; i < chkPeriodStr.length(); i++) {
            if (chkPeriodStr.substring(i, i + 1).equals("0")) {
                return 0;
            }
        }
        return 1;
    }

    private int getWeekIndex(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK) - 1;
    }

    private ServiceItemDTO setStoreServiceItem(Long id, String code, String codeName, Byte type, Integer price, Byte oncharge, Byte required) {
        ServiceItemDTO dto = new ServiceItemDTO();
        dto.setId(id);
        dto.setCode(code);
        dto.setName(codeName);
        dto.setUnit("次");
        dto.setType(type);
        dto.setPrice(price);
        dto.setOnCharge(oncharge);
        dto.setRequired(required);
        dto.setDescription(StringUtils.EMPTY);
        return dto;
    }

    @Override
    public List<ServiceItemDTO> selectStoreService(Long merchantId, Long channelId, StorePairDTO pairDTO, PointDTO pickUpGis, PointDTO returnGis, Date pickUpTime, Date returnTime) {
        List<StorePairUniDTO<List<ServiceItemDTO>>> storePairUniDTOS =
                this.selectStoreServiceV2(merchantId, channelId, Lists.newArrayList(pairDTO), pickUpGis, returnGis, pickUpTime, returnTime, null, null);
        if (null == storePairUniDTOS || storePairUniDTOS.isEmpty()) {
            return new ArrayList<>();
        }
        return storePairUniDTOS.get(0).getData();
    }

    @Override
    public StorePairVehicleModelUniDTO selectVehicleModelUni(Long merchantId, Long paramChannelId, StorePairAndVehicleModelDTO pairDTO, Date pickUpTime, Date returnTime) {
        List<StorePairVehicleModelUniDTO> list = this.selectVehicleModelUni(merchantId, paramChannelId, Lists.newArrayList(pairDTO), pickUpTime, returnTime);
        return null == list || list.isEmpty() ? null : list.get(0);
    }

    /**
     * 高德最优路线
     * https://lbs.amap.com/api/webservice/guide/api/newroute 高德api说明
     * 返回2点距离（单位公里）
     *
     * @param pickUpGis, returnGis
     */
    @Override
    public int caseDistance(PointDTO pickUpGis, PointDTO returnGis) {
        GaodeDriverParam param = new GaodeDriverParam();
        param.setKey(gaodeWebKey);
        param.setOrigin(pickUpGis.getLongitude() + "," + pickUpGis.getLatitude());
        param.setDestination(returnGis.getLongitude() + "," + returnGis.getLatitude());
        try {
            String url = gaodeWebUrl + "?key=" + gaodeWebKey +
                    "&origin=" + pickUpGis.getLongitude() + "," + pickUpGis.getLatitude() +
                    "&destination=" + returnGis.getLongitude() + "," + returnGis.getLatitude();
            String response = HttpUtil.get(url);
            if (response == null) {
                log.error("调用高德最优路线异常" + url);
                throw new BizException("调用高德最优路线异常");
            }
            String responseJson = response.toString(); //new String(response.body().bytes(), "UTF-8");
            GaodeDriverVo vo = JSON.parseObject(responseJson, GaodeDriverVo.class);
            if (vo.getStatus().equals("1")) {
                List<GaodeDriverVo.Paths> paths = vo.getRoute().getPaths();
                int i = 1;
                for (GaodeDriverVo.Paths path : paths) {
                    log.info("路线" + i + "距离=" + path.getDistance());
                    i++;
                }
                if (CollectionUtils.isNotEmpty(paths)) {
                    return new BigDecimal(Double.parseDouble(paths.get(0).getDistance()) / 1000).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                }
                log.error("未获取到高德路线方案" + url);
                throw new BizException("未获取到高德路线方案");
            } else {
                log.error("高德最优路线异常" + url);
                throw new BizException("高德最优路线异常:" + vo.getInfo());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("高德最优路线异常:" + e.getMessage());
        }
    }

    @Override
    public List<StoreDistanceVo> selectByDistance(Long cityId, Long merchantId, Long storeId, Long channelId, Double longitude, Double latitude, Integer distance) {
        return storeMapper.selectByDistance(cityId, merchantId, storeId, channelId, longitude, latitude, distance);
    }

    public static void main(String[] a) throws Exception {
//        param.setOrigin("120.13,30.29");
//        param.setDestination("120.76,29.48");
//        PointDTO pickUpGis = new PointDTO();
//        pickUpGis.setLongitude(120.13);
//        pickUpGis.setLatitude(30.29);
//        PointDTO returnGis = new PointDTO();
//        returnGis.setLongitude(120.76);
//        returnGis.setLatitude(29.48);
//        int km = caseDistance(pickUpGis, returnGis);
//        System.out.println(km);
    }

    @Override
    public List<ThirdStoreInfosVo> selectStoreInfos(StoreThirdParam param) {
        List<ThirdStoreInfosVo> retList = new ArrayList<>();
        StoreInfoChannelExample example = new StoreInfoChannelExample();
        StoreInfoChannelExample.Criteria criteria = example.createCriteria();
        if (param.getMerchantId() != null) {
            criteria.andMerchantIdEqualTo(param.getMerchantId());
        }
        if (CollectionUtils.isNotEmpty(param.getStoreIds())) {
            criteria.andStoreIdIn(param.getStoreIds());
        }
        if (param.getStoreId() != null) {
            criteria.andStoreIdEqualTo(param.getStoreId());
        }
        Long channelId = (long) OrderSourceEnum.OFFLINE.getSource();
        List<Long> channelIds = new ArrayList<>();
        channelIds.add(channelId);
        if (param.getChannelId() != null) {
            channelId = param.getChannelId();
            channelIds.add(param.getChannelId());
        }
        criteria.andChannelIdIn(channelIds);
        if (param.getStoreStatus() != null) {
            criteria.andStoreStatusEqualTo(param.getStoreStatus());
        }
        criteria.andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        ;
        List<StoreInfoChannel> storeChannelList = storeInfoChannelMapper.selectByExample(example);
        storeChannelList = filterStoreChannelByChannel1(storeChannelList, channelId);

        List<Long> allStoreIds = storeChannelList.stream().map(StoreInfoChannel::getStoreId).distinct().collect(Collectors.toList());
        List<Long> channelStores = getChannelExiteStore(channelId, storeChannelList);
        if (CollectionUtils.isEmpty(allStoreIds)) {
            return retList;
        }
        // 门店夜间服务费、门店指引图片表、门店夜间服务费、门店上门取送车服务、门店图片、门店联系人、门店取还车指引、门店信息

        // 门店基础
        StoreInfoExample storeInfoExample = new StoreInfoExample();
        StoreInfoExample.Criteria storeInfoCriteria = storeInfoExample.createCriteria();
        storeInfoCriteria.andIdIn(allStoreIds);
        if (param.getMerchantId() != null) {
            storeInfoCriteria.andMerchantIdEqualTo(param.getMerchantId());
        }
        if (CollectionUtils.isNotEmpty(param.getCityIds())) {
            storeInfoCriteria.andCityIdIn(param.getCityIds());
        }
        List<StoreInfo> storeInfoList = storeInfoMapper.selectByExample(storeInfoExample);
        if (CollectionUtils.isEmpty(storeInfoList)) {
            return retList;
        }

        allStoreIds = storeInfoList.stream().map(StoreInfo::getId).distinct().collect(Collectors.toList());

        // 营业时间
        List<BusinessTime> businessTimeList = filterBusinessTimeByChannel(allStoreIds, channelStores, channelId);

        // 夜间服务
        List<Long> businessTimeIds = businessTimeList.stream()
                .filter(e -> YesOrNoEnum.isYes(e.getNightService())).map(e -> e.getId()).collect(Collectors.toList());
        List<NightService> nightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessTimeIds)) {
            nightList = filterNightByChannel(allStoreIds, businessTimeIds, channelStores, channelId);
        }

        // 休息时间
        List<RestTime> restTimeList = filterRestTimeByChannel(allStoreIds, channelStores, channelId);

        // 联系电话
        List<StoreContact> storeContactList = filterContactByChannel(allStoreIds, channelStores, channelId);

        List<StoreHourlyChargeVo> chargeList = filterChargeByChannel(allStoreIds, channelId);

        // 门店图片 目前无数据
//        StoreAttExample storeAttExample = new StoreAttExample();
//        storeAttExample.createCriteria().andStoreIdIn(allStoreIds)
//            .andChannelIdIn(Arrays.asList(OrderSourceEnum.OFFLINE.getSource().longValue(), channelId))
//            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
//        List<StoreAtt> storeAttList = storeAttMapper.selectByExample(storeAttExample);
//        storeAttList = filterStoreAttByChannel(channelStores, storeAttList, channelId);
        List<StoreAtt> storeAttList = new ArrayList<>();

        // 门店指引
        List<StoreGuide> storeGuideList = filterGuideByChannel(allStoreIds);

        // 门店指引图片
        List<GuidePic> guidePicList = filterGuidePicByChannel(allStoreIds);

        // 查询服务圈
        List<ServicePickup> pickupList = filterPickupByChannel(allStoreIds, channelStores, channelId, false);

        // 查询服务圈休息时间
        List<ServiceRestTime> serviceRestTimeList = filterServiceRestTimeByChannel(pickupList);

        ThirdIdRelationExample idExample = new ThirdIdRelationExample();
        idExample.createCriteria().andStoreIdIn(allStoreIds)
                .andTypeEqualTo(IdRelationEnum.CICLE.getType())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdIdRelation> circles = thirdIdRelationMapper.selectByExample(idExample);
        Map<Long, String> cMap = circles.stream().collect(Collectors.toMap(e -> e.getSaasId(), e -> e.getThirdId(), (v1, v2) -> v1));

        for (Long storeId : allStoreIds) {
            ThirdStoreInfosVo store = new ThirdStoreInfosVo();
            // 门店基础
            ThirdStoreInfoVo storeInfoVo = new ThirdStoreInfoVo();
            List<StoreInfo> tmpStoreInfoList = storeInfoList.stream()
                    .filter(e -> e.getId().equals(storeId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tmpStoreInfoList)) {
                BeanUtils.copyProperties(tmpStoreInfoList.get(0), storeInfoVo);
                if (YesOrNoEnum.isYes(storeInfoVo.getDeleted())) {
                    continue;
                }
                StoreInfo gisInfo = storeInfoMapper.selectByPrimaryKey(storeId);
                setGis(gisInfo.getGis(), storeInfoVo);
            }
            // 门店渠道
            List<StoreInfoChannel> tmpStoreInfoChannelList = storeChannelList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tmpStoreInfoChannelList)) {
                StoreInfoChannel bean = tmpStoreInfoChannelList.get(0);
                BeanUtils.copyProperties(bean, storeInfoVo);
                storeInfoVo.setId(bean.getStoreId());
                StoreInfoChannelExtraVo storeInfoChannelExtraVo = StoreInfoChannelExtraVo.fromJson(bean.getExtra());
                storeInfoVo.setDealership(storeInfoChannelExtraVo.getDealership());
            }
            store.setStoreInfoVo(storeInfoVo);
            // 营业时间
            List<ThirdBusinessTimeVo> businessTimeVoList = new ArrayList<>();
            List<BusinessTime> tmpBusinessTimeList = businessTimeList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (BusinessTime bean : tmpBusinessTimeList) {
                ThirdBusinessTimeVo vo = new ThirdBusinessTimeVo();
                BeanUtils.copyProperties(bean, vo);
                businessTimeVoList.add(vo);
            }
            store.setBusinessTimeList(businessTimeVoList);
            // 夜间服务
            List<ThirdNightServiceVo> nightServiceVoList = new ArrayList<>();
            List<NightService> tmpNightServiceList = nightList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (NightService bean : tmpNightServiceList) {
                ThirdNightServiceVo vo = new ThirdNightServiceVo();
                BeanUtils.copyProperties(bean, vo);
                nightServiceVoList.add(vo);
            }
            store.setNightServiceList(nightServiceVoList);
            // 休息时间
            List<ThirdRestTimeVo> restTimeVoList = new ArrayList<>();
            List<RestTime> tmpRestTimeList = restTimeList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (RestTime bean : tmpRestTimeList) {
                ThirdRestTimeVo vo = new ThirdRestTimeVo();
                BeanUtils.copyProperties(bean, vo);
                restTimeVoList.add(vo);
            }
            store.setRestTimeList(restTimeVoList);
            // 联系人
            List<ThirdStoreContactVo> storeContactVoList = new ArrayList<>();
            List<StoreContact> tmpStoreContactList = storeContactList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (StoreContact bean : tmpStoreContactList) {
                ThirdStoreContactVo vo = new ThirdStoreContactVo();
                BeanUtils.copyProperties(bean, vo);
                try {
                    if (bean.getMobileTypes() == null) {
                        // 数据兼容
                        vo.setMobileTypeList(Arrays.asList(MobileTypeEnum.GENERAL.getCode()));
                    } else {
                        vo.setMobileTypeList(JSON.parseArray(bean.getMobileTypes(), Byte.class));
                    }
                } catch (Exception ignore){}
                storeContactVoList.add(vo);
            }
            store.setStoreContactList(storeContactVoList);
            // 零散小时
            List<StoreHourlyChargeVo> tmpChargeList = chargeList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            store.setHourlyChargeList(tmpChargeList);
            // 门店图片
            List<ThirdStoreAttVo> storeAttVoList = new ArrayList<>();
            List<StoreAtt> tmpStoreAttList = storeAttList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (StoreAtt bean : tmpStoreAttList) {
                ThirdStoreAttVo vo = new ThirdStoreAttVo();
                BeanUtils.copyProperties(bean, vo);
                storeAttVoList.add(vo);
            }
            store.setStoreAttList(storeAttVoList);
            // 门店指引
            List<ThirdStoreGuideVo> storeGuideVoList = new ArrayList<>();
            List<StoreGuide> tmpStoreGuideList = storeGuideList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (StoreGuide bean : tmpStoreGuideList) {
                ThirdStoreGuideVo vo = new ThirdStoreGuideVo();
                BeanUtils.copyProperties(bean, vo);
                storeGuideVoList.add(vo);
            }
            store.setStoreGuideList(storeGuideVoList);
            // 门店指引图片
            List<ThirdGuidePicVo> guidePicVoList = new ArrayList<>();
            List<GuidePic> tmpGuidePicList = guidePicList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (GuidePic bean : tmpGuidePicList) {
                ThirdGuidePicVo vo = new ThirdGuidePicVo();
                BeanUtils.copyProperties(bean, vo);
                vo.setGuidePic(FileUploader.addFilePrefix(vo.getGuidePic()));
                guidePicVoList.add(vo);
            }
            // 条条这边已处理（空时返回null）
//            if (CollectionUtils.isEmpty(guidePicVoList)) {
//                guidePicVoList = null;
//            }
            store.setGuidePicList(guidePicVoList);
            // 上门取送车圈
            List<ThirdServicePickupVo> servicePickupVoList = new ArrayList<>();
            List<ServicePickup> tmpServicePickupList = pickupList.stream()
                    .filter(e -> e.getStoreId().equals(storeId)).collect(Collectors.toList());
            for (ServicePickup bean : tmpServicePickupList) {
                ThirdServicePickupVo vo = new ThirdServicePickupVo();
                BeanUtils.copyProperties(bean, vo);
                ServicePickup pickupGis = servicePickupMapper.selectByPrimaryKey(vo.getId());
                try {
                    if (pickupGis.getGis() != null) {
                        Geometry geometry = StoreInfoServiceImpl.getGeometryByBytes(pickupGis.getGis());
                        List<LongLatVo> listVo = new ArrayList<>();
                        for (Coordinate coordinate : geometry.getCoordinates()) {
                            LongLatVo longLat = new LongLatVo();
                            longLat.setLongitude(coordinate.getX());
                            longLat.setLatitude(coordinate.getY());
                            listVo.add(longLat);
                        }
                        // 同步过来的圈，删除最后一个经纬度, 需要与携程一致
                        String thirdCircle = cMap.get(bean.getId());
                        if (thirdCircle != null && listVo.size() > 0) {
                            listVo.remove(listVo.size() - 1);
                        }
                        vo.setLongLatList(listVo);
                    }
                } catch (Exception e) {
                    throw new BizException("圈gis转换失败");
                }
                servicePickupVoList.add(vo);
            }
            store.setServicePickupList(servicePickupVoList);
            // 服务圈休息时间
            List<ThirdServiceRestTimeVo> serviceRestTimeVoList = new ArrayList<>();
            for (ServiceRestTime serviceRestTime : serviceRestTimeList) {
                ThirdServiceRestTimeVo vo = new ThirdServiceRestTimeVo();
                BeanUtils.copyProperties(serviceRestTime, vo);
                serviceRestTimeVoList.add(vo);
            }
            store.setServiceRestTimeList(serviceRestTimeVoList);

            retList.add(store);
        }

        return retList;
    }

    private List<ServiceRestTime> filterServiceRestTimeByChannel(List<ServicePickup> pickupList) {
        List<ServiceRestTime> ret = new ArrayList<>();
        for (ServicePickup servicePickup : pickupList) {
            String key = String.format(RedisConstant.StoreRedisKey.STORE_SERVICE_RESTTIME_KEY, servicePickup.getId());
            List<ServiceRestTime> list = (List<ServiceRestTime>) redisService.get(key);
            if (list == null) {
                ServiceRestTimeExample serviceRestTimeExample = new ServiceRestTimeExample();
                serviceRestTimeExample.createCriteria().andServicePickupIdEqualTo(servicePickup.getId()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                list = serviceRestTimeMapper.selectByExample(serviceRestTimeExample);
                redisService.set(key, list, RedisConstant.RedisExpireTime.DAY_10);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                ret.addAll(list);
            }
        }

        return ret;
    }

    @Override
    public List<StoreSimpleVo> allStoreInfo(Long merchantId, Long channelId) {
        return storeInfoService.storeInfoList(merchantId);
    }

    @Override
    public StoreInfoVo getStoreInfoByStoreId(Long merchantId, Long storeId, Long channelId) {
        try {
            Result<StoreInfoVo> storeInfoResult = storeInfoService.storeInfoByStoreId(merchantId, storeId, channelId);
            if (BooleanUtils.isTrue(storeInfoResult.isSuccess()) && Objects.nonNull(storeInfoResult.getModel())) {
                return storeInfoResult.getModel();
            }
        } catch (Exception e) {
            throw new BizException("获取门店信息异常 ", e);
        }
        return null;
    }

    private List<StoreInfoChannel> filterStoreChannelByChannel1(List<StoreInfoChannel> list, Long channelId) {
        List<StoreInfoChannel> ret =
                list.stream().filter(e -> e.getChannelId().equals(channelId)).collect(Collectors.toList());
        List<Long> stores = ret.stream().map(e -> e.getStoreId()).distinct().collect(Collectors.toList());
        ret.addAll(
                list.stream().filter(e -> !stores.contains(e.getStoreId())).collect(Collectors.toList())
        );
        return ret;
    }

    private void setGis(byte[] gis, ThirdStoreInfoVo vo) {
        // 经纬度
        try {
            if (gis == null) {
                vo.setLongLat(new LongLatVo());
                return;
            }
            Geometry geometry = StoreInfoServiceImpl.getGeometryByBytes(gis);
            LongLatVo longLat = new LongLatVo();
            longLat.setLongitude(geometry.getCentroid().getX());
            longLat.setLatitude(geometry.getCentroid().getY());
            vo.setLongLat(longLat);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("获取经纬度信息异常");
        }
    }

    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.STORE)
    @Override
    public CtripStoreVO importStoreFormCtrip(String vendorId) throws Exception {
        CtripStoreVO retStoreVo = new CtripStoreVO();
        // 查询商家ID
        Result<ApiConnMoreVo> apiConnMoreVoResult = apiConnService.
                getApiConnMoreByVendorIdAndChannelId(vendorId, OrderSourceEnum.CTRIP.getSource().longValue());
        if (apiConnMoreVoResult.getModel() == null) {
            throw new BizException("携程上货,门店;商户未创建或渠道未设置（ApiConn）");
        }
        Long merchantId = apiConnMoreVoResult.getModel().getMerchantId();
        retStoreVo.setVendorId(Long.parseLong(vendorId));
        retStoreVo.setMerchantId(merchantId);
        List<CtripStoreVO.StoreVO> retStoreList = new ArrayList<>();
        // 圈默认颜色LIST
        List<String> colorList = Arrays.asList("#fa8c16", "#fadb14", "#52c41a", "#13c2c2", "#1890ff", "#722ed1",
                "#eb2f96", "#ff7a45", "#ffd666", "#f5222d", "#bd193a", "#fb9966", "#ffba84", "#7ba23f", "#5dac81",
                "#0089a7", "#77428d", "#c1328e", "#ffc408", "#fad689");
        // 线下渠道
        Long channelId = OrderSourceEnum.OFFLINE.getSource().longValue();

        // 获取服务圈数据，解决服务圈数据不重新生成
        ThirdIdRelationExample example = new ThirdIdRelationExample();
        example.createCriteria().andMerchantIdEqualTo(merchantId)
                .andTypeEqualTo(IdRelationEnum.CICLE.getType())
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<ThirdIdRelation> idRelations = thirdIdRelationMapper.selectByExample(example);
        Map<String, Long> idRelationMap = idRelations.stream()
                .collect(Collectors.toMap(e -> e.getStoreId() + ":" + e.getThirdId(), e -> e.getSaasId()));

        StoreInfoChannelExample storeInfoChannelExample = new StoreInfoChannelExample();
        storeInfoChannelExample.createCriteria().andMerchantIdEqualTo(merchantId)
                .andDeletedEqualTo(YesOrNoEnum.NO.getValue())
                .andChannelIdEqualTo(channelId);
        List<StoreInfoChannel> channelStores = storeInfoChannelMapper.selectByExample(storeInfoChannelExample);
        Map<Long, Long> channelStoreMap = channelStores.stream()
                .collect(Collectors.toMap(e -> e.getStoreId(), e -> e.getId()));

        Long maxStoreId = 0L;
        int pageSize = 500;
        while (true) {
            // 请求参数
            GetStoreListForSaasRequest request = new GetStoreListForSaasRequest();
            request.setVendorIdList(Arrays.asList(Long.parseLong(vendorId)));
            request.setPageSize(pageSize);
            request.setMaxStoreId(maxStoreId);
            SignRequest signRequest = ctripRequestSignBuilder.build(merchantId, request);
            log.info("携程上货,门店;request={}", JSON.toJSONString(signRequest));
            GetStoreListForSaasResponse storeResp = ctripApiClient.pullStoreListForSaas(signRequest);
            log.info("携程上货,门店;response={}", JSON.toJSONString(storeResp));
            if (!storeResp.isSuccess()) {
                log.error("携程上货,门店,异常;message={}", storeResp.getMessage());
                throw new BizException(storeResp.getMessage());
            }
            // 商家id关系保存
            if (maxStoreId == 0 && CollectionUtils.isNotEmpty(storeResp.getStoreInfoList())) {
                IdRelationVo vo = new IdRelationVo();
                vo.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
                vo.setType((IdRelationEnum.MERCHANT.getType()));
                vo.setThirdId(vendorId);
                vo.setSaasId(merchantId);
                vo.setMerchantId(merchantId);
                vo.setStoreId(0L);
                thirdIdRelationService.save(vo);
            }
            List<CtripStoreDTO> storeInfoList = storeResp.getStoreInfoList();
            if (CollectionUtils.isEmpty(storeInfoList)) {
                if (maxStoreId.longValue() == 0) {
                    log.error("携程上货,门店;数据返回空");
                    throw new BizException(storeResp.getMessage());
                } else {
                    break;
                }
            }
            // 数据入库
            List<CtripStoreVO.StoreVO> tmpList = insertStoreInfo(storeInfoList, merchantId, channelId, colorList,
                    idRelationMap, channelStoreMap);
            retStoreList.addAll(tmpList);
            // 小于最大页 返回
            if (storeInfoList.size() < pageSize) {
                break;
            }
            // 获取下一页最大ID
            maxStoreId = storeInfoList.stream().map(e -> e.getStoreId())
                    .max(Comparator.comparing(e -> e)).orElse(null);
        }
        // 计算门店对距离
        storeInfoService.initStoreDistance(merchantId);
        // 返回
        retStoreVo.setStoreList(retStoreList);
        //Integer.parseInt("adfsfdf");
        return retStoreVo;
    }

    private List<CtripStoreVO.StoreVO> insertStoreInfo(
            List<CtripStoreDTO> storeInfoList, Long merchantId, Long channelId, List<String> colorList,
            Map<String, Long> idRelationMap, Map<Long, Long> channelStoreMap) throws Exception {

        List<CtripStoreVO.StoreVO> retStoreList = new ArrayList<>();

        for (CtripStoreDTO dto : storeInfoList) {
            CtripStoreVO.StoreVO storeVO = new CtripStoreVO.StoreVO();
            storeVO.setCtripStoreCode(dto.getVendorStoreCode());
            storeVO.setCtripStoreName(dto.getStoreName());
            storeVO.setCtripStoreId(dto.getStoreId());
            if (dto.getLocationInfo() != null) {
                storeVO.setCtripCityId(dto.getLocationInfo().getCityId());
            }
            long t = System.currentTimeMillis();
            // 查询门店是否已处理
            IdRelationParam param = new IdRelationParam();
            param.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
            param.setType((IdRelationEnum.STORE.getType()));
            param.setThirdId(dto.getStoreId().toString());
            param.setMerchantId(merchantId);
            Result<String> idResult = thirdIdRelationService.relation(param);
//            if (idResult.getModel() != null) {
//                log.info("携程上货,门店;第三方门店已导入,返回；vendorId={},storeId={},saas storeId={}",
//                    dto.getVendorId(), dto.getStoreId(), idResult.getModel().longValue());
//                storeVO.setStoreId(idResult.getModel().longValue());
//                retStoreList.add(storeVO);
//                continue;
//            }

            // 门店基础
            Byte storeStatus = dto.getStoreStatus();
            StoreInfo storeInfo = new StoreInfo();
            if(NumberUtils.isCreatable(idResult.getModel())){
                storeInfo.setId(Long.valueOf(idResult.getModel()));
            }

            storeInfo.setCountryId(0L);
            double longitude = dto.getLocationInfo().getLongitude();
            double latitude = dto.getLocationInfo().getLatitude();
            String areaCode = getAreaCodeJWD(longitude, latitude);
            if (areaCode == null) {
                throw new BizException("携程上货,门店;高德根据经纬度查询区县失败");
            }
            // 东莞市层级只有2级，其他未知
            storeInfo.setProvinceId(0L);
            storeInfo.setCityId(0L);
            Result<AreaVo> areaResult = areaService.findByCode(areaCode);
            if (!areaResult.isSuccess() || areaResult.getModel() == null) {
                if (YesOrNoEnum.isYes(storeStatus)) {
                    throw new BizException("携程上货,门店;区县不存在");
                }
            } else {
                if (areaResult.getModel().getDepth() == 3) {
                    areaResult = areaService.findByCode(areaResult.getModel().getParentCode());
                    if (!areaResult.isSuccess() || areaResult.getModel() == null) {
                        throw new BizException("携程上货,门店;城市不存在");
                    }
                    storeInfo.setCityId(areaResult.getModel().getId());
                    areaResult = areaService.findByCode(areaResult.getModel().getParentCode());
                    if (!areaResult.isSuccess() || areaResult.getModel() == null) {
                        throw new BizException("携程上货,门店;省份不存在");
                    }
                    storeInfo.setProvinceId(areaResult.getModel().getId());
                } else if (areaResult.getModel().getDepth() == 2) {
                    storeInfo.setCityId(areaResult.getModel().getId());
                    areaResult = areaService.findByCode(areaResult.getModel().getParentCode());
                    if (!areaResult.isSuccess() || areaResult.getModel() == null) {
                        throw new BizException("携程上货,门店;省份不存在");
                    }
                    storeInfo.setProvinceId(areaResult.getModel().getId());
                } else {
                    throw new BizException("携程上货,门店;城市级别错误");
                }
            }
            storeInfo.setStoreName(dto.getStoreName());
            storeInfo.setMerchantId(merchantId);
            storeInfo.setStoreSize(dto.getStoreArea());
            storeInfo.setStoreDecorate(dto.getStoreDecorateLevel());
            storeInfo.setAddress(dto.getLocationInfo().getAddress());
            //门店位置类型 1:市区门店;2:机场店;3:火车站店
            storeInfo.setStorePosType((byte) 1); //todo
            //携程 stationType 枢纽站店类型（1 枢纽站内  2枢纽站外 3 非枢纽站）
            //saas PosHub枢纽类型 0:非枢纽;1:枢纽站外;2:枢纽站内
            byte postHub = 0;
            if (dto.getLocationInfo().getStationType() == 1) {
                postHub = 2;
            } else if (dto.getLocationInfo().getStationType() == 2) {
                postHub = 1;
            } else if (dto.getLocationInfo().getStationType() == 3) {
                postHub = 0;
            }
            storeInfo.setPosHub(postHub);
            //携程门店类型 1 实体店 2 服务点 3 接送虚拟点 4 送取虚拟点
            //saas门店类型 0:实体门店;1:服务网点
            byte storeType = 1;
            // 234都是服务网点 @露比 0729
            if (dto.getStoreType() == 1) {
                storeType = 0;
            }
            storeInfo.setStoreType(storeType);
            storeInfo.setStoreStatus(YesOrNoEnum.YES.getValue());
            storeInfo.setDeleted(YesOrNoEnum.NO.getValue());
            storeInfo.setStoreStatus(storeStatus);
            storeInfo.setIsTest(YesOrNoEnum.NO.getValue());
            storeInfo.setOpTime(t);
            if (storeInfo.getId() == null) {
                storeInfo.setCreateTime(t);
                storeInfoMapper.insertSelective(storeInfo);
            } else {
                storeInfoMapper.updateByPrimaryKeySelective(storeInfo);
            }

            String gis = String.format("point(%s %s)", longitude, latitude);
            storeMapper.updateGis(storeInfo.getId(), gis);
            Long storeId = storeInfo.getId();

            storeVO.setStoreId(storeId);
            retStoreList.add(storeVO);

            // 门店id关系保存
            IdRelationVo vo = new IdRelationVo();
            vo.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
            vo.setType((IdRelationEnum.STORE.getType()));
            vo.setThirdId(dto.getStoreId().toString());
            vo.setSaasId(storeInfo.getId());
            vo.setMerchantId(merchantId);
            vo.setStoreId(storeId);
            thirdIdRelationService.save(vo);
            // 门店code关系保存
            vo = new IdRelationVo();
            vo.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
            vo.setType((IdRelationEnum.STORECODE.getType()));
            vo.setThirdId(dto.getVendorStoreCode());
            vo.setSaasId(storeInfo.getId());
            vo.setMerchantId(merchantId);
            vo.setStoreId(storeId);
            thirdIdRelationService.save(vo);

            // 指引类型
            StoreGuide storeGuide = new StoreGuide();
            storeGuide.setDeleted(YesOrNoEnum.YES.getValue());
            storeGuide.setOpTime(t);
            StoreGuideExample gexample = new StoreGuideExample();
            gexample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            storeGuideMapper.updateByExampleSelective(storeGuide, gexample);
            List<CtripStoreDTO.CtripLocationDTO.CtripGuideDTO> guideInfoList = dto.getLocationInfo().getGuideInfoList();
            if (CollectionUtils.isEmpty(guideInfoList)) guideInfoList = new ArrayList<>();
            for (CtripStoreDTO.CtripLocationDTO.CtripGuideDTO guideDTO : guideInfoList) {
                StoreGuide guide = new StoreGuide();
                //携程指引类型 1 取车指引 2 还车指引 3 门店指引 必填
                //saas门店指引 0:取车指引;1:还车指引
                if (guideDTO.getGuideType() == 1) {
                    guide.setGuideType((byte) 0);
                } else if (guideDTO.getGuideType() == 2) {
                    guide.setGuideType((byte) 1);
                } else {
                    continue;
                }
                guide.setStoreId(storeInfo.getId());
                guide.setGuideDesc(guideDTO.getGuideDesc());
                guide.setStep((byte) guideDTO.getStepNum().intValue());
                guide.setDeleted(YesOrNoEnum.NO.getValue());
                guide.setCreateTime(t);
                guide.setOpTime(t);
                storeGuideMapper.insertSelective(guide);
                if (CollectionUtils.isNotEmpty(guideDTO.getGuideImg())) {
                    GuidePic puidePic = new GuidePic();
                    puidePic.setDeleted(YesOrNoEnum.YES.getValue());
                    puidePic.setOpTime(t);
                    GuidePicExample guidePicExample = new GuidePicExample();
                    guidePicExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
                    guidePicMapper.updateByExampleSelective(puidePic, guidePicExample);
                    for (String pic : guideDTO.getGuideImg()) {
                        GuidePic guidePic = new GuidePic();
                        guidePic.setStoreId(storeInfo.getId());
                        guidePic.setGuideId(guide.getId());
                        guidePic.setGuidePic(pic);
                        guidePic.setDeleted(YesOrNoEnum.NO.getValue());
                        guidePic.setCreateTime(t);
                        guidePic.setOpTime(t);
                        guidePicMapper.insertSelective(guidePic);
                    }
                }
            }

            // 营业时间 常规营业时间信息
            BusinessTime bTime = new BusinessTime();
            bTime.setDeleted(YesOrNoEnum.YES.getValue());
            bTime.setOpTime(t);
            BusinessTimeExample businessTimeExample = new BusinessTimeExample();
            businessTimeExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            businessTimeMapper.updateByExampleSelective(bTime, businessTimeExample);

            NightService nTime = new NightService();
            nTime.setDeleted(YesOrNoEnum.YES.getValue());
            nTime.setOpTime(t);
            NightServiceExample nightServiceExample = new NightServiceExample();
            nightServiceExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            nightServiceMapper.updateByExampleSelective(nTime, nightServiceExample);
            List<CtripStoreDTO.CtripBusinessHoursDTO> normalBusinessHoursList = dto.getNormalBusinessHoursList();
            if (CollectionUtils.isEmpty(normalBusinessHoursList)) normalBusinessHoursList = new ArrayList<>();
            for (CtripStoreDTO.CtripBusinessHoursDTO normalDTO : normalBusinessHoursList) {
                // "businessHoursCycle": [1, 2, 3, 4, 5, 6, 7]
                String businessPeriod = getBusinessPeriod(normalDTO.getBusinessHoursCycle());
                long bussId = 0;
                for (CtripStoreDTO.CtripBusinessHoursDTO.CtripBusinessTimeDTO businessDTO : normalDTO.getPeriodTime()) {
                    BusinessTime businessTime = new BusinessTime();
                    businessTime.setChannelId(channelId);
                    businessTime.setStoreId(storeId);
                    businessTime.setBusinessPeriod(businessPeriod);
                    businessTime.setBusinessFrom(formatTime(businessDTO.getBusinessHoursStartTime()));
                    businessTime.setBusinessTo(formatTime(businessDTO.getBusinessHoursEndTime()));
                    businessTime.setNightService(YesOrNoEnum.YES.getValue());
                    businessTime.setDeleted(YesOrNoEnum.NO.getValue());
                    businessTime.setCreateTime(t);
                    businessTime.setOpTime(t);
                    businessTimeMapper.insertSelective(businessTime);
                    if (bussId == 0) {
                        bussId = businessTime.getId();
                    }
                }
                int i = 0;
                for (CtripStoreDTO.CtripBusinessHoursDTO.CtripNightDTO businessDTO : normalDTO.getNightPeriodTime()) {
                    if (bussId == 0) {
                        break;
                    }
                    NightService night = new NightService();
                    night.setChannelId(channelId);
                    night.setStoreId(storeId);
                    night.setBusinessTimeId(bussId);
                    night.setBusinessPeriod(businessPeriod);
                    night.setBusinessFrom(formatTime(businessDTO.getBusinessHoursStartTime()));
                    night.setBusinessTo(formatTime(businessDTO.getBusinessHoursEndTime()));
                    night.setFeeType(YesOrNoEnum.YES.getValue());
                    night.setFee(Objects.isNull(businessDTO.getChargeFee()) ? 0 : businessDTO.getChargeFee().multiply(new BigDecimal("100")).intValue());
                    night.setSortNo(i);
                    night.setDeleted(YesOrNoEnum.NO.getValue());
                    night.setCreateTime(t);
                    night.setOpTime(t);
                    nightServiceMapper.insertSelective(night);
                    i++;
                }
            }

            // 特殊营业时间信息 todo

            // 暂停营业时间
            RestTime rTime = new RestTime();
            rTime.setDeleted(YesOrNoEnum.YES.getValue());
            rTime.setOpTime(t);
            RestTimeExample restTimeExample = new RestTimeExample();
            restTimeExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            restTimeMapper.updateByExampleSelective(rTime, restTimeExample);
            List<CtripStoreDTO.CtripSuspendTimeDTO> suspendBusinessHoursList = dto.getSuspendBusinessHoursList();
            if (CollectionUtils.isEmpty(suspendBusinessHoursList)) suspendBusinessHoursList = new ArrayList<>();
            List<RestTime> insertRestTimeList = new ArrayList<>();
            for (CtripStoreDTO.CtripSuspendTimeDTO restDTO : suspendBusinessHoursList) {
                // 这里有多个时间 （已支持多个时间）
                for (CtripStoreDTO.CtripSuspendTimeDTO.CtripPeriodDTO timeDTO : restDTO.getPeriodTime()) {
                    RestTime rest = new RestTime();
                    rest.setChannelId(channelId);
                    rest.setStoreId(storeId);
                    rest.setStartDate(formatDate(restDTO.getStartDate()));
                    rest.setEndDate(formatDate(restDTO.getEndDate()));
                    rest.setStartTime(formatTime(timeDTO.getBusinessHoursStartTime()));
                    rest.setEndTime(formatTime(timeDTO.getBusinessHoursEndTime()));
                    rest.setDeleted(YesOrNoEnum.NO.getValue());
                    rest.setLastVer(1);
                    rest.setCreateTime(t);
                    rest.setOpTime(t);
                    rest.setOpUserId(0L);
                    insertRestTimeList.add(rest);
                }
            }
            if (CollectionUtils.isNotEmpty(insertRestTimeList)) {
                restTimeMapper.batchInsert(insertRestTimeList);
            }

            // 联系人
            StoreContact sContact = new StoreContact();
            sContact.setDeleted(YesOrNoEnum.YES.getValue());
            sContact.setOpTime(t);
            StoreContactExample storeContactExample = new StoreContactExample();
            storeContactExample.createCriteria().andStoreIdEqualTo(storeId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            storeContactMapper.updateByExampleSelective(sContact, storeContactExample);
            List<CtripStoreDTO.CtripContactDTO> contactInfoList = dto.getContactInfoList();
            if (CollectionUtils.isEmpty(contactInfoList)) contactInfoList = new ArrayList<>();
            for (CtripStoreDTO.CtripContactDTO contactDTO : contactInfoList) {
                StoreContact contact = new StoreContact();
                contact.setChannelId(channelId);
                contact.setStoreId(storeId);
                // 联系人类型 0:常用联系人;1:备用联系人 todo
                contact.setContactType((byte) 0);
                contact.setLinkName(contactDTO.getContactName());
                String code = StringUtils.isNotEmpty(contactDTO.getMobilePhoneAreaCode()) ?
                        contactDTO.getMobilePhoneAreaCode() : contactDTO.getBackupMobilePhoneAreaCode();
                if (code != null && code.equals("86")) code = "+" + code;
                contact.setCountryCode(code);
                String mobile = StringUtils.isNotEmpty(contactDTO.getMobilePhone()) ?
                        contactDTO.getMobilePhone() : contactDTO.getBackupMobilePhone();
                contact.setMobile(mobile);
                contact.setTelArea(contactDTO.getFixedPhoneAreaCode());
                contact.setTel(contactDTO.getFixedPhone());
                contact.setTelExt(contactDTO.getFixedPhoneExtensionNumber());
                contact.setDeleted(YesOrNoEnum.NO.getValue());
                contact.setCreateTime(t);
                contact.setOpTime(t);
                storeContactMapper.insertSelective(contact);
            }

            // 门店服务圈信息列表
            byte freeShuttleEnabled = 0;
            byte pickupEnabled = 0;
            List<CtripStoreDTO.CtripCircleDTO> serviceCircleList = dto.getServiceCircleList();
            if (CollectionUtils.isEmpty(serviceCircleList)) serviceCircleList = new ArrayList<>();
            int idx = 0;
            for (CtripStoreDTO.CtripCircleDTO circleDTO : serviceCircleList) {
                int startTime = 0;
                int endTime = 0;
                int fee = circleDTO.getChargeFee().multiply(new BigDecimal("100")).intValue();
                // 时间取的是常规营业时间信息.第一个时间 todo
                List<CtripStoreDTO.CtripBusinessHoursDTO> normalBusiness = circleDTO.getNormalBusinessHoursList();
                if (CollectionUtils.isNotEmpty(normalBusiness)) {
                    List<CtripStoreDTO.CtripBusinessHoursDTO.CtripBusinessTimeDTO> normalTime =
                            normalBusiness.get(0).getPeriodTime();
                    if (normalTime.size() > 0) {
                        startTime = formatTime(normalTime.get(0).getBusinessHoursStartTime());
                        endTime = formatTime(normalTime.get(0).getBusinessHoursEndTime());
                        //fee = normalTime.get(0).getChargeFee().multiply(new BigDecimal("100")).intValue();
                    }
                }

                ServicePickup circle = new ServicePickup();
                circle.setId(idRelationMap.get(storeId + ":" + circleDTO.getVendorServiceCircleCode().toString()));
                circle.setChannelId(channelId);
                circle.setStoreId(storeId);
                circle.setEnabled(circleDTO.getStatus());
                circle.setFeeType(YesOrNoEnum.YES.getValue());
                // 接送服务类型 1-免费接送服务（门店支持在5公里范围内），2-上门送取车服务
                if (circleDTO.getStatus().equals(YesOrNoEnum.YES.getValue())) {
                    if (circleDTO.getDeliveryServiceType().equals((byte) 1)) {
                        freeShuttleEnabled = YesOrNoEnum.YES.getValue();
                    } else {
                        pickupEnabled = YesOrNoEnum.YES.getValue();
                    }
                }
                // advancePeriodUnit 提前预定期单位; 0：分钟 1：小时
                // advancePeriod 提前预定期
                BigDecimal advanceBookingTime = new BigDecimal(circleDTO.getAdvancePeriod());
                if (circleDTO.getAdvancePeriodUnit() == 0) {
                    int ad = advanceBookingTime.intValue();
                    int zd = (int) ad / 60;
                    int mod = ad % 60;
                    advanceBookingTime = new BigDecimal(mod).divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP);
                    advanceBookingTime = advanceBookingTime.add(new BigDecimal(zd));
                }
                circle.setMinAdvanceBookingTime(advanceBookingTime);
                circle.setFee(fee);
                // 携程这边 免费接送类型有可能为空，携程需要这个名字
                String circleName = circleDTO.getDeliveryServiceName();
                if (StringUtils.isEmpty(circleName)) {
                    circleName = "免费接送";
                }
                circle.setName(circleName);
                circle.setBusinessFrom(startTime);
                circle.setBusinessTo(endTime);
                circle.setColor(colorList.get(idx % 20));
                circle.setPickupType(circleDTO.getDeliveryServiceType());
                circle.setOpTime(t);
                if (circle.getId() == null) {
                    circle.setDeleted(YesOrNoEnum.NO.getValue());
                    circle.setCreateTime(t);
                    servicePickupMapper.insertSelective(circle);
                } else {
                    servicePickupMapper.updateByPrimaryKeySelective(circle);
                }

                // 服务圈id关系保存
                vo = new IdRelationVo();
                vo.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
                vo.setType((IdRelationEnum.CICLE.getType()));
                vo.setThirdId(circleDTO.getVendorServiceCircleCode().toString());
                vo.setSaasId(circle.getId());
                vo.setMerchantId(merchantId);
                vo.setStoreId(storeId);
                thirdIdRelationService.save(vo);

                StringBuffer points = new StringBuffer();
                if (circleDTO.getDeliveryServiceType().equals((byte) 1)) {
                    List<GisUtils.GPSPoint> gisList = GisUtils.rangeGPSPoint(longitude, latitude, 5000);
                    gisList.add(new GisUtils.GPSPoint(gisList.get(0).getLatitude(), gisList.get(0).getLongitude()));
                    for (GisUtils.GPSPoint point : gisList) {
                        if (StringUtils.isNotEmpty(points)) {
                            points.append(",");
                        }
                        points.append(point.getLongitude() + " " + point.getLatitude());
                    }
                } else {
                    List<CtripStoreDTO.CtripCircleDTO.CtripLngLatDTO> gisList = circleDTO.getCircleRangePoiList();
                    if (CollectionUtils.isNotEmpty(gisList)) {
                        CtripStoreDTO.CtripCircleDTO.CtripLngLatDTO pointFirst =
                                new CtripStoreDTO.CtripCircleDTO.CtripLngLatDTO();
                        for (CtripStoreDTO.CtripCircleDTO.CtripLngLatDTO point : gisList) {
                            if (StringUtils.isNotEmpty(points)) {
                                points.append(",");
                            } else {
                                BeanUtils.copyProperties(point, pointFirst);
                            }
                            if (point == null || point.getLatitude() == null || point.getLongitude() == null) {
                                continue;
                            }
                            points.append(point.getLongitude() + " " + point.getLatitude());
                        }
                        points.append(",");
                        points.append(pointFirst.getLongitude() + " " + pointFirst.getLatitude());
                        idx++;
                    }
                }
                if (StringUtils.isNotEmpty(points.toString())) {
                    String gisField = String.format("Polygon((%s))", points.toString());
                    log.info("{}, {}", circle.getId(), gisField);
                    storeMapper.updatePickupGis(circle.getId(), gisField);
                }
            }

            // 门店渠道
            StoreInfoChannel channel = new StoreInfoChannel();
            channel.setId(channelStoreMap.get(storeId));
            channel.setMerchantId(merchantId);
            channel.setStoreId(storeInfo.getId());
            channel.setChannelId(channelId);
            // 是否支持异地还车 0:否;1:是
            channel.setAllopatryReturnEnabled(YesOrNoEnum.NO.getValue());
            // 异地取还服务收费 单位分
            channel.setAllopatryReturnFee(0);
            // 免费接送 1:启用  0：禁用
            channel.setFreeShuttleEnabled(freeShuttleEnabled);
            // 免费接送范围（公里）
            channel.setFreeShuttle(5);
            // 上门送取车服务是否开启 1:已开启；0:未开启
            channel.setPickupEnabled(pickupEnabled);
            // 订单间隔
            channel.setOrderInterval(1);
            channel.setStoreStatus(storeStatus);
            channel.setOpTime(t);
            if (channel.getId() == null) {
                channel.setDeleted(YesOrNoEnum.NO.getValue());
                channel.setCreateTime(t);
                storeInfoChannelMapper.insertSelective(channel);
            } else {
                storeInfoChannelMapper.updateByPrimaryKeySelective(channel);
            }
            // 删除门店缓存
            //storeInfoService.removeStoreKey(storeId);
            List<String> rKeys =  Lists.newArrayList();
            rKeys.addAll(storeInfoService.removeStoreKey(storeId)); // 等同步门店修改后删除
            rKeys.addAll(storeInfoService.removeStoreKey(storeId, channelId));
            TransactionalRedisParam transactionalRedisParam = TransactionalRedisParam.listBuildParam(rKeys);
            eventPublisher.publishEvent(transactionalRedisParam);
        }

        return retStoreList;
    }

    @Override
    public List<StoreHourlyChargeVo> getChargeList(List<Long> storeIds, Long channelId) {
        return filterChargeByChannel(storeIds, channelId);
    }

    @Override
    public List<StoreHourlyChargeVo> getChargeListByMerchant(Long merchantId, Long channelId) {
        if (merchantId == null) {
            return null;
        }
        List<Long> storeIds = storeInfoService.storeInfoList(merchantId, null).stream().map(StoreInfoVo::getStoreId).collect(Collectors.toList());
        return filterChargeByChannel(storeIds, channelId);
    }

    @Override
    public List<StoreHourlyVo> getChargeListV2(Long merchantId, List<Long> storeIds, Long searchChannelId, Long inquiryChannelId) {
        List<StoreInfoChannel> channelStores = filterStoreChannelByChannel(storeIds, searchChannelId);
        List<StoreHourlyVo> retList = new ArrayList<>();
        for (StoreInfoChannel channelStore : channelStores) {
            retList.addAll(storeInfoService.findHourlyChargeV2(merchantId, channelStore.getStoreId(), channelStore.getChannelId(), inquiryChannelId));
        }
        return retList;
    }

    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.STORE)
    @Override
    public void importDiffStoreFormCtrip(String vendorId) {

        Result<ApiConnMoreVo> apiConnMoreVoResult = apiConnService.
                getApiConnMoreByVendorIdAndChannelId(vendorId, OrderSourceEnum.CTRIP.getSource().longValue());
        if (apiConnMoreVoResult.getModel() == null) {
            throw new BizException("商户未创建或渠道未设置（ApiConn）");
        }
        Long merchantId = apiConnMoreVoResult.getModel().getMerchantId();

        GetStoreListForSaasRequest request = new GetStoreListForSaasRequest();
        request.setVendorIdList(Arrays.asList(Long.parseLong(vendorId)));
        request.setPageSize(10);
        request.setMaxDiffStoreConfigCode(0);
        SignRequest signRequest = ctripRequestSignBuilder.build(merchantId, request);
        log.info(JSON.toJSONString(signRequest));
        GetDiffStoreConfigListForSaas storeResp = ctripApiClient.pullDiffStoreListForSaas(signRequest);
        log.info(JSON.toJSONString(storeResp));
    }

    @Transactional(rollbackFor = Throwable.class, transactionManager = TransactionManagerName.STORE)
    @Override
    public void importgetHourlyChargeFormCtrip(CtripStoreVO vo) {
        GetStoreListForSaasRequest request = new GetStoreListForSaasRequest();
        request.setVendorIdList(Arrays.asList(Long.parseLong(vo.getVendorId().toString())));
        request.setVendorId(vo.getVendorId());

        StoreHourlyChargeExample example = new StoreHourlyChargeExample();
        example.createCriteria().andMerchantIdEqualTo(vo.getMerchantId()).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<StoreHourlyCharge> oldCharges = storeHourlyChargeMapper.selectByExample(example);

        Map<String, CtripStoreVO.StoreVO> map =
                vo.getStoreList().stream().collect(Collectors.toMap(CtripStoreVO.StoreVO::getCtripStoreCode, item -> item));

        Long maxInfoId = 0L;
        int pageSize = 500;
        GetHourlyChargeForSaasResponse resp;
        while (true) {
            request.setPageSize(pageSize);
            request.setMaxInfoId(maxInfoId);
            SignRequest signRequest = ctripRequestSignBuilder.build(vo.getMerchantId(), request);
            log.info("携程上货,零散小时;request={}", JSON.toJSONString(signRequest));
            resp = ctripApiClient.pullHourlyChargeForSaas(signRequest);
            log.info("携程上货,零散小时;response={}", JSON.toJSONString(resp));
            if (!resp.isSuccess()) {
                log.error("携程上货,零散小时,异常;message={}", resp.getMessage());
                throw new BizException(resp.getMessage());
            }
            List<CtripChargeDTO> hourlyList = resp.getHourlyCharge();
            if (CollectionUtils.isEmpty(hourlyList)) {
                if (maxInfoId == 0) {
                    log.info("携程上货,零散小时;数据返回空");
                    break;
                } else {
                    break;
                }
            }
            // 数据保存
            insertHourlyChargeForm(vo, hourlyList, oldCharges, map);
            // 小于最大页 返回
            if (hourlyList.size() < pageSize) {
                break;
            }
            // 获取下一页最大ID
            maxInfoId = hourlyList.stream().map(CtripChargeDTO::getId)
                    .max(Comparator.comparing(e -> e)).orElse(null);
        }
    }

    @Override
    public List<CancelPolicyDTO> getCancelRuleList(Long merchantId) {
        Result<List<CancelRuleVo>> listResult = cancelRuleService.ruleList(merchantId);
        List<CancelRuleVo> cancelRuleList = listResult.getModel();
        if (CollectionUtils.isEmpty(cancelRuleList)) {
            return null;
        }
        return cancelRuleList.stream().map(ruleVo -> {
            CancelPolicyDTO cancelPolicyDTO = new CancelPolicyDTO();
            cancelPolicyDTO.setId(ruleVo.getId());
            cancelPolicyDTO.setName(ruleVo.getRuleName());
            cancelPolicyDTO.setRuleType(ruleVo.getRuleType());
            cancelPolicyDTO.setStartDate(ruleVo.getStartDate());
            cancelPolicyDTO.setEndDate(ruleVo.getEndDate());
            cancelPolicyDTO.setFreeCancel(ruleVo.getFreeCancel());
            cancelPolicyDTO.setCostRule(ruleVo.getCostRule());
            cancelPolicyDTO.setCostRulePer(ruleVo.getCostRulePer());
            cancelPolicyDTO.setTimeoutPer(ruleVo.getTimeoutPer());
            cancelPolicyDTO.setStatus(ruleVo.getStatus());
            cancelPolicyDTO.setRuleList(ruleVo.getRuleTimeList().stream().map(cancelRuleTimeVo -> {
                CancelRuleDTO cancelRuleDTO = new CancelRuleDTO();
                cancelRuleDTO.setId(cancelRuleTimeVo.getId());
                cancelRuleDTO.setCancelRuleId(cancelRuleTimeVo.getCancelRuleId());
                cancelRuleDTO.setTimeType(cancelRuleTimeVo.getTimeType());
                cancelRuleDTO.setBeforeHour(cancelRuleTimeVo.getBeforeHour());
                cancelRuleDTO.setBeforeHourPer(cancelRuleTimeVo.getBeforeHourPer());
                return cancelRuleDTO;
            }).collect(Collectors.toList()));
            return cancelPolicyDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ServicePolicyStoreMappingDTO> servicePolicyStoreMappingSearch(Long merchantId) {
        Result<List<ServicePolicyStoreMappingDTO>> servicePolicyRes =
            servicePolicyService.servicePolicyStoreMappingSearch(merchantId);
        if (servicePolicyRes.isSuccess()) {
            return servicePolicyRes.getModel();
        }
        return null;
    }

    public void insertHourlyChargeForm(CtripStoreVO vo, List<CtripChargeDTO> hourlyList,
                                       List<StoreHourlyCharge> oldCharges, Map<String, CtripStoreVO.StoreVO> map) {
        Long channelId = OrderSourceEnum.OFFLINE.getSource().longValue();
        long t = System.currentTimeMillis();

        List<Long> storeIds = new ArrayList<>();
        for (CtripChargeDTO dto : hourlyList) {
            StoreHourlyCharge record = new StoreHourlyCharge();
            record.setMerchantId(vo.getMerchantId());
            CtripStoreVO.StoreVO storeVO = map.get(dto.getSupplierStoreCode());
            if (storeVO == null) {
                continue;
            }
            storeIds.add(storeVO.getStoreId());
            record.setStoreId(storeVO.getStoreId());
            record.setScene(dto.getScene());
            record.setChargeItem(dto.getChargeItem());
            int item = dto.getChargeItem();
            List<StoreHourlyCharge> tmpCharges = oldCharges.stream().filter(e ->
                    e.getStoreId().equals(storeVO.getStoreId()) &&
                            e.getChargeItem().equals(dto.getChargeItem()) &&
                            e.getScene().equals(dto.getScene())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tmpCharges)) {
                record.setId(tmpCharges.get(0).getId());
                record.setLastVer(tmpCharges.get(0).getLastVer() + 1);
            }
            // 收费项目 1.日租金+基础服务费 2.优享 3.尊享 4.儿童座椅
            if (item == 1) {
                String[] arr = new String[]{"1.00", "1.00", "1.00", "1.00", "1.00", "1.00", "1.00", "1.00"};
                List<CtripChargeDTO.CtripRateDTO> rateObjs = dto.getRange();
                int index = 0;
                if (CollectionUtils.isNotEmpty(rateObjs)) {
                    for (CtripChargeDTO.CtripRateDTO rateObj : rateObjs) {
                        for (int i = rateObj.getRangeStart(); i < rateObj.getRangeEnd(); i++) {
                            if (index > 7) {
                                break;
                            }
                            arr[index] = rateObj.getRatio().toString();
                            index++;
                        }
                    }
                    record.setChargeValue(String.join(",", arr));
                } else {
                    record.setChargeValue(Strings.EMPTY);
                }
            } else {
                // 收费方式 1.区间比例 2.固定金额 3.每小时固定金额 4.不收 5.收全部
                if (dto.getChargeWay() == 4) {
                    record.setChargeValue(Byte.toString(YesOrNoEnum.NO.getValue()));
                } else if (dto.getChargeWay() == 5) {
                    record.setChargeValue(Byte.toString(YesOrNoEnum.YES.getValue()));
                }
            }
            record.setOpTime(t);
            record.setChannelId(channelId);
            if (dto.getIsActive()) {
                record.setDeleted(YesOrNoEnum.NO.getValue());
            } else {
                record.setDeleted(YesOrNoEnum.YES.getValue());
            }

            if (record.getId() == null) {
                record.setLastVer(1);
                record.setCreateTime(t);
                storeHourlyChargeMapper.insertSelective(record);
            } else {
                storeHourlyChargeMapper.updateByPrimaryKeySelective(record);
            }
        }
        if (CollectionUtils.isNotEmpty(storeIds)) {
            storeIds = storeIds.stream().distinct().collect(Collectors.toList());
            for (Long storeId : storeIds) {
                redisService.remove(RedisConstant.StoreRedisKey.STORE_CHARGE_KEY + storeId);
            }
        }
    }

    private String getBusinessPeriod(List<Long> wIdx) {
        String str = "";
        for (long i = 1; i <= 7; i++) {
            if (wIdx.contains(i)) {
                str = str + "1";
            } else {
                str = str + "0";
            }
        }
        return str;
    }

    /**
     * 根据经纬度获取省市区
     */
    public String getAreaCodeJWD(double longitude, double latitude) {
        //注意key是在高德开放平台申请的key,具体获得key的步骤请查看网址:https://developer.amap.com/api/webservice/guide/create-project/get-key
        String parameters = "?key=" + gaodeWebKey;
        parameters += "&location=" + longitude + "," + latitude;//经纬度坐标
        //parameters += "&extensions=all";//返回结果控制，extensions 参数取值为 all 时会返回基本地址信息、附近 POI 内容、道路信息以及道路交叉口信息。
        parameters += "&batch=false";//批量查询控制，batch 参数设置为 false 时进行单点查询，此时即使传入多个经纬度也只返回第一个经纬度的地址解析查询结果。
        //parameters += "&roadlevel=0";//道路等级，当 roadlevel = 0 时，显示所有道路
        parameters += "&output=JSON";
        //parameters+="&radius=300";//搜索半径，radius取值范围在0~3000，默认是1000。单位：米
        String urlString = "https://restapi.amap.com/v3/geocode/regeo" + parameters;
        StringBuilder res = new StringBuilder();
        try {
            URL url = new URL(urlString);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            BufferedReader in =
                    new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                res.append(line);
            }
            in.close();
            //解析结果
            log.info("携程上货,门店;查询区县code；req url=" + urlString + "高德返回结果：" + res);
        } catch (Exception e) {
            log.info("携程上货,门店;获取地址信息异常");
            e.printStackTrace();
            return null;
        }
        JSONObject obj = JSONObject.parseObject(res.toString());
        obj = obj.getJSONObject("regeocode");
        if (obj == null) {
            log.error("携程上货,门店;高德获取地址信息异常：节点regeocode=null");
            return null;
        }
        obj = obj.getJSONObject("addressComponent");
        if (obj == null) {
            log.error("携程上货,门店;高德获取地址信息异常：节点addressComponent=null");


        }
        String code = obj.getString("adcode");
        if (obj == null) {
            log.error("携程上货,门店;高德获取地址信息异常：节点adcode=null");
            return null;
        }
        return code;
    }

    private int formatTime(String val) {
        val = val.replace(":", "");
        return Integer.parseInt(val);
    }

    private Date formatDate(String val) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(val);
    }
}
