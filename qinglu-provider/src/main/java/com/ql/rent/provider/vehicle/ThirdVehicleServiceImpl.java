package com.ql.rent.provider.vehicle;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.Constant;
import com.ql.dto.open.request.vehicle.VehicleStatusNotifyRequest;
import com.ql.dto.vehicle.*;
import com.ql.dto.vehicle.request.GetlVehicleRequest;
import com.ql.enums.VehicleInfoEnums;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.request.VehicleStatusNotifyReq;
import com.ql.rent.common.IRedisService;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dao.trade.DataCommonMapper;
import com.ql.rent.dao.vehicle.*;
import com.ql.rent.dao.vehicle.ex.RentMainMapperEx;
import com.ql.rent.entity.trade.DataCommon;
import com.ql.rent.entity.vehicle.*;
import com.ql.rent.entity.vehicle.ex.VehicleInfoExtraDO;
import com.ql.rent.enums.TimeUnitEnum;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.common.ResultEnum;
import com.ql.rent.enums.price.CalendarTypeEnum;
import com.ql.rent.enums.price.FeeTypeEnum;
import com.ql.rent.enums.store.HourlyChargeEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.enums.vehicle.VehicleStatusEnum;
import com.ql.rent.param.price.InsuranceAddApiQuery;
import com.ql.rent.param.vehicle.VehicleMediaQueryParam;
import com.ql.rent.provider.store.CommStore;
import com.ql.rent.provider.trade.OrderComponent;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.price.*;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.trade.IOrderHourlyService;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.common.ApiConnMoreVo;
import com.ql.rent.vo.price.*;
import com.ql.rent.vo.store.StoreHourlyChargeVo;
import com.ql.rent.vo.store.StoreHourlyVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.OrderHourlyVo;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.vehicle.*;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/29 13:52
 */
@Service
@Slf4j
public class ThirdVehicleServiceImpl implements IThirdVehicleService {

    private final Map<String, ServiceFeeTypeEnum> insuranceServiceNeedMap = new HashMap<>(3);

    // 初始化时，调用initInsuranceServiceNeedMap()方法
    {
        initInsuranceServiceNeedMap();
    }

    @Resource
    private RentCalendarMapper rentCalendarMapper;
    @Resource
    private RentDayPriceMapper rentDayPriceMapper;
    @Resource
    private InsuranceServicePriceMapper insuranceServicePriceMapper;
    @Resource
    private AddedServiceMapper addedServiceMapper;
    @Resource
    private IAddedServiceSettingService addedServiceSettingService;
    @Resource
    private IInsuranceServiceSettingService insuranceServiceSettingService;
    @Resource
    private RentMainMapperEx rentMainMapperEx;
    @Resource
    private IRentMainService rentMainService;
    @Resource
    private IThirdStoreService thirdStoreService;
    @Resource
    private IRedisService redisService;
    @Resource
    private IApiConnService apiConnService;
    @Resource
    private IRentChannelPriceService rentChannelPriceService;
    @Resource
    private VehicleWorryFreeRentChannelMapper vehicleWorryFreeRentChannelMapper;
    @Resource
    private IVehicleTagService vehicleTagService;
    @Resource
    private VehicleInfoMapper vehicleInfoMapper;
    @Resource
    private VehicleChannelMapper vehicleChannelMapper;
    @Resource
    private VehicleComponent vehicleComponent;
    @Resource
    private VehicleInfoAttMapper vehicleInfoAttMapper;
    @Resource
    private DataCommonMapper dataCommonMapper;
    @Resource
    private VehicleInfoInsuranceMapper vehicleInfoInsuranceMapper;
    @Resource
    private IVehicleModelService vehicleModelService;

    @Resource
    private InsuranceServicePriceChannelMapper insuranceServicePriceChannelMapper;
    @Resource
    private LeaseTermConfigService leaseTermConfigService;
    @Resource
    private IThirdIdRelationService thirdIdRelationService;
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private CtripPriceFilterInfoMapper ctripPriceFilterInfoMapper;

    @Resource
    private IVehicleMediaService vehicleMediaService;
    @Resource
    private ICtripVehicleService ctripVehicleService;
    @Resource
    private IOrderHourlyService orderHourlyService;
    @Resource
    private OrderComponent orderComponent;

    void initInsuranceServiceNeedMap() {
        insuranceServiceNeedMap.put("基础保险", ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE);
        insuranceServiceNeedMap.put("优享保险", ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE);
        insuranceServiceNeedMap.put("尊享保险", ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE);
    }

    private static boolean fiterCalendarDate(RentCalendar e, Date startOldDate, Date endOldDate) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate;
        Date endDate;
        try {
            startDate = dateFormat.parse(dateFormat.format(startOldDate));
            endDate = dateFormat.parse(dateFormat.format(endOldDate));
        } catch (Exception ee) {
            throw new BizException("日期转换失败");
        }
        return
            (startDate.getTime() >= e.getStartDate().getTime() && startDate.getTime() <= e.getEndDate().getTime()) ||
            (endDate.getTime() >= e.getStartDate().getTime() && endDate.getTime() <= e.getEndDate().getTime()) ||
            (e.getStartDate().getTime() >= startDate.getTime() && e.getStartDate().getTime() <= endDate.getTime()) ||
            (e.getEndDate().getTime() >= startDate.getTime() && e.getEndDate().getTime() <= endDate.getTime());
    }

    @Override
    public void selectModelCalendarPrice(Map<Integer, List<VehicleModelUniDTO<List<DailyPriceDTO>>>> modelPriceMap, Long merchantId, Long channelId, List<VehicleModelUniDTO> storeAndModelIds, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId) {
        List<VehicleModelUniDTO<List<DailyPriceDTO>>> retList = selectModelCalendarPrice(merchantId, channelId, storeAndModelIds, pickUpTime, returnTime, checkMinBook, orderId, null, null, false);
        modelPriceMap.put(0, retList);
    }

    @Override
    public List<VehicleModelUniDTO<List<DailyPriceDTO>>> selectModelCalendarPrice(Long merchantId, Long channelId, List<VehicleModelUniDTO> storeAndModelIds, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId, String debugInfo, List<String> outMsgMap, boolean platformCal) {
        log.info("selectModelCalendarPrice入参,merchantId={},channelId={},storeAndModelIds={},pickUpTime={},returnTime={},checkMinBook={},orderId={},platformCal={}",
                merchantId, channelId, JSON.toJSONString(storeAndModelIds), pickUpTime, returnTime, checkMinBook, orderId,platformCal);
        long t = System.currentTimeMillis();
        if (channelId == null || pickUpTime == null || returnTime == null) {
            log.error("通道、取车还车时间为空");
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(storeAndModelIds)) {
            log.error("门店车型参数为空");
            return new ArrayList<>();
        }

        // 如果不是携程标准化订单, orderId为null
        if (orderId != null) {
            if (orderComponent.isOrderBizByBizType(OrderBizTypeEnum.CTRIP_STANDARD_FEE, orderId)) {
                platformCal = true;
            } else {
                orderId = null;
            }
        }
        // todo 以platformCal为准,为true是携程费用标准订单

        // 如数量是1，认为是详情页，下单这些页面；询价也有可能为1的情况
        boolean vehicleModelSingle = storeAndModelIds.size() == 1;
        // 查询单日价格
        Map<Long, List<Long>> storeAndModelMap = storeAndModelIds.stream().collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId,
                Collectors.collectingAndThen(Collectors.toList(), crs -> crs.stream()
                        .map(VehicleModelUniDTO::getVehicleModelId)
                        .collect(Collectors.toList()))));
        List<Long> storeIds = Lists.newArrayList(storeAndModelMap.keySet());
        Date startDate = new Date(pickUpTime.getTime());
        startDate.setHours(0);
        startDate.setMinutes(0);
        startDate.setSeconds(0);
        RentDayPriceExample dayExample = new RentDayPriceExample();
        dayExample.createCriteria().andMerchantIdEqualTo(merchantId)
            .andStoreIdIn(storeIds)
            .andChannelIdEqualTo(channelId)
            .andRentDateBetween(startDate, returnTime)
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<RentDayPrice> dayPriceList = rentDayPriceMapper.selectByExample(dayExample);
        Map<Long, Map<Long, List<RentDayPrice>>> dayStoreMaps = dayPriceList.stream()
                .collect(Collectors.groupingBy(RentDayPrice::getStoreId,
                         Collectors.groupingBy(RentDayPrice::getVehicleModelId, Collectors.toList())));
        Span.current().setAttribute("　ae查询单日格耗时,storeIds=" + JSON.toJSONString(storeIds) + ",from db", System.currentTimeMillis() - t);
        // 查询无忧租设置
        long parentChannelId = CommStore.getParentChannelId(channelId);
        Map<Long, Map<Long, List<VehicleWorryFreeRentChannel>>> worryFreeMaps = new HashMap<>();
        // 查询飞猪安心租及携程无忧租
        if (Arrays.asList(OrderSourceEnum.CTRIP.longValue(), OrderSourceEnum.FEIZHU.longValue()).contains(parentChannelId)) {
            VehicleWorryFreeRentChannelExample worryFreeMapper = new VehicleWorryFreeRentChannelExample();
            worryFreeMapper.createCriteria().andMerchantIdEqualTo(merchantId)
                    .andStoreIdIn(storeIds)
                    .andChannelIdEqualTo(parentChannelId)
                    .andStatusEqualTo(YesOrNoEnum.YES.getValue())
                    .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            List<VehicleWorryFreeRentChannel> worryFreeList = vehicleWorryFreeRentChannelMapper.selectByExample(worryFreeMapper);
            worryFreeMaps = worryFreeList.stream()
                    .collect(Collectors.groupingBy(VehicleWorryFreeRentChannel::getStoreId,
                            Collectors.groupingBy(VehicleWorryFreeRentChannel::getVehicleModelId, Collectors.toList())));
            Span.current().setAttribute("　ae查询无忧租耗时,storeIds=" + JSON.toJSONString(storeIds) + ",from db", System.currentTimeMillis() - t);
        }
        // 查询日历
        List<RentCalendar> calendarAllList = getCalendarList(merchantId);
        //        自定义价格 门店拆分 0:基础价格(平时，周末);1:节假日;2:自定义价格
        calendarAllList=calendarAllList.stream()
                .filter(f ->!(f.getCalendarType() == 2 && CollectionUtils.intersection(JSONUtil.toList(f.getStoreIdList(), Long.class),storeIds).isEmpty()) )
                .collect(Collectors.toList());

        List<RentCalendar> calendarList = new ArrayList<>();
        List<Long> calendarIds = new ArrayList<>();
        for (RentCalendar calendar : calendarAllList) {
            if (calendar.getCalendarType().equals(CalendarTypeEnum.BASE.getType()) ||
                fiterCalendarDate(calendar, pickUpTime, returnTime)) {
                calendarList.add(calendar);
                calendarIds.add(calendar.getId());
            }
        }
        List<RentChannelPriceBaseVo> channelPriceBaseVos = rentChannelPriceService.list(merchantId, storeAndModelMap, calendarIds, channelId);
        Map<Long, Map<Long, List<RentChannelPriceBaseVo>>> storeModelPriceMaps = channelPriceBaseVos.stream()
                .collect(Collectors.groupingBy(RentChannelPriceBaseVo::getStoreId,
                         Collectors.groupingBy(RentChannelPriceBaseVo::getVehicleModelId, Collectors.toList())));
        t = System.currentTimeMillis();

        //查询租期信息
        Map<Long, Map<Long, List<ModelLeaseTermVo>>> modelLeaseTermByStore = leaseTermConfigService.obtainByStoreIdAndVehicleId(merchantId, channelId, storeAndModelMap, pickUpTime, returnTime);
        Span.current().setAttribute("　ae获取租期耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();

        // 获取零散小时规则
        List<StoreHourlyChargeVo> chargeVos = null;
        List<StoreHourlyVo> chargeVosV2 = null;
        Result<Boolean> standardStatus;
        // 如携程渠道：platformCal不变
        // 如其他渠道：看标准化是否开启；
        if (platformCal == false || !CommStore.getParentChannelId(channelId).equals(Constant.ChannelId.CTRIP)) {
            standardStatus = apiConnService.getApiConnOpenStandardStatus(merchantId);
            if (standardStatus.isSuccess() && standardStatus.getModel()) {
                platformCal = true;
            }
        }
        if (orderId != null) {
            Result<List<OrderHourlyVo>> orderHourlyResult = orderHourlyService.listHourly(orderId);
            chargeVosV2 = new ArrayList<>();
            if (orderHourlyResult.isSuccess()) {
                for (OrderHourlyVo OrderHourlyVo : orderHourlyResult.getModel()) {
                    StoreHourlyVo storeHourlyVo = new StoreHourlyVo();
                    BeanUtils.copyProperties(OrderHourlyVo, storeHourlyVo);
                    chargeVosV2.add(storeHourlyVo);
                }
            }
        } else {
            if (platformCal) {
                chargeVosV2 = thirdStoreService.getChargeListV2(merchantId, storeIds, CommStore.getParentChannelId(channelId), channelId);
            } else {
                chargeVos = thirdStoreService.getChargeList(storeIds, CommStore.getParentChannelId(channelId));
            }
        }
        Span.current().setAttribute("　ae获取零散小时耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();
        // 数据组装
        List<RentCalendar> tmpBaseCalendarList = calendarList.stream()
            .filter(e -> e.getCalendarType().equals(CalendarTypeEnum.BASE.getType()))
            .collect(Collectors.toList());

        // 直连商家 或 当前询价是携程渠道 需要check租期 todo
        boolean checkBook = Constant.ChannelId.CTRIP.equals(channelId) || Constant.ChannelId.CTRIP_RESALE.equals(channelId);
        if (!checkBook) {
            String rKey = String.format(RedisConstant.CtripRedisKey.CHECK_BOOKING_METHOD, merchantId);
            Object shangQiIsStockOpen = redisService.get(rKey);
            checkBook = Boolean.TRUE.equals(shangQiIsStockOpen);
        }

        List<VehicleModelUniDTO<List<DailyPriceDTO>>> retList = new ArrayList<>();
        String tmpDebugInfo = null;
        for (Map.Entry<Long, List<Long>> entry : storeAndModelMap.entrySet()) {
            Long storeId = entry.getKey();
            List<SplitBusiDateVO> dates = null;
            if (chargeVosV2 != null) {
                // 零散新规则
                dates = splitBusiDateV2(chargeVosV2, pickUpTime, returnTime);
            } else {
                List<double[]> charges = getCharges(merchantId, storeId, chargeVos);
                // index下标  0:满天  1:未满天
                // 数据库存储  1满一天[N+X]  2不满一天[0+X]
                dates = splitBusiDate(charges.get(0), charges.get(1), pickUpTime, returnTime);
                if (!StringUtils.isEmpty(debugInfo)) {
                    log.info("零散小时,商家={},门店={},N+0={},N+X={}", merchantId, storeId,
                            JSON.toJSONString(charges.get(0)), JSON.toJSONString(charges.get(1)));
                }
            }
            Map<Long, List<ModelLeaseTermVo>> modelLeaseTermListByModelId = modelLeaseTermByStore.get(storeId);
            List<Long> vehicleModelIds = entry.getValue();
            Map<Long, List<RentDayPrice>> dayStoreMap = dayStoreMaps.get(storeId);
            Map<Long, List<RentChannelPriceBaseVo>> storeModelPriceMap = storeModelPriceMaps.get(storeId);
            Map<Long, List<VehicleWorryFreeRentChannel>> storeModelWorryFreeMap = worryFreeMaps.get(storeId);
            for (Long vehicelModelId : vehicleModelIds) {
                VehicleModelUniDTO<List<DailyPriceDTO>> paramDto = new VehicleModelUniDTO<List<DailyPriceDTO>>();
                paramDto.setStoreId(storeId);
                paramDto.setVehicleModelId(vehicelModelId);
                if (!StringUtils.isEmpty(debugInfo)) {
                    tmpDebugInfo = debugInfo + ",vehicleModelId=" + paramDto.getVehicleModelId();
                }

                List<DailyPriceDTO> dtoList = new ArrayList<>();
                boolean addFlg = true;
                List<RentDayPrice> tmpSMDayPriceList = null;
                if (dayStoreMap != null) {
                    tmpSMDayPriceList = dayStoreMap.get(vehicelModelId);
                }
                if (tmpSMDayPriceList == null) {
                    tmpSMDayPriceList = new ArrayList<>();
                }
                List<RentChannelPriceBaseVo> tmpSMPriceList = null;
                if (storeModelPriceMap != null) {
                    tmpSMPriceList = storeModelPriceMap.get(vehicelModelId);
                }
                if (tmpSMPriceList == null) {
                    tmpSMPriceList = new ArrayList<>();
                }
//                //直连商家 校验最短租期 todo
//                String rKey = String.format(RedisConstant.CtripRedisKey.CHECK_BOOKING_METHOD, merchantId);
//                Object shangQiIsStockOpen = redisService.get(rKey);
//                //
//                if (Objects.nonNull(shangQiIsStockOpen) && BooleanUtils.isTrue((Boolean) shangQiIsStockOpen)){
                if (checkBook) {
                    if(modelLeaseTermListByModelId!=null&&modelLeaseTermListByModelId.get(vehicelModelId)!=null){
                        List<ModelLeaseTermVo> modelLeaseTermVos = modelLeaseTermListByModelId.get(vehicelModelId);
                        ModelLeaseTermVo modelLeaseTermVo = modelLeaseTermVos.stream()
                                .filter(e -> returnTime.after(e.getStartDate()))
                                .filter(calendar -> {
                                    LocalDateTime endDateTime = LocalDateTime.of(DateUtil.dateToLocalDate(calendar.getEndDate()), LocalTime.MAX);
                                    LocalDateTime pickUpDateTime = DateUtil.dateTimeToLocalDateTime(pickUpTime);
                                    return calendar.getStartDate().after(pickUpTime) || endDateTime.isAfter(pickUpDateTime);
                                })
                                .min(Comparator.comparingLong(calendar -> Math.abs(calendar.getStartDate().getTime() - pickUpTime.getTime())))
                                .orElse(null);
                        if(Objects.nonNull(modelLeaseTermVo)){
                            boolean greaterEq = returnTime.getTime() >= pickUpTime.getTime() + modelLeaseTermVo.getMinRentTerm().intValue() * 60 * 60 * 1000;
                            if (BooleanUtils.isFalse(greaterEq)) {
                                if (!StringUtils.isEmpty(tmpDebugInfo)) {
                                    outMsgMap.add("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足新租期["+modelLeaseTermVo.getThrIdName()+"]最短租期" + modelLeaseTermVo.getMinRentTerm() + "小时");
                                }
                                if (vehicleModelSingle) {
                                    log.info("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足新租期["+modelLeaseTermVo.getThrIdName()+"]最短租期" + modelLeaseTermVo.getMinRentTerm() + "小时");
                                }
                                continue;
                            }
                        }
                    }
                    //非直连商家  租期校验
                } else {
                // 自定义及节假日价格
                List<Long> filterRentCalendarIds = tmpSMPriceList.stream().filter(Objects::nonNull)
                        .map(RentChannelPriceBaseVo::getRentCalendarId).collect(Collectors.toList());
                RentCalendar rentCalendar = calendarAllList.stream()
                        .filter(e -> !e.getCalendarType().equals(CalendarTypeEnum.BASE.getType()))
                        .filter(e -> returnTime.after(e.getStartDate()))
                        .filter(calendar -> {
                            LocalDateTime endDateTime = LocalDateTime.of(DateUtil.dateToLocalDate(calendar.getEndDate()), LocalTime.MAX);
                            LocalDateTime pickUpDateTime = DateUtil.dateTimeToLocalDateTime(pickUpTime);
                            return calendar.getStartDate().after(pickUpTime) || endDateTime.isAfter(pickUpDateTime);
                        })
                        .filter(e -> filterRentCalendarIds.contains(e.getId()))
                        .sorted(Comparator.comparingLong(RentCalendar::getCalendarType).reversed())
                        .min(Comparator.comparingLong(calendar -> Math.abs(calendar.getStartDate().getTime() - pickUpTime.getTime())))
                        .orElse(null);

                // 校验车型最短租期规则，不满足则不透出，节假日和自定义规则
                if (Objects.nonNull(rentCalendar)) {
                    RentChannelPriceBaseVo baseVo = tmpSMPriceList.stream()
                            .filter(e -> e.getRentCalendarId().equals(rentCalendar.getId()))
                            .findFirst().orElse(null);
                    if (Objects.nonNull(baseVo)) {
                        boolean greaterEq = returnTime.getTime() >= pickUpTime.getTime() + baseVo.getLeastRentalTime().intValue() * 60 * 60 * 1000;
                        if (BooleanUtils.isFalse(greaterEq)) {
                            if (!StringUtils.isEmpty(tmpDebugInfo)) {
                                RentCalendar calendar = calendarList.stream().filter(e -> e.getId().equals(baseVo.getRentCalendarId())).findFirst().orElse(null);
                                String calendarName = calendar == null ? baseVo.getRentCalendarId().toString() : calendar.getRentName();
                                outMsgMap.add("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足[" + calendarName+ "]最短租期");
                            }
                            if (vehicleModelSingle) {
                                RentCalendar calendar = calendarList.stream().filter(e -> e.getId().equals(baseVo.getRentCalendarId())).findFirst().orElse(null);
                                String calendarName = calendar == null ? baseVo.getRentCalendarId().toString() : calendar.getRentName();
                                log.info("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足[" + calendarName+ "]最短租期");
                            }
                            continue;
                        }
                    }
                }
                // 基础价格规则
                else {
                    RentCalendar rentCalendar1 = tmpBaseCalendarList.stream().findFirst().orElse(null);
                    if (Objects.nonNull(rentCalendar1)) {
                        RentChannelPriceBaseVo baseVo = tmpSMPriceList.stream().filter(e -> e.getRentCalendarId().equals(rentCalendar1.getId()))
                                .findFirst().orElse(null);
                        if (Objects.nonNull(baseVo) && Objects.nonNull(baseVo.getLeastRentalTime())) {
                            boolean greaterEq = returnTime.getTime() >= pickUpTime.getTime() + baseVo.getLeastRentalTime().intValue() * 60 * 60 * 1000;
                            if (BooleanUtils.isFalse(greaterEq)) {
                                if (!StringUtils.isEmpty(tmpDebugInfo)) {
                                    outMsgMap.add("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足[基础价格]最短租期");
                                }
                                if (vehicleModelSingle) {
                                    log.info("取车门店[" + storeId + "],车型[" + vehicelModelId + "],未满足[基础价格]最短租期");
                                }
                                continue;
                            }
                        }
                    }
                }
                }
                int dayIndex = 0;
                for (SplitBusiDateVO date : dates) {
                    Integer dayPrice = null;
                    Integer daySinglePrice = null;
                    // 单日价格
                    List<RentDayPrice> tmpDayPriceList = tmpSMDayPriceList.stream()
                            .filter(e -> e.getRentDate().equals(date.getDate()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tmpDayPriceList) && tmpDayPriceList.get(0).getPrice() > 0) {
                        daySinglePrice = tmpDayPriceList.get(0).getPrice();
                        dayPrice = daySinglePrice;
                    }

                    RentChannelPriceBaseVo rentChannelPrice = null;
                    // 自定义及节假日价格
                    List<RentCalendar> tmpCalendarList = calendarList.stream().filter(
                            e -> date.getDate().compareTo(e.getStartDate()) >= 0
                                    && date.getDate().compareTo(e.getEndDate()) <= 0
                                    && !e.getCalendarType().equals(CalendarTypeEnum.BASE.getType())
                    ).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tmpCalendarList)) {
                        for (RentCalendar calendar : tmpCalendarList) {
                            RentChannelPriceBaseVo baseVo = tmpSMPriceList.stream()
                                    .filter(e -> e.getRentCalendarId().equals(calendar.getId()) && e.getPrice() > 0)
                                    .findFirst().orElse(null);
                            if (Objects.nonNull(baseVo)) {
                                rentChannelPrice = baseVo;
                                if (daySinglePrice == null) {
                                    dayPrice = rentChannelPrice.getPrice();
                                } else {
                                    dayPrice = daySinglePrice;
                                }
                                break;
                            }
                        }
                    }
                    // 获取基础价格
                    if (rentChannelPrice == null) {
                        if (CollectionUtils.isNotEmpty(tmpBaseCalendarList)) {
                            RentCalendar calendar = tmpBaseCalendarList.get(0);
                            // 周末
                            int weekIndex = date.getDate().getDay();
                            if (weekIndex == 0) weekIndex = 7;
                            boolean weekFlg = calendar.getBasePricePeriod().substring(weekIndex - 1, weekIndex).equals("1");
                            RentChannelPriceBaseVo baseVo = tmpSMPriceList.stream()
                                    .filter(e -> e.getRentCalendarId().equals(calendar.getId()))
                                    .findFirst().orElse(null);
                            if (Objects.nonNull(baseVo)) {
                                rentChannelPrice = baseVo;
                                if (weekFlg) {
                                    // 周日
                                    if (daySinglePrice == null) {
                                        dayPrice = rentChannelPrice.getPriceWeekend();
                                    } else {
                                        dayPrice = daySinglePrice;
                                    }
                                } else {
                                    // 平日
                                    if (daySinglePrice == null) {
                                        dayPrice = rentChannelPrice.getPrice();
                                    } else {
                                        dayPrice = daySinglePrice;
                                    }
                                }
                                // 兼容凹凸基础价：周一-周日每天价格
                                if (StringUtils.isNotEmpty(baseVo.getPriceMondaySunday())) {
                                    String[] wkDays = baseVo.getPriceMondaySunday().split(",");
                                    String wkDayPrice = wkDays[weekIndex - 1];
                                    if (StringUtils.isNotEmpty(wkDayPrice)) {
                                        if (daySinglePrice == null) {
                                            dayPrice = Integer.valueOf(wkDayPrice.trim());
                                        } else {
                                            dayPrice = daySinglePrice;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 租期&提前check
                    //if (dayIndex == 0) {   // 适配上汽
                    if (dayIndex == 0 && rentChannelPrice != null) {
                        if (!checkPriceTime(rentChannelPrice, pickUpTime, returnTime, checkMinBook, tmpDebugInfo, outMsgMap)) {
                            addFlg = false;
                            if (vehicleModelSingle) {
                                log.info("取车门店[" + storeId + "],车型[" + vehicelModelId + "],提前预定不满足,rentChannelPrice={}", JSON.toJSONString(rentChannelPrice));
                            }
                            break;
                        }
                    }
                    dayIndex++;
                    //if (dayPrice == null || dayPrice <= 0 || rentChannelPrice == null) {
                    if (dayPrice == null || dayPrice <= 0) {  // 适配上汽
                        addFlg = false;
                        if (!StringUtils.isEmpty(tmpDebugInfo)) {
                            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            outMsgMap.add("取车门店[" + storeId + "],车型[" + vehicelModelId + "],日期[" + dateFormat.format(date.getDate()) + "],价格未设置");
                        }
                        if (vehicleModelSingle) {
                            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            log.info("取车门店[" + storeId + "],车型[" + vehicelModelId + "],日期[" + dateFormat.format(date.getDate()) + "],价格未设置");
                        }
                        break;
                    }
                    // 组装返回
                    DailyPriceDTO dto = new DailyPriceDTO();
                    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    dto.setDate(dateFormat.format(date.getDate()));
                    dto.setHour(date.getHour());
                    dto.setAllDay(date.isAllDay());
                    dayPrice = getIntFeeYuan(dayPrice);
                    dto.setPrice(dayPrice);
                    int partDayPrice = (int) (dayPrice * date.getPer());
                    // 向上取整到元
                    partDayPrice = getIntFeeYuan(partDayPrice);
                    dto.setPartDailyPrice(partDayPrice);
                    dto.setPer(date.getPer());
                    // 无优租
                    Integer worryFreePrice = null;
                    Integer worryFreeDailyPrice = null;
                    List<VehicleWorryFreeRentChannel> worryFreeRentChannels = null;
                    if (storeModelWorryFreeMap != null) {
                        worryFreeRentChannels = storeModelWorryFreeMap.get(vehicelModelId);
                    }
                    if (CollectionUtils.isNotEmpty(worryFreeRentChannels)) {
                        // 整天
                        worryFreePrice =  (int) (dayPrice * (1 + Float.valueOf(worryFreeRentChannels.get(0).getPercentageIncrease()) / 100));
                        worryFreePrice = getIntFeeYuan(worryFreePrice);
                        // 实际时间
                        worryFreeDailyPrice =  (int) (partDayPrice * (1 + Float.valueOf(worryFreeRentChannels.get(0).getPercentageIncrease()) / 100));
                        worryFreeDailyPrice = getIntFeeYuan(worryFreeDailyPrice);
                    }
                    dto.setWorryFreePrice(worryFreePrice);
                    dto.setWorryFreeDailyPrice(worryFreeDailyPrice);
                    dtoList.add(dto);
                }
                if (addFlg) {
                    paramDto.setData(dtoList);
                    retList.add(paramDto);
                }
            }
        }
        Span.current().setAttribute("　ae其他处理耗时", System.currentTimeMillis() - t);
        return retList;
    }

    private Integer getIntFeeYuan(Integer fee) {
        if (fee == null) {
            return null;
        }
        return fee % 100 == 0 ? fee : ((int) (fee / 100) + 1) * 100;
    }

    private List<RentCalendar> getCalendarList(Long merchantId){
        Long t = System.currentTimeMillis();
        List<RentCalendar> calendarAllList = (List<RentCalendar>) redisService.get(RedisConstant.PriceRedisKey.PRICE_CALENDAR + merchantId);
        String dbOrRedis = "redis";
        if (calendarAllList == null) {
            RentCalendarExample example = new RentCalendarExample();
            example.createCriteria().andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
            example.setOrderByClause("calendar_type DESC");
            calendarAllList = rentCalendarMapper.selectByExample(example);
            redisService.set(RedisConstant.PriceRedisKey.PRICE_CALENDAR + merchantId, calendarAllList, RedisConstant.RedisExpireTime.DAY_10);
            dbOrRedis = "db";
        }
        Span.current().setAttribute("　ae查询价格日历耗时,merchantId=" + merchantId + ",from " + dbOrRedis, System.currentTimeMillis() - t);
        return calendarAllList;
    }

    private boolean checkPriceTime(RentChannelPriceBaseVo price, Date pickUpTime, Date returnTime, boolean checkMinBook, String debugInfo, List<String> outMsgMap) {
        if (price == null) {
            if (!StringUtils.isEmpty(debugInfo)) {
                log.info(debugInfo + ",未命中日历,车型过滤返回");
            }
            return false;
        }
        if (pickUpTime == null) {
            throw new BizException("取车时间不能为空");
        } else if (returnTime == null) {
            throw new BizException("还车时间不能为空");
        }
        long rentTime = returnTime.getTime() - pickUpTime.getTime();
        long minTime = price.getLeastRentalTime() * 1000 * 60 * 60;
       /* if (checkMinBook && minTime != 0 && rentTime < minTime) {
            if (!StringUtils.isEmpty(debugInfo)) {
                //log.info(debugInfo + ",最小租期不满足{}小时,车型过滤返回", price.getLeastRentalTime());
                outMsgMap.put("最小租期", "最小租期不满足" + price.getLeastRentalTime() + "小时,车型过滤返回");
            }
            //log.info("==ThirdVehicleService.checkPriceRentTime rentTime < minRentTime {}hour", price.getLeastRentalTime());
            return false;
        }*/
        long nowTime = System.currentTimeMillis();
        //提前预定期增加 单位
        long minBook = TimeUnitEnum.MinAdvanceBookingUnitEnum.convertBookingByUnit(price.getLatestBookingUnit(),price.getLatestBookingTime());
//        long minBook = price.getLatestBookingTime().multiply(new BigDecimal(1000)).longValue() * 60 * 60;
        //携程和携程 分销 取消 sku 提前预定期校验  && price.getChannelId() != 2L && price.getChannelId() != 10L
        if (checkMinBook && minBook != 0 && pickUpTime.getTime() < nowTime + minBook && price.getChannelId() != 2L && price.getChannelId() != 10L) {
            if (!StringUtils.isEmpty(debugInfo)) {
                //log.info(debugInfo + ",提前预定不满足{}小时,车型过滤返回", price.getLatestBookingTime());
                RentCalendar calendar = rentCalendarMapper.selectByPrimaryKey(price.getRentCalendarId());
                String calendarStr = calendar == null ? price.getRentCalendarId().toString() : calendar.getRentName();
                outMsgMap.add("取车门店[" + price.getStoreId() + "],车型[" + price.getVehicleModelId() + "],日历[" + calendarStr + "],提前预定不满足" + price.getLatestBookingTime() + TimeUnitEnum.MinAdvanceBookingUnitEnum.forUnitByValue(price.getLatestBookingUnit()));
            }
            //log.info("==ThirdVehicleService.checkPriceBookTime pickupTime < minBookTime {}hour", price.getLatestBookingTime());
            return false;
        }
        return true;
    }

    @Override
    public List<DailyPriceDTO> selectModelCalendarPrice(Long merchantId, Long channelId, VehicleModelUniDTO storeAndModelId, Date pickUpTime, Date returnTime, boolean checkMinBook, Long orderId, boolean platformCal) {
        List<VehicleModelUniDTO<List<DailyPriceDTO>>> vehicleModelUniDTOS =
                this.selectModelCalendarPrice(merchantId, channelId, Lists.newArrayList(storeAndModelId), pickUpTime, returnTime, checkMinBook, orderId,null, null, platformCal);
        if (null == vehicleModelUniDTOS || vehicleModelUniDTOS.isEmpty()) {
            return new ArrayList<>();
        }
        return vehicleModelUniDTOS.get(0).getData();
    }

    @Override
    public void selectModelService(Map<Integer, List<VehicleModelUniDTO<List<ServiceItemDTO>>>> modelServiceMap, Long merchantId, List<VehicleModelUniDTO> storeAndModelIds, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas) {
        List<VehicleModelUniDTO<List<ServiceItemDTO>>> retList = selectModelService(merchantId, storeAndModelIds, channelId, pickUpTime, returnTime, isSaas, null, null);
        modelServiceMap.put(0, retList);
    }

    //@Override
    private List<VehicleModelUniDTO<List<ServiceItemDTO>>> selectModelServicebak(Long merchantId, List<VehicleModelUniDTO> storeAndModelIds, Date pickUpTime, Date returnTime, Boolean isSaas) {
        //Span.current().updateName("车型服务项");
        long t = System.currentTimeMillis();
        List<Long> storeIds = storeAndModelIds.stream().map(e -> e.getStoreId()).distinct().collect(Collectors.toList());
        Map<Long, List> map = new HashMap<>();
        for (Long storeId : storeIds) {
            List<Long> modeIds = storeAndModelIds.stream().filter(e -> e.getStoreId().equals(storeId)).map(e -> e.getVehicleModelId()).collect(
                    Collectors.toList());
            if (CollectionUtils.isNotEmpty(modeIds)) {
                map.put(storeId, modeIds);
            }
        }
        // 保险
        Result<List<InsuranceServiceSettingVo>> baseInsuranceResult = insuranceServiceSettingService.insuranceList(merchantId);
        List<Long> iIds = baseInsuranceResult.getModel().stream()
            .filter(e -> e.getStatus().equals(YesOrNoEnum.YES.getValue()))
            .map(e -> e.getId()).collect(Collectors.toList());
        InsuranceServicePriceExample example = new InsuranceServicePriceExample();
        int i = 0;
        for (Long storeId : storeIds) {
            if (i == 0) {
                example.createCriteria().andStoreIdEqualTo(storeId).andStatusEqualTo(YesOrNoEnum.YES.getValue())
                    .andVehicleModelIdIn(map.get(storeId)).andInsuranceServiceSettingIdIn(iIds);
            } else {
                example.or().andStoreIdEqualTo(storeId).andStatusEqualTo(YesOrNoEnum.YES.getValue())
                    .andVehicleModelIdIn(map.get(storeId)).andInsuranceServiceSettingIdIn(iIds);
            }
            i++;
        }
        List<InsuranceServicePrice> insuranceServicePriceList = insuranceServicePriceMapper.selectByExample(example);
        insuranceServicePriceList = insuranceServicePriceList.stream()
            .filter(e -> iIds.contains(e.getInsuranceServiceSettingId()))
            .collect(Collectors.toList());
        Map<Long, InsuranceServiceSettingVo> insuranceBaseMap = baseInsuranceResult.getModel().stream()
                .collect(Collectors.toMap(insurance -> insurance.getId(), insurance -> insurance));
        Span.current().setAttribute("　af查询保险服务耗时", System.currentTimeMillis() - t);
        t = System.currentTimeMillis();


        // 附加
        Result<List<AddedServiceSettingVo>> baseAddedResult = addedServiceSettingService.addedList(merchantId);
        List<Long> aIds = baseAddedResult.getModel().stream()
            .filter(e -> e.getStatus().equals(YesOrNoEnum.YES.getValue()))
            .map(e -> e.getId()).collect(Collectors.toList());
        AddedServiceExample addExample = new AddedServiceExample();
        i = 0;
        for (Long storeId : storeIds) {
            if (i == 0) {
                addExample.createCriteria().andStoreIdEqualTo(storeId).andStatusEqualTo(YesOrNoEnum.YES.getValue())
                    .andVehicleModelIdIn(map.get(storeId)).andAddedServiceSettingIdIn(aIds);
            } else {
                addExample.or().andStoreIdEqualTo(storeId).andStatusEqualTo(YesOrNoEnum.YES.getValue())
                    .andVehicleModelIdIn(map.get(storeId)).andAddedServiceSettingIdIn(aIds);
            }
            i++;
        }
        List<AddedService> addedServiceList = addedServiceMapper.selectByExample(addExample);

        addedServiceList = addedServiceList.stream()
            .filter(e -> aIds.contains(e.getAddedServiceSettingId()))
            .collect(Collectors.toList());
        Map<Long, AddedServiceSettingVo> addedBaseMap = baseAddedResult.getModel().stream()
                .collect(Collectors.toMap(added -> added.getId(), added -> added));
        Span.current().setAttribute("　af查询附加服务耗时", System.currentTimeMillis() - t);

        List<VehicleModelUniDTO<List<ServiceItemDTO>>> retList = new ArrayList<>();
        for (VehicleModelUniDTO paramDto : storeAndModelIds) {
            VehicleModelUniDTO<List<ServiceItemDTO>> service = new VehicleModelUniDTO<List<ServiceItemDTO>>();
            BeanUtils.copyProperties(paramDto, service);
            List<ServiceItemDTO> dtoList = new ArrayList<>();
            // 保险
            List<InsuranceServicePrice> tmpList = insuranceServicePriceList.stream().filter(
                            e -> e.getStoreId().equals(paramDto.getStoreId()) &&
                                    e.getVehicleModelId().equals(paramDto.getVehicleModelId()))
                    // 判断是否开启
                    .filter(o -> null != o.getStatus() && o.getStatus().compareTo(YesOrNoEnum.YES.getValue()) == 0)
                    // 价格保护 0的时候不进行资源露出
                    .filter(o -> null != o.getPrice() && o.getPrice().compareTo(0) > 0)
                    .collect(Collectors.toList());
            for (InsuranceServicePrice insurance : tmpList) {
                ServiceItemDTO dto = new ServiceItemDTO();
                dto.setId(insurance.getId());
                dto.setSettingId(insurance.getInsuranceServiceSettingId());
                dto.setOnHighestPrice(insurance.getOnHighestPrice());
                dto.setType(OrderTypeEnum.ADD_INSURANCE.getType());
                InsuranceServiceSettingVo baseVo = insuranceBaseMap.get(insurance.getInsuranceServiceSettingId());
                if (baseVo != null) {
                    dto.setRequired(baseVo.getRequired());
                    // 先固定ID判断
                    if (baseVo.getParentId() == 1L) {
                        dto.setCode(ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceCode());
                        dto.setName(ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceName());
                    } else if (baseVo.getParentId() == 2L) {
                        dto.setCode(ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceCode());
                        dto.setName(ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceName());
                    } else if (baseVo.getParentId() == 3L) {
                        dto.setCode(ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceCode());
                        dto.setName(ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceName());
                    } else {
                        dto.setName(baseVo.getName());
                        if (isSaas == null || !isSaas) {
                            continue;
                        }
                    }
                }
                dto.setUnit("天");
                dto.setDescription(StringUtils.EMPTY);
                dto.setPrice(insurance.getPrice()); //保险有个最高价格*7天
                dto.setOnCharge(YesOrNoEnum.YES.getValue());
                dtoList.add(dto);
            }
            // 附加
            List<AddedService> addedTmpList = addedServiceList.stream().filter(
                            e -> e.getStoreId().equals(paramDto.getStoreId()) &&
                                    e.getVehicleModelId().equals(paramDto.getVehicleModelId()))
                    .collect(Collectors.toList());
            for (AddedService added : addedTmpList) {
                ServiceItemDTO dto = new ServiceItemDTO();
                dto.setId(added.getId());
                dto.setSettingId(added.getAddedServiceSettingId());
                dto.setType(OrderTypeEnum.ADD_SERVICE.getType());
                AddedServiceSettingVo baseVo = addedBaseMap.get(added.getAddedServiceSettingId());
                if (baseVo != null) {
                    dto.setRequired(baseVo.getRequired());
                    // 附加先固定ID判断
                    if (baseVo.getParentId() == 1L) {
                        dto.setCode(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceCode());
                        dto.setName(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceName());
                    } else if (baseVo.getParentId() == 2L) {
                        dto.setCode(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode());
                        dto.setName(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceName());
                    } else {
                        dto.setName(baseVo.getName());
                        if (isSaas == null || !isSaas) {
                            continue;
                        }
                    }
                }
                dto.setUnit("次");
                dto.setDescription(StringUtils.EMPTY);
                int fee = added.getPrice();
                if (YesOrNoEnum.isNo(added.getOnCharge())) {
                    fee = 0;
                }
                dto.setPrice(fee);
                dto.setOnCharge(added.getOnCharge());
                dtoList.add(dto);
            }

            service.setData(dtoList);
            retList.add(service);
        }
        //Span.current().setStatus(StatusCode.OK);
        return retList;
    }

    @Override
    public List<VehicleModelUniDTO<List<ServiceItemDTO>>> selectModelService(Long merchantId, List<VehicleModelUniDTO> storeAndModelIds, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas,  String debugInfo, List<String> outMsgMap) {
        //Span.current().updateName("车型服务项");
        long t = System.currentTimeMillis();

        List<Long> storeIds = storeAndModelIds.stream().map(e -> e.getStoreId()).distinct().collect(Collectors.toList());
        List<InsuranceAddApiVo> insuranceServicePriceList = new ArrayList<>();
        for (Long storeId : storeIds) {
            List<InsuranceAddApiVo> tmpList = new ArrayList<>();
            List<Long> models = storeAndModelIds.stream().filter(e -> e.getStoreId().equals(storeId))
                .map(e -> e.getVehicleModelId()).distinct().collect(Collectors.toList());
            String key = String.format(RedisConstant.PriceRedisKey.PRICE_INSURANCE_PRICE, storeId, channelId);
            List<InsuranceAddApiVo> list = (List<InsuranceAddApiVo>) redisService.get(key);
            String dbOrRedis = "db";
            if (list != null) {
                tmpList.addAll(list);
                dbOrRedis = "redis";
            } else {
                InsuranceAddApiQuery query = new InsuranceAddApiQuery();
                query.setMerchantId(merchantId);
                query.setStoreId(storeId);
                query.setChannelId(channelId);
                tmpList = rentMainMapperEx.selectInsuranceForApi(query);
                redisService.set(key, tmpList, RedisConstant.RedisExpireTime.DAY_10);
            }
            tmpList = tmpList.stream().filter(e -> models.contains(e.getVehicleModelId())).collect(Collectors.toList());
            insuranceServicePriceList.addAll(tmpList);

            Span.current().setAttribute("　af查询保险服务耗时,storeId=" + storeId + ",from " + dbOrRedis, System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }
        if (isSaas) {
            log.info("insuranceServicePriceList:{}", JSON.toJSONString(insuranceServicePriceList));
        }
        Map<Long, Map<Long, List<InsuranceAddApiVo>>> insurancesMaps = insuranceServicePriceList.stream()
                .collect(Collectors.groupingBy(InsuranceAddApiVo::getStoreId, Collectors.groupingBy(InsuranceAddApiVo::getVehicleModelId, Collectors.toList())));

        //查询商家下儿童座椅配置
//        Map<Long,List<InsuranceAddApiVo>> childSeatMapByStoreId = additionalProductService.obtainPriceChildSeats(merchantId, storeIds,channelId.byteValue());

        // 附加
        List<InsuranceAddApiVo> addedServiceList = new ArrayList<>();
        for (Long storeId : storeIds) {
            List<InsuranceAddApiVo> tmpList = new ArrayList<>();
            List<Long> models = storeAndModelIds.stream().filter(e -> e.getStoreId().equals(storeId))
                .map(e -> e.getVehicleModelId()).distinct().collect(Collectors.toList());
            String key = String.format(RedisConstant.PriceRedisKey.PRICE_ADDED_PRICE, storeId, channelId);
            List<InsuranceAddApiVo> list = (List<InsuranceAddApiVo>) redisService.get(key);
            String dbOrRedis = "db";
            if (list != null) {
                tmpList.addAll(list);
                dbOrRedis = "redis";
            } else {
                InsuranceAddApiQuery query = new InsuranceAddApiQuery();
                query.setMerchantId(merchantId);
                query.setStoreId(storeId);
                query.setChannelId(channelId);
                tmpList = rentMainMapperEx.selectAddServiceForApi(query);
                //处理儿童座椅
//                if(childSeatMapByStoreId!=null&&CollectionUtils.isNotEmpty(childSeatMapByStoreId.get(storeId))){
//                    tmpList.addAll(childSeatMapByStoreId.get(storeId));
//                }
                redisService.set(key, tmpList, RedisConstant.RedisExpireTime.DAY_10);
            }
            tmpList = tmpList.stream().filter(e -> models.contains(e.getVehicleModelId())).collect(Collectors.toList());
            addedServiceList.addAll(tmpList);

            // 附加询价分channel，结束
            Span.current().setAttribute("　af查询附加服务耗时,storeId=" + storeId + ",from " + dbOrRedis, System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }
        if (isSaas) {
            log.info("addedServiceList:{}", JSON.toJSONString(addedServiceList));
        }
        Map<Long, Map<Long, List<InsuranceAddApiVo>>> addedMaps = addedServiceList.stream()
                .collect(Collectors.groupingBy(InsuranceAddApiVo::getStoreId, Collectors.groupingBy(InsuranceAddApiVo::getVehicleModelId, Collectors.toList())));

        List<VehicleModelUniDTO<List<ServiceItemDTO>>> retList = new ArrayList<>();
        Long storeId = 0L;
        Map<Long, List<InsuranceAddApiVo>> modelInsuranceMaps = null;
        Map<Long, List<InsuranceAddApiVo>> modelAddedMaps = null;
        for (VehicleModelUniDTO paramDto : storeAndModelIds) {
            VehicleModelUniDTO<List<ServiceItemDTO>> service = new VehicleModelUniDTO<List<ServiceItemDTO>>();
            BeanUtils.copyProperties(paramDto, service);
            List<ServiceItemDTO> dtoList = new ArrayList<>();
            // 保险
//            List<InsuranceAddApiVo> tmpList = insuranceServicePriceList.stream().filter(
//                e -> e.getStoreId().equals(paramDto.getStoreId()) &&
//                    e.getVehicleModelId().equals(paramDto.getVehicleModelId()))
//                // 价格保护 0的时候不进行资源露出
//                //.filter(o -> null != o.getPrice() && o.getPrice().compareTo(0) > 0)
//                .collect(Collectors.toList());
            List<InsuranceAddApiVo> tmpList = null;
            if (storeId.compareTo(service.getStoreId()) != 0) {
                modelInsuranceMaps = insurancesMaps.get(service.getStoreId());
            }
            if (modelInsuranceMaps != null) {
                tmpList = modelInsuranceMaps.get(service.getVehicleModelId());
            }
            if (tmpList == null) {
                tmpList = new ArrayList<>();
            }
            List<Long> parentIds = tmpList.stream().map(e -> e.getParentId()).collect(Collectors.toList());
            if (!parentIds.contains(1L)) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "]," +
                            "保险[" + ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceName() + "],未启用");
                }
                continue;
            }
            if (channelId.compareTo(OrderSourceEnum.CTRIP.getSource().longValue()) == 0) {
                if (!parentIds.contains(2L)) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "]," +
                                "保险[" + ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceName() + "],未启用");
                    }
                    //continue;
                }
                if (!parentIds.contains(3L)) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "]," +
                                "保险[" + ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceName() + "],未启用");
                    }
                    // 尊享有些豪车不需要设置，继续执行
                }
            }
            // 附加
            List<InsuranceAddApiVo> addedTmpList = null;
            if (storeId.compareTo(service.getStoreId()) != 0) {
                modelAddedMaps = addedMaps.get(service.getStoreId());
            }
            if (modelAddedMaps != null) {
                addedTmpList = modelAddedMaps.get(service.getVehicleModelId());
            }
            if (addedTmpList == null) {
                addedTmpList = new ArrayList<>();
            }
            if (addedTmpList.stream().filter(e -> e.getParentId() == 1L).count() < 1) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "]," +
                            "附加[" + ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceName() + "],未启用");
                }
                //飞猪，滴滴手续费不设置也返回
                //continue;
            }
            for (InsuranceAddApiVo insurance : tmpList) {
                ServiceItemDTO dto = new ServiceItemDTO();
                dto.setId(insurance.getId());
                dto.setSettingId(insurance.getSettingId());
                dto.setOnHighestPrice(insurance.getOnHighestPrice());
                dto.setType(OrderTypeEnum.ADD_INSURANCE.getType());

                dto.setRequired(insurance.getRequired());
                // 先固定ID判断
                if (insurance.getParentId() == 1L) {
                    dto.setCode(ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceCode());
                    dto.setName(ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceName());
                } else if (insurance.getParentId() == 2L) {
                    dto.setCode(ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceCode());
                    dto.setName(ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE.getServiceName());
                } else if (insurance.getParentId() == 3L) {
                    dto.setCode(ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceCode());
                    dto.setName(ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE.getServiceName());
                } else {
                    dto.setName(insurance.getParentName());
                    if (isSaas == null || !isSaas) {
                        continue;
                    }
                }

                dto.setUnit("天");
                dto.setDescription(StringUtils.EMPTY);
                Integer price = insurance.getChannelPrice() == null ? insurance.getPrice() : insurance.getChannelPrice();
                dto.setPrice(price); //保险有个最高价格*7天
                dto.setOnCharge(YesOrNoEnum.YES.getValue());
                if (price <= 0) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        //log.info(debugInfo + ",selectModelService;vehicleModelId={},price=0,dto={}", paramDto.getVehicleModelId(), JSON.toJSONString(dto));
                        outMsgMap.add("取车门店[" + insurance.getStoreId() + "],车型[" + insurance.getVehicleModelId() + "],保险[" + dto.getName() + "],价格未设置");
                    }
                    //continue;
                }
                dtoList.add(dto);
            }
            if (isSaas) {
                log.info("insurance tmpList add dtoList:{}", JSON.toJSONString(dtoList));
            }
            // 附加
//            List<InsuranceAddApiVo> addedTmpList = addedServiceList.stream().filter(
//                e -> e.getStoreId().equals(paramDto.getStoreId()) &&
//                    e.getVehicleModelId().equals(paramDto.getVehicleModelId()))
//                .collect(Collectors.toList());
            for (InsuranceAddApiVo added : addedTmpList) {
                ServiceItemDTO dto = new ServiceItemDTO();
                dto.setId(added.getId());
                dto.setSettingId(added.getSettingId());
                dto.setType(OrderTypeEnum.ADD_SERVICE.getType());

                dto.setRequired(added.getRequired());
                // 附加先固定ID判断
                if (added.getParentId() == 1L) {
                    dto.setCode(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceCode());
                    dto.setName(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceName());
                } else if (added.getParentId() == 2L) {
                    dto.setCode(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode());
                    dto.setName(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceName());
                } else {
                    dto.setName(added.getParentName());
                    if (isSaas == null || !isSaas) {
                        continue;
                    }
                }

                dto.setUnit("次");
                dto.setDescription(StringUtils.EMPTY);
                int fee = added.getChannelPrice() == null ? added.getPrice() : added.getChannelPrice();
                if (YesOrNoEnum.isNo(added.getOnCharge())) {
                    fee = 0;
                }
                dto.setPrice(fee);
                dto.setOnCharge(added.getOnCharge());
                if (fee <= 0) {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        //log.info(debugInfo + ",selectModelService;vehicleModelId={},price=0,dto={}", paramDto.getVehicleModelId(), JSON.toJSONString(dto));
                        outMsgMap.add("取车门店[" + added.getStoreId() + "],车型[" + added.getVehicleModelId() + "],附加[" + dto.getName() + "],价格未设置");
                    }
                    //continue;
                }
                dtoList.add(dto);
            }
            if (isSaas) {
                service.setData(dtoList);
                retList.add(service);
                storeId = service.getStoreId();
                log.info("Insurance addedTmpList dtoList:{}", JSON.toJSONString(dtoList));
                continue;
            }
            // 过滤数据
            long cnt = dtoList.stream().filter(e -> e.getCode().equals(ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceCode()) && e.getPrice() > 0).count();
            if (cnt == 0) {
                if (!StringUtils.isEmpty(debugInfo)) {
                    outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "]," +
                            "保险[" + ServiceFeeTypeEnum.INSURANCE_BASE_SERVICE.getServiceName() + "],价格未设置,车型被过滤");
                }
                // 过滤车型
                continue;
            }

            cnt = dtoList.stream().filter(e -> e.getCode().equals(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceCode()) && e.getPrice() > 0).count();
            if (isSaas) {
                log.info("cnt 03001 dtoList filter:{} ", JSON.toJSONString(dtoList));
            }
            if (cnt == 0) {
                if ((channelId.compareTo(OrderSourceEnum.DIDI.getSource().longValue()) == 0 ||
                        channelId.compareTo(OrderSourceEnum.FEIZHU.getSource().longValue()) == 0 ||
                        channelId.compareTo(OrderSourceEnum.WUKONG.getSource().longValue()) == 0)) {
                    //滴滴，飞猪，悟空 手续费为0，也返回
                } else {
                    if (!StringUtils.isEmpty(debugInfo)) {
                        //log.info(debugInfo + ",selectModelService;vehicleModelId={},price=0,dto={}", paramDto.getVehicleModelId(), JSON.toJSONString(dto));
                        outMsgMap.add("取车门店[" + paramDto.getStoreId() + "],车型[" + paramDto.getVehicleModelId() + "],附加[" + ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceName() + "],价格未设置,车型被过滤");
                    }
                    // 过滤车型
                    continue;
                }
            }

            if (channelId.compareTo(OrderSourceEnum.ALIPAY_MINI_PROGRAM.getSource().longValue()) != 0) {
                dtoList = dtoList.stream().filter(e -> e.getPrice() > 0 || e.getCode().equals(ServiceFeeTypeEnum.ADD_FEE_SERVICE.getServiceCode())).collect(Collectors.toList());
            }
            if (isSaas) {
                log.info("03001 dtoList filter:{} ", JSON.toJSONString(dtoList));
            }
            service.setData(dtoList);
            retList.add(service);
            storeId = service.getStoreId();
        }
        Span.current().setAttribute("　af其他处理", System.currentTimeMillis() - t);
        //Span.current().setStatus(StatusCode.OK);
        return retList;
    }

    @Override
    public List<StoreChargeDTO<List<DailyPriceListDTO>>> getStoreCharge(Long merchantId, Long channelId, List<Long> storeIds, Date pickUpTime, Date returnTime, Long orderId, String debugInfo, boolean platformCal) {
        log.info("getStoreCharge, merchantId={},channelId={},storeIds={},pickUpTime={},returnTime={},orderId={},platformCal={}",
                merchantId, channelId, storeIds, pickUpTime, returnTime, orderId, platformCal);
        long t = System.currentTimeMillis();
        if (channelId == null || pickUpTime == null || returnTime == null) {
            log.error("通道、取车还车时间为空");
            return new ArrayList<>();
        }

        // 如果不是携程标准化订单, orderId为null
        if (orderId != null) {
            if (orderComponent.isOrderBizByBizType(OrderBizTypeEnum.CTRIP_STANDARD_FEE, orderId)) {
                platformCal = true;
            } else {
                orderId = null;
            }
        }
        // todo 以platformCal为准,为true是携程费用标准订单

         // 获取零散小时规则
        List<StoreHourlyVo> hourlyList = null;
        Map<String, StoreHourlyChargeVo> chargeMaps = null;
        if (orderId != null) {
            Result<List<OrderHourlyVo>> orderHourlyResult = orderHourlyService.listHourly(orderId);
            if (orderHourlyResult.isSuccess()) {
                hourlyList = new ArrayList<>();
                for (OrderHourlyVo OrderHourlyVo : orderHourlyResult.getModel()) {
                    StoreHourlyVo storeHourlyVo = new StoreHourlyVo();
                    BeanUtils.copyProperties(OrderHourlyVo, storeHourlyVo);
                    hourlyList.add(storeHourlyVo);
                }
            }
        } else {
            Result<Boolean> standardStatus;
            // 如携程渠道：platformCal不变
            // 如其他渠道：看标准化是否开启；
            if (platformCal == false || !CommStore.getParentChannelId(channelId).equals(Constant.ChannelId.CTRIP)) {
                standardStatus = apiConnService.getApiConnOpenStandardStatus(merchantId);
                if (standardStatus.isSuccess() && standardStatus.getModel()) {
                    platformCal = true;
                }
            }
            if (platformCal) {
                // 零散新规则
                hourlyList = thirdStoreService.getChargeListV2(merchantId, storeIds, CommStore.getParentChannelId(channelId), channelId);
            } else {
                List<StoreHourlyChargeVo> chargeVos = thirdStoreService.getChargeList(storeIds, CommStore.getParentChannelId(channelId));
                chargeMaps = new HashMap<>();
                for (StoreHourlyChargeVo vo : chargeVos) {
                    chargeMaps.put(vo.getStoreId() + ":" + vo.getChargeItem() + ":" + vo.getScene(), vo);
                }
            }
        }

        List<StoreChargeDTO<List<DailyPriceListDTO>>> retList = new ArrayList<>();
        for (Long storeId : storeIds) {
            List<DailyPriceListDTO> list;
            if (hourlyList != null) {
                List<StoreHourlyVo> hourlyStoreList = null;
                if (orderId != null) {
                    hourlyStoreList = hourlyList.stream().collect(Collectors.toList());
                } else {
                    hourlyStoreList = hourlyList.stream()
                            .filter(e -> e.getStoreId().equals(storeId))
                            .collect(Collectors.toList());
                }
                list = getChargesAddInV2(merchantId, storeId, hourlyStoreList, pickUpTime, returnTime);
            } else {
                list = getChargesAddIn(merchantId, storeId, chargeMaps, pickUpTime, returnTime);
            }
            StoreChargeDTO<List<DailyPriceListDTO>> single = new StoreChargeDTO<List<DailyPriceListDTO>>();
            single.setStoreId(storeId);
            single.setData(list);
            retList.add(single);
            if (!StringUtils.isEmpty(debugInfo)) {
                log.info("零散小时,附加&保险,商家={},门店={},response={}", merchantId, storeId, JSON.toJSONString(list));
            }
        }
        return retList;
    }

    @Override
    public List<ServiceItemDTO> selectModelService(Long merchantId, VehicleModelUniDTO storeAndModelId, Long channelId, Date pickUpTime, Date returnTime, Boolean isSaas) {
        List<VehicleModelUniDTO<List<ServiceItemDTO>>> vehicleModelUniDTOS = this.selectModelService(merchantId, Lists.newArrayList(storeAndModelId), channelId, pickUpTime, returnTime, isSaas, null, null);
        if (null == vehicleModelUniDTOS || vehicleModelUniDTOS.isEmpty()) {
            return null;
        }
        return vehicleModelUniDTOS.get(0).getData();
    }


    @Override
    public List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> selectModelOther(List<VehicleModelUniDTO> storeAndModelIds, Long channelId, List<RentMainVo> rentSellList, Long merchantId) {
        //Span.current().updateName("车型其他");
        long t = System.currentTimeMillis();
        Map<Long, List<Long>> storeAndModelIdMap = storeAndModelIds.stream().collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId,
                Collectors.collectingAndThen(Collectors.toList(), crs -> crs.stream()
                        .map(VehicleModelUniDTO::getVehicleModelId)
                        .collect(Collectors.toList()))));
        // 押金查询
        if (rentSellList == null) {
            rentSellList = rentMainService.selectRentModel(Lists.newArrayList(storeAndModelIdMap.keySet()));
            Span.current().setAttribute("　ag查询租车押金耗时", System.currentTimeMillis() - t);
            t = System.currentTimeMillis();
        }
        // 免押查询
        List<RentUndepostitVo> vos = rentMainService.selectModelDeposits(channelId, storeAndModelIdMap);
        //Span.current().setAttribute("　ag查询免押耗时", System.currentTimeMillis() - t);
        // 查询车辆标签
        Map<String, List<String>> vehicleTagMap = this.selectVehicleTag(storeAndModelIds, merchantId);

        // 数据组装返回
        List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> retList = new ArrayList<>();
        for (VehicleModelUniDTO param : storeAndModelIds) {
            VehicleModelUniDTO<VehicleModelSubAbbrDTO> dto = new VehicleModelUniDTO<VehicleModelSubAbbrDTO>();
            BeanUtils.copyProperties(param, dto);
            VehicleModelSubAbbrDTO bean = new VehicleModelSubAbbrDTO();
            List<RentMainVo> baseList = rentSellList.stream().filter(
                            e -> e.getStoreId().equals(param.getStoreId()) && e.getVehicleModelId().equals(param.getVehicleModelId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(baseList)) {
                RentMainVo baseVo = baseList.get(0);
                bean.setPreAuthRentDepositPrice(baseVo.getRentDeposit());
                bean.setPreAuthIllegalDepositPrice(baseVo.getIllegalDeposit());
                bean.setMileage(baseVo.getMileage());
                bean.setOverMileagePrice(baseVo.getMileageRent().intValue());
                // 0： 限制 1：不限制
                if (YesOrNoEnum.isYes(baseVo.getMileageLimit())) {
                    bean.setUnlimitedMileage(0);
                } else {
                    bean.setUnlimitedMileage(1);
                }
            }
            int depostit = (int) vos.stream()
                    .filter(Objects::nonNull)
                    .filter(e -> Objects.nonNull(e.getStoreId())
                            && Objects.nonNull(e.getVehicleModelId())
                            && e.getStoreId().equals(param.getStoreId())
                            && e.getVehicleModelId().equals(param.getVehicleModelId()))
                    .count();
            bean.setFreeDepositType(depostit > 0 ? 2 : 0);
            // 车辆标签
            bean.setVehicleTags(
                vehicleTagMap.getOrDefault(param.getStoreId() + "," + param.getVehicleModelId(), Collections.emptyList()));

            dto.setData(bean);
            retList.add(dto);
        }
        //Span.current().setStatus(StatusCode.OK);
        return retList;
    }

    @Override
    public VehicleModelUniDTO<VehicleModelSubAbbrDTO> selectModelOther(VehicleModelUniDTO storeAndModelId, Long channelId, List<RentMainVo> rentSellList, Long merchantId) {
        List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> vehicleModelUniDTOS = this.selectModelOther(Arrays.asList(storeAndModelId), channelId, rentSellList, merchantId);
        if (null == vehicleModelUniDTOS || vehicleModelUniDTOS.isEmpty()) {
            return null;
        }
        return vehicleModelUniDTOS.get(0);
    }

    @Override
    public List<InsuranceServicePolicyDTO> getModelInsuranceServicePolicy(Long merchantId) {
        if (merchantId == null) {
            return Collections.emptyList();
        }

        Result<List<InsuranceServiceSettingVo>> listResult = insuranceServiceSettingService.insuranceList(merchantId);
        if (!listResult.isSuccess()) {
            throw new BizException(listResult.getResultCode(), listResult.getMessage());
        }
        List<InsuranceServicePolicyDTO> insuranceServicePolicyDTOS = Lists.newArrayList();
        for (InsuranceServiceSettingVo serviceSettingVo : listResult.getModel()) {
            // 商家自定义的保险,不需要返回
            ServiceFeeTypeEnum serviceFeeTypeEnum = insuranceServiceNeedMap.get(serviceSettingVo.getName());
            if (serviceFeeTypeEnum == null) {
                continue;
            }
            InsuranceServicePolicyDTO insuranceServicePolicyDTO = new InsuranceServicePolicyDTO();
            insuranceServicePolicyDTO.setCode(serviceFeeTypeEnum.getServiceCode());
            insuranceServicePolicyDTO.setName(serviceFeeTypeEnum.getServiceName());
            insuranceServicePolicyDTO.setDamageInsurance(serviceSettingVo.getDamageInsurance());
            insuranceServicePolicyDTO.setDamageInsuranceAmount(serviceSettingVo.getDamageInsuranceAmount());
            insuranceServicePolicyDTO.setGlass(serviceSettingVo.getGlass());
            insuranceServicePolicyDTO.setTire(serviceSettingVo.getTire());
            insuranceServicePolicyDTO.setThirdPartyInsurance(serviceSettingVo.getThirdPartyInsurance());
            insuranceServicePolicyDTO.setOutageFee(serviceSettingVo.getOutageFee());
            insuranceServicePolicyDTO.setOutageFeeRatio(serviceSettingVo.getOutageFeeRatio());
            insuranceServicePolicyDTO.setDepreciation(serviceSettingVo.getDepreciation());
            insuranceServicePolicyDTO.setDepreciationFee(serviceSettingVo.getDepreciationFee());
            insuranceServicePolicyDTO.setRepairFeeRatio(serviceSettingVo.getRepairFeeRatio());
            insuranceServicePolicyDTOS.add(insuranceServicePolicyDTO);
        }
        return insuranceServicePolicyDTOS;
    }

    @Override
    public ServiceItemListVo selectModelServiceItemListVo(Long merchantId, Long storeId, Long channelId, Long vehicleModelId, Date pickUpTime, Date returnTime) {
        VehicleModelUniDTO dto = new VehicleModelUniDTO();
        if (vehicleModelId != null) {
            dto.setVehicleModelId(vehicleModelId);
        }
        dto.setStoreId(storeId);
        List<ServiceItemDTO> list = selectModelService(merchantId, dto, channelId, pickUpTime, returnTime, true);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        ServiceItemListVo serviceItemListVo = new ServiceItemListVo();
        List<ServiceItemDTO> addedList = new ArrayList<>();
        List<ServiceItemDTO> insuranceList = new ArrayList<>();
        for (ServiceItemDTO serviceItemDTO : list) {
            if (serviceItemDTO.getType() == OrderTypeEnum.ADD_INSURANCE.getType()) {
                insuranceList.add(serviceItemDTO);
            } else if (serviceItemDTO.getType() == OrderTypeEnum.ADD_SERVICE.getType()) {
                addedList.add(serviceItemDTO);
            }
        }
        serviceItemListVo.setInsuranceList(insuranceList);
        serviceItemListVo.setAddedList(addedList);
        return serviceItemListVo;
    }

    @Override
    public List<Long> storeUnDeposits(Long channelId, Long storeId) {
        Map<Long, List<Long>> storeModelKeys = Maps.newHashMap();
        storeModelKeys.put(storeId, null);
        List<RentUndepostitVo> undepostitVoList = rentMainService.selectModelDeposits(channelId, storeModelKeys);

        if (CollectionUtils.isEmpty(undepostitVoList)) {
            return Lists.newArrayList();
        }

        return undepostitVoList.stream()
                .map(RentUndepostitVo::getVehicleModelId)
                .collect(Collectors.toList());
    }

    @Override
    public void vehicleStatusNotify(VehicleStatusNotifyReq notifyReq, Long merchantId, boolean isRealCar) {
        log.info("渠道车辆状态变更回调通知:{}", JSON.toJSONString(notifyReq));
        if (notifyReq == null) {
            return;
        }
        VehicleInfoExample vehicleInfoExample = new VehicleInfoExample();
        vehicleInfoExample.createCriteria().andLicenseEqualTo(notifyReq.getPlateNumber())
            .andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleInfo> vehicleInfos = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        if (CollectionUtils.isEmpty(vehicleInfos)) {
            return;
        }

        VehicleInfo vehicleInfo = vehicleInfos.get(0);
        VehicleChannelExample vehicleChannelExample = new VehicleChannelExample();
        vehicleChannelExample.createCriteria().andVehicleIdEqualTo(vehicleInfo.getId())
            .andChannelIdEqualTo(notifyReq.getChannelId())
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleChannel> vehicleChannels = vehicleChannelMapper.selectByExample(vehicleChannelExample);
        if (CollectionUtils.isNotEmpty(vehicleChannels)) {
            VehicleChannel vehicleChannel = new VehicleChannel();
            vehicleChannel.setId(vehicleChannels.get(0).getId());
            this.convertVehicleChannel(vehicleChannel, notifyReq);
            vehicleChannelMapper.updateByPrimaryKeySelective(vehicleChannel);
        } else {
            VehicleChannel vehicleChannel = new VehicleChannel();
            vehicleChannel.setVehicleId(vehicleInfo.getId());
            vehicleChannel.setChannelId(notifyReq.getChannelId());
            this.convertVehicleChannel(vehicleChannel, notifyReq);
            vehicleChannel.setMerchantId(merchantId);
            vehicleChannel.setDeleted(YesOrNoEnum.NO.getValue());
            vehicleChannel.setCreateTime(System.currentTimeMillis());
            vehicleChannel.setOpTime(System.currentTimeMillis());
            vehicleChannel.setOpUserId(0L);
            vehicleChannelMapper.insertSelective(vehicleChannel);
        }
    }

    private void convertVehicleChannel(VehicleChannel vehicleChannel, VehicleStatusNotifyReq notifyReq) {
        vehicleChannel.setVehicleStatus(notifyReq.getVehicleStatus());
        vehicleChannel.setVehicleFailReason(notifyReq.getCarStatusReason());
        vehicleChannel.setAuditStatus(notifyReq.getAuditStatus());
        vehicleChannel.setAuditFailReason(notifyReq.getAuditFailReason());
    }


    @Override
    public Result<List<VehicleInfoDTO>> listVehicleInfo(Long merchantId, Long channelId, GetlVehicleRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            log.warn("拉取车辆信息参数缺失, merchantId: {}", merchantId);
            return ResultUtil.failResult(ResultEnum.e001, "拉取车辆信息参数缺失");
        }

        // 游标起始位置
        long startId = request.getId();
        VehicleInfoExample vehicleInfoExample = new VehicleInfoExample();
        VehicleInfoExample.Criteria criteria = vehicleInfoExample.createCriteria().andIdGreaterThanOrEqualTo(startId)
            .andMerchantIdEqualTo(merchantId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
            .andVehicleStatusNotEqualTo(VehicleStatusEnum.NOT_ONLINE.getStatus())
            .andPlatformSoldEqualTo(YesOrNoEnum.YES.getValue());
        if (StringUtils.isNotBlank(request.getLicense())) {
            criteria.andLicenseEqualTo(request.getLicense());
        }
        if (CollectionUtils.isNotEmpty(request.getStoreIds())) {
            criteria.andStoreIdIn(request.getStoreIds());
        }
        if (StringUtils.isNotBlank(request.getStoreCode())) {
            criteria.andStoreIdEqualTo(Long.parseLong(request.getStoreCode()));
        }
        if (request.getVehicleModelId() != null) {
            criteria.andVehicleModelIdEqualTo(request.getVehicleModelId());
        }
        if (CollectionUtils.isNotEmpty(request.getVehicleModelIds())) {
            criteria.andVehicleModelIdIn(request.getVehicleModelIds());
        }

        vehicleInfoExample.setOrderByClause("id limit " + request.getSize());
        List<VehicleInfo> vehicleInfos = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        if (CollectionUtils.isEmpty(vehicleInfos)) {
            return ResultUtil.successResult(Collections.emptyList());
        }
        return ResultUtil.successResult(vehicleComponent.assembleVehicleAllInfo(vehicleInfos, merchantId));
    }

    @Override
    public Result<List<VehicleMediaVO>> listByParam(VehicleMediaQueryParam queryParam) {
        return vehicleMediaService.listByParam(queryParam);
    }

    @Override
    public Result<VehicleInfoDTO> addVehicle(Long merchantId, VehicleInfoDTO addVehicle) {
        String plateNumber = addVehicle.getDrivingLicenseInfo().getPlateNumber();

        VehicleInfoExample vehicleInfoExample = new VehicleInfoExample();
        vehicleInfoExample.createCriteria().andLicenseEqualTo(plateNumber).andMerchantIdEqualTo(merchantId)
            .andDeletedEqualTo(YesOrNoEnum.NO.getValue());
        List<VehicleInfo> existVehicles = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        if (CollectionUtils.isNotEmpty(existVehicles)) {
            VehicleInfo vehicleInfo1 = existVehicles.get(0);
            addVehicle.setId(vehicleInfo1.getId());
            return ResultUtil.successResult(addVehicle);
        }

        Long storeId = addVehicle.getSaasStoreId();
        StoreSimpleVo storeInfo = storeInfoService.storeSampleForUnion(storeId).getModel();

        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.setVehicleModelId(addVehicle.getVehicleModelId());
        vehicleInfo.setVehicleModelName(vehicleComponent.generateVehicleName(addVehicle.getVehicleModelId()));
        if (storeInfo != null) {
            vehicleInfo.setStoreId(storeInfo.getStoreId());
            vehicleInfo.setStoreName(storeInfo.getStoreName());
        } else {
            vehicleInfo.setStoreId(0L);
            vehicleInfo.setStoreName("");
        }
        List<VehicleInfoAtt> atts = new ArrayList<>();
        vehicleInfo.setMerchantId(merchantId);
        vehicleInfo.setVehicleColorId(addVehicle.getVehicleColor());
        vehicleInfo.setVehicleStatus(addVehicle.getVehicleStatus());
        vehicleInfo.setVehicleSource(addVehicle.getVehicleSource());
        vehicleInfo.setUsageNature(addVehicle.getUsageNature());
        boolean platformSold = !VehicleInfoEnums.VehicleSourceEnum.VIRTUAL_INVENTORY.getSource().equals(addVehicle.getVehicleSource());
        vehicleInfo.setPlatformSold(YesOrNoEnum.booleanToByte(platformSold));
        vehicleInfo.setSelfServiceReturn(addVehicle.getSelfServiceReturn());
        VehicleInfoExtraDO vehicleInfoExtraDO = new VehicleInfoExtraDO();
        vehicleInfoExtraDO.setEnableCtripAuditing(YesOrNoEnum.YES.getValue());
        vehicleInfo.setExtra(JSON.toJSONString(vehicleInfoExtraDO));

        DrivingLicenseInfo drivingLicenseInfo = addVehicle.getDrivingLicenseInfo();
        if (drivingLicenseInfo != null) {
            vehicleInfo.setLicense(drivingLicenseInfo.getPlateNumber());
            vehicleInfo.setFrameNum(drivingLicenseInfo.getVin());
            vehicleInfo.setEngineNum(drivingLicenseInfo.getEngineNo());
            vehicleInfo.setRegDate(drivingLicenseInfo.getRegisterDate());
            vehicleInfo.setOwner(drivingLicenseInfo.getOwner());
            VehicleInfoAtt vehicleInfoAtt = this.buildAtt(drivingLicenseInfo.getImgUrl(),
                null, VehicleInfoEnums.VehicleFileEnum.DRIVER.getValue(), vehicleInfo.getId());
            if (Objects.nonNull(vehicleInfoAtt)) {
                atts.add(vehicleInfoAtt);
            }
        }

        AnnualCheckInfo annualCheckInfo = addVehicle.getAnnualCheckInfo();
        if (annualCheckInfo != null) {
            vehicleInfo.setYearlyInspectionPeriod(StringUtils.defaultString(annualCheckInfo.getActiveData()));
            VehicleInfoAtt vehicleInfoAtt = this.buildAtt(annualCheckInfo.getImgUrl(),
                null, VehicleInfoEnums.VehicleFileEnum.YEARLY.getValue(), vehicleInfo.getId());
            if (Objects.nonNull(vehicleInfoAtt)) {
                atts.add(vehicleInfoAtt);
            }
        }
        vehicleInfo.setLastVer(1);
        vehicleInfo.setDeleted(YesOrNoEnum.NO.getValue());
        vehicleInfo.setCreateTime(System.currentTimeMillis());
        vehicleInfo.setOpTime(System.currentTimeMillis());
        vehicleInfo.setOpUserId(0L);
        vehicleInfoMapper.insertSelective(vehicleInfo);
        addVehicle.setId(vehicleInfo.getId());
//        if (!checkInsurance(addVehicle)) {
//            return ResultUtil.failResult(ResultEnum.e006, "保险单号重复");
//        }
        this.saveVehicleInsurance(vehicleInfo, addVehicle, atts);
        if (CollectionUtils.isNotEmpty(atts)) {
            atts.forEach(e -> e.setVehicleId(vehicleInfo.getId()));
            vehicleInfoAttMapper.batchInsert(atts);
        }
        return ResultUtil.successResult(addVehicle);
    }

    @Override
    public Result<Integer> saveHelloVehicleAudit(Long vehicleId, VehicleStatusNotifyRequest request) {
        if (vehicleId == null || request == null || request.getApprovalStatus() == null) {
            return ResultUtil.failResult(ResultEnum.e001);
        }

        VehicleInfo vehicleInfo = vehicleInfoMapper.selectByPrimaryKey(vehicleId);

        VehicleChannelExample channelExample = new VehicleChannelExample();
        channelExample.createCriteria().andVehicleIdEqualTo(vehicleId).andDeletedEqualTo(YesOrNoEnum.NO.getValue())
            .andChannelIdEqualTo(Constant.ChannelId.HELLO);
        channelExample.setOrderByClause("id limit 1");
        List<VehicleChannel> vehicleChannels = vehicleChannelMapper.selectByExample(channelExample);
        if (CollectionUtils.isEmpty(vehicleChannels)) {
            VehicleChannel vehicleChannel = this.converterVehicleChannelForHello(request);
            vehicleChannel.setVehicleId(vehicleId);
            vehicleChannel.setChannelId(Constant.ChannelId.HELLO);
            vehicleChannel.setCreateTime(System.currentTimeMillis());
            vehicleChannel.setMerchantId(vehicleInfo.getMerchantId());
            vehicleChannelMapper.insertSelective(vehicleChannel);
        } else {
            VehicleChannel vehicleChannel = this.converterVehicleChannelForHello(request);
            vehicleChannel.setId(vehicleChannels.get(0).getId());
            vehicleChannelMapper.updateByPrimaryKeySelective(vehicleChannel);
        }
        return ResultUtil.successResult(1);
    }

    /**
     *
     */
    private VehicleChannel converterVehicleChannelForHello(VehicleStatusNotifyRequest request) {
        VehicleChannel vehicleChannel = new VehicleChannel();
        if (request.getApprovalStatus() == 0) {
            vehicleChannel.setVehicleStatus(VehicleInfoEnums.VehicleChannelVehicleStatusEnum.FOR_ONLINE.getStatus());
            vehicleChannel.setAuditStatus(VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_PROCESSING.getStatus());
            vehicleChannel.setAuditFailReason("");
            vehicleChannel.setVehicleFailReason("");
        } else if (request.getApprovalStatus() == 1) {
            vehicleChannel.setVehicleStatus(VehicleInfoEnums.VehicleChannelVehicleStatusEnum.FOR_RENT.getStatus());
            vehicleChannel.setAuditStatus(VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_SUCCESS.getStatus());
            vehicleChannel.setAuditFailReason("");
            vehicleChannel.setVehicleFailReason("");
        } else if (request.getApprovalStatus() == 2) {
            vehicleChannel.setVehicleStatus(VehicleInfoEnums.VehicleChannelVehicleStatusEnum.SYSTEM_OFFLINE.getStatus());
            vehicleChannel.setAuditStatus(VehicleInfoEnums.VehicleChannelAuditStatusEnum.AUDIT_FAILED.getStatus());
            vehicleChannel.setAuditFailReason(request.getReason());
        }
        vehicleChannel.setOpTime(System.currentTimeMillis());
        return vehicleChannel;
    }

    private VehicleInfoAtt buildAtt(String url, Long insuranceId, Byte attType, Long vehicleId) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        VehicleInfoAtt att = new VehicleInfoAtt();
        att.setVehicleInsuranceId(insuranceId != null ? insuranceId : 0L);
        att.setFilePath(url);
        att.setVehicleId(vehicleId);
        att.setDeleted(YesOrNoEnum.NO.getValue());
        att.setCreateTime(System.currentTimeMillis());
        att.setOpTime(System.currentTimeMillis());
        att.setOpUserId(1L);
        att.setFileType(attType);
        return att;
    }

    private boolean checkInsurance(VehicleInfoDTO newParam) {
        List<InsuranceDetailInfo> insuranceList = new ArrayList<>();
        if (Objects.nonNull(newParam.getCommercialInsurance()) && CollectionUtils.isNotEmpty(
            newParam.getCommercialInsurance().getCommercialDetailInfo())) {
            insuranceList.addAll(newParam.getCommercialInsurance().getCommercialDetailInfo());
        }
        if (Objects.nonNull(newParam.getCompulsoryInsurance()) && CollectionUtils.isNotEmpty(
            newParam.getCompulsoryInsurance().getCompulsoryDetailInfo())) {
            insuranceList.addAll(newParam.getCompulsoryInsurance().getCompulsoryDetailInfo());
        }
        // 校验insuranceId是否有重复
        Set<String> insuranceIdSet = new HashSet<>();
        for (InsuranceDetailInfo insuranceDetailInfo : insuranceList) {
            if (insuranceIdSet.contains(insuranceDetailInfo.getInsuranceId())) {
                return false;
            }
            insuranceIdSet.add(insuranceDetailInfo.getInsuranceId());
        }
        return true;
    }

    private void saveVehicleInsurance(VehicleInfo vehicleInfo, VehicleInfoDTO param, List<VehicleInfoAtt> atts) {
        DrivingLicenseInfo drivingLicenseInfo = param.getDrivingLicenseInfo();

        // 商业保险
        Optional.ofNullable(param.getCommercialInsurance())
            .ifPresent(commercialDetailInfos -> {
                for (InsuranceDetailInfo commercialDetailInfo : commercialDetailInfos.getCommercialDetailInfo()) {
                    VehicleInfoInsurance insurance = this.buildVehicleInsurance(vehicleInfo.getId(),
                        drivingLicenseInfo, commercialDetailInfo);
                    insurance.setInsuranceType(VehicleInfoEnums.InsuranceTypeEnum.BIZINSURANCE.getType());
                    insurance.setFrameNum(vehicleInfo.getFrameNum());
                    insurance.setEngineNum(vehicleInfo.getEngineNum());
                    vehicleInfoInsuranceMapper.insertSelective(insurance);
                    for (String url : commercialDetailInfo.getUrlList()) {
                        atts.add(this.buildAtt(url, insurance.getId(),
                            VehicleInfoEnums.VehicleFileEnum.INSURANCE.getValue(), vehicleInfo.getId()));
                    }
                }
            });

        // 交强险
        Optional.ofNullable(param.getCompulsoryInsurance())
            .ifPresent(compulsoryInfos -> {
                for (InsuranceDetailInfo compulsoryInfo : compulsoryInfos.getCompulsoryDetailInfo()) {
                    VehicleInfoInsurance insurance = this.buildVehicleInsurance(vehicleInfo.getId(),
                        drivingLicenseInfo, compulsoryInfo);
                    insurance.setInsuranceType(VehicleInfoEnums.InsuranceTypeEnum.SALIINSURANCE.getType());
                    insurance.setFrameNum(vehicleInfo.getFrameNum());
                    insurance.setEngineNum(vehicleInfo.getEngineNum());
                    vehicleInfoInsuranceMapper.insertSelective(insurance);
                    for (String url : compulsoryInfo.getUrlList()) {
                        atts.add(this.buildAtt(url, insurance.getId(),
                            VehicleInfoEnums.VehicleFileEnum.INSURANCE.getValue(), vehicleInfo.getId()));
                    }
                }
            });
    }

    private VehicleInfoInsurance buildVehicleInsurance(Long vehicleId, DrivingLicenseInfo drivingLicenseInfo,
                                                       InsuranceDetailInfo insuranceDetailInfo) {
        VehicleInfoInsurance insurance = vehicleComponent.buildVehicleInsurance(vehicleId, insuranceDetailInfo);
        if (drivingLicenseInfo != null) {
            insurance.setFrameNum(StringUtils.defaultString(drivingLicenseInfo.getVin()));
            insurance.setEngineNum(StringUtils.defaultString(drivingLicenseInfo.getEngineNo()));
        }
        return insurance;
    }

    @Override
    public void saveVehicleModelLimitPriceUpdate(String content) {
        log.info("saveVehicleModelLimitPriceUpdate, content={}", JSON.toJSONString(content));
        if (StringUtils.isEmpty(content)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(content);
        String vendorId = jsonObject.getString("vendorId");
        JSONArray updateDetailArray = jsonObject.getJSONArray("updateDetail");
        for (int i = 0; i < updateDetailArray.size(); i++) {
            JSONObject detailObject = updateDetailArray.getJSONObject(i);
            String vvc = detailObject.getString("vvc");
            String storeCode = detailObject.getString("storeCode");
            String priceDetail = detailObject.getString("priceDetail");
            if (StringUtils.isNotEmpty(vvc)) {
                String vehicleModelId =vvc.split("_")[0];
                String subVehicleId =vvc.split("_")[1];
                DataCommon record = new DataCommon();
                record.setSourceId(storeCode + "_" + vehicleModelId);
                record.setSourceType((byte) 0);
                record.setContent(priceDetail);
                record.setCreateTime(System.currentTimeMillis());
                record.setOpTime(System.currentTimeMillis());
                dataCommonMapper.insertSelective(record);

                // 更新保险
                try {
                    updateInsurance(vendorId, storeCode, Long.parseLong(vehicleModelId), subVehicleId, detailObject.getJSONArray("priceDetail"), content);
                } catch (Exception e) {
                    log.error("保险改价回调,更新保险失败, storeCode:{}, vehicleModelId:{}, priceDetail:{}", storeCode, vehicleModelId, priceDetail, e);
                }
            }
        }
    }

    private void updateInsurance(String vendorId, String storeCode, long vehicleModelId, String subVehicleId, JSONArray priceDetail, String content) {
        if (StringUtils.isEmpty(vendorId) || StringUtils.isEmpty(storeCode)) {
            log.info("保险改价回调,vendorId或storeCode=null, content:{},", content);
            return;
        }
        if (CollectionUtils.isEmpty(priceDetail)) {
            log.info("保险改价回调,改价数据priceDetail=null, content:{},", content);
            return;
        }
        long mappingChannel = 2L;
        // 查询商家ID
        Result<ApiConnMoreVo>  apiConnResult = apiConnService.getApiConnMoreByVendorIdAndChannelId(vendorId, OrderSourceEnum.CTRIP.longValue());
        if (apiConnResult == null || !apiConnResult.isSuccess() || apiConnResult.getModel() == null) {
            log.info("保险改价回调,未查询到vendorId={}的商家, content:{},", vendorId, content);
            return;
        }
        // 查询门店ID
        long merchantId = apiConnResult.getModel().getMerchantId();
        Long storeId = thirdIdRelationService.getMappingForSaas(mappingChannel, IdRelationEnum.STORECODE.getType(), storeCode, merchantId);
        if (storeId == null) {
            log.info("保险改价回调,未查询到storeCode={}门店, content:{},", storeCode, content);
            return;
        }
        // 更新保险信息
        Result<List<InsuranceServiceSettingVo>> result = insuranceServiceSettingService.insuranceList(merchantId);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getModel())) {
            log.info("保险改价回调,未查询到商家保险数据={}, content:{},", merchantId, content);
            return;
        }
        Map<Long, Long> insuranceSettingIdMap = result.getModel().stream()
                .filter(e -> YesOrNoEnum.isYes(e.getPreset()))
                .collect(Collectors.toMap(InsuranceServiceSettingVo::getParentId, InsuranceServiceSettingVo::getId));
        if (insuranceSettingIdMap.isEmpty()) {
            log.info("保险改价回调,商家未设置保险数据={}, content:{},", merchantId, content);
            return;
        }
        CtripPriceFilterInfo updFilterRecord = new CtripPriceFilterInfo();
        boolean updLimitFlg = false;
        String msg = "";
        String insuranceName = "";
        for (Object item : priceDetail) {
            JSONObject jsonObject = (JSONObject) item;
            // json结构 [{"feeCode":"1002","beforePrice":40.00,"afterPrice":50,"channel":"ctrip_channel","beforePriceLimit":{"priceFloor":30,"priceCeiling":80},"afterPriceLimit":{"priceFloor":50,"priceCeiling":100}},{"feeCode":"1002","beforePrice":40.00,"afterPrice":60,"channel":"others_channel","beforePriceLimit":{"priceFloor":0,"priceCeiling":200},"afterPriceLimit":{"priceFloor":60,"priceCeiling":110}}]
            String channel = jsonObject.getString("channel");
            String feeCode = jsonObject.getString("feeCode");
            int beforePrice = jsonObject.getIntValue("beforePrice");
            int afterPrice = jsonObject.getIntValue("afterPrice");

            long channelId = OrderSourceEnum.CTRIP_RESALE.longValue();
            if (channel.equals("ctrip_channel")) {
                channelId = OrderSourceEnum.CTRIP.longValue();
            }
            // 获取保险配置数据Id
            Long insuranceSettingId = null;
            JSONObject beforePriceLimit = (JSONObject) jsonObject.get("beforePriceLimit");
            JSONObject afterPriceLimit = (JSONObject) jsonObject.get("afterPriceLimit");
            if (feeCode.equals(FeeTypeEnum.BASIC_SERVICE_FEE.getValue())) {
                insuranceSettingId = insuranceSettingIdMap.get(1L);
                if (channel.equals("ctrip_channel")) {
                    updFilterRecord.setBaiscInsurancePriceLower(afterPriceLimit.getIntValue("priceFloor") * 100);
                    updFilterRecord.setBaiscInsurancePriceUpper(afterPriceLimit.getIntValue("priceCeiling") * 100);
                    updLimitFlg = true;
                    insuranceName = "基础保险";

                }
            } else if (feeCode.equals(FeeTypeEnum.ADVANCED_SERVICE_FEE.getValue())) {
                insuranceSettingId = insuranceSettingIdMap.get(2L);
                if (channel.equals("ctrip_channel")) {
                    updFilterRecord.setPremiuminsurancePriceLower(afterPriceLimit.getIntValue("priceFloor") * 100);
                    updFilterRecord.setPremiuminsurancePriceUpper(afterPriceLimit.getIntValue("priceCeiling") * 100);
                    updLimitFlg = true;
                    insuranceName = "优享保险";
                }
            } else if (feeCode.equals(FeeTypeEnum.PREMIUM_SERVICE_FEE.getValue())) {
                insuranceSettingId = insuranceSettingIdMap.get(3L);
                if (channel.equals("ctrip_channel")) {
                    updFilterRecord.setExclusiveinsurancepricelower(afterPriceLimit.getIntValue("priceFloor") * 100);
                    updFilterRecord.setExclusiveinsurancePriceUpper(afterPriceLimit.getIntValue("priceCeiling") * 100);
                    updLimitFlg = true;
                    insuranceName = "尊享保险";
                }
            }
            if (insuranceSettingId == null) {
                log.info("保险改价回调,保险配置数据Id={}, 商家Id={}, feeCode未Mapping到数据, content:{},", feeCode, merchantId, content);
                continue;
            }
            if (channel.equals("ctrip_channel")) {
                msg = msg + insuranceName + "["+ beforePriceLimit.getIntValue("priceFloor") * 100;
                msg = msg + "," + beforePriceLimit.getIntValue("priceCeiling") * 100 + "]";
                msg = msg + "修改为[" + afterPriceLimit.getIntValue("priceFloor") * 100;
                msg = msg + "," + afterPriceLimit.getIntValue("priceCeiling") * 100 + "]; ";
            }
            InsuranceServicePriceChannel updateRecord = new InsuranceServicePriceChannel();
            updateRecord.setPrice(afterPrice * 100);
            updateRecord.setOpTime(System.currentTimeMillis());
            updateRecord.setOpUserId(-2L); // 携程
            InsuranceServicePriceChannelExample updateExample = new InsuranceServicePriceChannelExample();
            updateExample.createCriteria()
                    .andInsuranceServiceSettingIdEqualTo(insuranceSettingId)
                    .andStoreIdEqualTo(storeId)
                    .andVehicleModelIdEqualTo(vehicleModelId)
                    .andChannelEqualTo(channelId)
                    .andStatusLessThan((byte) 2);
            int count = insuranceServicePriceChannelMapper.updateByExampleSelective(updateRecord, updateExample);
            log.info("保险改价回调,价格保险数据修改; 门店{},车型{},{}价格修改[{}->{}],修改数据count={}", storeId, vehicleModelId, insuranceName, beforePrice * 100, afterPrice * 100, count);
        }

        // 更新限价数据
        if (updLimitFlg) {
            Result<StoreInfoVo> storeInfoResult = storeInfoService.storeInfoBaseFind(storeId);
            if (storeInfoResult == null || !storeInfoResult.isSuccess() || storeInfoResult.getModel() == null) {
                log.info("保险改价回调,未查询到storeId={}门店, content:{},", storeId, content);
                return;
            }
            Long saasCityId = storeInfoResult.getModel().getCityId();
            // 查找标准子车系id
            CtripPriceFilterInfoExample example = new CtripPriceFilterInfoExample();
            example.createCriteria().andChannelIdEqualTo(OrderSourceEnum.CTRIP.longValue())
                    .andStoreIdEqualTo(storeId)
                    .andVehicleSeriesIdEqualTo(Long.parseLong(subVehicleId))
                    .andSassCityIdEqualTo(saasCityId);
            List<CtripPriceFilterInfo> ctripPriceFilterInfos = ctripPriceFilterInfoMapper.selectByExample(example);
            ctripPriceFilterInfos.sort(Comparator.comparing(CtripPriceFilterInfo::getOpTime));
            // 根据城市及标准车系更新限价数据
            if (CollectionUtils.isNotEmpty(ctripPriceFilterInfos)) {
                CtripPriceFilterInfo filter = ctripPriceFilterInfos.get(0);
                updFilterRecord.setOpTime(System.currentTimeMillis());
                CtripPriceFilterInfoExample updFilterExample = new CtripPriceFilterInfoExample();
                updFilterExample.createCriteria().andChannelIdEqualTo(OrderSourceEnum.CTRIP.longValue())
                        .andSassCityIdEqualTo(saasCityId)
                        .andStandardProductIdEqualTo(filter.getStandardProductId());
                int count = ctripPriceFilterInfoMapper.updateByExampleSelective(updFilterRecord, updFilterExample);
                log.info("保险改价回调,根据城市基础车型批量上下限修改;擎路城市id{},携程基础车型{},{} 修改数据count={}", saasCityId, filter.getStandardProductId(), msg, count);
            }
        }
    }


    /**
     * 查询门店车辆标签
     * @param storeAndModelIds 门店车型键值对
     * @param merchantId 商家id
     */
    private Map<String, List<String>> selectVehicleTag(List<VehicleModelUniDTO> storeAndModelIds, Long merchantId) {
        Set<Long> vehicleModelIds = new HashSet<>();
        Set<Long> storeIds = new HashSet<>();
        for (VehicleModelUniDTO storeAndModelId : storeAndModelIds) {
            vehicleModelIds.add(storeAndModelId.getVehicleModelId());
            storeIds.add(storeAndModelId.getStoreId());
        }

        // 询价只展示预设标签，带个标识。内部逻辑直接不查库
        Result<List<VehicleStoreTagVO>> result = vehicleTagService.listStoreVehicleTag(vehicleModelIds, storeIds,
            YesOrNoEnum.YES.getValue(), merchantId);
        if (result == null || CollectionUtils.isEmpty(result.getModel())) {
            return Collections.emptyMap();
        }
        List<VehicleStoreTagVO> storeTags = result.getModel();

        Map<String, List<String>> resultMap = new HashMap<>(storeTags.size() * 20);
        for (VehicleStoreTagVO storeTag : storeTags) {
            List<VehicleTagVO> tags = storeTag.getTags();
            if (CollectionUtils.isNotEmpty(tags)) {
                for (VehicleTagVO tag : tags) {
                    String key = storeTag.getStoreId() + "," + tag.getVehicleModelId();
                    List<String> tagNames = resultMap.computeIfAbsent(key, k -> new ArrayList<>());
                    tagNames.add(tag.getTagName());
                }
            }
        }
        return resultMap;
    }


    private static Integer getDatePoor(Date startDate, Date endDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        Long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        Long mymint = diff / 1000 / 60;
        int intValue = mymint.intValue();
        return intValue;
    }

    private List<DailyPriceListDTO> getChargesAddIn(Long merchantId, Long storeId, Map<String, StoreHourlyChargeVo> chargeMaps, Date pickUpTime, Date returnTime) {
        Map<String, ServiceFeeTypeEnum> maping = new HashMap<>();
        maping.put("2", ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE);
        maping.put("3", ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE);
        //maping.put("4", ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE);

        List<DailyPriceListDTO> retList = new ArrayList<>();
        maping.forEach((key,value) -> {
            double[] dayLess = formatDefaultValue(chargeMaps, storeId, key, "2");
            double[] dayAbove = formatDefaultValue(chargeMaps, storeId, key, "1");
            List<SplitBusiDateVO> splitDate = splitBusiDate(dayLess, dayAbove, pickUpTime, returnTime);
            DailyPriceListDTO single = new DailyPriceListDTO();
            single.setCode(value.getServiceCode());
            single.setName(value.getServiceName());
            List<DailyPriceDTO> list = new ArrayList<>();
            Double high7Per = 0d;
            Double totalPer = 0d;
            Double loosePer = 0d;
            int index = 1;
            for (SplitBusiDateVO date : splitDate) {
                DailyPriceDTO dto = new DailyPriceDTO();
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dto.setDate(dateFormat.format(date.getDate()));
                dto.setHour(date.getHour());
                dto.setPrice(null);
                dto.setAllDay(date.isAllDay());
                dto.setPartDailyPrice(null);
                if (date.getHour() < 24) {
                    int changeIdx = date.getHour() > 8 ? 8 : date.getHour();
                    if (index > 1) {
                        date.setPer(dayAbove[changeIdx - 1]);
                    } else {
                        date.setPer(dayLess[changeIdx - 1]);
                    }
                } else {
                    date.setPer(1d);
                }
                dto.setPer(date.getPer());
                if (value.getServiceCode().equals(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode())) {
                    if (index <= 1) {
                        high7Per = high7Per + date.getPer();
                    }
                } else {
                    if (index <= 7) {
                        high7Per = high7Per + date.getPer();
                    }
                }
                totalPer = totalPer + date.getPer();
                loosePer = date.getPer();
                list.add(dto);
                index ++;
            }
            single.setHigh7Per(high7Per);
            single.setTotalPer(totalPer);
            single.setLoosePer(loosePer);
            single.setList(list);
            retList.add(single);
        });
        return retList;
    }

    private List<DailyPriceListDTO> getChargesAddInV2(Long merchantId, Long storeId, List<StoreHourlyVo> hourlyList, Date pickUpTime, Date returnTime) {
        Map<String, ServiceFeeTypeEnum> maping = new HashMap<>();
        maping.put("2", ServiceFeeTypeEnum.INSURANCE_ENJOY_SERVICE);
        maping.put("3", ServiceFeeTypeEnum.INSURANCE_EXCLUSIVE_SERVICE);

        if (CollectionUtils.isEmpty(hourlyList)) {
            return new ArrayList<>();
        }

        List<DailyPriceListDTO> retList = new ArrayList<>();
        maping.forEach((key,value) -> {
            DailyPriceListDTO single = new DailyPriceListDTO();
            single.setCode(value.getServiceCode());
            single.setName(value.getServiceName());
            List<DailyPriceDTO> list = new ArrayList<>();
            Double high7Per = 0d;
            Double totalPer = 0d;
            Double loosePer = 0d;
            List<SplitBusiDateVO> splitDate = splitBusiDateV2(hourlyList, pickUpTime, returnTime);
            int index = 1;
            for (SplitBusiDateVO date : splitDate) {
                DailyPriceDTO dto = new DailyPriceDTO();
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dto.setDate(dateFormat.format(date.getDate()));
                dto.setHour(date.getHour());
                dto.setPrice(null);
                dto.setAllDay(date.isAllDay());
                dto.setPartDailyPrice(null);
                dto.setPer(date.getPer());
                if (value.getServiceCode().equals(ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode())) {
                    if (index <= 1) {
                        high7Per = high7Per + date.getPer();
                    }
                } else {
                    if (index <= 7) {
                        high7Per = high7Per + date.getPer();
                    }
                }
                totalPer = totalPer + date.getPer();
                loosePer = date.getPer();
                list.add(dto);
                index ++;
            }
            single.setHigh7Per(high7Per);
            single.setTotalPer(totalPer);
            single.setLoosePer(loosePer);
            single.setList(list);
            retList.add(single);
        });
        return retList;
    }


    private double[] formatDefaultValue(Map<String, StoreHourlyChargeVo> chargeMaps, Long storeId, String chargeItem, String sence) {
        StoreHourlyChargeVo item = chargeMaps.get(storeId + ":" + chargeItem + ":" + sence);
        String value = "";
        if (item == null || StringUtils.isEmpty(item.getChargeValue())) {
            value = "1,1,1,1,1,1,1,1";
        } else if (item.getChargeValue().equals("1")){
            value = "1,1,1,1,1,1,1,1";
        } else if (item.getChargeValue().equals("0")){
            value = "0,0,0,0,0,0,0,0";
        } else {
            value = item.getChargeValue();
        }
        return  Arrays.asList(value.split(",")).stream().mapToDouble(Double::parseDouble).toArray();
    }

    private List<double[]> getCharges(Long merchantId, Long storeId, List<StoreHourlyChargeVo> chargeVos) {
        List<double[]> ret = new ArrayList<>(2);
        List<StoreHourlyChargeVo> tmpList = chargeVos.stream()
            .filter(e -> e.getChargeItem().equals(HourlyChargeEnum.CHARGE.getType()) && e.getStoreId().equals(storeId))
            .sorted((o1, o2) -> o2.getScene().compareTo(o1.getScene())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tmpList)) {
            boolean retFlg = true;
            for (StoreHourlyChargeVo vo : tmpList) {
                if (StringUtils.isNotEmpty(vo.getChargeValue())){
                    double[] dayLess = Arrays.asList(vo.getChargeValue().split(","))
                        .stream().mapToDouble(Double::parseDouble).toArray();
                    ret.add(dayLess);
                } else {
                    retFlg = false;
                    ret = new ArrayList<>(2);
                    break;
                }
            }
            if (retFlg) {
                return ret;
            }
        }

        // 0+X 场景,以后迁移到表管理
        double[] dayLess = new double[]{0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95, 1.00};
        // N+X 场景
        double[] dayAbove = new double[]{0.00, 0.25, 0.40, 0.50, 0.65, 0.75, 0.90, 1.00};
        if (storeId != null) {
            if (merchantId == 78L) {
                // 78-中海鑫
                dayLess = new double[] {0.25, 0.5, 0.75, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.25, 0.5, 0.75, 1.00, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 15L) {
                // 15-合肥鲸鱼
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.00, 0.25, 0.50, 0.75, 1.00, 1.00, 1.00, 1.00};
            } else if (Arrays.asList(60L).contains(storeId)) {
                // 98-美凯 60-长春店
                dayLess = new double[] {0.60, 0.65, 0.65, 0.80, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.25, 0.45, 0.50, 0.70, 1.00, 1.00, 1.00, 1.00};
            } else if (Arrays.asList(65L).contains(storeId)) {
                // 98-美凯 65-成都店
                dayLess = new double[] {0.25, 0.55, 0.85, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.35, 0.45, 0.55, 0.85, 1.00, 1.00, 1.00, 1.00};
            } else if (Arrays.asList(66L).contains(storeId)) {
                // 98-美凯 66-三亚店
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.01, 0.15, 0.30, 0.40, 0.50, 0.60, 0.75, 1.00};
            } else if (merchantId == 174L) {
                // 174-轻风
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.00, 0.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 166L) {
                // 166-竞优
                dayLess = new double[] {0.30, 0.40, 0.50, 0.60, 0.70, 0.90, 1.00, 1.00};
                dayAbove = new double[] {0.30, 0.40, 0.50, 0.60, 0.70, 0.90, 1.00, 1.00};
            } else if (merchantId == 155L) {
                // 155-路友
                dayLess = new double[] {0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 0.99, 1.00};
                dayAbove = new double[] {0.25, 0.50, 0.50, 0.70, 0.80, 1.00, 1.00, 1.00};
            } else if (merchantId == 178L) {
                // 178-路博达
                dayLess = new double[] {0.35, 0.45, 0.55, 0.65, 0.75, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.35, 0.45, 0.55, 0.65, 0.75, 1.00, 1.00, 1.00};
            } else if (merchantId == 65L || merchantId == 205L) {
                // 205-九夏, 65-千澄
                dayLess = new double[] {0.25, 0.50, 0.75, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.25, 0.50, 0.75, 1.00, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 130L) {
                // 130-可瑞
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.00, 0.25, 0.50, 0.50, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 119L || merchantId == 131L) {
                // 131-金义方  119-幕远
                dayLess = new double[] {0.80, 0.80, 0.80, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.10, 0.55, 0.80, 1.00, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 148L) {
                // 148-凯美
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.01, 0.01, 0.01, 0.20, 0.35, 0.55, 0.70, 0.80};
                if (Arrays.asList(91L).contains(storeId)) {
                    dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                    dayAbove = new double[] {0.00, 0.25, 0.25, 0.50, 0.65, 0.75, 0.90, 1.00};
                }
            } else if (merchantId == 163L) {
                // 163-如行
                dayLess = new double[] {0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95, 1.00};
                dayAbove = new double[] {0.00, 0.25, 0.40, 0.50, 0.65, 0.75, 0.90, 1.00};
            } else if (merchantId == 39L) {
                // 39-龙嘉
                dayLess = new double[] {0.35, 0.45, 0.55, 0.55, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.25, 0.35, 0.45, 0.55, 1.00, 1.00, 1.00, 1.00};
            } else if (merchantId == 221L) {
                // 221-港梦 超跑俱乐部
                dayLess = new double[] {1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00};
                dayAbove = new double[] {0.35, 0.50, 0.50, 0.50, 1.00, 1.00, 1.00, 1.00};
            }
        }
        ret.add(dayLess);
        ret.add(dayAbove);
        return ret;
    }


    private List<SplitBusiDateVO> splitBusiDate(double[] dayLess, double[] dayAbove, Date pickUpTime, Date returnTime) {
        try {
            Integer datePoor = getDatePoor(pickUpTime, returnTime);
            Integer hour = new BigDecimal(datePoor / 60).setScale(1).intValue();
            boolean lastIsAllDay =  (double) hour % 24 == 0 ? true : false;
            hour = hour == (double) datePoor / 60 ? hour : (hour + 1);
            int day = (int) (hour / 24);
            day = day == (double) hour / 24 ? day : (day + 1);
            List<SplitBusiDateVO> retList = new ArrayList<>();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date tmpDate = dateFormat.parse(dateFormat.format(pickUpTime));
            for (int i = 0; i <= day; i++) {
                SplitBusiDateVO vo = new SplitBusiDateVO();
                int tmpHour = 24;
                if (hour < 24)
                    tmpHour = hour;
                if (tmpHour < 1) {
                    continue;
                }
                double per = 1;
                boolean allDay = true;
                if (day == 1) {
                    if (hour <= 8) {
                        per = dayLess[tmpHour - 1];
                        allDay = false;
                    } else if (hour <= 23) {
                        per = dayLess[7];
                        allDay = false;
                    } else {
                        if (!lastIsAllDay && i == day - 1) {
                            per = dayLess[7];
                            allDay = false;
                            if (hour == 24) {
                                tmpHour = 23; //携程这边23.5需要向下取整
                            }
                        }
                    }
                } else {
                    if (hour <= 8) {
                        per = dayAbove[tmpHour - 1];
                        allDay = false;
                    } else if (hour <= 23) {
                        per = dayAbove[7];
                        allDay = false;
                    } else {
                        if (!lastIsAllDay && i == day - 1) {
                            per = dayAbove[7];
                            allDay = false;
                            if (hour == 24) {
                                tmpHour = 23; //携程这边23.5需要向下取整
                            }
                        }
                    }
                }
                vo.setDate(tmpDate);
                vo.setHour(tmpHour);
                vo.setPer(per);
                vo.setAllDay(allDay);
                retList.add(vo);
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(tmpDate);
                calendar.add(Calendar.DATE, 1);
                tmpDate = calendar.getTime();
                hour = hour - 24;
            }
            return retList;
        } catch (Exception e) {
            //log.error("零散小时,异常!商家={},门店={},msg={}", merchantId, storeId, e.getMessage());
            e.printStackTrace();
            throw new BizException("计算零散小时费规则失败");
        }
    }

    /**
     * 零散小时 v2
     *
     * @param hourlyList
     * @param pickUpTime
     * @param returnTime
     * @return
     */
    private List<SplitBusiDateVO> splitBusiDateV2(List<StoreHourlyVo> hourlyList, Date pickUpTime, Date returnTime) {
        try {
            Integer datePoor = getDatePoor(pickUpTime, returnTime);
            Integer hour = new BigDecimal(datePoor / 60).setScale(1).intValue();
            boolean lastIsAllDay =  (double) hour % 24 == 0 ? true : false;
            hour = hour == (double) datePoor / 60 ? hour : (hour + 1);
            int day = (int) (hour / 24);
            day = day == (double) hour / 24 ? day : (day + 1);
            List<SplitBusiDateVO> retList = new ArrayList<>();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date tmpDate = dateFormat.parse(dateFormat.format(pickUpTime));
            // 过滤零散计算规则
            List<StoreHourlyVo> tmpHourlyList = new ArrayList<>();
            if (hour < 24) {
                tmpHourlyList = hourlyList.stream()
                        .filter(e -> e.getScene().equals((byte) 2))
                        .collect(Collectors.toList());
            } else {
                tmpHourlyList = hourlyList.stream()
                        .filter(e -> e.getScene().equals((byte) 1))
                        .collect(Collectors.toList());
            }
            for (int i = 0; i < day; i++) {
                SplitBusiDateVO vo = new SplitBusiDateVO();
                double per = 1;
                int tmpHour = 24;
                boolean allDay = true;
                if (hour < 24) {
                    tmpHour = hour;
                    allDay = false;
                    for (StoreHourlyVo hourlyVo : tmpHourlyList) {
                        if (tmpHour >= hourlyVo.getStartHour() && tmpHour <= hourlyVo.getEndHour()) {
                            per = (double) hourlyVo.getChargeValue() / 100;
                            break;
                        }
                    }
                } else if (hour == 24 && !lastIsAllDay && i == day - 1) {
                    tmpHour = 23;
                    allDay = false;
                }
                vo.setDate(tmpDate);
                vo.setHour(tmpHour);
                vo.setPer(per);
                vo.setAllDay(allDay);
                retList.add(vo);
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(tmpDate);
                calendar.add(Calendar.DATE, 1);
                tmpDate = calendar.getTime();
                hour = hour - 24;
            }
            return retList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("计算新零散小时费规则失败");
        }
    }


    /**
     * 查询车型基础信息
     * @param vehicleModelId
     * @return
     */
    @Override
    public VehicleModelVO selectModelBase(long vehicleModelId){
        Result<VehicleModelVO> modelVOResult = vehicleModelService.getVehicleModelBaseById(vehicleModelId);
        if (modelVOResult.getModel() == null) {
            throw new BizException("车型不存在");
        }
        return modelVOResult.getModel();
    }
}
