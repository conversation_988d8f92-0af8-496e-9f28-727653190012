package com.ql.rent.entity.vehicle;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EtcDeviceExample {

    public Integer vehicleStatus;

    public String frameNum;

    public List<Long> vehicleModelIdList;

    public List<Long> getVehicleModelIdList() {
        return vehicleModelIdList;
    }

    public void setVehicleModelIdList(List<Long> vehicleModelIdList) {
        this.vehicleModelIdList = vehicleModelIdList;
    }

    public Integer getVehicleStatus() {
        return vehicleStatus;
    }

    public void setVehicleStatus(Integer vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getFrameNum() {
        return frameNum;
    }

    public void setFrameNum(String frameNum) {
        this.frameNum = frameNum;
    }

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EtcDeviceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdEqualTo(Long value) {
            addCriterion("merchant_id =", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotEqualTo(Long value) {
            addCriterion("merchant_id <>", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThan(Long value) {
            addCriterion("merchant_id >", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("merchant_id >=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThan(Long value) {
            addCriterion("merchant_id <", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThanOrEqualTo(Long value) {
            addCriterion("merchant_id <=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIn(List<Long> values) {
            addCriterion("merchant_id in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotIn(List<Long> values) {
            addCriterion("merchant_id not in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdBetween(Long value1, Long value2) {
            addCriterion("merchant_id between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotBetween(Long value1, Long value2) {
            addCriterion("merchant_id not between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(Long value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(Long value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(Long value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(Long value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(Long value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<Long> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<Long> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(Long value1, Long value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(Long value1, Long value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNull() {
            addCriterion("vehicle_model_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNotNull() {
            addCriterion("vehicle_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdEqualTo(Long value) {
            addCriterion("vehicle_model_id =", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotEqualTo(Long value) {
            addCriterion("vehicle_model_id <>", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThan(Long value) {
            addCriterion("vehicle_model_id >", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vehicle_model_id >=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThan(Long value) {
            addCriterion("vehicle_model_id <", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThanOrEqualTo(Long value) {
            addCriterion("vehicle_model_id <=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIn(List<Long> values) {
            addCriterion("vehicle_model_id in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotIn(List<Long> values) {
            addCriterion("vehicle_model_id not in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdBetween(Long value1, Long value2) {
            addCriterion("vehicle_model_id between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotBetween(Long value1, Long value2) {
            addCriterion("vehicle_model_id not between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(Long value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(Long value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(Long value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(Long value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(Long value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<Long> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<Long> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(Long value1, Long value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(Long value1, Long value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdIsNull() {
            addCriterion("third_vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdIsNotNull() {
            addCriterion("third_vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdEqualTo(String value) {
            addCriterion("third_vehicle_id =", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdNotEqualTo(String value) {
            addCriterion("third_vehicle_id <>", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdGreaterThan(String value) {
            addCriterion("third_vehicle_id >", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdGreaterThanOrEqualTo(String value) {
            addCriterion("third_vehicle_id >=", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdLessThan(String value) {
            addCriterion("third_vehicle_id <", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdLessThanOrEqualTo(String value) {
            addCriterion("third_vehicle_id <=", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdLike(String value) {
            addCriterion("third_vehicle_id like", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdNotLike(String value) {
            addCriterion("third_vehicle_id not like", value, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdIn(List<String> values) {
            addCriterion("third_vehicle_id in", values, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdNotIn(List<String> values) {
            addCriterion("third_vehicle_id not in", values, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdBetween(String value1, String value2) {
            addCriterion("third_vehicle_id between", value1, value2, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleIdNotBetween(String value1, String value2) {
            addCriterion("third_vehicle_id not between", value1, value2, "thirdVehicleId");
            return (Criteria) this;
        }

        public Criteria andEtcNoIsNull() {
            addCriterion("etc_no is null");
            return (Criteria) this;
        }

        public Criteria andEtcNoIsNotNull() {
            addCriterion("etc_no is not null");
            return (Criteria) this;
        }

        public Criteria andEtcNoEqualTo(String value) {
            addCriterion("etc_no =", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoNotEqualTo(String value) {
            addCriterion("etc_no <>", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoGreaterThan(String value) {
            addCriterion("etc_no >", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoGreaterThanOrEqualTo(String value) {
            addCriterion("etc_no >=", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoLessThan(String value) {
            addCriterion("etc_no <", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoLessThanOrEqualTo(String value) {
            addCriterion("etc_no <=", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoLike(String value) {
            addCriterion("etc_no like", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoNotLike(String value) {
            addCriterion("etc_no not like", value, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoIn(List<String> values) {
            addCriterion("etc_no in", values, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoNotIn(List<String> values) {
            addCriterion("etc_no not in", values, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoBetween(String value1, String value2) {
            addCriterion("etc_no between", value1, value2, "etcNo");
            return (Criteria) this;
        }

        public Criteria andEtcNoNotBetween(String value1, String value2) {
            addCriterion("etc_no not between", value1, value2, "etcNo");
            return (Criteria) this;
        }

        public Criteria andIcNoIsNull() {
            addCriterion("ic_no is null");
            return (Criteria) this;
        }

        public Criteria andIcNoIsNotNull() {
            addCriterion("ic_no is not null");
            return (Criteria) this;
        }

        public Criteria andIcNoEqualTo(String value) {
            addCriterion("ic_no =", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoNotEqualTo(String value) {
            addCriterion("ic_no <>", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoGreaterThan(String value) {
            addCriterion("ic_no >", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoGreaterThanOrEqualTo(String value) {
            addCriterion("ic_no >=", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoLessThan(String value) {
            addCriterion("ic_no <", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoLessThanOrEqualTo(String value) {
            addCriterion("ic_no <=", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoLike(String value) {
            addCriterion("ic_no like", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoNotLike(String value) {
            addCriterion("ic_no not like", value, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoIn(List<String> values) {
            addCriterion("ic_no in", values, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoNotIn(List<String> values) {
            addCriterion("ic_no not in", values, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoBetween(String value1, String value2) {
            addCriterion("ic_no between", value1, value2, "icNo");
            return (Criteria) this;
        }

        public Criteria andIcNoNotBetween(String value1, String value2) {
            addCriterion("ic_no not between", value1, value2, "icNo");
            return (Criteria) this;
        }

        public Criteria andOnlineIsNull() {
            addCriterion("online is null");
            return (Criteria) this;
        }

        public Criteria andOnlineIsNotNull() {
            addCriterion("online is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineEqualTo(Byte value) {
            addCriterion("online =", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineNotEqualTo(Byte value) {
            addCriterion("online <>", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineGreaterThan(Byte value) {
            addCriterion("online >", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineGreaterThanOrEqualTo(Byte value) {
            addCriterion("online >=", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineLessThan(Byte value) {
            addCriterion("online <", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineLessThanOrEqualTo(Byte value) {
            addCriterion("online <=", value, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineIn(List<Byte> values) {
            addCriterion("online in", values, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineNotIn(List<Byte> values) {
            addCriterion("online not in", values, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineBetween(Byte value1, Byte value2) {
            addCriterion("online between", value1, value2, "online");
            return (Criteria) this;
        }

        public Criteria andOnlineNotBetween(Byte value1, Byte value2) {
            addCriterion("online not between", value1, value2, "online");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusIsNull() {
            addCriterion("hard_nink_status is null");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusIsNotNull() {
            addCriterion("hard_nink_status is not null");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusEqualTo(Byte value) {
            addCriterion("hard_nink_status =", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusNotEqualTo(Byte value) {
            addCriterion("hard_nink_status <>", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusGreaterThan(Byte value) {
            addCriterion("hard_nink_status >", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("hard_nink_status >=", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusLessThan(Byte value) {
            addCriterion("hard_nink_status <", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusLessThanOrEqualTo(Byte value) {
            addCriterion("hard_nink_status <=", value, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusIn(List<Byte> values) {
            addCriterion("hard_nink_status in", values, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusNotIn(List<Byte> values) {
            addCriterion("hard_nink_status not in", values, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusBetween(Byte value1, Byte value2) {
            addCriterion("hard_nink_status between", value1, value2, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andHardNinkStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("hard_nink_status not between", value1, value2, "hardNinkStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusIsNull() {
            addCriterion("work_status is null");
            return (Criteria) this;
        }

        public Criteria andWorkStatusIsNotNull() {
            addCriterion("work_status is not null");
            return (Criteria) this;
        }

        public Criteria andWorkStatusEqualTo(Byte value) {
            addCriterion("work_status =", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusNotEqualTo(Byte value) {
            addCriterion("work_status <>", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusGreaterThan(Byte value) {
            addCriterion("work_status >", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("work_status >=", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusLessThan(Byte value) {
            addCriterion("work_status <", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusLessThanOrEqualTo(Byte value) {
            addCriterion("work_status <=", value, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusIn(List<Byte> values) {
            addCriterion("work_status in", values, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusNotIn(List<Byte> values) {
            addCriterion("work_status not in", values, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusBetween(Byte value1, Byte value2) {
            addCriterion("work_status between", value1, value2, "workStatus");
            return (Criteria) this;
        }

        public Criteria andWorkStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("work_status not between", value1, value2, "workStatus");
            return (Criteria) this;
        }

        public Criteria andServiceIsNull() {
            addCriterion("service is null");
            return (Criteria) this;
        }

        public Criteria andServiceIsNotNull() {
            addCriterion("service is not null");
            return (Criteria) this;
        }

        public Criteria andServiceEqualTo(Byte value) {
            addCriterion("service =", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceNotEqualTo(Byte value) {
            addCriterion("service <>", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceGreaterThan(Byte value) {
            addCriterion("service >", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceGreaterThanOrEqualTo(Byte value) {
            addCriterion("service >=", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceLessThan(Byte value) {
            addCriterion("service <", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceLessThanOrEqualTo(Byte value) {
            addCriterion("service <=", value, "service");
            return (Criteria) this;
        }

        public Criteria andServiceIn(List<Byte> values) {
            addCriterion("service in", values, "service");
            return (Criteria) this;
        }

        public Criteria andServiceNotIn(List<Byte> values) {
            addCriterion("service not in", values, "service");
            return (Criteria) this;
        }

        public Criteria andServiceBetween(Byte value1, Byte value2) {
            addCriterion("service between", value1, value2, "service");
            return (Criteria) this;
        }

        public Criteria andServiceNotBetween(Byte value1, Byte value2) {
            addCriterion("service not between", value1, value2, "service");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andPlateColorIsNull() {
            addCriterion("plate_color is null");
            return (Criteria) this;
        }

        public Criteria andPlateColorIsNotNull() {
            addCriterion("plate_color is not null");
            return (Criteria) this;
        }

        public Criteria andPlateColorEqualTo(Byte value) {
            addCriterion("plate_color =", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorNotEqualTo(Byte value) {
            addCriterion("plate_color <>", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorGreaterThan(Byte value) {
            addCriterion("plate_color >", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorGreaterThanOrEqualTo(Byte value) {
            addCriterion("plate_color >=", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorLessThan(Byte value) {
            addCriterion("plate_color <", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorLessThanOrEqualTo(Byte value) {
            addCriterion("plate_color <=", value, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorIn(List<Byte> values) {
            addCriterion("plate_color in", values, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorNotIn(List<Byte> values) {
            addCriterion("plate_color not in", values, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorBetween(Byte value1, Byte value2) {
            addCriterion("plate_color between", value1, value2, "plateColor");
            return (Criteria) this;
        }

        public Criteria andPlateColorNotBetween(Byte value1, Byte value2) {
            addCriterion("plate_color not between", value1, value2, "plateColor");
            return (Criteria) this;
        }

        public Criteria andAxlesIsNull() {
            addCriterion("axles is null");
            return (Criteria) this;
        }

        public Criteria andAxlesIsNotNull() {
            addCriterion("axles is not null");
            return (Criteria) this;
        }

        public Criteria andAxlesEqualTo(String value) {
            addCriterion("axles =", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesNotEqualTo(String value) {
            addCriterion("axles <>", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesGreaterThan(String value) {
            addCriterion("axles >", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesGreaterThanOrEqualTo(String value) {
            addCriterion("axles >=", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesLessThan(String value) {
            addCriterion("axles <", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesLessThanOrEqualTo(String value) {
            addCriterion("axles <=", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesLike(String value) {
            addCriterion("axles like", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesNotLike(String value) {
            addCriterion("axles not like", value, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesIn(List<String> values) {
            addCriterion("axles in", values, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesNotIn(List<String> values) {
            addCriterion("axles not in", values, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesBetween(String value1, String value2) {
            addCriterion("axles between", value1, value2, "axles");
            return (Criteria) this;
        }

        public Criteria andAxlesNotBetween(String value1, String value2) {
            addCriterion("axles not between", value1, value2, "axles");
            return (Criteria) this;
        }

        public Criteria andLengthIsNull() {
            addCriterion("length is null");
            return (Criteria) this;
        }

        public Criteria andLengthIsNotNull() {
            addCriterion("length is not null");
            return (Criteria) this;
        }

        public Criteria andLengthEqualTo(String value) {
            addCriterion("length =", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotEqualTo(String value) {
            addCriterion("length <>", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThan(String value) {
            addCriterion("length >", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThanOrEqualTo(String value) {
            addCriterion("length >=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThan(String value) {
            addCriterion("length <", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThanOrEqualTo(String value) {
            addCriterion("length <=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLike(String value) {
            addCriterion("length like", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotLike(String value) {
            addCriterion("length not like", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthIn(List<String> values) {
            addCriterion("length in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotIn(List<String> values) {
            addCriterion("length not in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthBetween(String value1, String value2) {
            addCriterion("length between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotBetween(String value1, String value2) {
            addCriterion("length not between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(String value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(String value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(String value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(String value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(String value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(String value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLike(String value) {
            addCriterion("width like", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotLike(String value) {
            addCriterion("width not like", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<String> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<String> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(String value1, String value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(String value1, String value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(String value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(String value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(String value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(String value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(String value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(String value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLike(String value) {
            addCriterion("height like", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotLike(String value) {
            addCriterion("height not like", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<String> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<String> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(String value1, String value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(String value1, String value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andTotalWeightIsNull() {
            addCriterion("total_weight is null");
            return (Criteria) this;
        }

        public Criteria andTotalWeightIsNotNull() {
            addCriterion("total_weight is not null");
            return (Criteria) this;
        }

        public Criteria andTotalWeightEqualTo(String value) {
            addCriterion("total_weight =", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightNotEqualTo(String value) {
            addCriterion("total_weight <>", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightGreaterThan(String value) {
            addCriterion("total_weight >", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightGreaterThanOrEqualTo(String value) {
            addCriterion("total_weight >=", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightLessThan(String value) {
            addCriterion("total_weight <", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightLessThanOrEqualTo(String value) {
            addCriterion("total_weight <=", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightLike(String value) {
            addCriterion("total_weight like", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightNotLike(String value) {
            addCriterion("total_weight not like", value, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightIn(List<String> values) {
            addCriterion("total_weight in", values, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightNotIn(List<String> values) {
            addCriterion("total_weight not in", values, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightBetween(String value1, String value2) {
            addCriterion("total_weight between", value1, value2, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andTotalWeightNotBetween(String value1, String value2) {
            addCriterion("total_weight not between", value1, value2, "totalWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWassIsNull() {
            addCriterion("gross_wass is null");
            return (Criteria) this;
        }

        public Criteria andGrossWassIsNotNull() {
            addCriterion("gross_wass is not null");
            return (Criteria) this;
        }

        public Criteria andGrossWassEqualTo(String value) {
            addCriterion("gross_wass =", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassNotEqualTo(String value) {
            addCriterion("gross_wass <>", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassGreaterThan(String value) {
            addCriterion("gross_wass >", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassGreaterThanOrEqualTo(String value) {
            addCriterion("gross_wass >=", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassLessThan(String value) {
            addCriterion("gross_wass <", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassLessThanOrEqualTo(String value) {
            addCriterion("gross_wass <=", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassLike(String value) {
            addCriterion("gross_wass like", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassNotLike(String value) {
            addCriterion("gross_wass not like", value, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassIn(List<String> values) {
            addCriterion("gross_wass in", values, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassNotIn(List<String> values) {
            addCriterion("gross_wass not in", values, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassBetween(String value1, String value2) {
            addCriterion("gross_wass between", value1, value2, "grossWass");
            return (Criteria) this;
        }

        public Criteria andGrossWassNotBetween(String value1, String value2) {
            addCriterion("gross_wass not between", value1, value2, "grossWass");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(Date value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(Date value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(Date value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(Date value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(Date value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(Date value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<Date> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<Date> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(Date value1, Date value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(Date value1, Date value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateIsNull() {
            addCriterion("grant_date is null");
            return (Criteria) this;
        }

        public Criteria andGrantDateIsNotNull() {
            addCriterion("grant_date is not null");
            return (Criteria) this;
        }

        public Criteria andGrantDateEqualTo(Date value) {
            addCriterion("grant_date =", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateNotEqualTo(Date value) {
            addCriterion("grant_date <>", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateGreaterThan(Date value) {
            addCriterion("grant_date >", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateGreaterThanOrEqualTo(Date value) {
            addCriterion("grant_date >=", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateLessThan(Date value) {
            addCriterion("grant_date <", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateLessThanOrEqualTo(Date value) {
            addCriterion("grant_date <=", value, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateIn(List<Date> values) {
            addCriterion("grant_date in", values, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateNotIn(List<Date> values) {
            addCriterion("grant_date not in", values, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateBetween(Date value1, Date value2) {
            addCriterion("grant_date between", value1, value2, "grantDate");
            return (Criteria) this;
        }

        public Criteria andGrantDateNotBetween(Date value1, Date value2) {
            addCriterion("grant_date not between", value1, value2, "grantDate");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNull() {
            addCriterion("owner_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNotNull() {
            addCriterion("owner_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualTo(String value) {
            addCriterion("owner_name =", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualTo(String value) {
            addCriterion("owner_name <>", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThan(String value) {
            addCriterion("owner_name >", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_name >=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThan(String value) {
            addCriterion("owner_name <", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualTo(String value) {
            addCriterion("owner_name <=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLike(String value) {
            addCriterion("owner_name like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLike(String value) {
            addCriterion("owner_name not like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIn(List<String> values) {
            addCriterion("owner_name in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotIn(List<String> values) {
            addCriterion("owner_name not in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetween(String value1, String value2) {
            addCriterion("owner_name between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetween(String value1, String value2) {
            addCriterion("owner_name not between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andActivateStatusIsNull() {
            addCriterion("activate_status is null");
            return (Criteria) this;
        }

        public Criteria andActivateStatusIsNotNull() {
            addCriterion("activate_status is not null");
            return (Criteria) this;
        }

        public Criteria andActivateStatusEqualTo(Byte value) {
            addCriterion("activate_status =", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusNotEqualTo(Byte value) {
            addCriterion("activate_status <>", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusGreaterThan(Byte value) {
            addCriterion("activate_status >", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("activate_status >=", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusLessThan(Byte value) {
            addCriterion("activate_status <", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusLessThanOrEqualTo(Byte value) {
            addCriterion("activate_status <=", value, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusIn(List<Byte> values) {
            addCriterion("activate_status in", values, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusNotIn(List<Byte> values) {
            addCriterion("activate_status not in", values, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusBetween(Byte value1, Byte value2) {
            addCriterion("activate_status between", value1, value2, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andActivateStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("activate_status not between", value1, value2, "activateStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusIsNull() {
            addCriterion("availability_status is null");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusIsNotNull() {
            addCriterion("availability_status is not null");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusEqualTo(Byte value) {
            addCriterion("availability_status =", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusNotEqualTo(Byte value) {
            addCriterion("availability_status <>", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusGreaterThan(Byte value) {
            addCriterion("availability_status >", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("availability_status >=", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusLessThan(Byte value) {
            addCriterion("availability_status <", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusLessThanOrEqualTo(Byte value) {
            addCriterion("availability_status <=", value, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusIn(List<Byte> values) {
            addCriterion("availability_status in", values, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusNotIn(List<Byte> values) {
            addCriterion("availability_status not in", values, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusBetween(Byte value1, Byte value2) {
            addCriterion("availability_status between", value1, value2, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andAvailabilityStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("availability_status not between", value1, value2, "availabilityStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andSampleIsIsNull() {
            addCriterion("sample_is is null");
            return (Criteria) this;
        }

        public Criteria andSampleIsIsNotNull() {
            addCriterion("sample_is is not null");
            return (Criteria) this;
        }

        public Criteria andSampleIsEqualTo(Byte value) {
            addCriterion("sample_is =", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsNotEqualTo(Byte value) {
            addCriterion("sample_is <>", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsGreaterThan(Byte value) {
            addCriterion("sample_is >", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsGreaterThanOrEqualTo(Byte value) {
            addCriterion("sample_is >=", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsLessThan(Byte value) {
            addCriterion("sample_is <", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsLessThanOrEqualTo(Byte value) {
            addCriterion("sample_is <=", value, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsIn(List<Byte> values) {
            addCriterion("sample_is in", values, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsNotIn(List<Byte> values) {
            addCriterion("sample_is not in", values, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsBetween(Byte value1, Byte value2) {
            addCriterion("sample_is between", value1, value2, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andSampleIsNotBetween(Byte value1, Byte value2) {
            addCriterion("sample_is not between", value1, value2, "sampleIs");
            return (Criteria) this;
        }

        public Criteria andLicenseIsNull() {
            addCriterion("license is null");
            return (Criteria) this;
        }

        public Criteria andLicenseIsNotNull() {
            addCriterion("license is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseEqualTo(String value) {
            addCriterion("license =", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseNotEqualTo(String value) {
            addCriterion("license <>", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseGreaterThan(String value) {
            addCriterion("license >", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("license >=", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseLessThan(String value) {
            addCriterion("license <", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseLessThanOrEqualTo(String value) {
            addCriterion("license <=", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseLike(String value) {
            addCriterion("license like", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseNotLike(String value) {
            addCriterion("license not like", value, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseIn(List<String> values) {
            addCriterion("license in", values, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseNotIn(List<String> values) {
            addCriterion("license not in", values, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseBetween(String value1, String value2) {
            addCriterion("license between", value1, value2, "license");
            return (Criteria) this;
        }

        public Criteria andLicenseNotBetween(String value1, String value2) {
            addCriterion("license not between", value1, value2, "license");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdIsNull() {
            addCriterion("etc_apply_order_id is null");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdIsNotNull() {
            addCriterion("etc_apply_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdEqualTo(String value) {
            addCriterion("etc_apply_order_id =", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdNotEqualTo(String value) {
            addCriterion("etc_apply_order_id <>", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdGreaterThan(String value) {
            addCriterion("etc_apply_order_id >", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("etc_apply_order_id >=", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdLessThan(String value) {
            addCriterion("etc_apply_order_id <", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdLessThanOrEqualTo(String value) {
            addCriterion("etc_apply_order_id <=", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdLike(String value) {
            addCriterion("etc_apply_order_id like", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdNotLike(String value) {
            addCriterion("etc_apply_order_id not like", value, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdIn(List<String> values) {
            addCriterion("etc_apply_order_id in", values, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdNotIn(List<String> values) {
            addCriterion("etc_apply_order_id not in", values, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdBetween(String value1, String value2) {
            addCriterion("etc_apply_order_id between", value1, value2, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andEtcApplyOrderIdNotBetween(String value1, String value2) {
            addCriterion("etc_apply_order_id not between", value1, value2, "etcApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoIsNull() {
            addCriterion("biz_agreement_no is null");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoIsNotNull() {
            addCriterion("biz_agreement_no is not null");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoEqualTo(String value) {
            addCriterion("biz_agreement_no =", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoNotEqualTo(String value) {
            addCriterion("biz_agreement_no <>", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoGreaterThan(String value) {
            addCriterion("biz_agreement_no >", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoGreaterThanOrEqualTo(String value) {
            addCriterion("biz_agreement_no >=", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoLessThan(String value) {
            addCriterion("biz_agreement_no <", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoLessThanOrEqualTo(String value) {
            addCriterion("biz_agreement_no <=", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoLike(String value) {
            addCriterion("biz_agreement_no like", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoNotLike(String value) {
            addCriterion("biz_agreement_no not like", value, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoIn(List<String> values) {
            addCriterion("biz_agreement_no in", values, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoNotIn(List<String> values) {
            addCriterion("biz_agreement_no not in", values, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoBetween(String value1, String value2) {
            addCriterion("biz_agreement_no between", value1, value2, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andBizAgreementNoNotBetween(String value1, String value2) {
            addCriterion("biz_agreement_no not between", value1, value2, "bizAgreementNo");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailIsNull() {
            addCriterion("device_status_detail is null");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailIsNotNull() {
            addCriterion("device_status_detail is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailEqualTo(String value) {
            addCriterion("device_status_detail =", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailNotEqualTo(String value) {
            addCriterion("device_status_detail <>", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailGreaterThan(String value) {
            addCriterion("device_status_detail >", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailGreaterThanOrEqualTo(String value) {
            addCriterion("device_status_detail >=", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailLessThan(String value) {
            addCriterion("device_status_detail <", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailLessThanOrEqualTo(String value) {
            addCriterion("device_status_detail <=", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailLike(String value) {
            addCriterion("device_status_detail like", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailNotLike(String value) {
            addCriterion("device_status_detail not like", value, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailIn(List<String> values) {
            addCriterion("device_status_detail in", values, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailNotIn(List<String> values) {
            addCriterion("device_status_detail not in", values, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailBetween(String value1, String value2) {
            addCriterion("device_status_detail between", value1, value2, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andDeviceStatusDetailNotBetween(String value1, String value2) {
            addCriterion("device_status_detail not between", value1, value2, "deviceStatusDetail");
            return (Criteria) this;
        }

        public Criteria andEtcSourceIsNull() {
            addCriterion("etc_source is null");
            return (Criteria) this;
        }

        public Criteria andEtcSourceIsNotNull() {
            addCriterion("etc_source is not null");
            return (Criteria) this;
        }

        public Criteria andEtcSourceEqualTo(Byte value) {
            addCriterion("etc_source =", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceNotEqualTo(Byte value) {
            addCriterion("etc_source <>", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceGreaterThan(Byte value) {
            addCriterion("etc_source >", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("etc_source >=", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceLessThan(Byte value) {
            addCriterion("etc_source <", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceLessThanOrEqualTo(Byte value) {
            addCriterion("etc_source <=", value, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceIn(List<Byte> values) {
            addCriterion("etc_source in", values, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceNotIn(List<Byte> values) {
            addCriterion("etc_source not in", values, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceBetween(Byte value1, Byte value2) {
            addCriterion("etc_source between", value1, value2, "etcSource");
            return (Criteria) this;
        }

        public Criteria andEtcSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("etc_source not between", value1, value2, "etcSource");
            return (Criteria) this;
        }

        public Criteria andPublisherIsNull() {
            addCriterion("publisher is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIsNotNull() {
            addCriterion("publisher is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherEqualTo(Byte value) {
            addCriterion("publisher =", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotEqualTo(Byte value) {
            addCriterion("publisher <>", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThan(Byte value) {
            addCriterion("publisher >", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThanOrEqualTo(Byte value) {
            addCriterion("publisher >=", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherLessThan(Byte value) {
            addCriterion("publisher <", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherLessThanOrEqualTo(Byte value) {
            addCriterion("publisher <=", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherIn(List<Byte> values) {
            addCriterion("publisher in", values, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotIn(List<Byte> values) {
            addCriterion("publisher not in", values, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherBetween(Byte value1, Byte value2) {
            addCriterion("publisher between", value1, value2, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotBetween(Byte value1, Byte value2) {
            addCriterion("publisher not between", value1, value2, "publisher");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusIsNull() {
            addCriterion("blacklist_status is null");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusIsNotNull() {
            addCriterion("blacklist_status is not null");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusEqualTo(Byte value) {
            addCriterion("blacklist_status =", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusNotEqualTo(Byte value) {
            addCriterion("blacklist_status <>", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusGreaterThan(Byte value) {
            addCriterion("blacklist_status >", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("blacklist_status >=", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusLessThan(Byte value) {
            addCriterion("blacklist_status <", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusLessThanOrEqualTo(Byte value) {
            addCriterion("blacklist_status <=", value, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusIn(List<Byte> values) {
            addCriterion("blacklist_status in", values, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusNotIn(List<Byte> values) {
            addCriterion("blacklist_status not in", values, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusBetween(Byte value1, Byte value2) {
            addCriterion("blacklist_status between", value1, value2, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andBlacklistStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("blacklist_status not between", value1, value2, "blacklistStatus");
            return (Criteria) this;
        }

        public Criteria andOpTimeIsNull() {
            addCriterion("op_time is null");
            return (Criteria) this;
        }

        public Criteria andOpTimeIsNotNull() {
            addCriterion("op_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpTimeEqualTo(Long value) {
            addCriterion("op_time =", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotEqualTo(Long value) {
            addCriterion("op_time <>", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeGreaterThan(Long value) {
            addCriterion("op_time >", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("op_time >=", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeLessThan(Long value) {
            addCriterion("op_time <", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeLessThanOrEqualTo(Long value) {
            addCriterion("op_time <=", value, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeIn(List<Long> values) {
            addCriterion("op_time in", values, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotIn(List<Long> values) {
            addCriterion("op_time not in", values, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeBetween(Long value1, Long value2) {
            addCriterion("op_time between", value1, value2, "opTime");
            return (Criteria) this;
        }

        public Criteria andOpTimeNotBetween(Long value1, Long value2) {
            addCriterion("op_time not between", value1, value2, "opTime");
            return (Criteria) this;
        }

        public Criteria andLastVerIsNull() {
            addCriterion("last_ver is null");
            return (Criteria) this;
        }

        public Criteria andLastVerIsNotNull() {
            addCriterion("last_ver is not null");
            return (Criteria) this;
        }

        public Criteria andLastVerEqualTo(Integer value) {
            addCriterion("last_ver =", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerNotEqualTo(Integer value) {
            addCriterion("last_ver <>", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerGreaterThan(Integer value) {
            addCriterion("last_ver >", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_ver >=", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerLessThan(Integer value) {
            addCriterion("last_ver <", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerLessThanOrEqualTo(Integer value) {
            addCriterion("last_ver <=", value, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerIn(List<Integer> values) {
            addCriterion("last_ver in", values, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerNotIn(List<Integer> values) {
            addCriterion("last_ver not in", values, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerBetween(Integer value1, Integer value2) {
            addCriterion("last_ver between", value1, value2, "lastVer");
            return (Criteria) this;
        }

        public Criteria andLastVerNotBetween(Integer value1, Integer value2) {
            addCriterion("last_ver not between", value1, value2, "lastVer");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andOpUserIdIsNull() {
            addCriterion("op_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOpUserIdIsNotNull() {
            addCriterion("op_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOpUserIdEqualTo(Long value) {
            addCriterion("op_user_id =", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdNotEqualTo(Long value) {
            addCriterion("op_user_id <>", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdGreaterThan(Long value) {
            addCriterion("op_user_id >", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("op_user_id >=", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdLessThan(Long value) {
            addCriterion("op_user_id <", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdLessThanOrEqualTo(Long value) {
            addCriterion("op_user_id <=", value, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdIn(List<Long> values) {
            addCriterion("op_user_id in", values, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdNotIn(List<Long> values) {
            addCriterion("op_user_id not in", values, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdBetween(Long value1, Long value2) {
            addCriterion("op_user_id between", value1, value2, "opUserId");
            return (Criteria) this;
        }

        public Criteria andOpUserIdNotBetween(Long value1, Long value2) {
            addCriterion("op_user_id not between", value1, value2, "opUserId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}