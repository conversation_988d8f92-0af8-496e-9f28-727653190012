package com.ql.rent.entity.vehicle;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class EtcDevice {
    private Long id;

    private Long merchantId;

    private Long storeId;

    private Long vehicleModelId;

    private Long vehicleId;

    private String thirdVehicleId;

    private String etcNo;

    private String icNo;

    private Byte online;

    private Byte hardNinkStatus;

    private Byte workStatus;

    private Byte service;

    private String remark;

    private Byte plateColor;

    private String axles;

    private String length;

    private String width;

    private String height;

    private String totalWeight;

    private String grossWass;

    private Date registerDate;

    private Date grantDate;

    private String ownerName;

    private Byte activateStatus;

    private Byte availabilityStatus;

    private Long createTime;

    private Byte sampleIs;

    private String license;

    private String etcApplyOrderId;

    private String bizAgreementNo;

    private String deviceStatusDetail;

    private Byte etcSource;

    private Byte publisher;

    private Byte blacklistStatus;

    private Long opTime;

    private Integer lastVer;

    private Byte deleted;

    private Long opUserId;

    private byte[] gis;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getThirdVehicleId() {
        return thirdVehicleId;
    }

    public void setThirdVehicleId(String thirdVehicleId) {
        this.thirdVehicleId = thirdVehicleId == null ? null : thirdVehicleId.trim();
    }

    public String getEtcNo() {
        return etcNo;
    }

    public void setEtcNo(String etcNo) {
        this.etcNo = etcNo == null ? null : etcNo.trim();
    }

    public String getIcNo() {
        return icNo;
    }

    public void setIcNo(String icNo) {
        this.icNo = icNo == null ? null : icNo.trim();
    }

    public Byte getOnline() {
        return online;
    }

    public void setOnline(Byte online) {
        this.online = online;
    }

    public Byte getHardNinkStatus() {
        return hardNinkStatus;
    }

    public void setHardNinkStatus(Byte hardNinkStatus) {
        this.hardNinkStatus = hardNinkStatus;
    }

    public Byte getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(Byte workStatus) {
        this.workStatus = workStatus;
    }

    public Byte getService() {
        return service;
    }

    public void setService(Byte service) {
        this.service = service;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Byte getPlateColor() {
        return plateColor;
    }

    public void setPlateColor(Byte plateColor) {
        this.plateColor = plateColor;
    }

    public String getAxles() {
        return axles;
    }

    public void setAxles(String axles) {
        this.axles = axles == null ? null : axles.trim();
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length == null ? null : length.trim();
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width == null ? null : width.trim();
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height == null ? null : height.trim();
    }

    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight == null ? null : totalWeight.trim();
    }

    public String getGrossWass() {
        return grossWass;
    }

    public void setGrossWass(String grossWass) {
        this.grossWass = grossWass == null ? null : grossWass.trim();
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public Date getGrantDate() {
        return grantDate;
    }

    public void setGrantDate(Date grantDate) {
        this.grantDate = grantDate;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName == null ? null : ownerName.trim();
    }

    public Byte getActivateStatus() {
        return activateStatus;
    }

    public void setActivateStatus(Byte activateStatus) {
        this.activateStatus = activateStatus;
    }

    public Byte getAvailabilityStatus() {
        return availabilityStatus;
    }

    public void setAvailabilityStatus(Byte availabilityStatus) {
        this.availabilityStatus = availabilityStatus;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Byte getSampleIs() {
        return sampleIs;
    }

    public void setSampleIs(Byte sampleIs) {
        this.sampleIs = sampleIs;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license == null ? null : license.trim();
    }

    public String getEtcApplyOrderId() {
        return etcApplyOrderId;
    }

    public void setEtcApplyOrderId(String etcApplyOrderId) {
        this.etcApplyOrderId = etcApplyOrderId == null ? null : etcApplyOrderId.trim();
    }

    public String getBizAgreementNo() {
        return bizAgreementNo;
    }

    public void setBizAgreementNo(String bizAgreementNo) {
        this.bizAgreementNo = bizAgreementNo == null ? null : bizAgreementNo.trim();
    }

    public String getDeviceStatusDetail() {
        return deviceStatusDetail;
    }

    public void setDeviceStatusDetail(String deviceStatusDetail) {
        this.deviceStatusDetail = deviceStatusDetail == null ? null : deviceStatusDetail.trim();
    }

    public Byte getEtcSource() {
        return etcSource;
    }

    public void setEtcSource(Byte etcSource) {
        this.etcSource = etcSource;
    }

    public Byte getPublisher() {
        return publisher;
    }

    public void setPublisher(Byte publisher) {
        this.publisher = publisher;
    }

    public Byte getBlacklistStatus() {
        return blacklistStatus;
    }

    public void setBlacklistStatus(Byte blacklistStatus) {
        this.blacklistStatus = blacklistStatus;
    }

    public Long getOpTime() {
        return opTime;
    }

    public void setOpTime(Long opTime) {
        this.opTime = opTime;
    }

    public Integer getLastVer() {
        return lastVer;
    }

    public void setLastVer(Integer lastVer) {
        this.lastVer = lastVer;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    public Long getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(Long opUserId) {
        this.opUserId = opUserId;
    }

    public byte[] getGis() {
        return gis;
    }

    public void setGis(byte[] gis) {
        this.gis = gis;
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table etc_device
     *
     * @mbg.generated
     * @project https://github.com/itfsw/mybatis-generator-plugin
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        merchantId("merchant_id", "merchantId", "BIGINT", false),
        storeId("store_id", "storeId", "BIGINT", false),
        vehicleModelId("vehicle_model_id", "vehicleModelId", "BIGINT", false),
        vehicleId("vehicle_id", "vehicleId", "BIGINT", false),
        thirdVehicleId("third_vehicle_id", "thirdVehicleId", "VARCHAR", false),
        etcNo("etc_no", "etcNo", "VARCHAR", false),
        icNo("ic_no", "icNo", "VARCHAR", false),
        online("online", "online", "TINYINT", false),
        hardNinkStatus("hard_nink_status", "hardNinkStatus", "TINYINT", false),
        workStatus("work_status", "workStatus", "TINYINT", false),
        service("service", "service", "TINYINT", false),
        remark("remark", "remark", "VARCHAR", false),
        plateColor("plate_color", "plateColor", "TINYINT", false),
        axles("axles", "axles", "VARCHAR", false),
        length("length", "length", "VARCHAR", false),
        width("width", "width", "VARCHAR", false),
        height("height", "height", "VARCHAR", false),
        totalWeight("total_weight", "totalWeight", "VARCHAR", false),
        grossWass("gross_wass", "grossWass", "VARCHAR", false),
        registerDate("register_date", "registerDate", "TIMESTAMP", false),
        grantDate("grant_date", "grantDate", "TIMESTAMP", false),
        ownerName("owner_name", "ownerName", "VARCHAR", false),
        activateStatus("activate_status", "activateStatus", "TINYINT", false),
        availabilityStatus("availability_status", "availabilityStatus", "TINYINT", false),
        createTime("create_time", "createTime", "BIGINT", false),
        sampleIs("sample_is", "sampleIs", "TINYINT", false),
        license("license", "license", "VARCHAR", false),
        etcApplyOrderId("etc_apply_order_id", "etcApplyOrderId", "VARCHAR", false),
        bizAgreementNo("biz_agreement_no", "bizAgreementNo", "VARCHAR", false),
        deviceStatusDetail("device_status_detail", "deviceStatusDetail", "VARCHAR", false),
        etcSource("etc_source", "etcSource", "TINYINT", false),
        publisher("publisher", "publisher", "TINYINT", false),
        blacklistStatus("blacklist_status", "blacklistStatus", "TINYINT", false),
        opTime("op_time", "opTime", "BIGINT", false),
        lastVer("last_ver", "lastVer", "INTEGER", false),
        deleted("deleted", "deleted", "TINYINT", false),
        opUserId("op_user_id", "opUserId", "BIGINT", false),
        gis("gis", "gis", "BINARY", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table etc_device
         *
         * @mbg.generated
         * @project https://github.com/itfsw/mybatis-generator-plugin
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }
    }
}