package com.ql.rent.api.aggregate.controller;

import com.alibaba.fastjson.JSON;
import com.ql.dto.open.request.trade.CheckCarPickReturnRequest;
import com.ql.rent.api.aggregate.dto.ActivityAndCouponDTO;
import com.ql.rent.api.aggregate.dto.AddressDTO;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.request.CancelOrderReq;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.request.*;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.CtripCheckCarPickReturnResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.GetCarContractResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.PushPickReturnToVendorResponse;
import com.ql.rent.api.aggregate.model.request.*;
import com.ql.rent.api.aggregate.model.response.*;
import com.ql.rent.api.aggregate.service.VehicleAggregateDomainService;
import com.ql.rent.api.aggregate.vo.request.CreateOrderReq;
import com.ql.rent.api.aggregate.vo.request.*;
import com.ql.rent.api.aggregate.vo.response.CreateOrderResp;
import com.ql.rent.api.aggregate.vo.response.GetOrderPenaltyResp;
import com.ql.rent.api.aggregate.vo.response.SaasResponse;
import com.ql.rent.constant.RedisConstant;
import com.ql.rent.dto.trade.OrderServiceItemDTO;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.third.ServiceItemEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.enums.vehicle.VehicleBusyEnum;
import com.ql.rent.service.common.IApiConnService;
import com.ql.rent.service.price.IInsuranceServiceSettingService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.service.trade.IThirdSelfPickReturnService;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.third.dto.VehicleDeviceStateDTO;
import com.ql.rent.vo.common.ApiConnVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.store.LongLatVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.trade.OrderOptionServiceDTO;
import com.ql.rent.vo.trade.third.*;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.websocket.server.PathParam;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单相关Api接口
 *
 * <AUTHOR>
 * @Date 2022/10/26 22:26
 */
@Slf4j
@RestController
@RequestMapping("/aggregate")
@AllArgsConstructor
public class OrderAggregate {

    private IThirdOrderService iThirdOrderService;
    private IThirdVehicleService iThirdVehicleService;
    private VehicleAggregateDomainService vehicleDomainService;
    private IStoreInfoService iThirdStoreService;
    private final IThirdSelfPickReturnService thirdSelfPickReturnService;
    private IApiConnService apiConnService;
    private IInsuranceServiceSettingService insuranceServiceSetting;

    /**
     * 授权通知接口
     *
     * @param merchantId
     * @param freezeNotifyReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/freezeNotify")
    public SaasResponse freezeNotify(@PathVariable("id") Long merchantId,
            @RequestBody FreezeNotifyReq freezeNotifyReq) {
        Span.current().updateName("授权扣款通知");
        try {
            ThirdFreezeNotifyDTO notifyDTO = new ThirdFreezeNotifyDTO();
            BeanUtils.copyProperties(freezeNotifyReq, notifyDTO);
            notifyDTO.setMerchantId(merchantId);
            if (StringUtils.isNotBlank(freezeNotifyReq.getVendorOrderCode())) {
                notifyDTO.setOrderId(Long.valueOf(freezeNotifyReq.getVendorOrderCode()));
            }
            Result<Boolean> result = iThirdOrderService.freezeNotify(notifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed(result.getResultCode());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 新版信用租免押的扣款、退款结果通知
     *
     * @param merchantId
     * @param freeDepositTradeNotifyReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/freeDepositTradeNotify")
    SaasResponse freeDepositTradeNotify(@PathVariable("id") Long merchantId,
            @RequestBody FreeDepositTradeNotifyReq freeDepositTradeNotifyReq) {
        Span.current().updateName("新版信用租免押的扣款、退款结果通知");
        try {
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setTradeNo(freeDepositTradeNotifyReq.getVendorTradeNo());
            thirdFreeDepositTradeNotifyDTO.setThirdTradeNo(freeDepositTradeNotifyReq.getCtripTradeNo());
            thirdFreeDepositTradeNotifyDTO.setOrderId(Long.valueOf(freeDepositTradeNotifyReq.getVendorOrderCode()));
            thirdFreeDepositTradeNotifyDTO.setResult(freeDepositTradeNotifyReq.getResult());
            BigDecimal bigDecimal = freeDepositTradeNotifyReq.getAmount();
            if (null != bigDecimal) {
                thirdFreeDepositTradeNotifyDTO.setAmount(bigDecimal.multiply(new BigDecimal(100)));
            } else {
                thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.ZERO);
            }
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            thirdFreeDepositTradeNotifyDTO.setTradeType(freeDepositTradeNotifyReq.getTradeType());
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotify(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed(result.getResultCode());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 新版信用租免押的扣款、退款结果通知V2
     * 平台通用（目前悟空）
     *
     * @param merchantId
     * @param freeDepositTradeNotifyReq
     * @returnV2
     */
    @RequestMapping(path = "/v2/order/merchant/{merchantId}/freeDepositTradeNotify")
    SaasResponse freeDepositTradeNotifyV2(@PathVariable("merchantId") Long merchantId,
            @RequestBody FreeDepositTradeNotifyReqV2 freeDepositTradeNotifyReq) {
        Span.current().updateName("新版信用租免押的扣款、退款结果通知");
        try {
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setTradeNo(freeDepositTradeNotifyReq.getVendorTradeNo());
            thirdFreeDepositTradeNotifyDTO.setThirdTradeNo(freeDepositTradeNotifyReq.getThirdTradeNo());
            thirdFreeDepositTradeNotifyDTO.setThirdOrderId(freeDepositTradeNotifyReq.getThirdOrderId());
            thirdFreeDepositTradeNotifyDTO.setDeductionType(freeDepositTradeNotifyReq.getDeductionType());
            thirdFreeDepositTradeNotifyDTO.setResult(freeDepositTradeNotifyReq.getResult());
            BigDecimal bigDecimal = freeDepositTradeNotifyReq.getAmount();
            if (null != bigDecimal) {
                thirdFreeDepositTradeNotifyDTO.setAmount(bigDecimal.multiply(new BigDecimal(100)));
            } else {
                thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.ZERO);
            }
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            thirdFreeDepositTradeNotifyDTO.setTradeType(freeDepositTradeNotifyReq.getTradeType());
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotify(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed(result.getResultCode());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消订单
     *
     * @param merchantId 渠道ID
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/cancel")
    public SaasResponse cancelOrder(@PathVariable("id") Long merchantId,
            @RequestBody com.ql.rent.api.aggregate.vo.request.CancelOrderReq cancelOrderReq) {
        Span.current().updateName("取消订单");
        try {
            Long orderId = cancelOrderReq.getOrderCode() == null ? null : Long.valueOf(cancelOrderReq.getOrderCode());
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService.cancelOrder(orderId, cancelOrderReq.getOrderId(),
                    pricePrecisionAlgorithm(cancelOrderReq.getPenaltyAmount()), new CancelOrderDTO());
            CancelOrderResp cancelOrderResp = new CancelOrderResp();
            if (result.isSuccess() && result.getModel() != null) {
                if (result.getModel().getPenaltyAmount() == 0) {
                    cancelOrderResp.setDeductRemark("无违约金");
                    cancelOrderResp.setDeductAmount(0);
                } else {
                    cancelOrderResp.setDeductAmount(result.getModel().getPenaltyAmount() / 100);
                    if (CollectionUtils.isNotEmpty(result.getModel().getCancelRuleList())) {
                        cancelOrderResp.setDeductRemark(String.join(";", result.getModel().getCancelRuleList()));
                    }
                }
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(cancelOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消预付订单查询违约金
     *
     * @param merchantId
     * @param getOrderPenaltyReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/getOrderPenalty")
    public SaasResponse<GetOrderPenaltyResp> getOrderPenalty(@PathVariable("id") Long merchantId,
            GetOrderPenaltyReq getOrderPenaltyReq) {
        Span.current().updateName("取消预付订单查询违约金");
        try {
            // TODO 取消预付订单查询违约金
            SaasResponse saasResponse = SaasResponse.builder().build();
            saasResponse.success(null);
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 创建订单, 只有携程在使用
     *
     * @param merchantId 渠道ID
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/create")
    public SaasResponse createOrder(@PathVariable("id") Long merchantId, @RequestBody CreateOrderReq createOrderReq) {
        Span.current().updateName("创建订单");
        try {
            log.info("三方下单, createOrderReq={}", JSON.toJSONString(createOrderReq));

            iThirdOrderService.transferOrder(Long.valueOf(createOrderReq.getMerchantId()),
                    createOrderReq.getChannelId(), createOrderReq, RedisConstant.MsgTopic.ORDER_TRANSFER_CREATE);

            ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();

            if (CollectionUtils.isNotEmpty(createOrderReq.getStandardFeeList())) {
                createOrderReq.getStandardFeeList().forEach(data -> {
                    // 租车押金
                    if ("4003".equals(data.getFeeCode())) {
                        thirdOrderDTO.setRentalDeposit(data.getFeeAmount().intValue());
                    }
                    // 违章押金
                    if ("4004".equals(data.getFeeCode())) {
                        thirdOrderDTO.setIllegalDeposit(data.getFeeAmount().intValue());
                    }
                });
            }

            thirdOrderDTO.setLevelCorrEarliestRegisterTime(createOrderReq.getLevelCorrEarliestRegisterTime());
            thirdOrderDTO.setPlatformCal(createOrderReq.isPlatformCal());
            thirdOrderDTO.setHourlyChargeSnapShot(createOrderReq.getHourlyChargeSnapShot());
            thirdOrderDTO.setPenaltyInfoSnapShot(createOrderReq.getPenaltyInfoSnapShot());
            thirdOrderDTO.setRemark(createOrderReq.getRemark());
            thirdOrderDTO.setStandardFeeList(createOrderReq.getStandardFeeList());
            thirdOrderDTO.setPriceDailyList(createOrderReq.getPriceDailyList());
            thirdOrderDTO.setSelfPrOrder((byte) (createOrderReq.isSelfPickupReturnOrder() ? 1 : 0));
            thirdOrderDTO.setChannelId(createOrderReq.getChannelId());
            thirdOrderDTO.setVehicleModelId(Long.valueOf(createOrderReq.getVehicleModelId()));
            thirdOrderDTO.setSourceOrderId(createOrderReq.getOrderId());
            thirdOrderDTO.setPickupDate(formatMinuteToMilliseconds(createOrderReq.getPickupDate()));
            thirdOrderDTO.setReturnDate(formatMinuteToMilliseconds(createOrderReq.getReturnDate()));
            thirdOrderDTO.setPickupStoreId(Long.valueOf(createOrderReq.getPickupStoreCode()));
            thirdOrderDTO.setReturnStoreId(Long.valueOf(createOrderReq.getReturnStoreCode()));
            thirdOrderDTO.setPickupCityCode(createOrderReq.getPickupCityCode() == null ? 0
                    : Integer.valueOf(createOrderReq.getPickupCityCode()));
            thirdOrderDTO.setReturnCityCode(createOrderReq.getReturnCityCode() == null ? 0
                    : Integer.valueOf(createOrderReq.getReturnCityCode()));
            thirdOrderDTO.setMerchantId(Long.valueOf(createOrderReq.getMerchantId()));
            thirdOrderDTO.setOrderTime(System.currentTimeMillis());
            thirdOrderDTO.setTagList(createOrderReq.getConvertedTagsList());
            thirdOrderDTO.setPriceChannel(createOrderReq.getPriceChannel());
            thirdOrderDTO.setIsPickOffRentalCenter(createOrderReq.getIsPickOffRentalCenter());
            thirdOrderDTO.setIsPickUpRentalCenter(createOrderReq.getIsPickUpRentalCenter());
            thirdOrderDTO.setPackageId(createOrderReq.getPackageId());
            thirdOrderDTO.setNoWorriedOrder(createOrderReq.isNoWorriedOrder());
            thirdOrderDTO.setFollowLimitDailyPrice(createOrderReq.isFollowLimitDailyPriceOrder() ? 1 : 0);
            thirdOrderDTO.setLevelCorrEarliestRegisterTime(createOrderReq.getLevelCorrEarliestRegisterTime());
            if (CollectionUtils.isNotEmpty(createOrderReq.getPickUpServiceCircleIdList())) {
                List<Long> pickUpStoreServiceCircleIds = createOrderReq.getPickUpServiceCircleIdList().stream()
                        .map(data -> data.getServiceCircleCode().longValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pickUpStoreServiceCircleIds)) {
                    thirdOrderDTO.setPickupCircleId(pickUpStoreServiceCircleIds.get(0));
                }
            }
            if (CollectionUtils.isNotEmpty(createOrderReq.getReturnServiceCircleIdList())) {
                List<Long> returnStoreServiceCircleIds = createOrderReq.getReturnServiceCircleIdList().stream()
                        .map(data -> data.getServiceCircleCode().longValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(returnStoreServiceCircleIds)) {
                    thirdOrderDTO.setReturnCircleId(returnStoreServiceCircleIds.get(0));
                }
            }

            if (CollectionUtils.isNotEmpty(createOrderReq.getActivityAndCouponList())) {
                List<com.ql.rent.api.aggregate.model.dto.ActivityAndCouponDTO> activityAndCouponDTOList = new ArrayList<>();
                for (ActivityAndCouponDTO dto : createOrderReq.getActivityAndCouponList()) {
                    com.ql.rent.api.aggregate.model.dto.ActivityAndCouponDTO activityAndCouponDTO = new com.ql.rent.api.aggregate.model.dto.ActivityAndCouponDTO();
                    BeanUtils.copyProperties(dto, activityAndCouponDTO);
                    activityAndCouponDTOList.add(activityAndCouponDTO);
                }
                thirdOrderDTO.setActivityAndCouponList(activityAndCouponDTOList);
            }
            int childrenSeatNum = getChildrenSeatNum(createOrderReq);
            thirdOrderDTO.setChildrenSeatNum(childrenSeatNum);
            try {
                // 验证订单价格
                validOrderPrice(merchantId, createOrderReq, thirdOrderDTO);
            } catch (BizException e) {
                log.info("三方下单, 校验订单金额失败, 携程订单号={}", createOrderReq.getOrderId());
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().failed("2001", "校验订单金额失败");
            }
            // 取车方式
            thirdOrderDTO.setPickupAddrType(PickupTypeEnum.CAR_SHOP.getType());
            if (Optional.ofNullable(createOrderReq.getIsPickupOndoor()).filter(Boolean::booleanValue).isPresent() ||
                    Optional.ofNullable(createOrderReq.getPickupOndoorAddr()).isPresent()) {
                AddressDTO pickUpAddressDTO = createOrderReq.getPickupOndoorAddr();
                if (Objects.nonNull(pickUpAddressDTO)) {
                    thirdOrderDTO.setPickupAddr(pickUpAddressDTO.getAddress());
                    // 计算异门店的时候需要使用坐标这里全部设置
                    LongLatVo pickupLongLatVo = new LongLatVo();
                    pickupLongLatVo.setLatitude(pickUpAddressDTO.getLatitude());
                    pickupLongLatVo.setLongitude(pickUpAddressDTO.getLongitude());
                    thirdOrderDTO.setPickupLongLatVo(pickupLongLatVo);
                    if (pickUpAddressDTO.getAddressType() == 1) {
                        thirdOrderDTO.setPickupAddr(pickUpAddressDTO.getAddress());
                        thirdOrderDTO.setPickupAddrType(PickupTypeEnum.CAR_DOOR.getType());
                    } else if (pickUpAddressDTO.getAddressType() == 2) {
                        thirdOrderDTO.setPickupAddr(pickUpAddressDTO.getAddress());
                        thirdOrderDTO.setPickupAddrType(PickupTypeEnum.PICK_UP.getType());
                    } else if (pickUpAddressDTO.getAddressType() == 0) {
                        thirdOrderDTO.setPickupAddrType(PickupTypeEnum.CAR_SHOP.getType());
                    }
                }
            }

            // 还车方式
            thirdOrderDTO.setReturnAddrType(ReturnTypeEnum.CAR_SHOP.getType());
            if (Optional.ofNullable(createOrderReq.getIsPickoffOndoor()).filter(Boolean::booleanValue).isPresent() ||
                    Optional.ofNullable(createOrderReq.getPickoffOndoorAddr()).isPresent()) {
                AddressDTO pickoffAddressDTO = createOrderReq.getPickoffOndoorAddr();
                if (Objects.nonNull(pickoffAddressDTO)) {
                    thirdOrderDTO.setReturnAddr(pickoffAddressDTO.getAddress());
                    // 计算异门店的时候需要使用坐标这里全部设置
                    LongLatVo returnLongLatVo = new LongLatVo();
                    returnLongLatVo.setLatitude(pickoffAddressDTO.getLatitude());
                    returnLongLatVo.setLongitude(pickoffAddressDTO.getLongitude());
                    thirdOrderDTO.setReturnLongLatVo(returnLongLatVo);
                    // 携程的取车上门为1，SaaS的取车上门是2
                    if (pickoffAddressDTO.getAddressType() == 1) {
                        thirdOrderDTO.setReturnAddrType(ReturnTypeEnum.CAR_DOOR.getType());
                        thirdOrderDTO.setReturnAddr(pickoffAddressDTO.getAddress());
                    } else if (pickoffAddressDTO.getAddressType() == 2) {
                        thirdOrderDTO.setReturnAddrType(ReturnTypeEnum.SEE_OFF.getType());
                        thirdOrderDTO.setReturnAddr(pickoffAddressDTO.getAddress());
                    } else if (pickoffAddressDTO.getAddressType() == 0) {
                        thirdOrderDTO.setReturnAddrType(ReturnTypeEnum.CAR_SHOP.getType());
                    }
                }
            }

            // 数据库精确到分，所以需要乘以100
            Integer payAmount = (int) Math.round(createOrderReq.getPayAmount() * 100);
            thirdOrderDTO.setPayAmount(payAmount);
            thirdOrderDTO.setReceivableAmount(payAmount);
            if (createOrderReq.getFreeDepositDegree() == 10) {
                thirdOrderDTO.setFreeDepositDegree(Integer.valueOf(FreeDepositTypeEnum.FREE_RENT.getType()));
            } else if (createOrderReq.getFreeDepositDegree() == 20) {
                thirdOrderDTO.setFreeDepositDegree(Integer.valueOf(FreeDepositTypeEnum.FREE_ALL.getType()));
            } else if (createOrderReq.getFreeDepositDegree() == 30) {
                thirdOrderDTO.setFreeDepositDegree(Integer.valueOf(FreeDepositTypeEnum.FREE_ILLEGAL.getType()));
            } else {
                thirdOrderDTO.setFreeDepositDegree(createOrderReq.getFreeDepositDegree());
            }
            thirdOrderDTO.setFreeDepositWay(createOrderReq.getFreeDepositWay() == null ? ""
                    : String.valueOf(createOrderReq.getFreeDepositWay()));
            if (Objects.equals("4", thirdOrderDTO.getFreeDepositWay())) {
                thirdOrderDTO.setFreeDepositWay(CtripFreeDepositWayEnum.WEIXIN.getWay());
            }
            // 订单来源，最好使用枚举
            // 携程的渠道ID
            Byte channelId = createOrderReq.getChannelId() == null ? OrderSourceEnum.CTRIP.getSource().byteValue()
                    : createOrderReq.getChannelId().byteValue();
            thirdOrderDTO.setOrderSource(channelId);
            // 订单状态，最好使用枚举
            thirdOrderDTO.setOrderStatus(OrderStatusEnum.CONFIRMED.getStatus());

            ThirdOrderUserDTO userDTO = new ThirdOrderUserDTO();
            userDTO.setUserName(createOrderReq.getUserInfo().getName());
            userDTO.setMobile(createOrderReq.getUserInfo().getMobile());
            Integer idType = createOrderReq.getUserInfo().getIdType();
            // 身份证
            if (1 == idType) {
                userDTO.setIdcardNo(createOrderReq.getUserInfo().getIdNo());
            } else if (2 == idType) {
                // 护照
                userDTO.setPassport(createOrderReq.getUserInfo().getIdNo());
            }
            userDTO.setPickUpIdType(idType);
            userDTO.setPickUpIdNo(createOrderReq.getUserInfo().getIdNo());
            thirdOrderDTO.setThirdOrderUserDTO(userDTO);

            // 一口价校验
            if (createOrderReq.isNoWorriedOrder()) {
                if (CollectionUtils.isEmpty(createOrderReq.getPriceDailyList())
                        || CollectionUtils.isEmpty(createOrderReq.getStandardFeeList())) {
                    Span.current().setStatus(StatusCode.ERROR);
                    log.error("三方下单失败! 一口价订单, 无价格日历或标准费用code, createOrderReq={}", JSON.toJSONString(createOrderReq));
                    return SaasResponse.builder().code("-1").message("下单失败").build();
                }
                log.info("三方下单, 校验订单金额, 携程订单号={}, 一口价订单", createOrderReq.getOrderId());
            }
            Result<Long> result = iThirdOrderService.createOrder(thirdOrderDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                CreateOrderResp createOrderResp = new CreateOrderResp(result.getModel().toString());
                return SaasResponse.builder().build().success(createOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("三方下单失败! result={}", JSON.toJSONString(result));
                if (StringUtils.isBlank(result.getResultCode())) {
                    result.setResultCode("2002");
                    result.setMessage("校验订单库存失败");
                }
                return SaasResponse.builder().code(result.getResultCode()).message(result.getMessage()).build();
            }

        } catch (BizException e) {
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(e);
            log.error("三方下单业务异常! createOrderReq={}", JSON.toJSONString(createOrderReq), e);
            return SaasResponse.builder().build().failed("2002", "校验订单库存失败");
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(e);
            log.error("三方下单异常! createOrderReq={}", JSON.toJSONString(createOrderReq), e);
            return SaasResponse.builder().code("-1").message("下单失败").build();
        } finally {
            Span.current().end();
        }
    }

    private int getChildrenSeatNum(CreateOrderReq createOrderReq) {
        int childrenSeat = 0;
        if (CollectionUtils.isNotEmpty(createOrderReq.getStandardFeeList())) {
            for (StandardFeeDTO standardFeeDTO : createOrderReq.getStandardFeeList()) {
                if (ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode().equals(standardFeeDTO.getFeeCode())) {
                    childrenSeat = standardFeeDTO.getQuantity().intValue();
                }
            }
        }
        return childrenSeat;
    }

    public void validOrderPrice(Long merchantId, CreateOrderReq createOrderReq, ThirdOrderDTO thirdOrderDTO) {
        StorePairDTO storePairDTO = new StorePairDTO();
        // 封装取车门店
        StoreCircleIdDTO pickUpStore = new StoreCircleIdDTO();
        pickUpStore.setId(Long.valueOf(createOrderReq.getPickupStoreCode()));
        pickUpStore.setCityCode(createOrderReq.getPickupCityCode());
        // 如果存在取车服务圈，转换取车服务圈信息
        if (null != createOrderReq.getPickUpServiceCircleIdList()
                && createOrderReq.getPickUpServiceCircleIdList().size() > 0) {
            List<Long> pickUpStoreServiceCircleIds = createOrderReq.getPickUpServiceCircleIdList().stream()
                    .map(data -> data.getServiceCircleCode().longValue()).collect(Collectors.toList());
            pickUpStore.setServiceCircleIdList(pickUpStoreServiceCircleIds);
        }
        storePairDTO.setPickUpStore(pickUpStore);
        // 封装还车门店
        StoreCircleIdDTO returnStore = new StoreCircleIdDTO();
        returnStore.setId(Long.valueOf(createOrderReq.getReturnStoreCode()));
        returnStore.setCityCode(createOrderReq.getReturnCityCode());
        // 如果存在还车服务圈，转换还车服务圈信息
        if (null != createOrderReq.getReturnServiceCircleIdList()
                && createOrderReq.getReturnServiceCircleIdList().size() > 0) {
            List<Long> returnStoreServiceCircleIds = createOrderReq.getReturnServiceCircleIdList().stream()
                    .map(data -> data.getServiceCircleCode().longValue()).collect(Collectors.toList());
            returnStore.setServiceCircleIdList(returnStoreServiceCircleIds);
        }
        storePairDTO.setReturnStore(returnStore);
        // 车型ID
        Long vehicleModelId = Long.valueOf(createOrderReq.getVehicleModelId());
        // 携程的渠道ID
        Long channelId = createOrderReq.getChannelId() == null ? OrderSourceEnum.CTRIP.getSource().longValue()
                : createOrderReq.getChannelId();
        log.info("携程下单, 订单渠道ID={}, channelId={}", createOrderReq.getChannelId(), channelId);
        // 取车时间
        Date pickUpTime = formatDate(createOrderReq.getPickupDate());
        // 还车时间
        Date returnTime = formatDate(createOrderReq.getReturnDate());
        // 调用车型价格日历服务，获取车型的价格日历
        VehicleModelUniDTO vehicleModelUniDTO = new VehicleModelUniDTO(storePairDTO.getPickUpStore().getId(),
                vehicleModelId, createOrderReq.isSelfPickupReturnOrder() ? (byte) 1 : (byte) 0, null);
        // 是否检查最小提前预定时间
        boolean checkMinBook = true;
        List<DailyPriceDTO> vehicleModelDailyPrice = iThirdVehicleService.selectModelCalendarPrice(merchantId,
                channelId, vehicleModelUniDTO, pickUpTime, returnTime, checkMinBook, null, thirdOrderDTO.isPlatformCal());

        // 获取取车门店的坐标信息
        PointDTO pickupPointDTO;
        if (createOrderReq.getIsPickupOndoor() && Objects.nonNull(createOrderReq.getPickupOndoorAddr())) {
            AddressDTO addressDTO = createOrderReq.getPickupOndoorAddr();
            pickupPointDTO = new PointDTO(addressDTO.getLongitude(), addressDTO.getLatitude());
        } else {
            Result<StoreInfoVo> pickupStoreInfoVoResult = iThirdStoreService.storeInfoBaseFind(pickUpStore.getId());
            Double pickupLng = pickupStoreInfoVoResult.getModel().getLongLat().getLongitude();
            Double pickupLat = pickupStoreInfoVoResult.getModel().getLongLat().getLatitude();
            pickupPointDTO = new PointDTO(pickupLng, pickupLat);
        }
        // 获取还车门店的坐标信息
        PointDTO returnPointDTO;
        if (createOrderReq.getIsPickoffOndoor() && Objects.nonNull(createOrderReq.getPickoffOndoorAddr())) {
            AddressDTO addressDTO = createOrderReq.getPickoffOndoorAddr();
            returnPointDTO = new PointDTO(addressDTO.getLongitude(), addressDTO.getLatitude());
        } else {
            Result<StoreInfoVo> returnStoreInfoVoResult = iThirdStoreService.storeInfoBaseFind(pickUpStore.getId());
            Double returnLng = returnStoreInfoVoResult.getModel().getLongLat().getLongitude();
            Double returnLat = returnStoreInfoVoResult.getModel().getLongLat().getLatitude();
            returnPointDTO = new PointDTO(returnLng, returnLat);
        }
        // 调用车型算价服务
        VehicleModelPriceAbbrDTO vehicleModelPriceAbbrDTO = vehicleDomainService.getModelPrice(merchantId, channelId,
                storePairDTO,
                vehicleModelUniDTO, vehicleModelDailyPrice, pickupPointDTO, returnPointDTO, pickUpTime, returnTime,
                createOrderReq.getAddedServices(), Collections.singletonList(createOrderReq.getCouponCode()),
                thirdOrderDTO.getActivityAndCouponList(), thirdOrderDTO.getChildrenSeatNum(), thirdOrderDTO.isPlatformCal());

        if (StringUtils.isNotBlank(createOrderReq.getPackageId())
                && vehicleModelPriceAbbrDTO.getNoWorriedPriceAbbrDTO() != null) {
            vehicleModelPriceAbbrDTO = vehicleModelPriceAbbrDTO.getNoWorriedPriceAbbrDTO();
            log.info("无忧租, 价格替换, getModelPrice result={}", JSON.toJSONString(vehicleModelPriceAbbrDTO));
        }

        log.info("getModelPrice result={}", JSON.toJSONString(vehicleModelPriceAbbrDTO));
        thirdOrderDTO.setVehicleModelPriceAbbrDTO(vehicleModelPriceAbbrDTO);
        // 车型根据算计，得出这段时间内的报价（包含服务费）
        // 下单前的校验，是实付，需要减掉优惠金额
        Integer totalAmount = vehicleModelPriceAbbrDTO.getTotalAmount() - vehicleModelPriceAbbrDTO.getDiscountAmount();
        // 携程订单的支付价格，剔除了优惠金额
        Integer compareTotalAmount = pricePrecisionAlgorithm(createOrderReq.getPayAmount());
        if (null != vehicleModelPriceAbbrDTO.getServiceItemAmountList()
                && vehicleModelPriceAbbrDTO.getServiceItemAmountList().size() > 0) {
            // 附加服务费用转换
            List<ServiceItemAmountDTO> serviceItemDTOS = vehicleModelPriceAbbrDTO.getServiceItemAmountList().stream()
                    .map(item -> {
                        ServiceItemAmountDTO serviceItemAmountDTO = new ServiceItemAmountDTO();
                        serviceItemAmountDTO.setAmount(item.getAmount());
                        serviceItemAmountDTO.setId(item.getId());
                        serviceItemAmountDTO.setType(item.getType());
                        serviceItemAmountDTO.setPrice(item.getPrice());
                        serviceItemAmountDTO.setCount(item.getCount());
                        serviceItemAmountDTO.setName(item.getName());
                        serviceItemAmountDTO.setIncludeInTotalAmount(item.getIncludeInTotalAmount());
                        // serviceItemAmountDTO.setRequired(item.getRequired());
                        serviceItemAmountDTO.setUnit(item.getUnit());
                        serviceItemAmountDTO.setQuantity(item.getQuantity());
                        serviceItemAmountDTO.setCode(item.getCode());
                        serviceItemAmountDTO.setCalPer(item.getCalPer());
                        serviceItemAmountDTO.setCtripStandardFee(item.isCtripStandardFee());
                        return serviceItemAmountDTO;
                    }).collect(Collectors.toList());
            log.info("createOrderReq serviceItemDTOS={}", JSON.toJSONString(serviceItemDTOS));
            thirdOrderDTO.setServiceItemDTOList(serviceItemDTOS);
        }
        thirdOrderDTO.setAddedServiceItemCode(createOrderReq.getAddedServices());
        // 获取上货开关 是否取消验价
        long checkChannel = OrderSourceEnum.transferCtripResale(channelId.byteValue()).longValue();
        Result<ApiConnVo> apiConnVoResult = apiConnService.getByMerchantIdAndChannelId(merchantId, checkChannel);

        boolean skipCheckPrice = false;
        if (checkChannel == OrderSourceEnum.CTRIP.longValue() && apiConnVoResult.getModel() != null
                && apiConnVoResult.getModel().getCheckPriceSwitch() != null
                && YesOrNoEnum.isYes(apiConnVoResult.getModel().getCheckPriceSwitch())) {
            skipCheckPrice = true;
        }
        thirdOrderDTO.setSkipCheckPrice(skipCheckPrice);

        log.info("三方下单, 三方订单ID={}, 跳过验价开关={}", createOrderReq.getOrderId(), skipCheckPrice);

        // 一口价订单
        boolean noWorriedOrder = createOrderReq.isNoWorriedOrder();
        // 1、验价金额不等而且跳过验价订单
        // 2、一口价订单
        if (compareTotalAmount.intValue() != totalAmount.intValue()) {
            if (skipCheckPrice || noWorriedOrder || createOrderReq.isPlatformCal()) {
                log.info("三方下单, 是否为无忧租一口价={}, 三方订单ID={}, 订单金额不等, 跳过验价", noWorriedOrder, createOrderReq.getOrderId());
                // 将我们SaaS的费用项价格、总价转为携程的费用项价格、总价
                replaceServiceItemDTOS(vehicleModelPriceAbbrDTO, thirdOrderDTO);
                thirdOrderDTO.setReplacePrice(true);
            } else {
                log.info("验证订单金额失败失败, 携程金额={}, SaaS再计算金额={}", compareTotalAmount, totalAmount);
                throw new BizException("验证订单金额失败");
            }
        } else {
            if (createOrderReq.isPlatformCal()) {
                log.info("三方下单, 携程费用标准化订单, 三方订单ID={}, standardFeeList={}, serviceItemAmountList={}",
                        createOrderReq.getOrderId(), JSON.toJSONString(thirdOrderDTO.getStandardFeeList()), JSON.toJSONString(thirdOrderDTO.getServiceItemDTOList()));
                if (CollectionUtils.isNotEmpty(thirdOrderDTO.getStandardFeeList())
                        && CollectionUtils.isNotEmpty(thirdOrderDTO.getServiceItemDTOList())) {
                    Map<String, StandardFeeDTO> standardFeeDTOMap = thirdOrderDTO.getStandardFeeList().stream()
                            .collect(Collectors.toMap(
                                    StandardFeeDTO::getFeeCode,
                                    item -> item,
                                    (existing, replacement) -> existing // 合并函数，保留第一个出现的值
                            ));
                    for (ServiceItemAmountDTO item : thirdOrderDTO.getServiceItemDTOList()) {
                       if (standardFeeDTOMap.get(item.getCode()) != null) {
                           item.setCtripStandardFee(standardFeeDTOMap.get(item.getCode()).isPlatformCal());
                       }
                    }
                }
            }
        }
    }

    private void replaceServiceItemDTOS(VehicleModelPriceAbbrDTO priceAbbrDTO, ThirdOrderDTO thirdOrderDTO) {
        log.info("三方下单, 三方订单ID={}, 订单金额不等, 替换前, 携程标准费用明细={}, SaaS标准费用明细={}",
                thirdOrderDTO.getSourceOrderId(), JSON.toJSONString(thirdOrderDTO.getStandardFeeList()),
                JSON.toJSONString(priceAbbrDTO.getServiceItemAmountList()));

        if (CollectionUtils.isNotEmpty(thirdOrderDTO.getStandardFeeList())
                && CollectionUtils.isNotEmpty(priceAbbrDTO.getServiceItemAmountList())) {
            Map<String, ServiceItemAmountDTO> serviceItemDTOMap = priceAbbrDTO.getServiceItemAmountList().stream()
                    .collect(Collectors.toMap(ServiceItemAmountDTO::getCode, item -> item));
            List<ServiceItemAmountDTO> serviceItemDTOS = new ArrayList<>();
            for (StandardFeeDTO item : thirdOrderDTO.getStandardFeeList()) {
                ServiceItemAmountDTO serviceItemAmountDTO = new ServiceItemAmountDTO();
                serviceItemAmountDTO.setAmount(item.getFeeAmount().intValue() * 100);
                serviceItemAmountDTO.setPrice(item.getUnitAmount().intValue() * 100);
                serviceItemAmountDTO.setCount(item.getQuantity().intValue());
                serviceItemAmountDTO.setIncludeInTotalAmount(1);
                serviceItemAmountDTO.setQuantity(item.getQuantity().doubleValue());
                serviceItemAmountDTO.setCode(item.getFeeCode());
                serviceItemAmountDTO.setCtripStandardFee(item.isPlatformCal());
                if (item.getFeeAmount().intValue() == 0 || item.getUnitAmount().intValue() == 0) {
                    serviceItemAmountDTO.setCalPer(0d);
                } else {
                    serviceItemAmountDTO.setCalPer(
                            item.getFeeAmount().divide(item.getUnitAmount(), 2, RoundingMode.UP).doubleValue());
                }
                // 这里code培森已经转了的
                // String serviceItemCode =
                // ServiceItemEnum.CtripMapping.getBackwardMapping().get(item.getFeeCode());
                if (serviceItemDTOMap.get(item.getFeeCode()) != null) {
                    ServiceItemAmountDTO serviceItemAmountDTOFromMap = serviceItemDTOMap.get(item.getFeeCode());
                    serviceItemAmountDTO.setName(serviceItemAmountDTOFromMap.getName());
                    serviceItemAmountDTO.setUnit(serviceItemAmountDTOFromMap.getUnit());
                    serviceItemAmountDTO.setId(serviceItemAmountDTOFromMap.getId());
                    serviceItemAmountDTO.setType(serviceItemAmountDTOFromMap.getType());
                    serviceItemDTOS.add(serviceItemAmountDTO);
                } else {
                    Map<String, String> map = ServiceItemEnum.CtripMapping.getBackwardMapping();
                    if (map.get(item.getFeeCode()) != null) {
                        ServiceItemEnum.ServiceItemCode serviceItemCode = ServiceItemEnum.CtripMapping
                                .getItemCodeByCode(map.get(item.getFeeCode()));
                        serviceItemAmountDTO.setName(serviceItemCode.getDesc());
                        serviceItemAmountDTO.setUnit("次");
                        serviceItemAmountDTO.setId(0L);
                        serviceItemAmountDTO.setType(serviceItemCode.getType().byteValue());
                        serviceItemDTOS.add(serviceItemAmountDTO);
                    } else {
                        log.info("三方下单, 三方订单ID={}, 订单金额不等, 未知的携程服务项={}", thirdOrderDTO.getSourceOrderId(),
                                JSON.toJSONString(item));
                    }

                }
            }
            thirdOrderDTO.setServiceItemDTOList(serviceItemDTOS);

            log.info("三方下单, 三方订单ID={}, 订单金额不等, 替换后, 订单明细={}, 携程标准费用明细={}",
                    thirdOrderDTO.getSourceOrderId(), JSON.toJSONString(thirdOrderDTO.getServiceItemDTOList()),
                    JSON.toJSONString(thirdOrderDTO.getStandardFeeList()));
        }
    }

    /**
     * 验证下订单V2 多平台通用
     *
     * @param channelId     渠道ID
     * @param merchantId    商户ID
     * @param checkOrderReq 验证订单实体
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/validateCreateOrder")
    public SaasResponse postValidateCreateOrderV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody ValidateCreateOrderReq checkOrderReq) {

        Span.current().updateName("验证下单V2");
        log.info("验证下订单V2, 请求参数={}", JSON.toJSONString(checkOrderReq));
        setChildrenSeatNum(checkOrderReq);
        if (checkOrderReq.getPickUpStore() != null && checkOrderReq.getPickUpStore().getCityId() == null) {
            checkOrderReq.getPickUpStore().setCityId(0L);
        }
        if (checkOrderReq.getPickUpStore() != null && checkOrderReq.getDropOffStore().getCityId() == null) {
            checkOrderReq.getDropOffStore().setCityId(0L);
        }
        // 是否有第三方人身险，有三方人身险，不用算价校验
        boolean thirdInsurance = false;
        if (CollectionUtils.isNotEmpty(checkOrderReq.getAddedServices())
                && checkOrderReq.getAddedServices().contains("unknown_ctrip_2000896")) {
            thirdInsurance = true;
            log.info("验证下订单V2, 携程人身险订单,", JSON.toJSONString(checkOrderReq));
        }
        // 先暂时去掉优惠券, 不然算价可能会对不上
        checkOrderReq.setActivityAndCouponList(null);
        try {
            long actualChannelId = Optional.ofNullable(checkOrderReq.getChannelId()).orElse(channelId);
            long checkChannel = OrderSourceEnum.transferCtripResale(channelId.byteValue()).longValue();
            Result<ApiConnVo> apiConnVoResult = apiConnService.getByMerchantIdAndChannelId(merchantId, checkChannel);
            boolean checkPriceSwitch = false;
            if (checkChannel == OrderSourceEnum.CTRIP.longValue() && apiConnVoResult.getModel() != null
                    && apiConnVoResult.getModel().getCheckPriceSwitch() != null
                    && YesOrNoEnum.isYes(apiConnVoResult.getModel().getCheckPriceSwitch())) {
                checkPriceSwitch = true;
            }
            // 增加验价开关
            boolean result = true;
            boolean anxin = checkOrderReq.isAnxinOrder();
            boolean platformCal = checkOrderReq.isPlatformCal();

            // 一口价订单、悟空订单不验价
            if ((!checkOrderReq.isNoWorriedOrder() && !anxin && !platformCal)
                    && (OrderSourceEnum.WUKONG.getSource().intValue() != channelId.intValue() &&
                    OrderSourceEnum.DIDI.getSource().intValue() != channelId.intValue())
                    && (!checkPriceSwitch)) {
                result = iThirdOrderService.validOrderPriceHasConsistency(actualChannelId, merchantId, checkOrderReq);
            }
            if (result || thirdInsurance) {
                String license = iThirdOrderService.checkOrderStock(actualChannelId, merchantId, checkOrderReq, VehicleBusyEnum.ORDER.getValue());
                if (StringUtils.isNotEmpty(license)) {
                    Span.current().setStatus(StatusCode.OK);
                    log.info("验证下单V2成功, channel={}, merchantId={}, vehicleModelId={}", channelId, merchantId, checkOrderReq.getVehicleModelId());
                    return SaasResponse.builder().build().success(license);
                } else {
                    Span.current().setStatus(StatusCode.OK);
                    log.info("验证下单V2无库存, channel={}, merchantId={}, vehicleModelId={}", channelId, merchantId, checkOrderReq.getVehicleModelId());
                    return SaasResponse.builder().build().failed("2002", "校验订单库存失败");
                }
            } else {
                log.info("验证下单V2金额校验失败, channel={}, merchantId={}, vehicleModelId={}", channelId, merchantId, checkOrderReq.getVehicleModelId());
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().failed("2001", "校验订单金额失败");
            }

        } catch (BizException e) {
            log.error("验证下订单V2, 业务异常, channel={}, merchantId={}, vehicleModelId={}, 异常code={}, checkOrderReq={}",
                    channelId, merchantId, checkOrderReq.getVehicleModelId(), e.getCode(), JSON.toJSONString(checkOrderReq), e);
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed(e.getCode(), e.getCode());
        } finally {
            Span.current().end();
        }
    }

    private static boolean isAnxin(ValidateCreateOrderReq checkOrderReq) {
        boolean anxin = false;
        if (checkOrderReq.getTagInfos() != null) {
            for (TagInfoDTO tagInfoDTO : checkOrderReq.getTagInfos()) {
                if (TagInfoDTO.ANXIN_ID == tagInfoDTO.getId().intValue()) {
                    anxin = true;
                    log.info("验证下订单V2, 飞猪安心租, checkOrderReq={}", JSON.toJSONString(checkOrderReq));
                }
            }
        }
        return anxin;
    }

    /**
     * 创建订单V2 多平台通用
     * 飞猪/哈啰/租租车
     *
     * @param channelId      渠道ID
     * @param merchantId     商户ID
     * @param createOrderReq 创建订单实体
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/create")
    public SaasResponse postCreateOrderV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody com.ql.rent.api.aggregate.model.request.CreateOrderReq createOrderReq) {
        Span.current().updateName("创建订单V2");
        log.info("创建订单V2, 请求参数={}", JSON.toJSONString(createOrderReq));
        // 先暂时去掉优惠券, 不然算价可能会对不上
        // createOrderReq.setActivityAndCouponList(null);
        if (CollectionUtils.isNotEmpty(createOrderReq.getActivityAndCouponList())) {
            createOrderReq.getActivityAndCouponList().forEach(item -> {
                item.setVendorDiscountAmount(item.getVendorDiscountAmount().divide(BigDecimal.valueOf(100)));
            });
        }
        try {

            iThirdOrderService.transferOrder(merchantId, channelId, createOrderReq.getOriginReq(),
                    RedisConstant.MsgTopic.ORDER_TRANSFER_CREATE);

            setChildrenSeatNum(createOrderReq);
            // 车型询价，匹配价格是否一致
            VehicleModelPriceAbbrDTO vehicleModelPriceAbbrDTO = iThirdOrderService.getVehicleModelPriceAbbr(channelId,
                    merchantId, createOrderReq);
            // 应收金额（未减去活动金额）匹配车型价格
            Integer totalAmount = vehicleModelPriceAbbrDTO.getTotalAmount();
            Integer payAmount = createOrderReq.getPayAmount();
            Integer receivableAmount = createOrderReq.getReceivableAmount();
            log.info("创建订单V2, 验价, 请求receivableAmount={}, payAmount={}, 算价totalAmount={}",
                    receivableAmount, payAmount, totalAmount);
            boolean isAnxin = createOrderReq.isAnxinOrder();
            // 悟空订单不验价
            if (channelId.intValue() != OrderSourceEnum.WUKONG.getSource().intValue() && !isAnxin
                    && (null == totalAmount || !totalAmount.equals(receivableAmount))) {

                if (channelId.intValue() == OrderSourceEnum.FEIZHU.getSource().intValue()
                        && createOrderReq.isNoWorriedOrder()) {
                    log.info("创建订单V2, 飞猪一口价订单, thirdOrderId={}, 请求receivableAmount={}, payAmount={}, 算价totalAmount={}",
                            createOrderReq.getThirdOrderId(), receivableAmount, payAmount, totalAmount);
                } else {

                    log.error("创建订单V2, 验价, 平台应收金额（未减去活动金额={}, 平台实际金额={}, 算价金额={}", receivableAmount, payAmount,
                            totalAmount);
                    Span.current().setStatus(StatusCode.ERROR);
                    return SaasResponse.builder().build()
                            .failed("2001", "校验订单价格失败, 应收金额（未减去活动金额）: " + totalAmount + ", 实际金额: " + receivableAmount);
                }
            }

            // 通过车型询价，拿到车型的服务项列表
            List<ServiceItemAmountDTO> serviceItemDTOS = null;
            if (null != vehicleModelPriceAbbrDTO.getServiceItemAmountList()
                    && vehicleModelPriceAbbrDTO.getServiceItemAmountList().size() > 0) {
                // 附加服务费用转换
                serviceItemDTOS = vehicleModelPriceAbbrDTO.getServiceItemAmountList().stream().map(item -> {
                    ServiceItemAmountDTO serviceItemAmountDTO = new ServiceItemAmountDTO();
                    serviceItemAmountDTO.setAmount(item.getAmount());
                    serviceItemAmountDTO.setId(item.getId());
                    serviceItemAmountDTO.setType(item.getType());
                    serviceItemAmountDTO.setPrice(item.getPrice());
                    serviceItemAmountDTO.setCount(item.getCount());
                    serviceItemAmountDTO.setName(item.getName());
                    serviceItemAmountDTO.setIncludeInTotalAmount(item.getIncludeInTotalAmount());
                    serviceItemAmountDTO.setQuantity(item.getQuantity());
                    serviceItemAmountDTO.setCode(item.getCode());
                    serviceItemAmountDTO.setCalPer(item.getCalPer());
                    // serviceItemAmountDTO.setRequired(item.getRequired());
                    serviceItemAmountDTO.setUnit(item.getUnit());
                    return serviceItemAmountDTO;
                }).collect(Collectors.toList());
            }

            ThirdOrderUserDTO thirdOrderUserDTO = new ThirdOrderUserDTO();
            thirdOrderUserDTO.setThirdUserId(createOrderReq.getPartnerUser().getId());
            thirdOrderUserDTO.setUserName(createOrderReq.getPartnerUser().getName());
            thirdOrderUserDTO.setMobile(createOrderReq.getPartnerUser().getMobile());
            thirdOrderUserDTO.setIdcardNo(createOrderReq.getPartnerUser().getIdNo());
            thirdOrderUserDTO.setPickUpMobile(createOrderReq.getPartnerUser().getPickUpMobile());
            thirdOrderUserDTO.setPickUpName(createOrderReq.getPartnerUser().getPickUpName());

            if (StringUtils.isNotBlank(createOrderReq.getPartnerUser().getPickUpIdcardNo())) {
                thirdOrderUserDTO.setPickUpIdType(1);
                thirdOrderUserDTO.setPickUpIdNo(createOrderReq.getPartnerUser().getPickUpIdcardNo());
            }
            if (createOrderReq.getPartnerUser().getIdType() != null
                    && createOrderReq.getPartnerUser().getIdType() == 9) {
                thirdOrderUserDTO.setOther(createOrderReq.getPartnerUser().getPickUpIdcardNo());
            }
            // Saas只支持身份证和驾驶证，忽略判断逻辑
            // if (Integer.valueOf(1).equals(createOrderReq.getPartnerUser().getIdType())) {
            // } else if
            // (Integer.valueOf(1).equals(createOrderReq.getPartnerUser().getIdType())) {
            // } else if
            // (Integer.valueOf(1).equals(createOrderReq.getPartnerUser().getIdType())) {
            // } else if
            // (Integer.valueOf(1).equals(createOrderReq.getPartnerUser().getIdType())) {
            // }
            // 取车坐标
            LongLatVo pickUpStorePoint = new LongLatVo();
            pickUpStorePoint.setLongitude(createOrderReq.getPickUpStore().getPoint().getLongitude());
            pickUpStorePoint.setLatitude(createOrderReq.getPickUpStore().getPoint().getLatitude());
            // 还车坐标
            LongLatVo dropOffStorePoint = new LongLatVo();
            dropOffStorePoint.setLongitude(createOrderReq.getDropOffStore().getPoint().getLongitude());
            dropOffStorePoint.setLatitude(createOrderReq.getDropOffStore().getPoint().getLatitude());
            ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
            thirdOrderDTO.setAnxin(isAnxin);
            thirdOrderDTO.setRemark(createOrderReq.getRemark());
            thirdOrderDTO.setMerchantId(merchantId); // 商户ID
            thirdOrderDTO.setVehicleModelId(createOrderReq.getVehicleModelId()); // 车型ID
            thirdOrderDTO.setOrderStatus(OrderStatusEnum.CONFIRMED.getStatus()); // 订单状态，初始已完成
            thirdOrderDTO.setOrderTime(System.currentTimeMillis()); // 订单时间
            thirdOrderDTO.setOrderSource(OrderSourceEnum.fromSource(channelId.byteValue()).getSource()); // 下单渠道，验证渠道是否存在
            thirdOrderDTO.setServiceItemDTOList(serviceItemDTOS); // 车型服务项
            thirdOrderDTO.setAddedServiceItemCode(createOrderReq.getAddedServices()); // 附加服务项
            thirdOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO); // 下单用户
            thirdOrderDTO.setFreeDepositWay(
                    createOrderReq.getFreeDepositWay() != null ? createOrderReq.getFreeDepositWay().toString() : null); // 信用免押类型，目前只有
                                                                                                                        // 芝麻免押
            thirdOrderDTO.setFreeDepositDegree(createOrderReq.getFreeDepositDegree()); // 免押金
                                                                                       // 0：不支持；1：免租车押；2：全免押金；3：免违章押金
            thirdOrderDTO.setSourceOrderId(createOrderReq.getThirdOrderId()); // 第三方平台订单号
            thirdOrderDTO.setPickupStoreId(createOrderReq.getPickUpStore().getStoreId()); // 取车门店ID
            thirdOrderDTO.setPickupCityCode(createOrderReq.getPickUpStore().getCityId().intValue()); // 取车门店城市ID
            thirdOrderDTO.setPickupAddrType(createOrderReq.getPickUpStore().getPickUpDropOffType().byteValue()); // 取还车类型:
                                                                                                                 // 1:门店自取
                                                                                                                 // ，2:上门取还
                                                                                                                 // 3
                                                                                                                 // :免费接送
            if (CollectionUtils.isNotEmpty(createOrderReq.getPickUpStore().getServiceCircleIds())) {
                thirdOrderDTO.setPickupCircleId(createOrderReq.getPickUpStore().getServiceCircleIds().get(0)); // 取车服务圈id
            }
            thirdOrderDTO.setPickupAddr(createOrderReq.getPickUpStore().getAddress()); // 取车门店地址
            thirdOrderDTO.setPickupDate(createOrderReq.getPickUpStore().getDatetime()); // 取车时间
            thirdOrderDTO.setPickupLongLatVo(pickUpStorePoint); // 取车坐标
            thirdOrderDTO.setReturnStoreId(createOrderReq.getDropOffStore().getStoreId()); // 还车门店ID
            thirdOrderDTO.setReturnCityCode(createOrderReq.getDropOffStore().getCityId().intValue());// 还车城市ID
            thirdOrderDTO.setReturnAddrType(createOrderReq.getDropOffStore().getPickUpDropOffType().byteValue()); // 取还车类型:
                                                                                                                  // 1:门店自取
                                                                                                                  // ，2:上门取还
                                                                                                                  // 3
                                                                                                                  // :免费接送
            if (CollectionUtils.isNotEmpty(createOrderReq.getDropOffStore().getServiceCircleIds())) {
                thirdOrderDTO.setReturnCircleId(createOrderReq.getDropOffStore().getServiceCircleIds().get(0)); // 还车服务圈id
            }
            thirdOrderDTO.setReturnAddr(createOrderReq.getDropOffStore().getAddress()); // 还车地址
            thirdOrderDTO.setReturnDate(createOrderReq.getDropOffStore().getDatetime()); // 还车时间
            thirdOrderDTO.setReturnLongLatVo(dropOffStorePoint); // 还车坐标
            thirdOrderDTO.setPayAmount(createOrderReq.getPayAmount()); // 实收金额（分）不含附加服务项价格
            thirdOrderDTO.setReceivableAmount(createOrderReq.getReceivableAmount()); // 应收金额（分）优惠前的价格 不含附加服务项价格
            thirdOrderDTO.setCardOrderExtDTO(createOrderReq.getCardOrderExt());
            thirdOrderDTO.setActivityAndCouponList(createOrderReq.getActivityAndCouponList());
            thirdOrderDTO.setVehicleModelPriceAbbrDTO(vehicleModelPriceAbbrDTO);
            thirdOrderDTO.setPayStatus(createOrderReq.getPayStatus());
            if (Objects.equals("4", thirdOrderDTO.getFreeDepositWay())) {
                thirdOrderDTO.setFreeDepositWay(CtripFreeDepositWayEnum.WEIXIN.getWay());
            }
            thirdOrderDTO.setNoWorriedOrder(createOrderReq.isNoWorriedOrder());
            try {
                Result<Long> result = iThirdOrderService.createOrder(thirdOrderDTO);
                if (result.isSuccess()) {
                    Span.current().setStatus(StatusCode.OK);
                    com.ql.rent.api.aggregate.model.response.CreateOrderResp createOrderResp = new com.ql.rent.api.aggregate.model.response.CreateOrderResp(
                            result.getModel());
                    return SaasResponse.builder().build().success(createOrderResp);
                } else {
                    Span.current().setStatus(StatusCode.ERROR);
                    return SaasResponse.builder().build().failed(result.getResultCode(), result.getMessage());
                }
            } catch (BizException e) {
                Span.current().setStatus(StatusCode.ERROR);
                Span.current().recordException(e);
                log.error("三方下单业务异常! createOrderReq={}", JSON.toJSONString(createOrderReq), e);
                return SaasResponse.builder().build().failed("2002", "校验订单库存失败");
            }
        } finally {
            Span.current().end();
        }
    }

    private void setChildrenSeatNum(ValidateCreateOrderReq createOrderReq) {
        int childrenSeatNum = 0;
        if (CollectionUtils.isNotEmpty(createOrderReq.getAddedServices())) {
            for (String code : createOrderReq.getAddedServices()) {
                if (ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode().equals(code)) {
                    childrenSeatNum = 1;
                }
            }
        }
        createOrderReq.setChildrenSeatNum(childrenSeatNum);
    }

    /**
     * 创建订单V3（悟空专用, SAAS完全不算价）
     * 悟空/滴滴
     *
     * @param channelId        渠道ID
     * @param merchantId       商户ID
     * @param createOrderV3Req 创建订单实体
     * @return
     */
    @RequestMapping(path = "/v3/order/{channelId}/{merchantId}/create")
    public SaasResponse postCreateOrderV3(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CreateOrderV3Req createOrderV3Req) {

        try {
            log.info("三方下单V3, createOrderV3Req={}", JSON.toJSONString(createOrderV3Req));
            ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
            Integer discountAmount = 0;
            if (CollectionUtils.isNotEmpty(createOrderV3Req.getActivityAndCouponList())) {
                for (com.ql.rent.api.aggregate.model.dto.ActivityAndCouponDTO activityAndCouponDTO : createOrderV3Req
                        .getActivityAndCouponList()) {

                    if (activityAndCouponDTO.getCouponAmount() != null) {
                        activityAndCouponDTO.setCouponAmount(
                                activityAndCouponDTO.getCouponAmount().divide(BigDecimal.valueOf(100)));
                    }
                    if (activityAndCouponDTO.getVendorDiscountAmount() != null) {
                        discountAmount = discountAmount + activityAndCouponDTO.getVendorDiscountAmount().intValue();
                        activityAndCouponDTO.setVendorDiscountAmount(
                                activityAndCouponDTO.getVendorDiscountAmount().divide(BigDecimal.valueOf(100)));
                    }
                }
            }
            thirdOrderDTO.setActivityAndCouponList(createOrderV3Req.getActivityAndCouponList());
            thirdOrderDTO.setServiceItemDTOList(createOrderV3Req.getServiceItemDTOList());
            thirdOrderDTO.setOrderSource(channelId.byteValue());
            thirdOrderDTO.setMerchantId(merchantId);
            thirdOrderDTO.setChannelId(channelId);
            thirdOrderDTO.setOrderTime(createOrderV3Req.getOrderTime());
            thirdOrderDTO.setSourceOrderId(createOrderV3Req.getSourceOrderId());
            thirdOrderDTO.setTagList(createOrderV3Req.getTagList());
            thirdOrderDTO.setVehicleNo(createOrderV3Req.getVehicleNo());
            thirdOrderDTO.setPickupDate(createOrderV3Req.getPickupDate());
            thirdOrderDTO.setPickupCityCode(createOrderV3Req.getPickupCityCode());
            thirdOrderDTO.setPickupStoreId(createOrderV3Req.getPickupStoreId());
            thirdOrderDTO.setPickupAddrType(createOrderV3Req.getPickupAddrType());

            LongLatVo pickupStorePoint = new LongLatVo();
            pickupStorePoint.setLongitude(createOrderV3Req.getPickupLongLatVo().getLongitude());
            pickupStorePoint.setLatitude(createOrderV3Req.getPickupLongLatVo().getLatitude());
            thirdOrderDTO.setPickupLongLatVo(pickupStorePoint);
            thirdOrderDTO.setPickupAddr(createOrderV3Req.getPickupAddr());

            thirdOrderDTO.setReturnDate(createOrderV3Req.getReturnDate());
            thirdOrderDTO.setReturnCityCode(createOrderV3Req.getReturnCityCode());
            thirdOrderDTO.setReturnStoreId(createOrderV3Req.getReturnStoreId());
            thirdOrderDTO.setReturnAddrType(createOrderV3Req.getReturnAddrType());

            LongLatVo returnStorePoint = new LongLatVo();
            returnStorePoint.setLongitude(createOrderV3Req.getReturnLongLatVo().getLongitude());
            returnStorePoint.setLatitude(createOrderV3Req.getReturnLongLatVo().getLatitude());
            thirdOrderDTO.setReturnLongLatVo(returnStorePoint);
            thirdOrderDTO.setReturnAddr(createOrderV3Req.getReturnAddr());

            thirdOrderDTO.setRemark(createOrderV3Req.getRemark());
            thirdOrderDTO.setReceivableAmount(createOrderV3Req.getReceivableAmount());
            thirdOrderDTO.setPayAmount(createOrderV3Req.getPayAmount());

            thirdOrderDTO.setFreeDepositDegree(createOrderV3Req.getFreeDepositDegree());
            thirdOrderDTO.setFreeDepositWay(createOrderV3Req.getFreeDepositWay());
            thirdOrderDTO.setOrderStatus(createOrderV3Req.getOrderStatus());
            thirdOrderDTO.setVehicleModelId(createOrderV3Req.getVehicleModelId());

            VehicleModelPriceAbbrDTO vehicleModelPriceAbbrDTO = new VehicleModelPriceAbbrDTO();
            vehicleModelPriceAbbrDTO.setDiscountAmount(discountAmount);
            thirdOrderDTO.setVehicleModelPriceAbbrDTO(vehicleModelPriceAbbrDTO);

            if (createOrderV3Req.getThirdOrderUserDTO() != null) {
                ThirdOrderUserDTO thirdOrderUserDTO = new ThirdOrderUserDTO();
                thirdOrderUserDTO.setUserName(createOrderV3Req.getThirdOrderUserDTO().getUserName());
                thirdOrderUserDTO.setMobile(createOrderV3Req.getThirdOrderUserDTO().getMobile());
                thirdOrderUserDTO.setIdcardNo(createOrderV3Req.getThirdOrderUserDTO().getIdcardNo());
                thirdOrderUserDTO.setPickUpMobile(createOrderV3Req.getThirdOrderUserDTO().getPickUpMobile());
                thirdOrderUserDTO.setPickUpName(createOrderV3Req.getThirdOrderUserDTO().getPickUpName());
                if (StringUtils.isNotBlank(createOrderV3Req.getThirdOrderUserDTO().getPickUpIdcardNo())) {
                    thirdOrderUserDTO.setPickUpIdType(1);
                    thirdOrderUserDTO.setPickUpIdNo(createOrderV3Req.getThirdOrderUserDTO().getPickUpIdcardNo());
                }
                thirdOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO);
                if (createOrderV3Req.getThirdOrderUserDTO().getIdType() != null
                        && StringUtils.isNotEmpty(createOrderV3Req.getThirdOrderUserDTO().getIdNo())) {
                    if (createOrderV3Req.getThirdOrderUserDTO().getIdType() == 1) {
                        thirdOrderUserDTO.setIdcardNo(createOrderV3Req.getThirdOrderUserDTO().getIdNo());
                    }
                    if (createOrderV3Req.getThirdOrderUserDTO().getIdType() == 2) {
                        thirdOrderUserDTO.setPassport(createOrderV3Req.getThirdOrderUserDTO().getIdNo());
                    }
                    if (createOrderV3Req.getThirdOrderUserDTO().getIdType() == 9) {
                        thirdOrderUserDTO.setOther(createOrderV3Req.getThirdOrderUserDTO().getIdNo());
                    }
                }
            }
            if (createOrderV3Req.getPickUpStore() != null
                    && CollectionUtils.isNotEmpty(createOrderV3Req.getPickUpStore().getServiceCircleIds())) {
                thirdOrderDTO.setPickupCircleId(createOrderV3Req.getPickUpStore().getServiceCircleIds().get(0));
            }
            if (createOrderV3Req.getDropOffStore() != null
                    && CollectionUtils.isNotEmpty(createOrderV3Req.getDropOffStore().getServiceCircleIds())) {
                thirdOrderDTO.setReturnCircleId(createOrderV3Req.getDropOffStore().getServiceCircleIds().get(0));
            }
            if (CollectionUtils.isNotEmpty(thirdOrderDTO.getServiceItemDTOList())) {
                for (ServiceItemAmountDTO serviceItemAmountDTO : thirdOrderDTO.getServiceItemDTOList()) {
                    if (ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode()
                            .equals(serviceItemAmountDTO.getCode())) {
                        thirdOrderDTO.setChildrenSeatNum(serviceItemAmountDTO.getQuantity().intValue());
                    }
                    if (serviceItemAmountDTO.getAmount() != null && serviceItemAmountDTO.getPrice()!= null && serviceItemAmountDTO.getPrice() > 0) {
                        serviceItemAmountDTO.setCalPer(
                                new BigDecimal(serviceItemAmountDTO.getAmount()).divide(new BigDecimal(serviceItemAmountDTO.getPrice()), 2, RoundingMode.UP).doubleValue());
                    }
                }
            }
            Result<Long> result = iThirdOrderService.createOrder(thirdOrderDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                com.ql.rent.api.aggregate.model.response.CreateOrderResp createOrderResp = new com.ql.rent.api.aggregate.model.response.CreateOrderResp(
                        result.getModel());
                return SaasResponse.builder().build().success(createOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed(result.getResultCode(), result.getMessage());
            }
        } catch (Exception e) {
            log.error("三方下单业务异常! createOrderV3Req={}", JSON.toJSONString(createOrderV3Req), e);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(e);
            return SaasResponse.builder().build().failed("2002", "校验订单库存失败");
        }
    }

    /**
     * 验证取消订单V2 多平台通用
     *
     * @param channelId           渠道ID
     * @param merchantId          商户ID
     * @param checkCancelOrderReq 验证取消订单实体
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/validateCancelOrder")
    public SaasResponse postValidateCancelOrderV2(@PathParam("channelId") Long channelId,
            @PathParam("merchantId") Long merchantId,
            @RequestBody ValidateCancelOrderReq checkCancelOrderReq) {
        // TODO 目前携程和hello都未实现取消订单的检查，需要返回取消订单所需要支付的违约金 @musi
        Span.current().updateName("验证取消订单V2");
        try {
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService
                    .validateCancelOrder(checkCancelOrderReq.getOrderId(), checkCancelOrderReq.getThirdOrderId());
            CheckCancelOrderResp checkCancelOrderResp = new CheckCancelOrderResp();
            if (result.isSuccess() && result.getModel() != null) {
                if (result.getModel().getPenaltyAmount() == 0) {
                    checkCancelOrderResp.setDeductRemark("无违约金");
                    checkCancelOrderResp.setDeductAmount(0);
                } else {
                    if (CollectionUtils.isNotEmpty(result.getModel().getCancelRuleList())) {
                        checkCancelOrderResp.setDeductRemark(String.join(";", result.getModel().getCancelRuleList()));
                    }
                    checkCancelOrderResp.setDeductAmount(result.getModel().getPenaltyAmount());
                }
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(checkCancelOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.info("验证取消订单V2异常", e);
            return SaasResponse.builder().build().failed("-1", "验证取消订单V2异常");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消订单V2 多平台通用
     *
     * @param channelId      渠道ID
     * @param merchantId     商户ID
     * @param cancelOrderReq 取消订单实体
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/cancel")
    public SaasResponse<CancelOrderResp> postCancelOrderV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CancelOrderReq cancelOrderReq) {
        Span.current().updateName("取消订单V2");
        try {
            log.info("取消订单V2, cancelOrderReq={}", JSON.toJSONString(cancelOrderReq));
            CancelOrderResp cancelOrderResp = new CancelOrderResp();
            // TODO 需要返回违约金额和违约备注，携程和hello的逻辑有所区别，携程取消订单会告诉违约金hello的不会，目前看违约金应该用不到，不过最好还是传回来
            // @musi
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService.cancelOrder(cancelOrderReq.getOrderId(),
                    cancelOrderReq.getThirdOrderId(), cancelOrderReq.getPenaltyAmount(), new CancelOrderDTO());
            if (result.isSuccess() && result.getModel() != null) {
                if (result.getModel().getPenaltyAmount() == 0) {
                    cancelOrderResp.setDeductRemark("无违约金");
                    cancelOrderResp.setDeductAmount(0);
                } else {
                    cancelOrderResp.setDeductAmount(result.getModel().getPenaltyAmount());
                    if (CollectionUtils.isNotEmpty(result.getModel().getCancelRuleList())) {
                        cancelOrderResp.setDeductRemark(String.join(";", result.getModel().getCancelRuleList()));
                    }
                }
                if (Objects.nonNull(cancelOrderReq.getCancelType())) {
                    iThirdOrderService.addCancelOrderRecord(merchantId, cancelOrderReq.getThirdUserId(),
                            cancelOrderReq.getOrderId(),
                            0, cancelOrderReq.getCancelType(), cancelOrderReq.getCancelReason());
                }
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(cancelOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed(e);
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消订单V2（含退款） 小程序用
     *
     * @param channelId      渠道ID
     * @param merchantId     商户ID
     * @param cancelOrderReq 取消订单实体
     * @return
     */
    @RequestMapping(path = "/v2/platform/order/{channelId}/{merchantId}/cancel")
    public SaasResponse<CancelOrderResp> postCancelOrderV2ForPlatform(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CancelOrderReq cancelOrderReq) {
        Span.current().updateName("取消订单V2和退款");
        try {
            log.info("取消订单V2和退款 小程序用, cancelOrderReq={}", JSON.toJSONString(cancelOrderReq));
            CancelOrderResp cancelOrderResp = new CancelOrderResp();

            CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
            cancelOrderDTO.setCancelReason(cancelOrderReq.getCancelReason());
            cancelOrderDTO.setCancelType(cancelOrderReq.getCancelType());
            // 取消订单
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService.cancelOrder(cancelOrderReq.getOrderId(),
                    cancelOrderReq.getThirdOrderId(), null, cancelOrderDTO);
            if (!result.isSuccess() || result.getModel() == null) {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
            if (result.getModel().getPenaltyAmount() == 0) {
                cancelOrderResp.setDeductRemark("无违约金");
                cancelOrderResp.setDeductAmount(0);
            } else {
                cancelOrderResp.setDeductAmount(result.getModel().getPenaltyAmount());
                if (CollectionUtils.isNotEmpty(result.getModel().getCancelRuleList())) {
                    cancelOrderResp.setDeductRemark(String.join(";", result.getModel().getCancelRuleList()));
                }
            }

            // 退款
            Result<Boolean> refundResult = iThirdOrderService.refundOrder(cancelOrderReq.getOrderId(), null,
                    Long.valueOf(cancelOrderResp.getDeductAmount()), 0L);
            // 解冻押金
            Result<Boolean> unfreezeResult = iThirdOrderService.unfreezeDeposit(cancelOrderReq.getOrderId(), 0L);

            Span.current().setStatus(StatusCode.OK);
            return SaasResponse.builder().build().success(cancelOrderResp);
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            log.info("取消订单V2和退款 小程序用, req={}, e", JSON.toJSONString(cancelOrderReq), e);
            return SaasResponse.builder().build().failed(e);
        } finally {
            Span.current().end();
        }
    }

    /**
     * 改单校验
     *
     * @param channelId
     * @param merchantId
     * @param updateOrderCheckReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/updateOrderCheck")
    public SaasResponse<List<Integer>> checkUpdateOrder(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody UpdateOrderCheckReq updateOrderCheckReq) {
        Span.current().updateName("改单校验");
        try {
            ThirdUpdateOrderCheckDTO thirdUpdateOrderCheckDTO = new ThirdUpdateOrderCheckDTO();
            BeanUtils.copyProperties(updateOrderCheckReq, thirdUpdateOrderCheckDTO);
            thirdUpdateOrderCheckDTO.setChannelId(channelId);
            thirdUpdateOrderCheckDTO.setMerchantId(merchantId);
            Result<List<Integer>> result = iThirdOrderService.checkUpdateOrder(thirdUpdateOrderCheckDTO);
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getModel())) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 改单
     *
     * @param channelId
     * @param merchantId
     * @param updateOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/updateOrder")
    public SaasResponse<CancelOrderResp> updateOrder(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody UpdateOrderReq updateOrderReq) {
        Span.current().updateName("改单");
        try {
            ThirdUpdateOrderDTO thirdUpdateOrderDTO = new ThirdUpdateOrderDTO();
            BeanUtils.copyProperties(updateOrderReq, thirdUpdateOrderDTO);
            thirdUpdateOrderDTO.setMerchantId(merchantId);
            thirdUpdateOrderDTO.setChannelId(channelId);
            ThirdOrderUserDTO thirdOrderUserDTO = new ThirdOrderUserDTO();
            thirdOrderUserDTO.setUserName(updateOrderReq.getPartnerUser().getName());
            thirdOrderUserDTO.setMobile(updateOrderReq.getPartnerUser().getMobile());
            thirdOrderUserDTO.setIdcardNo(updateOrderReq.getPartnerUser().getIdNo());
            thirdOrderUserDTO.setPickUpMobile(updateOrderReq.getPartnerUser().getPickUpMobile());
            thirdOrderUserDTO.setPickUpName(updateOrderReq.getPartnerUser().getPickUpName());
            thirdOrderUserDTO.setPickUpIdType(updateOrderReq.getPartnerUser().getPickUpIdType());
            thirdOrderUserDTO.setPickUpIdNo(updateOrderReq.getPartnerUser().getPickUpIdNo());
            thirdUpdateOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO);
            Result<Boolean> result = iThirdOrderService.updateOrder(thirdUpdateOrderDTO);
            if (result.isSuccess() && result.getModel() != null) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 校验续租订单V2
     *
     * @param channelId
     * @param merchantId
     * @param validateCreateRenewOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/validateCreateRenewOrder")
    public SaasResponse<ValidateCreateRenewOrderResp> postValidateCreateRenewOrderV2(
            @PathVariable("channelId") Long channelId, @PathVariable("merchantId") Long merchantId,
            @RequestBody ValidateCreateRenewOrderReq validateCreateRenewOrderReq) {
        Span.current().updateName("校验续租订单V2");
        try {
            log.info("校验续租订单V2, validateCreateRenewOrderReq={}", JSON.toJSONString(validateCreateRenewOrderReq));
            ValidateCreateRenewOrderResp validateCreateRenewOrderResp = new ValidateCreateRenewOrderResp();
            Long rerentTime = DateUtil
                    .getFormatDate(validateCreateRenewOrderReq.getRenewEndTime(), DateUtil.yyyyMMddHHmmss).getTime();
            Result<Boolean> result = iThirdOrderService.rerentCheck(validateCreateRenewOrderReq.getOrderId(),
                    validateCreateRenewOrderReq.getThirdOrderId(), rerentTime);
            if (result.isSuccess() && result.getModel()) {
                // 悟空不验价
                if (OrderSourceEnum.WUKONG.getSource().intValue() == channelId.intValue()) {
                    Span.current().setStatus(StatusCode.OK);
                    log.info("悟空不验价, validateCreateRenewOrderReq={}", JSON.toJSONString(validateCreateRenewOrderReq));
                    return SaasResponse.builder().build().success(null);
                }
                Result<VehicleModelPriceAbbrDTO> priceAbbrDTOResult = iThirdOrderService.rerentPriceCal(
                        validateCreateRenewOrderReq.getOrderId(), rerentTime,
                        channelId.byteValue());
                if (priceAbbrDTOResult.isSuccess() && priceAbbrDTOResult.getModel() != null) {
                    validateCreateRenewOrderResp.setRerentPickTime(priceAbbrDTOResult.getModel().getRerentPickTime());
                    // 费用总价
                    validateCreateRenewOrderResp
                            .setFeeItemTotalAmount(priceAbbrDTOResult.getModel().getTotalAmount() / 100);
                    // 取消规则
                    if (CollectionUtils.isNotEmpty(priceAbbrDTOResult.getModel().getCancelRuleList())) {
                        validateCreateRenewOrderResp
                                .setDeductRemark(String.join(";", priceAbbrDTOResult.getModel().getCancelRuleList()));
                    }
                    validateCreateRenewOrderResp.setInsuranceServicePolicyList(
                            priceAbbrDTOResult.getModel().getInsuranceServicePolicyList());
                    validateCreateRenewOrderResp.setVehicleModelPriceAbbrDTO(priceAbbrDTOResult.getModel());
                    // 费用项清单
                    if (CollectionUtils.isNotEmpty(priceAbbrDTOResult.getModel().getServiceItemAmountList())) {
                        List<FeeItemDTO> feeItems = new ArrayList<>();
                        for (ServiceItemAmountDTO serviceItemAmountDTO : priceAbbrDTOResult.getModel()
                                .getServiceItemAmountList()) {
                            FeeItemDTO feeItemDTO = new FeeItemDTO();
                            BeanUtils.copyProperties(serviceItemAmountDTO, feeItemDTO);
                            feeItems.add(feeItemDTO);
                        }
                        validateCreateRenewOrderResp.setFeeItems(feeItems);
                    }
                    Span.current().setStatus(StatusCode.OK);
                    // TODO 需要给费用项清单、费用总价、取消规则 @musi
                    log.info("校验续租订单V2, validateCreateRenewOrderReq={} , validateCreateRenewOrderResp={}",
                            JSON.toJSONString(validateCreateRenewOrderReq),
                            JSON.toJSONString(validateCreateRenewOrderResp));
                    return SaasResponse.builder().build().success(validateCreateRenewOrderResp);
                } else {
                    Span.current().setStatus(StatusCode.ERROR);
                    log.error("校验续租订单V2, errorMsg={}, validateCreateRenewOrderReq={}", priceAbbrDTOResult.getMessage(),
                            JSON.toJSONString(validateCreateRenewOrderReq));
                    return SaasResponse.builder().build().failed("-1", priceAbbrDTOResult.getMessage());
                }
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("续租车辆无库存, rerentCheckMsg={} validateCreateRenewOrderReq={}", result.getMessage(),
                        JSON.toJSONString(validateCreateRenewOrderReq));
                return SaasResponse.builder().build().failed("2002", "续租车辆无库存");
            }
        } catch (BizException e) {
            log.error("创建续租订单V2异常", e);
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("2002", "续租车辆无库存");
        } catch (Exception e) {
            log.error("创建续租订单V2异常", e);
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        } finally {
            Span.current().end();
        }
    }

    /**
     * 创建续租订单
     *
     * @param channelId
     * @param merchantId
     * @param createRenewOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/createRenewOrder")
    public SaasResponse<CreateRenewOrderResp> postCreateRenewOrderV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CreateRenewOrderReq createRenewOrderReq) {
        Span.current().updateName("创建续租订单V2");
        try {
            ThirdRerentOrderDTO thirdRerentOrderDTO = new ThirdRerentOrderDTO();
            thirdRerentOrderDTO.setOrderSource(OrderSourceEnum.fromSource(channelId.byteValue()).getSource());
            thirdRerentOrderDTO.setSourceOrderId(createRenewOrderReq.getThirdRenewOrderId());
            thirdRerentOrderDTO.setOrderId(createRenewOrderReq.getOrderId());
            thirdRerentOrderDTO.setMerchantId(merchantId);
            thirdRerentOrderDTO.setReceivableAmount(createRenewOrderReq.getTotalAmount());
            thirdRerentOrderDTO.setPayAmount(createRenewOrderReq.getTotalAmount());
            thirdRerentOrderDTO.setRerentTime(
                    DateUtil.getFormatDate(createRenewOrderReq.getRenewEndTime(), DateUtil.yyyyMMddHHmmss).getTime());
            if (OrderSourceEnum.DIDI.getSource().intValue() == channelId.intValue()) {
                if (createRenewOrderReq.getVehicleModelPriceAbbrDTO() == null) {
                    return SaasResponse.builder().build().failed("-1", "参数错误");
                }
                thirdRerentOrderDTO.setVehicleModelPriceAbbrDTO(createRenewOrderReq.getVehicleModelPriceAbbrDTO());
            }

            Result<Long> result = iThirdOrderService.createRerentOrder(thirdRerentOrderDTO);
            if (result.isSuccess()) {
                CreateRenewOrderResp createRenewOrderResp = new CreateRenewOrderResp();
                createRenewOrderResp.setRenewOrderId(result.getModel());
                createRenewOrderResp.setThirdOrderId(createRenewOrderReq.getThirdOrderId());
                createRenewOrderResp.setThirdRenewOrderId(createRenewOrderReq.getThirdRenewOrderId());
                createRenewOrderResp.setOrderId(createRenewOrderReq.getOrderId());
                createRenewOrderResp.setTotalAmount(createRenewOrderReq.getTotalAmount());
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(createRenewOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("2002", result.getMessage());
            }
        } catch (BizException e) {
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("2002", e.getMessage());
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("2002", "创建续租订单V2失败");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 创建续租订单V3
     * (三方算价)
     *
     * @param channelId
     * @param merchantId
     * @param createRenewOrderReq
     * @return
     */
    @RequestMapping(path = "/v3/order/{channelId}/{merchantId}/createRenewOrder")
    public SaasResponse<CreateRenewOrderResp> postCreateRenewOrderV3(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CreateRenewOrderReq createRenewOrderReq) {
        Span.current().updateName("创建续租订单V3");
        try {
            log.info("创建续租订单V3, channelId={}, merchantId={}, createRenewOrderReq={}", channelId, merchantId,
                    JSON.toJSONString(createRenewOrderReq));
            ThirdRerentOrderDTO thirdRerentOrderDTO = new ThirdRerentOrderDTO();
            thirdRerentOrderDTO.setOrderSource(OrderSourceEnum.fromSource(channelId.byteValue()).getSource());
            thirdRerentOrderDTO.setSourceOrderId(createRenewOrderReq.getThirdRenewOrderId());
            thirdRerentOrderDTO.setOrderId(createRenewOrderReq.getOrderId());
            thirdRerentOrderDTO.setMerchantId(merchantId);
            thirdRerentOrderDTO.setMainSourceOrderId(createRenewOrderReq.getThirdOrderId());
            thirdRerentOrderDTO.setReceivableAmount(
                    createRenewOrderReq.getTotalAmount() + createRenewOrderReq.getDiscountAmount());
            thirdRerentOrderDTO.setPayAmount(createRenewOrderReq.getTotalAmount());
            thirdRerentOrderDTO.setDiscountAmount(createRenewOrderReq.getDiscountAmount());
            thirdRerentOrderDTO.setRerentTime(
                    DateUtil.getFormatDate(createRenewOrderReq.getRenewEndTime(), DateUtil.yyyyMMddHHmmss).getTime());

            List<OrderOptionServiceDTO> list = new ArrayList<>();
            createRenewOrderReq.getServiceItemList().stream().forEach(item -> {
                OrderOptionServiceDTO dto = new OrderOptionServiceDTO();
                BeanUtils.copyProperties(item, dto);
                list.add(dto);
            });
            thirdRerentOrderDTO.setServiceItemList(list);

            Result<Long> result = iThirdOrderService.createRerentOrderV2(thirdRerentOrderDTO);
            if (result.isSuccess()) {
                CreateRenewOrderResp createRenewOrderResp = new CreateRenewOrderResp();
                createRenewOrderResp.setRenewOrderId(result.getModel());
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(createRenewOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } catch (BizException e) {
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            return SaasResponse.builder().build().failed("-1", "创建续租订单V3失败");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 校验取消租续订单
     *
     * @param channelId
     * @param merchantId
     * @param validateCancelRenewOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/validateCancelRenewOrder")
    public SaasResponse<ValidateCancelRenewOrderResp> postValidateCancelRenewOrderV2(
            @PathVariable("channelId") Long channelId, @PathVariable("merchantId") Long merchantId,
            @RequestBody ValidateCancelRenewOrderReq validateCancelRenewOrderReq) {
        Span.current().updateName("校验取消租续订单V2");
        try {
            // TODO 确认一下校验取消续租的时候支付金额是不是不需要，校验违约金需要违约金备注信息 @musi
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService.getRerentPenalty(
                    validateCancelRenewOrderReq.getOrderId(),
                    validateCancelRenewOrderReq.getRenewOrderId(), null);
            if (result.isSuccess()) {
                ValidateCancelRenewOrderResp validateCancelRenewOrderResp = new ValidateCancelRenewOrderResp();
                if (result.getModel() == null || result.getModel().getPenaltyAmount() == 0) {
                    validateCancelRenewOrderResp.setDeductRemark("无违约金");
                    validateCancelRenewOrderResp.setDeductAmount(0);
                } else {
                    if (CollectionUtils.isNotEmpty(result.getModel().getCancelRuleList())) {
                        validateCancelRenewOrderResp.setDeductRemark(
                                String.join(";", result.getModel().getCancelRuleList()));
                    }
                    if (channelId != null && (channelId.intValue() == OrderSourceEnum.DIDI.getSource().intValue()
                            || channelId.intValue() == OrderSourceEnum.WUKONG.getSource().intValue())) {
                        validateCancelRenewOrderResp.setDeductAmount(result.getModel().getPenaltyAmount());
                    } else {
                        validateCancelRenewOrderResp.setDeductAmount(result.getModel().getPenaltyAmount() / 100);
                    }
                }
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(validateCancelRenewOrderResp);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 修改订单信息
     *
     * @param channelId
     * @param merchantId
     * @param modifyOrderReq 修改订单信息请求
     * @return
     */
    @PostMapping(path = "/v1/order/{channelId}/{merchantId}/modifyOrderInfo")
    SaasResponse postModifyOrderInfo(@PathParam("channelId") Long channelId, @PathParam("merchantId") Long merchantId,
            @RequestBody ModifyOrderInfoReq modifyOrderReq) {
        Span.current().updateName("修改订单人员信息");
        try {
            OrderMemberDTO orderMemberDTO = new OrderMemberDTO();
            orderMemberDTO.setMobile(modifyOrderReq.getPartnerUser().getMobile());
            orderMemberDTO.setIdType(modifyOrderReq.getPartnerUser().getIdType());
            orderMemberDTO.setIdNo(modifyOrderReq.getPartnerUser().getIdNo());
            orderMemberDTO.setName(modifyOrderReq.getPartnerUser().getName());
            Result<Boolean> result = iThirdOrderService.updateOrderMember(Long.valueOf(modifyOrderReq.getOrderId()),
                    orderMemberDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消续租订单
     *
     * @param channelId
     * @param merchantId
     * @param cancelRenewOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/cancelRenewOrder")
    public SaasResponse<CancelRenewOrderResp> postCancelRenewOrderOrderV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CancelRenewOrderReq cancelRenewOrderReq) {
        Span.current().updateName("取消续租订单V2");
        try {
            if (OrderSourceEnum.WUKONG.getSource().equals(channelId.byteValue())) {
                Result<Boolean> result = iThirdOrderService.cancelRerentOrderV2(cancelRenewOrderReq.getThirdOrderId(),
                        channelId, cancelRenewOrderReq.getRerentReturnDate());
                if (result.isSuccess()) {
                    Span.current().setStatus(StatusCode.OK);
                    return SaasResponse.builder().build().success(null);
                } else {
                    Span.current().setStatus(StatusCode.ERROR);
                    return SaasResponse.builder().build().failed("-1", result.getMessage());
                }
            } else {
                Result<Boolean> result = iThirdOrderService.cancelRerentOrder(cancelRenewOrderReq.getOrderId(),
                        cancelRenewOrderReq.getRenewOrderId(), cancelRenewOrderReq.getPenaltyAmount(), channelId);
                if (result.isSuccess()) {
                    Result<ThirdCancelPenaltyDTO> cancelPenaltyDTOResult = iThirdOrderService.getRerentPenalty(
                            cancelRenewOrderReq.getOrderId(),
                            cancelRenewOrderReq.getRenewOrderId(), null);
                    // 添加取消续租原因记录
                    if (Objects.nonNull(cancelRenewOrderReq.getCancelType())) {
                        iThirdOrderService.addCancelOrderRecord(merchantId, cancelRenewOrderReq.getThirdUserId(),
                                cancelRenewOrderReq.getRenewOrderId(),
                                1, cancelRenewOrderReq.getCancelType(), cancelRenewOrderReq.getCancelReason());
                    }
                    CancelRenewOrderResp cancelRenewOrderResp = new CancelRenewOrderResp();

                    if (cancelPenaltyDTOResult.isSuccess() && cancelPenaltyDTOResult.getModel() != null
                            && CollectionUtils.isNotEmpty(cancelPenaltyDTOResult.getModel().getCancelRuleList())) {
                        cancelRenewOrderResp.setDeductRemark(
                                String.join(";", cancelPenaltyDTOResult.getModel().getCancelRuleList()));
                        if (cancelRenewOrderReq.getPenaltyAmount() == null) {
                            cancelRenewOrderResp.setDeductAmount(cancelPenaltyDTOResult.getModel().getPenaltyAmount());
                        } else {
                            cancelRenewOrderResp.setDeductAmount(cancelRenewOrderReq.getPenaltyAmount());
                            if (cancelRenewOrderReq.getPenaltyAmount() == 0) {
                                cancelRenewOrderResp.setDeductRemark("无违约金");
                            }
                        }
                    }
                    Span.current().setStatus(StatusCode.OK);
                    return SaasResponse.builder().build().success(cancelRenewOrderResp);
                } else {
                    Span.current().setStatus(StatusCode.ERROR);
                    return SaasResponse.builder().build().failed("-1", result.getMessage());
                }
            }

        } finally {
            Span.current().end();
        }
    }

    /**
     * 扣款通知
     *
     * @param channelId
     * @param merchantId
     * @param deductNotifyReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/deductNotify")
    public SaasResponse postDeductNotifyV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody DeductNotifyReq deductNotifyReq) {
        Span.current().updateName("扣款通知V2");
        try {
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.valueOf(deductNotifyReq.getAmount()));
            thirdFreeDepositTradeNotifyDTO.setOrderId(Long.parseLong(deductNotifyReq.getOrderNo()));
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            thirdFreeDepositTradeNotifyDTO.setTradeNo(deductNotifyReq.getBillNo());
            thirdFreeDepositTradeNotifyDTO.setThirdTradeNo(deductNotifyReq.getThirdBillNo());
            // 0:扣款,1:退款
            thirdFreeDepositTradeNotifyDTO.setTradeType(0);
            thirdFreeDepositTradeNotifyDTO.setResult(deductNotifyReq.success());
            thirdFreeDepositTradeNotifyDTO.setFailedReason(deductNotifyReq.getFailedReason());
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotify(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 退款通知
     *
     * @param channelId
     * @param merchantId
     * @param refundNotifyReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/refundNotify")
    public SaasResponse<CancelRenewOrderResp> postRefundNotifyV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody RefundNotifyReq refundNotifyReq) {
        Span.current().updateName("退款通知V2");
        try {
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.valueOf(refundNotifyReq.getAmount()));
            thirdFreeDepositTradeNotifyDTO.setOrderId(Long.parseLong(refundNotifyReq.getOrderNo()));
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            // TODO hello 这边是没有返回hello的退款流水 @musi
            thirdFreeDepositTradeNotifyDTO.setTradeNo(refundNotifyReq.getRefundTradeNo());
            // 0:扣款,1:退款
            thirdFreeDepositTradeNotifyDTO.setTradeType(1);

            if (1 == refundNotifyReq.getStatus()) {
                thirdFreeDepositTradeNotifyDTO.setResult(true);
            } else {
                thirdFreeDepositTradeNotifyDTO.setResult(false);
            }
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotify(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 退款通知（租租车专用）
     *
     * @param channelId
     * @param merchantId
     * @param refundNotifyReq
     * @return
     */
    @RequestMapping(path = "/v3/order/{channelId}/{merchantId}/refundNotify")
    public SaasResponse<CancelRenewOrderResp> postRefundNotifyV3(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody RefundNotifyReq refundNotifyReq) {
        Span.current().updateName("退款通知V3");
        try {
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.valueOf(refundNotifyReq.getAmount()));
            thirdFreeDepositTradeNotifyDTO.setOrderId(Long.parseLong(refundNotifyReq.getOrderNo()));
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            thirdFreeDepositTradeNotifyDTO.setTradeNo(refundNotifyReq.getTradeNo());
            // 0:扣款,1:退款
            thirdFreeDepositTradeNotifyDTO.setTradeType(1);
            if (1 == refundNotifyReq.getStatus()) {
                thirdFreeDepositTradeNotifyDTO.setResult(true);
            } else {
                thirdFreeDepositTradeNotifyDTO.setResult(false);
            }
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotifyV3(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 扣款通知v3（悟空使用）
     *
     * @param channelId
     * @param merchantId
     * @param deductNotifyReq
     * @return
     */
    @RequestMapping(path = "/v3/order/{channelId}/{merchantId}/deductNotify")
    public SaasResponse postDeductNotifyV3(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody DeductNotifyReq deductNotifyReq) {
        Span.current().updateName("扣款通知V3");
        try {
            log.info("postDeductNotifyV3 merchantId:{}, req:{}", merchantId, JSON.toJSONString(deductNotifyReq));
            ThirdFreeDepositTradeNotifyDTO thirdFreeDepositTradeNotifyDTO = new ThirdFreeDepositTradeNotifyDTO();
            thirdFreeDepositTradeNotifyDTO.setAmount(BigDecimal.valueOf(deductNotifyReq.getAmount()));
            thirdFreeDepositTradeNotifyDTO.setChannelId(channelId);
            thirdFreeDepositTradeNotifyDTO.setMerchantId(merchantId);
            thirdFreeDepositTradeNotifyDTO.setThirdTradeNo(deductNotifyReq.getThirdBillNo());
            thirdFreeDepositTradeNotifyDTO.setThirdOrderId(deductNotifyReq.getSourceOrderId());
            thirdFreeDepositTradeNotifyDTO.setFailedReason(deductNotifyReq.getFailedReason());
            // 0:扣款,1:退款
            thirdFreeDepositTradeNotifyDTO.setTradeType(0);
            thirdFreeDepositTradeNotifyDTO.setResult(deductNotifyReq.success());
            Result<Boolean> result = iThirdOrderService.freeDepositTradeNotifyV2(thirdFreeDepositTradeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 后补免押
     *
     * @param channelId
     * @param merchantId
     * @param addCreditSupportReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/addCreditSupport")
    public SaasResponse<CancelRenewOrderResp> postAddCreditSupportV2(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody AddCreditSupportReq addCreditSupportReq) {
        Span.current().updateName("后补免押通知");
        try {
            log.info("postAddCreditSupportV2={}", JSON.toJSONString(addCreditSupportReq));

            Integer creditSupport = addCreditSupportReq.getCreditSupport();
            if (creditSupport != 3) {
                return SaasResponse.builder().build().failed("-1", "后补免押仅支持双免");
            }

            ThirdFreezeNotifyDTO thirdFreezeNotifyDTO = new ThirdFreezeNotifyDTO();
            thirdFreezeNotifyDTO.setThirdOrderId(addCreditSupportReq.getThirdOrderId());
            if (StringUtils.isNotBlank(addCreditSupportReq.getOrderNo())) {
                thirdFreezeNotifyDTO.setOrderId(Long.valueOf(addCreditSupportReq.getOrderNo()));
            }
            if (null != addCreditSupportReq.getAmount()) {
                thirdFreezeNotifyDTO.setAmount(
                        new BigDecimal(addCreditSupportReq.getAmount()).divide(new BigDecimal(100)).doubleValue());
            } else {
                thirdFreezeNotifyDTO.setAmount(0D);
            }
            thirdFreezeNotifyDTO.setMerchantId(merchantId);
            thirdFreezeNotifyDTO.setAuthNo(addCreditSupportReq.getAuthNo());
            thirdFreezeNotifyDTO.setFreeDepositWay(addCreditSupportReq.getFreeDepositWay());
            if (thirdFreezeNotifyDTO.getFreeDepositWay() == null) {
                // 默认为芝麻免押
                thirdFreezeNotifyDTO.setFreeDepositWay(3);
            }

            Result<Boolean> result = iThirdOrderService.freezeNotify(thirdFreezeNotifyDTO);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 续租验单
     *
     * @param merchantId
     * @param reletCheckOrderReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/reletCheckOrder")
    public SaasResponse reletCheckOrder(@PathVariable("id") Long merchantId,
            @RequestBody ReletCheckOrderReq reletCheckOrderReq) {
        Span.current().updateName("续租验单");
        try {
            log.info("续租验单, reletCheckOrderReq={}", JSON.toJSONString(reletCheckOrderReq));
            Long orderId = Long.valueOf(reletCheckOrderReq.getVendorOrderCode());
            // TODO 先临时这样处理时间，后面再整理代码
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date rerentTime;
            try {
                rerentTime = sdf.parse(reletCheckOrderReq.getReturnDate());
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            Result<Boolean> result = iThirdOrderService.rerentCheck(orderId, reletCheckOrderReq.getCtripOrderCode(),
                    rerentTime.getTime());
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel()) {
                Span.current().setStatus(StatusCode.OK);
                saasResponse.success(result.getModel());
            } else if (result.isSuccess() && result.getModel() != null && !result.getModel()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().failed("2002", "续租车辆无库存");
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 续租下单
     *
     * @param merchantId
     * @param reletAddOrderReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/reletAddOrder")
    public SaasResponse<String> reletAddOrder(@PathVariable("id") Long merchantId,
            @RequestBody ReletAddOrderReq reletAddOrderReq) {
        Span.current().updateName("续租下单");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("续租下单, reletAddOrderReq={}", JSON.toJSONString(reletAddOrderReq));
            if (reletAddOrderReq.isNoWorriedOrder()) {
                if (CollectionUtils.isEmpty(reletAddOrderReq.getStandardFeeList()) ||
                        CollectionUtils.isEmpty(reletAddOrderReq.getPriceDailyList())) {
                    log.error("续租下单失败! 一口价订单, 无价格日历或标准费用code, createOrderReq={}", JSON.toJSONString(reletAddOrderReq));
                    Span.current().setStatus(StatusCode.ERROR);
                    saasResponse.failed("-1", "续租下单失败");
                    return saasResponse;
                }
                log.info("续租下单, 订单号={}, 一口价订单", reletAddOrderReq.getVendorOrderCode());
            }

            // 续租下单
            ThirdRerentOrderDTO thirdRerentOrderDTO = new ThirdRerentOrderDTO();
            thirdRerentOrderDTO.setStandardFeeList(reletAddOrderReq.getStandardFeeList());
            thirdRerentOrderDTO.setPriceDailyList(reletAddOrderReq.getPriceDailyList());
            thirdRerentOrderDTO.setNoWorriedOrder(reletAddOrderReq.isNoWorriedOrder());
            thirdRerentOrderDTO.setSourceOrderId(reletAddOrderReq.getCtripOrderCode());
            thirdRerentOrderDTO.setOrderId(Long.valueOf(reletAddOrderReq.getVendorOrderCode()));
            Byte channelId = reletAddOrderReq.getChannelId() == null ? OrderSourceEnum.CTRIP.getSource().byteValue()
                    : reletAddOrderReq.getChannelId().byteValue();
            thirdRerentOrderDTO.setOrderSource(channelId);
            thirdRerentOrderDTO.setPayAmount(reletAddOrderReq.getPayAmount().intValueExact() * 100);
            thirdRerentOrderDTO.setReceivableAmount(thirdRerentOrderDTO.getPayAmount());
            thirdRerentOrderDTO.setMerchantId(merchantId);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date rerentTime = sdf.parse(reletAddOrderReq.getReturnDate());
            thirdRerentOrderDTO.setRerentTime(rerentTime.getTime());
            // fee Code已转换，直接使用feeCode即可
            Result<Long> result = iThirdOrderService.createRerentOrder(thirdRerentOrderDTO);
            if (result.isSuccess()) {
                // 传入续租订单号
                Span.current().setStatus(StatusCode.OK);
                saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } catch (BizException e) {
            Span.current().setStatus(StatusCode.ERROR);
            saasResponse.failed("-1", e.getMessage());
            return saasResponse;
        } catch (Exception e) {
            Span.current().setStatus(StatusCode.ERROR);
            saasResponse.failed("-1", "续租下单失败");
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 获取续租违约金
     *
     * @param merchantId
     * @param getReletOrderPenaltyReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/getReletOrderPenalty")
    public SaasResponse<BigDecimal> getReletOrderPenalty(@PathVariable("id") Long merchantId,
            @RequestBody GetReletOrderPenaltyReq getReletOrderPenaltyReq) {
        Span.current().updateName("获取续租违约金");
        try {
            Long orderId = Long.valueOf(getReletOrderPenaltyReq.getVendorOrderCode());
            Long rerentOrderId = Long.valueOf(getReletOrderPenaltyReq.getReletOrderId());
            Integer payAmount = getReletOrderPenaltyReq.getPayAmount().intValue() * 100;
            Result<ThirdCancelPenaltyDTO> result = iThirdOrderService.getRerentPenalty(orderId, rerentOrderId,
                    payAmount);
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel() != null) {
                Span.current().setStatus(StatusCode.OK);
                saasResponse.success(result.getModel().getPenaltyAmount() / 100D);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("getReletOrderPenalty fail, result={}", JSON.toJSONString(result));
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消续租
     *
     * @param merchantId
     * @param reletCancelOrderReq
     * @return
     */
    @RequestMapping(path = "/v1/order/merchant/{id}/reletCancelOrder")
    public SaasResponse reletCancelOrder(@PathVariable("id") Long merchantId,
            @RequestBody ReletCancelOrderReq reletCancelOrderReq) {
        Span.current().updateName("取消续租");
        try {
            Long orderId = Long.valueOf(reletCancelOrderReq.getVendorOrderCode());
            Long rerentOrderId = Long.valueOf(reletCancelOrderReq.getReletOrderId());
            Integer penaltyAmount = reletCancelOrderReq.getPenaltyAmount().intValue() * 100;
            Long channelId = reletCancelOrderReq.getChannelId() == null ? OrderSourceEnum.CTRIP.getSource().longValue()
                    : reletCancelOrderReq.getChannelId();
            Result<Boolean> result = iThirdOrderService.cancelRerentOrder(orderId, rerentOrderId, penaltyAmount,
                    channelId);
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("reletCancelOrder fail, result={}", JSON.toJSONString(result));
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 查询订单详情
     *
     * @param channelId
     * @param merchantId
     * @param getOrderDetailReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/detail", method = RequestMethod.POST)
    SaasResponse<GetOrderDetailResponse> getOrderDetail(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetOrderDetailReq getOrderDetailReq) {
        Span.current().updateName("查询订单详情V2");
        try {
            log.info("getOrder getOrderReq={}", JSON.toJSONString(getOrderDetailReq));
            getOrderDetailReq.setMerchantId(merchantId);
            getOrderDetailReq.setChannelId(channelId);
            Result<ThirdOrderDetailDTO> result = iThirdOrderService.getOrderDetail(getOrderDetailReq);
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel() != null) {
                GetOrderDetailResponse getOrderDetailResponse = new GetOrderDetailResponse();
                BeanUtils.copyProperties(result.getModel(), getOrderDetailResponse);
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 查询订单
     *
     * @param merchantId
     * @param getOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/get", method = RequestMethod.POST)
    SaasResponse<GetOrderResp> getOrder(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetOrderReq getOrderReq) {
        Span.current().updateName("查询订单V2");
        try {
            log.info("getOrder getOrderReq={}", JSON.toJSONString(getOrderReq));
            Result<OrderInfoVo> result = iThirdOrderService.getOrderInfo(getOrderReq.getOrderId());
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel() != null) {
                GetOrderResp getOrderResp = new GetOrderResp();
                getOrderResp.setAmount(result.getModel().getPayAmount());
                getOrderResp.setOrderId(result.getModel().getId());
                getOrderResp.setOrderStatus(Integer.valueOf(result.getModel().getOrderStatus()));
                getOrderResp.setVehicleModelId(result.getModel().getVehicleModelId());
                getOrderResp.setSourceOrderId(result.getModel().getSourceOrderId());
                saasResponse.success(saasResponse);
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("getOrder fail, result={}", JSON.toJSONString(result));
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    /**
     * 查看订单详情
     */
    @RequestMapping(path = "/v2/order/orderDetail", method = RequestMethod.POST)
    SaasResponse<OrderDetailVo> getOrderDetail(@RequestBody GetOrderDetailReq getOrderDetailReq) {
        Span.current().updateName("查看订单详情 小程序");
        try {
            log.info("getOrderDetail getOrderReq={}", JSON.toJSONString(getOrderDetailReq));
            Result<OrderDetailVo> result = iThirdOrderService.orderDetail(getOrderDetailReq.getOrderId());
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel() != null) {
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } finally {
            Span.current().end();
        }
    }

    /**
     * 查询续租订单
     *
     * @param merchantId
     * @param getRenewalOrderReq
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/reletOrderGet", method = RequestMethod.POST)
    SaasResponse<GetRenewalOrderResp> getRenewalOrder(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetRenewalOrderReq getRenewalOrderReq) {
        Span.current().updateName("查询续租订单V2");
        try {
            log.info("getRenewalOrder getRenewalOrderReqt={}", JSON.toJSONString(getRenewalOrderReq));
            Result<RerentOrderVo> result = iThirdOrderService.getRerentOrder(getRenewalOrderReq.getOrderId(),
                    getRenewalOrderReq.getRenewalOrderId());
            SaasResponse saasResponse = SaasResponse.builder().build();
            if (result.isSuccess() && result.getModel() != null) {
                GetRenewalOrderResp getRenewalOrderResp = new GetRenewalOrderResp();
                getRenewalOrderResp.setAmount(result.getModel().getPayAmount());
                getRenewalOrderResp.setOrderId(result.getModel().getOrderId());
                getRenewalOrderResp.setOrderStatus(Integer.valueOf(result.getModel().getStatus()));
                getRenewalOrderResp.setVehicleModelId(result.getModel().getVehicleModelId());
                getRenewalOrderResp.setSourceOrderId(result.getModel().getSourceOrderId());
                getRenewalOrderResp.setRenewalOrderId(result.getModel().getRerentOrderId());
                getRenewalOrderResp.setSourceRenewalOrderId(result.getModel().getRerentSourceOrderId());
                saasResponse.success(saasResponse);
                Span.current().setStatus(StatusCode.OK);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                log.error("getRenewalOrder fail, result={}", JSON.toJSONString(result));
                saasResponse.failed("-1", result.getMessage());
            }
            return saasResponse;
        } finally {
            Span.current().end();
        }
    }

    @RequestMapping(path = "/v2/order/reptile/create", method = RequestMethod.POST)
    @WithSpan("同步爬虫订单")
    SaasResponse createOrderForReptile(@RequestBody ReptileOrderReq reptileOrderReq) {
        Span span = Span.current();
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            SpanEnhancer.of(span).withString("平台订单号", reptileOrderReq.getSourceOrderId());
//             log.info("创建爬虫订单, reptileOrderReq={}", JSON.toJSONString(reptileOrderReq));
            ReptileOrderDTO reptileOrderDTO = buildRetileOrderDTO(reptileOrderReq);
            Result<Long> result = iThirdOrderService.createOrderForReptile(reptileOrderDTO);

            if (result.isSuccess() && result.getModel() != null) {
                GetOrderDetailResponse getOrderDetailResponse = new GetOrderDetailResponse();
                BeanUtils.copyProperties(result.getModel(), getOrderDetailResponse);
                span.setStatus(StatusCode.OK);
                return saasResponse.success(result.getModel());
            } else {
                span.setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            log.error("创建爬虫订单异常, reptileOrderReq={}", JSON.toJSONString(reptileOrderReq), e);
            return saasResponse.failed("-1", "创建爬虫订单异常");
        } finally {
            span.end();
        }
    }

    @RequestMapping(path = "/v2/order/ctriphistory/create", method = RequestMethod.POST)
    SaasResponse createCtripHistoryOrder(@RequestBody CtripHistoryOrderDTO ctripHistoryOrderDTO) {
        Span.current().updateName("创建携程上货历史订单");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("创建携程上货历史订单, ctripHistoryOrderDTO={}", JSON.toJSONString(ctripHistoryOrderDTO));
            Result<Long> result = iThirdOrderService.createCtripHistoryOrder(ctripHistoryOrderDTO);

            if (result.isSuccess() && result.getModel() != null) {
                GetOrderDetailResponse getOrderDetailResponse = new GetOrderDetailResponse();
                BeanUtils.copyProperties(result.getModel(), getOrderDetailResponse);
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.error("创建携程上货历史订单异常", e);
            return saasResponse.failed("-1", "创建携程上货历史订单异常");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 订后加购服务项
     *
     * @param channelId
     * @param merchantId
     * @param thirdOrderId
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/{thirdOrderId}/serviceOptionGet", method = RequestMethod.POST)
    SaasResponse getOptionServiceList(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @PathVariable("thirdOrderId") String thirdOrderId) {
        Span.current().updateName("获取订单订后加购服务项");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("获取订单订后加购服务项, channelId={}, merchantId={}, thirdOrderId={}", channelId, merchantId, thirdOrderId);
            Result<List<OrderOptionServiceDTO>> result = iThirdOrderService.getOptionServiceList(channelId, merchantId,
                    thirdOrderId);
            if (result.isSuccess()) {
                List<GetOrderServiceOptionResponse> list = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(result.getModel())) {
                    result.getModel().stream().forEach(item -> {
                        GetOrderServiceOptionResponse response = new GetOrderServiceOptionResponse();
                        BeanUtils.copyProperties(item, response);
                        list.add(response);
                    });
                }
                Span.current().setStatus(StatusCode.OK);
                log.info("获取订单订后加购服务项, channelId={}, merchantId={}, thirdOrderId={}, list={}", channelId, merchantId,
                        thirdOrderId, JSON.toJSONString(list));
                return saasResponse.success(list);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.error("获取订单订后加购服务项异常", e);
            return saasResponse.failed("-1", "获取订单订后加购服务项异常");
        } finally {
            Span.current().end();
        }
    }



    /**
     * 订后加购服务项
     *
     * @param channelId
     * @param merchantId
     * @param thirdOrderId
     * @return
     */
    @RequestMapping(path = "/v3/order/{channelId}/{merchantId}/{thirdOrderId}/serviceOptionGet", method = RequestMethod.POST)
    SaasResponse getOptionServiceListV3(@PathVariable("channelId") Long channelId,
                                      @PathVariable("merchantId") Long merchantId,
                                      @PathVariable("thirdOrderId") String thirdOrderId) {
        Span.current().updateName("获取订单订后加购服务项V3");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("获取订单订后加购服务项V3, channelId={}, merchantId={}, thirdOrderId={}", channelId, merchantId, thirdOrderId);
            Result<OrderOptionServiceV2DTO> result = iThirdOrderService.getOptionServiceListV2(channelId, merchantId,
                    thirdOrderId);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                log.info("获取订单订后加购服务项V3, channelId={}, merchantId={}, thirdOrderId={}, result={}", channelId, merchantId,
                        thirdOrderId, JSON.toJSONString(result.getModel()));
                return saasResponse.success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.error("获取订单订后加购服务项V3异常", e);
            return saasResponse.failed("-1", "获取订单订后加购服务项V3异常");
        } finally {
            Span.current().end();
        }
    }




    /**
     * 订后加购保险项
     *
     * @param channelId
     * @param merchantId
     * @param thirdOrderId
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/{thirdOrderId}/insuranceGet", method = RequestMethod.POST)
    SaasResponse getInsuranceList(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @PathVariable("thirdOrderId") String thirdOrderId) {
        Span.current().updateName("获取订单订后加购保险项");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("获取订单订后加购保险项, channelId={}, merchantId={}, thirdOrderId={}", channelId, merchantId, thirdOrderId);
            Result<List<OrderInsuranceDTO>> result = iThirdOrderService.getInsuranceList(channelId, merchantId,
                    thirdOrderId);
            List<OrderInsuranceDTO> list = new ArrayList<>();
            if (result.isSuccess()) {
                list = result.getModel();
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(list);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.error("获取订单订后加购保险项", e);
            return saasResponse.failed("-1", "获取订单订后加购保险项");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 订后加购
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/serviceOptionAdd", method = RequestMethod.POST)
    SaasResponse addOptionService(@RequestBody OptionServiceAddReq req) {
        Span.current().updateName("获取订单订后加购服务项");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("订单订后加购, req={}", JSON.toJSONString(req));
            Long channelId = req.getChannelId();
            Long merchantId = req.getMerchantId();
            String thirdOrderId = req.getThirdOrderId();
            List<OrderOptionServiceDTO> list = new ArrayList<>();
            req.getOrderOptionServiceReqList().stream().forEach(item -> {
                OrderOptionServiceDTO dto = new OrderOptionServiceDTO();
                BeanUtils.copyProperties(item, dto);
                list.add(dto);
            });
            if (StringUtils.isEmpty(thirdOrderId)) {
                if (req.getOrderId() == null) {
                    return saasResponse.failed("-1", "订后加购参数错误");
                }
                Result<OrderInfoVo> orderResult = iThirdOrderService.getOrderInfo(req.getOrderId());
                if (!orderResult.isSuccess() || orderResult.getModel() == null) {
                    return saasResponse.failed("-1", "订后加购订单不存在");
                }
                thirdOrderId = orderResult.getModel().getSourceOrderId();
            }
            Result<Boolean> result = iThirdOrderService.addOptionService(channelId, merchantId, thirdOrderId,
                    req.getAddOrderId(), list);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("2002", result.getMessage());
            }
        } catch (BizException e) {
            log.error("订单订后加购异常", e);
            return saasResponse.failed("2002", "订后加购儿童座椅无库存");
        } catch (Exception e) {
            log.error("订单订后加购异常", e);
            return saasResponse.failed("-1", "订后加购异常");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 校验订后加购
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/serviceOptionCheck", method = RequestMethod.POST)
    SaasResponse checkOptionService(@RequestBody OptionServiceAddReq req) {
        Span.current().updateName("校验订后加购");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("校验订后加购, req={}", JSON.toJSONString(req));
            Long channelId = req.getChannelId();
            Long merchantId = req.getMerchantId();
            String thirdOrderId = req.getThirdOrderId();
            List<OrderOptionServiceDTO> list = new ArrayList<>();
            req.getOrderOptionServiceReqList().stream().forEach(item -> {
                OrderOptionServiceDTO dto = new OrderOptionServiceDTO();
                BeanUtils.copyProperties(item, dto);
                list.add(dto);
            });
            Result<Boolean> result = iThirdOrderService.checkOptionService(channelId, merchantId, thirdOrderId, list);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("2002", result.getMessage());
            }
        } catch (Exception e) {
            log.error("校验订后加购异常", e);
            return saasResponse.failed("2002", "订后加购儿童座椅无库存");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 取消订后加购
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/serviceOptionCancel", method = RequestMethod.POST)
    SaasResponse cancelOptionService(@RequestBody OptionServiceAddReq req) {
        Span.current().updateName("校验订后加购");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("取消订后加购, req={}", JSON.toJSONString(req));
            Long channelId = req.getChannelId();
            Long merchantId = req.getMerchantId();
            String thirdOrderId = req.getThirdOrderId();
            List<OrderOptionServiceDTO> list = new ArrayList<>();
            req.getOrderOptionServiceReqList().stream().forEach(item -> {
                OrderOptionServiceDTO dto = new OrderOptionServiceDTO();
                BeanUtils.copyProperties(item, dto);
                list.add(dto);
            });
            Result<Boolean> result = iThirdOrderService.cancelOptionService(channelId, merchantId, thirdOrderId,
                    req.getAddOrderId(), list);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("2002", result.getMessage());
            }
        } catch (Exception e) {
            log.error("取消订后加购异常", e);
            return saasResponse.failed("2002", "订后加购儿童座椅无库存");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 押金解冻
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/depositThaw", method = RequestMethod.POST)
    SaasResponse depositThaw(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody OrderDepositThawReq req) {
        Span.current().updateName("押金解冻");
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("押金解冻, req={}", JSON.toJSONString(req));
            req.setMerchantId(merchantId);
            req.setChannelId(channelId);
            Result<Boolean> result = iThirdOrderService.depositThaw(req);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return saasResponse.success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return saasResponse.failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            log.error("押金解冻", e);
            return saasResponse.failed("-1", "押金解冻");
        } finally {
            Span.current().end();
        }
    }

    /**
     * 控制车机
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/operateVehicle")
    public SaasResponse<Boolean> operateVehicle(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody CtripOperateDeviceReq request) {
        Span.current().updateName("控制车机");
        try {
            Result<Boolean> result = iThirdOrderService.operateVehicle(channelId, merchantId, request);
            if (ResultUtil.isModelNotNull(result)) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(true);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 车机状态查询接口
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/getCarEngineInfo")
    public SaasResponse<VehicleDeviceStateDTO> getCarEngineInfo(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetCarEngineRequest request) {
        Span.current().updateName("车机状态查询");
        try {
            Result<VehicleDeviceStateDTO> result = iThirdOrderService.getCarEngineInfo(channelId, merchantId, request);
            if (ResultUtil.isModelNotNull(result)) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(result.getModel());
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 自助取还车校验接口
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/checkCarPickReturn")
    public SaasResponse<CtripCheckCarPickReturnResponse> checkCarPickReturn(@PathVariable("channelId") Long channelId,
                                                                       @PathVariable("merchantId") Long merchantId,
                                                                       @RequestBody CheckCarPickReturnRequest request) {
        Span.current().updateName("自助取还车校验");
        try {
            Result<CtripCheckCarPickReturnResponse> result = thirdSelfPickReturnService.checkCarPickReturn(channelId,
                    merchantId, request);
            if (ResultUtil.isResultSuccess(result)) {
                return SaasResponse.builder().build().success(result.getModel());
            }
            return SaasResponse.builder().build().failed("-1", result.getMessage());
        } catch (BizException e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        } catch (Exception e) {
            log.info("自助取还车校验异常", e);
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 用户证件信息同步接口
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/pushUserDocument")
    public SaasResponse<Boolean> pushUserDocument(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody PushUserDocumentRequest request) {
        Span.current().updateName("用户证件信息同步");
        try {
            Result<Boolean> result = iThirdOrderService.updateOrderMemberForCtrip(channelId, merchantId, request);
            if (result.isSuccess()) {
                Span.current().setStatus(StatusCode.OK);
                return SaasResponse.builder().build().success(null);
            } else {
                Span.current().setStatus(StatusCode.ERROR);
                return SaasResponse.builder().build().failed("-1", result.getMessage());
            }
            // return SaasResponse.builder().build().success(true);
            // return SaasResponse.builder().build().failed("-1", null);
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 查询用户合同
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/getCarContract")
    public SaasResponse<GetCarContractResponse> getCarContract(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetCarContractRequest request) {
        Span.current().updateName("查询用户合同");
        try {

            return SaasResponse.builder().build().failed("-1", null);
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 取还车信息推送
     */
    @PostMapping(path = "/v1/ctrip/{channelId}/{merchantId}/pushPickReturnToVendor")
    public SaasResponse<PushPickReturnToVendorResponse> pushPickReturnToVendor(
            @PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody PushPickReturnToVendorRequest request) {
        Span.current().updateName("取还车信息推送");
        try {
            Result<PushPickReturnToVendorResponse> result = thirdSelfPickReturnService.notifyPickReturn(channelId,
                    merchantId, request);
            if (ResultUtil.isResultSuccess(result)) {
                return SaasResponse.builder().build().success(result.getModel());
            }
            return SaasResponse.builder().build().failed(result.getResultCode(), result.getMessage());
        } catch (BizException e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        } catch (Exception e) {
            log.info("取还车信息推送异常", e);
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    @GetMapping(path = "/{orderId}/getOrderBill")
    public SaasResponse<OrderDetailVo> getOrderBill(@PathVariable("orderId") Long orderId) {
        Span.current().updateName("查看订单明细");
        try {
            Result<OrderInfoVo> orderInfoVoResult = iThirdOrderService.getOrderInfo(orderId);
            if (!orderInfoVoResult.isSuccess() || orderInfoVoResult.getModel() == null) {
                return SaasResponse.builder().build().failed(orderInfoVoResult.getResultCode(),
                        orderInfoVoResult.getMessage());
            }

            Result<OrderBillDetailVo> billDetailVoResult = iThirdOrderService.getOrderBill(orderId,
                    orderInfoVoResult.getModel().getOrderSource());
            if (ResultUtil.isResultSuccess(billDetailVoResult)) {
                return SaasResponse.builder().build().success(billDetailVoResult.getModel());
            }
            return SaasResponse.builder().build().failed(billDetailVoResult.getResultCode(),
                    billDetailVoResult.getMessage());
        } catch (Exception e) {
            return SaasResponse.builder().build().failed("-1", e.getMessage());
        }
    }

    /**
     * 导入Excel订单数据
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/importExcel", method = RequestMethod.POST)
    SaasResponse importExcelOrder(@RequestBody ExcelOrderReq req) {
        SaasResponse saasResponse = SaasResponse.builder().build();
        try {
            log.info("导入Excel订单数据, req={}", JSON.toJSONString(req));
            if (req == null) {
                return saasResponse.failed("-1", "参数格式不对");
            }
            ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
            BeanUtils.copyProperties(req, thirdOrderDTO);
            ThirdOrderUserDTO thirdOrderUserDTO = new ThirdOrderUserDTO();
            thirdOrderUserDTO.setUserName(req.getUsername());
            thirdOrderUserDTO.setMobile(req.getMobile());
            thirdOrderDTO.setThirdOrderUserDTO(thirdOrderUserDTO);
            thirdOrderDTO.setIsExcel(1);
            thirdOrderDTO.setPickupAddrType((byte) (thirdOrderDTO.getPickupAddrType() + 1));
            thirdOrderDTO.setReturnAddrType((byte) (thirdOrderDTO.getReturnAddrType() + 1));
            thirdOrderDTO.setOrderTime(System.currentTimeMillis());

            // // 原始日期格式
            // SimpleDateFormat originalFormat = new SimpleDateFormat("M/d/yy HH:mm");
            // // 目标日期格式
            // SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // // 格式化日期为目标格式
            // thirdOrderDTO.setPickupDate(targetFormat.format(originalFormat.parse(thirdOrderDTO.getPickupDate())));
            // // 格式化日期为目标格式
            // thirdOrderDTO.setReturnDate(targetFormat.format(originalFormat.parse(thirdOrderDTO.getReturnDate())));

            Result<String> result = iThirdOrderService.importExcelOrder(thirdOrderDTO);
            log.info("导入Excel订单数据, thirdOrderDTO={}, result={}", JSON.toJSONString(thirdOrderDTO),
                    JSON.toJSONString(result));
            if (result.isSuccess()) {
                return saasResponse.success(result.getModel());
            } else {
                saasResponse.setData(req.getSourceOrderId());
                saasResponse.setCode("-1");
                saasResponse.setMessage(result.getMessage());
                return saasResponse;
            }
        } catch (Exception e) {
            log.error("导入Excel订单数据异常, req={}", JSON.toJSONString(req), e);
            saasResponse.setData(req.getSourceOrderId());
            saasResponse.setCode("-1");
            saasResponse.setMessage("导入Excel订单数据异常");
            return saasResponse;
        }
    }

    /**
     * 补充订单取消违约金
     *
     * @param channelId
     * @param merchantId
     * @param thirdOrderId
     * @param penaltyAmount
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/{thirdOrderId}/addPenaltyAmount/{penaltyAmount}", method = RequestMethod.POST)
    SaasResponse addPenaltyAmount(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @PathVariable("thirdOrderId") String thirdOrderId,
            @PathVariable("penaltyAmount") Integer penaltyAmount) {
        SaasResponse saasResponse = SaasResponse.builder().build();

        log.info("补充订单取消违约金, channelId={}, merchantId={}, thirdOrderId={}, penaltyAmount={}", channelId, merchantId,
                thirdOrderId, penaltyAmount);
        if (channelId == null || merchantId == null || thirdOrderId == null || penaltyAmount == null) {
            return saasResponse.failed("-1", "参数异常");
        }

        Result<Boolean> result = iThirdOrderService.addPenaltyAmount(channelId, merchantId, thirdOrderId,
                penaltyAmount);
        if (result.isSuccess()) {
            return saasResponse.success(true);
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    /**
     * 更新订单状态
     *
     * @param channelId
     * @param merchantId
     * @param thirdOrderId
     * @param status
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/{thirdOrderId}/updateStatus/{status}", method = RequestMethod.POST)
    SaasResponse updateOrderStatus(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @PathVariable("thirdOrderId") String thirdOrderId,
            @PathVariable("status") Byte status) {
        SaasResponse saasResponse = SaasResponse.builder().build();

        log.info("更新订单状态, channelId={}, merchantId={}, thirdOrderId={}, status={}", channelId, merchantId, thirdOrderId,
                status);
        if (channelId == null || merchantId == null || thirdOrderId == null || status == null) {
            return saasResponse.failed("-1", "参数异常");
        }
        if (1 > status.intValue() || 8 < status.intValue()) {
            return saasResponse.failed("-1", "参数异常");
        }

        Result<Boolean> result = iThirdOrderService.updateOrderStatus(channelId, merchantId, thirdOrderId, status);
        if (result.isSuccess()) {
            return saasResponse.success(true);
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    /**
     * 更新订单状态(仅更新订单状态，不同步库存)
     *
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/{merchantId}/onlyUpdateStatus", method = RequestMethod.POST)
    SaasResponse onlyUpdateOrderStatus(@PathVariable("merchantId") Long merchantId,
            @RequestBody UpdateOrderStatusReq req) {
        SaasResponse saasResponse = SaasResponse.builder().build();
        Long channelId = req.getChannelId();
        String thirdOrderId = req.getThirdOrderId();
        Byte status = req.getStatus();
        log.info("更新订单状态, channelId={}, merchantId={}, thirdOrderId={}, status={}", channelId, merchantId, thirdOrderId,
                status);
        if (channelId == null || merchantId == null || thirdOrderId == null || status == null) {
            return saasResponse.failed("-1", "参数异常");
        }
        if (1 > status.intValue() || 8 < status.intValue()) {
            return saasResponse.failed("-1", "参数异常");
        }

        Result<Boolean> result = iThirdOrderService.updateOnlyOrderStatus(channelId, merchantId, thirdOrderId, status);
        if (result.isSuccess()) {
            return saasResponse.success(true);
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    /**
     * 获取订单车型信息
     *
     * @param channelId
     * @param merchantId
     * @param req
     * @return
     */
    @RequestMapping(path = "/v2/order/{channelId}/{merchantId}/getOrderVehicleModel", method = RequestMethod.POST)
    SaasResponse<GetOrderVehicleModelResp> getOrderVehicleModel(@PathVariable("channelId") Long channelId,
            @PathVariable("merchantId") Long merchantId,
            @RequestBody GetOrderVehicleModelReq req) {
        SaasResponse saasResponse = SaasResponse.builder().build();
        log.info("获取订单车型信息, channelId={}, merchantId={}, req={}", channelId, merchantId, JSON.toJSONString(req));
        if (channelId == null || merchantId == null || req == null) {
            return saasResponse.failed("-1", "参数异常");
        }
        if (StringUtils.isEmpty(req.getThirdOrderId()) && req.getOrderId() == null
                && (req.getVehicleModelId() == null || req.getPickupStoreId() == null)) {
            return saasResponse.failed("-1", "参数异常");
        }
        Result<GetOrderVehicleModelResp> result = iThirdOrderService.getOrderVehicleModel(channelId, merchantId, req);
        if (result.isSuccess()) {
            return saasResponse.success(result.getModel());
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    /**
     * 更新订单支付状态
     * 目前仅小程序使用
     */
    @RequestMapping(path = "/v2/order/{orderId}/{status}/updateOrderPayStatus", method = RequestMethod.POST)
    SaasResponse updateOrderPayStatus(@PathVariable("orderId") Long orderId,
            @PathVariable("status") Integer status,
            @PathVariable("status") Integer amount) {
        SaasResponse saasResponse = SaasResponse.builder().build();
        log.info("更新订单支付状态, orderId={}, status={}, amount={}", orderId, status, amount);
        if (orderId == null || status == null || amount == null) {
            return saasResponse.failed("-1", "参数异常");
        }
        Result<Boolean> result = iThirdOrderService.updateOrderPayStatus(orderId, status, amount);
        if (result.isSuccess()) {
            return saasResponse.success(true);
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    @RequestMapping(path = "order/getOrderList", method = RequestMethod.POST)
    SaasResponse<PageListVo<OrderInfoVo>> getOrderList(@RequestBody SaasOrderQuery orderQuery) {
        SaasResponse saasResponse = SaasResponse.builder().build();

        log.info("查看订单列表, orderQuery={}", JSON.toJSONString(orderQuery));
        if (orderQuery == null) {
            return saasResponse.failed("-1", "参数异常");
        }

        Result<PageListVo<OrderInfoVo>> result = iThirdOrderService.getOrderList(orderQuery);
        if (result.isSuccess()) {
            return saasResponse.success(result.getModel());
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }

    private ReptileOrderDTO buildRetileOrderDTO(ReptileOrderReq reptileOrderReq) {
        ReptileOrderDTO reptileOrderDTO = new ReptileOrderDTO();
        BeanUtils.copyProperties(reptileOrderReq, reptileOrderDTO);

        if (reptileOrderReq.getReturnLongLatReq() != null) {
            LongLatVo longLatVo = new LongLatVo();
            BeanUtils.copyProperties(reptileOrderReq.getReturnLongLatReq(), longLatVo);
            reptileOrderDTO.setReturnLongLatVo(longLatVo);
        }
        if (reptileOrderReq.getPickupLongLatReq() != null) {
            LongLatVo longLatVo = new LongLatVo();
            BeanUtils.copyProperties(reptileOrderReq.getPickupLongLatReq(), longLatVo);
            reptileOrderDTO.setPickupLongLatVo(longLatVo);
        }
        if (CollectionUtils.isNotEmpty(reptileOrderReq.getServiceItemList())) {
            List<OrderServiceItemDTO> list = new ArrayList<>();
            for (OrderServiceItemReq orderServiceItemReq : reptileOrderReq.getServiceItemList()) {
                OrderServiceItemDTO orderServiceItemDTO = new OrderServiceItemDTO();
                BeanUtils.copyProperties(orderServiceItemReq, orderServiceItemDTO);
                list.add(orderServiceItemDTO);
            }
            reptileOrderDTO.setServiceItemList(list);
        }
        if (CollectionUtils.isNotEmpty(reptileOrderReq.getReptileRerentOrderReqList())) {
            List<ReptileRerentOrderDTO> list = new ArrayList<>();
            for (ReptileRerentOrderReq reptileRerentOrderReq : reptileOrderReq.getReptileRerentOrderReqList()) {
                ReptileRerentOrderDTO reptileRerentOrderDTO = new ReptileRerentOrderDTO();
                BeanUtils.copyProperties(reptileRerentOrderReq, reptileRerentOrderDTO);
                if (CollectionUtils.isNotEmpty(reptileRerentOrderReq.getServiceItemList())) {
                    List<OrderServiceItemDTO> rerentItemlist = new ArrayList<>();
                    for (OrderServiceItemReq orderServiceItemReq : reptileRerentOrderReq.getServiceItemList()) {
                        OrderServiceItemDTO orderServiceItemDTO = new OrderServiceItemDTO();
                        BeanUtils.copyProperties(orderServiceItemReq, orderServiceItemDTO);
                        rerentItemlist.add(orderServiceItemDTO);
                    }
                    reptileRerentOrderDTO.setServiceItemList(rerentItemlist);
                }
                list.add(reptileRerentOrderDTO);
            }
            reptileOrderDTO.setReptileRerentOrderDTOList(list);
        }
        if (reptileOrderReq.getUserInfoReq() != null) {
            OrderMemberDTO orderMemberDTO = new OrderMemberDTO();
            BeanUtils.copyProperties(reptileOrderReq.getUserInfoReq(), orderMemberDTO);
            reptileOrderDTO.setOrderMemberDTO(orderMemberDTO);
        }
        return reptileOrderDTO;
    }

    public Integer pricePrecisionAlgorithm(Double amount) {
        return Double.valueOf(amount * 100).intValue();
    }

    public Date formatDate(String fDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            return sdf.parse(fDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public String formatMinuteToMilliseconds(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            Date pickUpDate = sdf.parse(date);
            SimpleDateFormat convert = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return convert.format(pickUpDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }



    @RequestMapping(path = "/v2/order/settlement_order", method = RequestMethod.POST)
    SaasResponse settlementOrder(@RequestBody SettlementOrderRequest settlementOrderRequest) {
        SaasResponse saasResponse = SaasResponse.builder().build();

        log.info("订单结算, orderQuery={}", JSON.toJSONString(settlementOrderRequest));
        if (settlementOrderRequest == null) {
            return saasResponse.failed("-1", "参数异常");
        }

        Result<Boolean> result = iThirdOrderService.settlementOrder(settlementOrderRequest);
        if (result.isSuccess()) {
            return saasResponse.success(result.getModel());
        } else {
            return saasResponse.failed("-1", result.getMessage());
        }
    }
}
