package com.ql.rent.api.aggregate.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.ql.rent.api.aggregate.model.dto.*;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.BaseCtripResponse;
import com.ql.rent.api.aggregate.model.remote.ctrip.vo.response.DetectProductDisplayResponse;
import com.ql.rent.api.aggregate.model.request.*;
import com.ql.rent.api.aggregate.model.response.RenewalPriceDetailResp;
import com.ql.rent.api.aggregate.model.response.StoreVehicleModelPriceResp;
import com.ql.rent.api.aggregate.model.response.VehicleModelPriceDetailResp;
import com.ql.rent.api.aggregate.model.vo.store.ServicePolicyVO;
import com.ql.rent.api.aggregate.thread.ThreadPoolManager;
import com.ql.rent.bizdata.enums.PushEventEnum;
import com.ql.rent.common.ICtripService;
import com.ql.rent.common.IRedisService;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.trade.OrderSourceEnum;
import com.ql.rent.enums.trade.ServiceFeeTypeEnum;
import com.ql.rent.enums.vehicle.VehicleBusyEnum;
import com.ql.rent.param.store.StoreThirdParam;
import com.ql.rent.param.vehicle.VehicleBindQueryParam;
import com.ql.rent.service.common.IPushBizRecordService;
import com.ql.rent.service.price.AccessoryInventoryService;
import com.ql.rent.service.price.IRentMainService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.store.IThirdStoreService;
import com.ql.rent.service.trade.IThirdOrderService;
import com.ql.rent.service.vehicle.*;
import com.ql.rent.service.vehicle.IThirdVehicleService;
import com.ql.rent.service.vehicle.IVehicleBindService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.common.BizMethodQuery;
import com.ql.rent.vo.common.BizPushRecordDTO;
import com.ql.rent.vo.price.RentMainVo;
import com.ql.rent.vo.store.thirdSync.ThirdStoreInfosVo;
import com.ql.rent.vo.store.xunjia.XjBusinessTimeVO;
import com.ql.rent.vo.store.xunjia.XjStoreInfoChannelVO;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import com.ql.rent.vo.vehicle.VehicleBindVO;
import com.ql.rent.vo.vehicle.VehicleModelVO;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ql.Constant.ChannelId.FAKE_FEIZHU;
import static com.ql.rent.enums.trade.ServiceFeeTypeEnum.RENT_FEE_SERVICE;

/**
 * <AUTHOR>
 * @Date 2022/10/26 22:38
 */
@Slf4j
@Service
public class VehicleAggregateService {

    private final ObjectMapper objectMapper;
    private final IThirdOrderService orderService;

    private final IThirdStoreService storeService;

    private final IThirdVehicleService vehicleService;

    private final VehicleAggregateDomainService vehicleDomainService;

    private final IRentMainService rentMainService;

    private final IRedisService redisService;
    private final IVehicleModelService vehicleModelService;

    private final AccessoryInventoryService accessoryInventoryService;

    private final IPushBizRecordService pushBizRecordService;

    private final ICtripService ctripService;

    private final ICityCodeMappingInfoService cityCodeMappingInfoService;

    private final IThirdIdRelationService thirdIdRelationService;

    @Resource
    private IVehicleBindService vehicleBindService;

    private ThreadPoolManager threadPoolManager = ThreadPoolManager.INSTANCE;

    public VehicleAggregateService(ObjectMapper objectMapper, IThirdOrderService orderService,
                                   IThirdStoreService storeService, IThirdVehicleService vehicleService,
                                   VehicleAggregateDomainService vehicleDomainService,
                                   IRentMainService rentMainService,
                                   IRedisService redisService,
                                   IVehicleModelService vehicleModelService, AccessoryInventoryService accessoryInventoryService,
                                   IPushBizRecordService pushBizRecordService, ICtripService ctripService,
                                   ICityCodeMappingInfoService cityCodeMappingInfoService, IThirdIdRelationService thirdIdRelationService) {
        this.objectMapper = objectMapper;
        this.orderService = orderService;
        this.storeService = storeService;
        this.vehicleService = vehicleService;
        this.vehicleDomainService = vehicleDomainService;
        this.rentMainService = rentMainService;
        this.redisService = redisService;
        this.vehicleModelService = vehicleModelService;
        this.accessoryInventoryService = accessoryInventoryService;
        this.pushBizRecordService = pushBizRecordService;
        this.ctripService = ctripService;
        this.cityCodeMappingInfoService = cityCodeMappingInfoService;
        this.thirdIdRelationService = thirdIdRelationService;
    }

    public StoreVehicleModelPriceResp storeVehicleModelPriceSearch(Long merchantId, StoreVehicleModelPriceReq req) {
//        String merchantIdStr = (String) redisService.get("vehicleSearchRule"); //  ",1,2,"  or "all"  格式
//        if (StringUtils.isNotEmpty(merchantIdStr) && (merchantIdStr.equals("all") || merchantIdStr.indexOf("," + String.valueOf(merchantId) + ",") > -1)) {
//            return storeVehicleModelPriceSearchNew(merchantId, req);
//        } else {
//            return storeVehicleModelPriceSearchOld(merchantId, req);
//        }
        String xjOpenLog = (String) redisService.get("xjOpenLogRule"); //  ",1,"  or "all"  格式
        if (StringUtils.isNotEmpty(xjOpenLog) && xjOpenLog.indexOf("," + merchantId + "-close,") > -1) {
            log.info("商家{}欠费时间已超过系统最大预警时间，已被系统自动关闭；请联系商家及时续费...", merchantId);
            return new StoreVehicleModelPriceResp();
        }
        Long channelId = req.getChannelId();
        StoreVehicleModelPriceResp resp = storeVehicleModelPriceSearchOld(merchantId, req);
        if (StringUtils.isNotEmpty(xjOpenLog) && (xjOpenLog.equals("all") || xjOpenLog.indexOf("," + merchantId + "-" + channelId + ",") > -1)) {
            String title = "商家" + merchantId + "渠道" + channelId;
            if (req.getPickUpOnDoorAddr() != null && req.getPickUpOnDoorAddr().getAddress() != null) {
                title = title + "," + req.getPickUpOnDoorAddr().getAddress();
            }
            title = title + "," + req.getPickUpDate() + "-" + req.getReturnDate();
            log.info(title + ",request=" + JSON.toJSONString(req));
            log.info(title + ",response=" + JSON.toJSONString(resp));
        }
        return resp;
    }

    public StoreVehicleModelPriceResp storeVehicleModelPriceSearchOld(Long merchantId, StoreVehicleModelPriceReq req) {
        try {
            List<String> outMsgMap = new ArrayList<>();
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeVehicleModelPriceSearchOld merchantId={},req={}", merchantId, JSON.toJSONString(req));
            }
            Span.current().setAttribute("查询车型核心接口" + merchantId, 0);
            Stopwatch sw = Stopwatch.createStarted();
            Long channelId = req.getChannelId();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date pickUpTime = sdf.parse(req.getPickUpDate());
            Date returnTime = sdf.parse(req.getReturnDate());

            // 确认门店键值对id
            long fakeId = 1l;
            for (StorePairDTO storePairDTO : req.getStorePairList()) {
                if (null == storePairDTO.getId() || storePairDTO.getId().compareTo(0l) == 0) {
                    storePairDTO.setId(fakeId);
                    fakeId++;
                }
            }
            // 售卖车型
            List<Long> pickupStoreIds = req.getStorePairList().stream().map(e -> e.getPickUpStore().getId()).distinct().collect(Collectors.toList());
            List<Long> returnStoreIds = req.getStorePairList().stream().map(e -> e.getReturnStore().getId()).distinct().collect(Collectors.toList());
            List<RentMainVo> rentMainList = rentMainService.selectRentModel(pickupStoreIds);
            Span.current().setAttribute("a1查询售卖车型", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                for (Long pickupStoreId : pickupStoreIds) {
                    List<Long> vIds = rentMainList.stream().filter(e -> e.getStoreId().equals(pickupStoreId)).map(e -> e.getVehicleModelId()).distinct().collect(Collectors.toList());
                    outMsgMap.add("取车门店[" + pickupStoreId + "],售卖车型" + JSON.toJSONString(vIds));
                    if (CollectionUtils.isNotEmpty(req.getVehicleModelIds())) {
                        for (Long id : req.getVehicleModelIds()) {
                            outMsgMap.add("取车门店[" + pickupStoreId + "]车型[" + id + "],售卖状态=" + vIds.contains(id));
                        }
                    }
                }
            }
            if (rentMainList.isEmpty()) {
                return retEmptyXjResp(outMsgMap);
            }
            // 测试工具过滤车型
            if (!StringUtils.isEmpty(req.getDebugInfo()) && CollectionUtils.isNotEmpty(req.getVehicleModelIds())) {
                rentMainList = rentMainList.stream().filter(e -> req.getVehicleModelIds().contains(e.getVehicleModelId())).collect(Collectors.toList());
            }
            sw.reset().start();

            Long tmpChannelId = storeService.getParentChannelId(channelId);
            // 查询门店信息
            List<Long> allStoreIds = new ArrayList<>();
            allStoreIds.addAll(pickupStoreIds);
            allStoreIds.addAll(returnStoreIds);
            allStoreIds = allStoreIds.stream().distinct().collect(Collectors.toList());
            List<XjStoreInfoChannelVO> storeList = storeService.filterStoreChannelByChannelV2(allStoreIds, tmpChannelId);
            if (storeList.isEmpty()) {
                outMsgMap.add("门店已下线或不存在" + JSON.toJSONString(allStoreIds));
                return retEmptyXjResp(outMsgMap);
            }
            // 已开通的渠道门店
            List<Long> openStoreChannelIds = storeService.getOpenChannelStoreIds(tmpChannelId, storeList);
            // 查询门店营业时间
            List<XjBusinessTimeVO> businessTimeList = storeService.filterBusinessTimeByChannelV2(allStoreIds, openStoreChannelIds, tmpChannelId);

            // 查询是增加删选漏斗，门店 -》圈 -》 车型
            // 寻找所有可用车辆 并且 门店最大最小租期及提前预定最大最小check
            List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> storePairUniVehicleModelsPre = storeService
                    .selectOptionalVehicleModelsV2(merchantId, channelId, req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime, rentMainList, storeList, businessTimeList, req.getDebugInfo(), outMsgMap);
            List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> storePairUniVehicleModels = storePairUniVehicleModelsPre.stream()
                    .filter(o -> o.getData() != null && !o.getData().isEmpty())
                    .collect(Collectors.toList());
            Span.current().setAttribute("aa查询可用车辆", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (storePairUniVehicleModels.isEmpty()) {
                return retEmptyXjResp(outMsgMap);
            }
            sw.reset().start();

            // 查询服务圈信息 并且 圈的最小提前时间check & 圈的休息时间check
            List<StorePairServiceCircleDTO> storePairUniServiceCircles = storeService.selectServiceCirclePriceV2(channelId, req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime, req.getDebugInfo(), outMsgMap);
            Span.current().setAttribute("ab查询服务圈信息", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (null == storePairUniServiceCircles || storePairUniServiceCircles.isEmpty()) {
                // 如果为空的情况没有任何符合的内容
                return retEmptyXjResp(outMsgMap);
            }

            sw.reset().start();
            // 查询门店服务项内容 并且 营业时间check，如 提车时间是否在营业时间
            List<StorePairUniDTO<List<ServiceItemDTO>>> storePairUniServiceItems = storeService.selectStoreServiceV2(merchantId, channelId, req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime, storeList, businessTimeList);
            Span.current().setAttribute("ac查询门店服务项", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeService.selectStoreService;result={}", JSON.toJSONString(storePairUniServiceItems));
            }
            if (null == storePairUniServiceItems || storePairUniServiceItems.isEmpty()) {
                // 如果为空的情况没有任何符合的内容
                return retEmptyXjResp(outMsgMap);
            }

            sw.reset().start();
            // 这里增加门店二次过滤
            // 特殊增加门店是否24小时营业字段
            Map<Long, StorePairUniDTO<List<ServiceItemDTO>>> storeServiceItemMap = storePairUniServiceItems.stream()
                    .collect(Collectors.toMap(StorePairUniDTO::getId, o -> o));
            Map<Long, StorePairServiceCircleDTO> storeServiceCircleMap = storePairUniServiceCircles.stream()
                    // 过滤无效门店
                    .filter(o -> storeServiceItemMap.containsKey(o.getId()))
                    .collect(Collectors.toMap(StorePairServiceCircleDTO::getId, o -> o));
            // map 对象转换 (门店类最终过滤是用)
            Map<Long, List<VehicleModelAbbrDTO>> storeVehicleModelMap = storePairUniVehicleModels.stream()
                    // 过滤无效门店
                    .filter(o -> storeServiceCircleMap.containsKey(o.getId()))
                    .collect(Collectors.toMap(StorePairUniDTO::getId, StorePairUniDTO::getData));

            // 车型id
            Set<String> uniDistinctSet = new HashSet<>();
            List<StorePairAndVehicleModelDTO> storePairAndVehicleModelDTOS = new ArrayList<>();
            List<VehicleModelUniDTO> vehicleModelIds = storePairUniVehicleModels.stream()
                    // 过滤无效门店
                    .filter(o -> storeVehicleModelMap.containsKey(o.getId()))
                    .flatMap(storePairUniDTO -> {
                        final long storeId = storePairUniDTO.getPickUpStore().getId();
                        storePairAndVehicleModelDTOS.add(new StorePairAndVehicleModelDTO(storePairUniDTO));
                        return storePairUniDTO.getData().stream()
                                .map(vehicleModelAbbrDTO -> new VehicleModelUniDTO<>(storeId, vehicleModelAbbrDTO.getVehicleModelId(), null, null));
                    })
                    .filter(uni -> uniDistinctSet.add(uni.getStoreId() + "_" + uni.getVehicleModelId()))
                    .collect(Collectors.toList());
            Span.current().setAttribute("ad其他处理1", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            sw.reset().start();
            if (null == vehicleModelIds || vehicleModelIds.isEmpty()) {
                // 如果为空的情况没有任何符合的内容
                return retEmptyXjResp(outMsgMap);
            }

            // 门店车型关联服务项取得
            List<StorePairVehicleModelUniDTO> storePairVehicleModelUnis = storeService.selectVehicleModelUni(merchantId, channelId, storePairAndVehicleModelDTOS, pickUpTime, returnTime);
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeService.selectVehicleModelUni;result={}", JSON.toJSONString(storePairVehicleModelUnis));
            }
            Span.current().setAttribute("ae门店车型关联服务项取得", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            sw.reset().start();
            // 价格日历 并且 价格上的最短租期及最小提前时间check
            // 是否检查最小提前预定时间
            boolean checkMinBook = true;
            List<VehicleModelUniDTO<List<DailyPriceDTO>>> vehicleModelUniDailyPrices = vehicleService.selectModelCalendarPrice(merchantId, channelId, vehicleModelIds, pickUpTime, returnTime, checkMinBook, null, req.getDebugInfo(), outMsgMap, false);
            Span.current().setAttribute("ae查询价格日历", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeService.selectModelCalendarPrice;result={}", JSON.toJSONString(vehicleModelUniDailyPrices));
            }
            sw.reset().start();
            if (null == vehicleModelUniDailyPrices || vehicleModelUniDailyPrices.isEmpty()) {
                // 如果为空的情况没有任何符合的内容
                return retEmptyXjResp(outMsgMap);
            }

            // 服务项
            List<VehicleModelUniDTO<List<ServiceItemDTO>>> vehicleModelUniServiceItems = vehicleService.selectModelService(merchantId, vehicleModelIds, channelId, pickUpTime, returnTime, false, req.getDebugInfo(), outMsgMap);
            Span.current().setAttribute("af查询车型服务项", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeService.selectModelService;result={}", JSON.toJSONString(vehicleModelUniServiceItems));
            }
            sw.reset().start();
            // 车辆缩略信息
            List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> vehicleModelUniDTOS = vehicleService.selectModelOther(vehicleModelIds, channelId, rentMainList, merchantId);
            Span.current().setAttribute("ag查询车辆其他信息", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",storeService.selectModelOther;result={}", JSON.toJSONString(vehicleModelUniDTOS));
            }
            sw.reset().start();

            // 门店对 -》 车型 -》 对象
            Map<Long, Map<Long, List<ServiceItemDTO>>> storePairVehicleModelsServiceItemMap = storePairVehicleModelUnis.stream()
                    .filter(dto -> null != dto.getId() && null != dto.getVehicleModelId() && null != dto.getVehicleModelItems())
                    .collect(Collectors.groupingBy(StorePairVehicleModelUniDTO::getId, Collectors.toMap(
                            StorePairVehicleModelUniDTO::getVehicleModelId, StorePairVehicleModelUniDTO::getVehicleModelItems
                    )));
            // 门店 -》 车型 -》 对象
            Map<Long, Map<Long, List<DailyPriceDTO>>> storeVehicleModelDailyPriceMap = vehicleModelUniDailyPrices.stream()
                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getStoreId() && null != dto.getData() && !dto.getData().isEmpty())
                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));
            Map<Long, Map<Long, List<ServiceItemDTO>>> storeVehicleModelServiceItemMap = vehicleModelUniServiceItems.stream()
                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getData() && null != dto.getStoreId())
                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));
            Map<Long, Map<Long, VehicleModelSubAbbrDTO>> storeVehicleModelUniMap = vehicleModelUniDTOS.stream()
                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getData() && null != dto.getStoreId())
                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));

            List<VehicleModelPriceCalDTO> vehicleModelPriceCals = storePairUniVehicleModels.stream()
                    // 过滤无效门店 如果这里想要优化可以让上面的门店相关检索进行倒序检索并且同时过滤无效门店
                    .filter(o -> storeVehicleModelMap.containsKey(o.getId()))
                    // 根据价格日历过滤无效车型(门店)
                    .filter(o -> storeVehicleModelDailyPriceMap.containsKey(o.getPickUpStore().getId()))
                    .flatMap(storePairUni -> {
                        final long storeId = storePairUni.getPickUpStore().getId();
                        final Map<Long, List<DailyPriceDTO>> vehicleModelDailyPriceMap = storeVehicleModelDailyPriceMap.get(storeId);
                        final Map<Long, List<ServiceItemDTO>> vehicleModelServiceItemMap = storeVehicleModelServiceItemMap.get(storeId);

                        List<ServiceItemDTO> storeServiceItems = Optional.ofNullable(storeServiceItemMap.get(storePairUni.getId()))
                                .map(StorePairUniDTO::getData).orElseGet(ArrayList::new);
                        Map<Long, List<ServiceItemDTO>> storePairModelItemMap = storePairVehicleModelsServiceItemMap
                                .get(storePairUni.getId());
                        return storePairUni.getData().stream()
                                // 根据价格日历过滤无效车型(门店)
                                .filter(o -> vehicleModelDailyPriceMap.containsKey(o.getVehicleModelId()))
                                .filter(o -> vehicleModelServiceItemMap != null && vehicleModelServiceItemMap.get(o.getVehicleModelId()) != null && vehicleModelServiceItemMap.get(o.getVehicleModelId()).size() > 0)
                                .map(vehicleModelAbbrDTO -> {
                                    VehicleModelPriceCalDTO dto = new VehicleModelPriceCalDTO();
                                    dto.setCouponCodeList(req.getCouponCodeList());
                                    dto.setId(storePairUni.getId());
                                    dto.setMerchantId(merchantId);
                                    dto.setStoreId(storeId);
                                    dto.setChannelId(channelId);
                                    dto.setVehicleModelId(vehicleModelAbbrDTO.getVehicleModelId());
                                    dto.setSelfServiceReturn(vehicleModelAbbrDTO.getSelfServiceReturn());

                                    List<ServiceItemDTO> serviceItems = Lists.newArrayList(vehicleModelServiceItemMap
                                            .getOrDefault(dto.getVehicleModelId(), new ArrayList<>()));
                                    serviceItems.addAll(storeServiceItems);
                                    if (null != storePairModelItemMap) {
                                        Optional.ofNullable(storePairModelItemMap.get(vehicleModelAbbrDTO.getVehicleModelId()))
                                                .ifPresent(items -> serviceItems.addAll(items));
                                    }
                                    dto.setServiceItemList(serviceItems);
                                    dto.setPriceDailyList(vehicleModelDailyPriceMap.get(dto.getVehicleModelId()));
                                    return dto;
                                });
                    })
                    .collect(Collectors.toList());
            Span.current().setAttribute("ah其他处理2", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (vehicleModelPriceCals.isEmpty()) {
                log.warn("不存在有效车型 models: {}, storeVehicleModelDailyPrice: {}, ",
                        objectMapper.writeValueAsString(storePairUniVehicleModels),
                        objectMapper.writeValueAsString(storeVehicleModelDailyPriceMap));
                return retEmptyXjResp(outMsgMap);
            }
            // 算价
            sw.reset().start();
            Result<List<VehicleModelPriceAbbrDTO>> vehicleModelPricesRes = orderService
                    .priceCalForThird(pickUpTime.getTime(), returnTime.getTime(), vehicleModelPriceCals, true);
            if (!vehicleModelPricesRes.isSuccess()) {
                throw new RuntimeException(vehicleModelPricesRes.getMessage());
            }
            List<VehicleModelPriceAbbrDTO> vehicleModelPrices = vehicleModelPricesRes.getModel();
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                log.info(req.getDebugInfo() + ",调用订单算价接口,start={},end={},vehicleModelPriceCals={},返回={}", pickUpTime.getTime(),
                        returnTime.getTime(), JSON.toJSONString(vehicleModelPriceCals),
                        JSON.toJSONString(vehicleModelPrices));
            }
            Span.current().setAttribute("ai算价", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            // 车型类最终过滤使用
            sw.reset().start();
            // 门店对 -》 车型 -》 data
            Map<Long, Map<Long, VehicleModelPriceAbbrDTO>> storePairVehicleModelPriceMap = vehicleModelPrices.stream()
                    .collect(Collectors.groupingBy(VehicleModelPriceAbbrDTO::getId, Collectors.toMap(VehicleModelPriceAbbrDTO::getVehicleModelId, o -> o)));

            StoreVehicleModelPriceResp resp = new StoreVehicleModelPriceResp();
            List<StorePairVehicleModelDTO> storePairVehicleModels = req.getStorePairList().stream()
                    // 过滤无车辆价格信息信息店铺
                    .filter(storePairDTO -> storePairVehicleModelPriceMap.containsKey(storePairDTO.getId()))
                    .map(storePairDTO -> {
                        StorePairVehicleModelDTO storePairVehicleModelDTO = new StorePairVehicleModelDTO();
                        if (null != storeServiceCircleMap && storeServiceCircleMap.containsKey(storePairDTO.getId())) {
                            StorePairServiceCircleDTO storePairServiceCircleDTO = storeServiceCircleMap.get(storePairDTO.getId());
                            storePairVehicleModelDTO.setPickUpStore(storePairServiceCircleDTO.getPickUpStore());
                            storePairVehicleModelDTO.setReturnStore(storePairServiceCircleDTO.getReturnStore());
                        } else {
                            storePairVehicleModelDTO.setPickUpStore(new StoreCircleDTO(storePairDTO.getPickUpStore(), null));
                            storePairVehicleModelDTO.setReturnStore(new StoreCircleDTO(storePairDTO.getReturnStore(), null));
                        }
                        // 24小时营业
                        Optional.ofNullable(storeServiceItemMap.get(storePairDTO.getId())).ifPresent(uni -> {
                            storePairDTO.getPickUpStore().setAllDay(uni.getPickUpStore().getAllDay());
                            storePairDTO.getReturnStore().setAllDay(uni.getReturnStore().getAllDay());
                        });
                        Map<Long, VehicleModelPriceAbbrDTO> vehicleModelPriceMap = storePairVehicleModelPriceMap.get(storePairDTO.getId());
                        Map<Long, VehicleModelSubAbbrDTO> vehicleModelSubAbbrMap = storeVehicleModelUniMap.get(storePairDTO.getPickUpStore().getId());
                        // 免押车型ID
                        List<Long> feeDepositVehicleModelIds = vehicleService.storeUnDeposits(channelId, storePairDTO.getPickUpStore().getId());

                        // 填充车辆信息
                        List<VehicleModelPriceDetailDTO> vehicleModelPriceDetails = storeVehicleModelMap.get(storePairDTO.getId()).stream()
                                .filter(vehicleModelAbbrDTO -> vehicleModelPriceMap.containsKey(vehicleModelAbbrDTO.getVehicleModelId()))
                                .map(vehicleModelAbbrDTO -> {
                                    VehicleModelPriceDetailDTO priceDetailDTO = new VehicleModelPriceDetailDTO();
                                    priceDetailDTO.setVehicleModelId(vehicleModelAbbrDTO.getVehicleModelId());

                                    // 是否免押
                                    if (feeDepositVehicleModelIds.contains(priceDetailDTO.getVehicleModelId())) {
                                        priceDetailDTO.setFeeDeposit(true);
                                    } else {
                                        priceDetailDTO.setFeeDeposit(false);
                                    }

                                    Optional.ofNullable(vehicleModelSubAbbrMap.get(vehicleModelAbbrDTO.getVehicleModelId()))
                                            .ifPresent(vehicleModelSubAbbrDTO -> {
                                                priceDetailDTO.setLicenseType(vehicleModelSubAbbrDTO.getLicenseType());
                                                priceDetailDTO.setFreeDepositType(vehicleModelSubAbbrDTO.getFreeDepositType());
                                                priceDetailDTO.setPreAuthIllegalDepositPrice(vehicleModelSubAbbrDTO.getPreAuthIllegalDepositPrice());
                                                priceDetailDTO.setPreAuthRentDepositPrice(vehicleModelSubAbbrDTO.getPreAuthRentDepositPrice());
                                                priceDetailDTO.setUnlimitedMileage(vehicleModelSubAbbrDTO.getUnlimitedMileage());
                                                priceDetailDTO.setOverMileagePrice(vehicleModelSubAbbrDTO.getOverMileagePrice());
                                                priceDetailDTO.setMileage(vehicleModelSubAbbrDTO.getMileage().intValue());
                                                priceDetailDTO.setVehicleTags(vehicleModelSubAbbrDTO.getVehicleTags());
                                                priceDetailDTO.setLicenseType(vehicleModelAbbrDTO.getLicenseType());
                                                priceDetailDTO.setStockNum(vehicleModelAbbrDTO.getStockNum());
                                            });
                                    Optional.ofNullable(vehicleModelPriceMap.get(vehicleModelAbbrDTO.getVehicleModelId()))
                                            .ifPresent(vehicleModelPriceAbbrDTO -> {
                                                priceDetailDTO.copyFromDTO(vehicleModelPriceAbbrDTO);
                                                // 这边追加无忧组信息
                                                if (null != vehicleModelPriceAbbrDTO.getNoWorriedPriceAbbrDTO()) {
                                                    VehicleModelPriceAbsDTO noWorriedPrice = VehicleModelPriceAbsDTO
                                                            .instanceFromDTO(vehicleModelPriceAbbrDTO.getNoWorriedPriceAbbrDTO());
                                                    priceDetailDTO.setNoWorriedPrice(noWorriedPrice);
                                                }
                                                priceDetailDTO.setMarketCost(vehicleModelPriceAbbrDTO.getMarketCost());
                                            });
                                    Optional.ofNullable(storeVehicleModelDailyPriceMap.get(storePairDTO.getPickUpStore().getId()))
                                            .ifPresent(map -> Optional.ofNullable(map.get(vehicleModelAbbrDTO.getVehicleModelId()))
                                                    .ifPresent(dailyPriceDTOS -> {
                                                        priceDetailDTO.setDailyPriceList(dailyPriceDTOS);
                                                        if (null != priceDetailDTO.getNoWorriedPrice()) {
                                                            // 这边需要做价格处理
                                                            List<DailyPriceDTO> collect = dailyPriceNoWorriedHandler(dailyPriceDTOS);
                                                            priceDetailDTO.getNoWorriedPrice().setDailyPriceList(collect);
                                                        }
                                                    }));
                                    return priceDetailDTO;
                                }).collect(Collectors.toList());
                        storePairVehicleModelDTO.setVehicleModelPriceList(vehicleModelPriceDetails);
                        return storePairVehicleModelDTO;
                    }).collect(Collectors.toList());
            resp.setStorePairVehicleModelList(storePairVehicleModels);
            Span.current().setAttribute("aj其他处理3", sw.stop().elapsed(TimeUnit.MILLISECONDS));
            if (!StringUtils.isEmpty(req.getDebugInfo())) {
                // 平台规则校验
                getPushErrorRecords(merchantId, channelId, resp, outMsgMap);
                // 调用携程排查工具
                detectProductDisplay(merchantId, req, resp, outMsgMap);
                // 结果返回
                log.info(req.getDebugInfo() + ",response result={}", JSON.toJSONString(resp));
                for (StorePairVehicleModelDTO key : resp.getStorePairVehicleModelList()) {
                    List<Long> vIds = key.getVehicleModelPriceList().stream().map(e -> e.getVehicleModelId()).collect(Collectors.toList());
                    outMsgMap.add("询价结果,门店[" + key.getPickUpStore().getId() + "->" + key.getReturnStore().getId() + "],列表页返回车型=" + JSON.toJSONString(vIds));
                }
            }
            resp.setOutMsgMap(outMsgMap);
            return resp;
        } catch (Exception ex) {
            log.error("storeVehicleModelPriceSearch exception! request={}, error={}", JSON.toJSONString(req), ex.getMessage());
            ex.printStackTrace();
            return null;
        }
    }

    /**
     * 携程排查工具调用
     * @param merchantId
     * @param req
     * @param outMsgMap
     */
    private void detectProductDisplay(Long merchantId, StoreVehicleModelPriceReq req, StoreVehicleModelPriceResp resp, List<String> outMsgMap){
        if (req.getChannelId() != OrderSourceEnum.CTRIP_RESALE.getSource().longValue() &&
            req.getChannelId() != OrderSourceEnum.CTRIP.getSource().longValue()) {
            return;
        }
        if (CollectionUtils.isEmpty(req.getStorePairList()) && req.getStorePairList().size() != 1){
            return;
        }
        // 查询门店
        Long storeId = req.getStorePairList().get(0).getPickUpStore().getId();
        StoreThirdParam param = new StoreThirdParam();
        param.setMerchantId(merchantId);
        param.setStoreId(storeId);
        param.setChannelId(storeService.getParentChannelId(req.getChannelId()));
        List<ThirdStoreInfosVo> storeInfos = storeService.selectStoreInfos(param);
        ThirdStoreInfosVo storeInfosVo = storeInfos.get(0);

        // 查询城市关系
        Result<Long> cityResult = cityCodeMappingInfoService.getCityCodeForSaasCityId(storeInfosVo.getStoreInfoVo().getCityId(), OrderSourceEnum.CTRIP.getSource().longValue());
        if (!cityResult.isSuccess()) {
            outMsgMap.add("携程资源排查工具,城市Mapping不存在saasCityId:" + storeInfosVo.getStoreInfoVo().getCityId());
        }
        // 查询门店code
        String storeCode = thirdIdRelationService.getMappingForThird(OrderSourceEnum.CTRIP.getSource().longValue(), IdRelationEnum.STORECODE.getType(), storeId, merchantId);
        storeCode = storeCode == null ? storeId.toString() : storeCode;

        // 查询vvc
        List<Long> vehicleModelIds = req.getVehicleModelIds();
        if (CollectionUtils.isEmpty(vehicleModelIds)) {
            vehicleModelIds = resp.getStorePairVehicleModelList().get(0).getVehicleModelPriceList().stream().map(e -> e.getVehicleModelId()).collect(Collectors.toList());
        }
        VehicleBindQueryParam vehicleBindQueryParam = new VehicleBindQueryParam();
        vehicleBindQueryParam.setMerchantId(merchantId);
        vehicleBindQueryParam.setVehicleModelIdList(vehicleModelIds);
        vehicleBindQueryParam.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
        Result<List<VehicleBindVO>> bindResult = vehicleBindService.listVehicleBind(vehicleBindQueryParam);
        List<String> vccList = bindResult.getModel().stream().map(VehicleBindVO::getBindChannelVehicleId).collect(Collectors.toList());

        // 调用携程资源排查工具
        DetectProductDisplayRequest validQueryRequest = new DetectProductDisplayRequest();
        validQueryRequest.setDropoffDate(req.getReturnDate() + ":00");
        validQueryRequest.setPickupDate(req.getPickUpDate() + ":00");
        validQueryRequest.setPageNo(1L);
        validQueryRequest.setPageSize(200);
        validQueryRequest.setSupplierStoreCodes(Arrays.asList(storeCode));
        validQueryRequest.setVendorVehicleCodes(vccList);
        DetectProductDisplayRequest.DropInfo dropInfo = new DetectProductDisplayRequest.DropInfo();
        dropInfo.setCid(cityResult.getModel());
        dropInfo.setLat(storeInfosVo.getStoreInfoVo().getLongLat().getLatitude().toString());
        dropInfo.setLng(storeInfosVo.getStoreInfoVo().getLongLat().getLongitude().toString());
        validQueryRequest.setDropoffInfo(dropInfo);
        validQueryRequest.setPickupInfo(dropInfo);
        try {
            Result<BaseCtripResponse> ctripResult = ctripService.toTripCommonService(merchantId, validQueryRequest, "detectProductDisplay");
            if (!ctripResult.isSuccess()) {
                outMsgMap.add("调用携程资源排查工具,异常;code:" + ctripResult.getResultCode() + ",message:" + ctripResult.getMessage());
                return;
            }
            if (!ctripResult.getModel().isSuccess()) {
                outMsgMap.add("调用携程资源排查工具,异常;code:" + ctripResult.getModel().getCode() + ",message:" + ctripResult.getModel().getMessage());
                return;
            }
            List<Long> filterModels = new ArrayList<>();
            DetectProductDisplayResponse ctripSkuPriceFilterGetResponse = JSON.parseObject(ctripResult.getModel().getBody(), DetectProductDisplayResponse.class);
            if (null == ctripSkuPriceFilterGetResponse) {
                return;
            }
            DetectProductDisplayResponse.BaseResponse baseResponse = ctripSkuPriceFilterGetResponse.getBaseResponse();
            if (baseResponse != null && !baseResponse.isSuccess(baseResponse)) {
                outMsgMap.add("调用携程资源排查工具,异常;code:" + ctripSkuPriceFilterGetResponse.getBaseResponse().getCode() + ",message:" + ctripSkuPriceFilterGetResponse.getBaseResponse().getMessage());
                return;
            }
            if (null == ctripSkuPriceFilterGetResponse.getDetectItemList()) {
                return;
            }
            for (DetectProductDisplayResponse.DetectItem item : ctripSkuPriceFilterGetResponse.getDetectItemList()) {
                // sp_000001可正常售卖
                String msg = "调用携程资源排查工具,取车门店[" + storeId + "],车型[" + item.getVendorVehicleCodes().split("_")[0] + "]" +
                        ", " + item.getDetectReason();
                if (item.getDetectReason().indexOf("sp_000001") > 0) {
                    outMsgMap.add(msg);
                } else {
                    msg = msg + ",";
                    if (item.getDetectReason().indexOf("sp_000003") > 0 ||
                            item.getDetectReason().indexOf("sp_000004") > 0 ||
                            item.getDetectReason().indexOf("sp_000005") > 0) {
                        msg = msg + "车型被过滤,";
                        filterModels.add(Long.valueOf(item.getVendorVehicleCodes().split("_")[0]));
                    }
                    msg = msg + "requestId:" + ctripSkuPriceFilterGetResponse.getRequestId();
                    outMsgMap.add(msg);
                }
            }
            filterModels = filterModels.stream().distinct().collect(Collectors.toList());
            removeResp(resp.getStorePairVehicleModelList(), filterModels);
        } catch (Exception e) {
            outMsgMap.add("调用携程资源排查工具异常; " + e.getMessage());
        }
    }

    private void removeResp(List<StorePairVehicleModelDTO> storePairVehicleModelList, List<Long> filterModels) {
        for (StorePairVehicleModelDTO key : storePairVehicleModelList) {
            try {
                key.getVehicleModelPriceList().removeIf(e -> filterModels.contains(e.getVehicleModelId()));
            } catch (Exception e) {
                log.error("removeResp error", e);
            }
        }
    }

    private void getPushErrorRecords(Long merchantId, Long channelId, StoreVehicleModelPriceResp resp, List<String> outMsgMap) {
        long parentChannelId = channelId.longValue();
        if (channelId == OrderSourceEnum.CTRIP_RESALE.getSource().longValue()) {
            parentChannelId = OrderSourceEnum.CTRIP.getSource().longValue();
        }
        // 过滤是否VVC绑定
        // 线下渠道，支付宝21，微信22，抖音23 不需要校验
        if (channelId.longValue() <= 10 && channelId.longValue() > 1) {
            List<Long> filterModelsVvc = new ArrayList<>();
            for (StorePairVehicleModelDTO key : resp.getStorePairVehicleModelList()) {
                List<Long> allModels = key.getVehicleModelPriceList().stream().map(e -> e.getVehicleModelId()).collect(Collectors.toList());
                VehicleBindQueryParam param = new VehicleBindQueryParam();
                param.setMerchantId(merchantId);
                param.setChannelId(parentChannelId);
                // 如是飞猪，使用新的VVC_CODE查询绑定关系
                if (channelId == OrderSourceEnum.FEIZHU.getSource().longValue()) {
                    param.setChannelId(FAKE_FEIZHU);
                }
                param.setVehicleModelIdList(allModels);
                Result<List<VehicleBindVO>> result = vehicleBindService.listVehicleBind(param);
                if (!result.isSuccess()) {
                    outMsgMap.add("车型管理,VVC绑定查询异常");
                    continue;
                }
                List<Long> vvcBindIds = result.getModel().stream().map(e -> e.getVehicleModelId()).collect(Collectors.toList());
                for (VehicleModelPriceDetailDTO priceDetailDTO : key.getVehicleModelPriceList()) {
                    if (!vvcBindIds.contains(priceDetailDTO.getVehicleModelId())) {
                        outMsgMap.add("车型管理,车型[" + priceDetailDTO.getVehicleModelId() + "],VVC未绑定,请到车型管理绑定网络平台车型ID");
                        filterModelsVvc.add(priceDetailDTO.getVehicleModelId());
                    }
                }
                // 车型推送失败的错误(仅携程)
                if (parentChannelId == OrderSourceEnum.CTRIP.getSource().longValue()) {
                    List<VehicleBindVO> listModels = result.getModel().stream().filter(e -> StringUtils.isNotEmpty(e.getSyncResultCode()) && !e.getSyncResultCode().equals("0")).collect(Collectors.toList());
                    for (VehicleBindVO vo : listModels) {
                        outMsgMap.add("车型管理,车型[" + vo.getVehicleModelId() + "],车型推送信息: " + vo.getSyncFailedReason());
                        //filterModelsVvc.add(vo.getVehicleModelId());
                    }
                }
            }
            filterModelsVvc = filterModelsVvc.stream().distinct().collect(Collectors.toList());
            removeResp(resp.getStorePairVehicleModelList(), filterModelsVvc);
        }

        if (CollectionUtils.isEmpty(resp.getStorePairVehicleModelList()) || channelId != OrderSourceEnum.CTRIP.getSource().longValue()) {
            return;
        }
        // outMsgMap.add("===校验携程规则开始===");
        List<BizMethodQuery > querys = new ArrayList<>();
        // 门店错误
        BizMethodQuery query = new BizMethodQuery();
        query.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
        List<Long> bizIds = resp.getStorePairVehicleModelList().stream().map(e -> e.getPickUpStore().getId()).collect(Collectors.toList());
        bizIds.addAll(resp.getStorePairVehicleModelList().stream().map(e -> e.getReturnStore().getId()).collect(Collectors.toList()));
        bizIds =  bizIds.stream().distinct().collect(Collectors.toList());
        query.setBizMethod(PushEventEnum.STORE_CIRCLE.getBizType());
        query.setBizIds(bizIds);
        query.setResult(Boolean.toString(false));
        querys.add(query);
        List<BizPushRecordDTO> errers = pushBizRecordService.getPushErrorRecords(querys);
        for (BizPushRecordDTO bizPushRecordDTO : errers) {
            outMsgMap.add("携程规则校验,门店[" + bizPushRecordDTO.getStoreId() + "],最新推送错误原因:" + bizPushRecordDTO.getMessage());
            //resp.getStorePairVehicleModelList().removeIf(e -> e.getPickUpStore().getId().equals(bizPushRecordDTO.getStoreId()) ||
            //                e.getPickUpStore().getId().equals(bizPushRecordDTO.getStoreId()));
        }

        // 价格错误
        List<Long> fiterModels = new ArrayList<>();
        List<Long> allModels = new ArrayList<>();
        for (StorePairVehicleModelDTO key : resp.getStorePairVehicleModelList()) {
            querys = new ArrayList<>();
            query = new BizMethodQuery();
            query.setChannelId(OrderSourceEnum.CTRIP.getSource().longValue());
            query.setStoreId(key.getPickUpStore().getId());
            bizIds = key.getVehicleModelPriceList().stream().map(e -> e.getVehicleModelId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bizIds)) {
                continue;
            }
            allModels.addAll(bizIds);
            query.setBizMethod(PushEventEnum.SKU_PRICE.getBizType());
            query.setBizIds(bizIds);
            query.setResult(Boolean.toString(false));
            querys.add(query);
            errers = pushBizRecordService.getPushErrorRecords(querys);
            for (BizPushRecordDTO bizPushRecordDTO : errers) {
                outMsgMap.add("携程规则校验,取车门店[" + key.getPickUpStore().getId() + "],车型[" + bizPushRecordDTO.getBizId() + "],最新推送错误原因:" + bizPushRecordDTO.getMessage());
                fiterModels.add(bizPushRecordDTO.getBizId());
            }
        }
        allModels = allModels.stream().distinct().collect(Collectors.toList());

        // 校验车型指导价,单位万
        List<BaseVehicleModelVO> modelPriceList = vehicleModelService.getModelPriceIds(allModels);
        Map<Long, BigDecimal> modelPriceMap = modelPriceList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getPrice()));
        for (StorePairVehicleModelDTO key : resp.getStorePairVehicleModelList()) {
            // 携程子车系价格小于30万 非押金双免，资源被过滤
            for (VehicleModelPriceDetailDTO priceDetailDTO : key.getVehicleModelPriceList()) {
                BigDecimal modelPrice = modelPriceMap.get(priceDetailDTO.getVehicleModelId());
                if (modelPrice != null && modelPrice.compareTo(new BigDecimal("30")) < 0 && priceDetailDTO.isFeeDeposit() == false) {
                    outMsgMap.add("携程规则校验,取车门店[" + key.getPickUpStore().getId() + "]车型[" + priceDetailDTO.getVehicleModelId() + "],指导价格小于30万必须开启双免");
                    fiterModels.add(priceDetailDTO.getVehicleModelId());
                }
                if (modelPrice != null && modelPrice.compareTo(new BigDecimal("50")) < 0) {
                    long cnt = priceDetailDTO.getServiceItemAmountList().stream().filter(e -> e.getCode().equals("02003") || e.getCode().equals("02002")).count();
                    if (cnt < 2) {
                        outMsgMap.add("携程规则校验,取车门店[" + key.getPickUpStore().getId() + "]车型[" + priceDetailDTO.getVehicleModelId() + "],指导价格小于50万必须开启优享，尊享服务");
                        fiterModels.add(priceDetailDTO.getVehicleModelId());
                    }
                }
            }
        }

        // 过滤车型返回
        for (StorePairVehicleModelDTO key : resp.getStorePairVehicleModelList()) {
            //key.getVehicleModelPriceList().removeIf(e -> fiterModels.contains(e.getVehicleModelId()));
        }

        // outMsgMap.add("===校验携程规则结束===");
    }

    private StoreVehicleModelPriceResp retEmptyXjResp(List<String> outMsgMap) {
        StoreVehicleModelPriceResp resp = new StoreVehicleModelPriceResp();
        resp.setOutMsgMap(outMsgMap);
        return resp;
    }

    //TODO 异门店相关服务项取得此方法未做修改
//    public StoreVehicleModelPriceResp storeVehicleModelPriceSearchNew(Long merchantId, StoreVehicleModelPriceReq req) {
//        try {
//            Span.current().setAttribute("查询车型核心接口" + merchantId, 0);
//            Stopwatch sw = Stopwatch.createStarted();
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
//            Date pickUpTime = sdf.parse(req.getPickUpDate());
//            Date returnTime = sdf.parse(req.getReturnDate());
//            // 确认门店键值对id
//            long fakeId = 1l;
//            for (StorePairDTO storePairDTO : req.getStorePairList()) {
//                if (null == storePairDTO.getId() || storePairDTO.getId().compareTo(0l) == 0) {
//                    storePairDTO.setId(fakeId);
//                    fakeId++;
//                }
//            }
//            // 售卖车型
//            List<Long> pickupStoreIds = req.getStorePairList().stream().map(e -> e.getPickUpStore().getId()).collect(Collectors.toList());
//            List<RentMainVo> rentMainList = rentMainService.selectRentModel(pickupStoreIds);
//            Span.current().setAttribute("a1查询售卖车型", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            if (rentMainList.isEmpty()) {
//                return new StoreVehicleModelPriceResp();
//            }
//            sw.reset().start();
//            // 查询是增加删选漏斗，门店 -》圈 -》 车型
//            // 寻找所有可用车辆 并且 门店最大最小租期及提前预定最大最小check
//            List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> storePairUniVehicleModelsPre = storeService
//                    .selectOptionalVehicleModels(merchantId, req.getChannelId(), req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime, rentMainList);
//            List<StorePairUniDTO<List<VehicleModelAbbrDTO>>> storePairUniVehicleModels = storePairUniVehicleModelsPre.stream()
//                    .filter(o -> o.getData() != null && !o.getData().isEmpty())
//                    .collect(Collectors.toList());
//            Span.current().setAttribute("aa查询可用车辆", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            if (storePairUniVehicleModels.isEmpty()) {
//                return new StoreVehicleModelPriceResp();
//            }
//            sw.reset().start();
//            ListenableFuture<ReturnEntity> serviceCirclesFuture = threadPoolManager.addCoreExecuteTask(
//                    AsynTask.newTask("Qinglu_api_get_serviceCircles").registExecute(() -> {
//                        // 查询服务圈信息 并且 圈的最小提前时间check
//                        return storeService.selectServiceCirclePrice(req.getChannelId(),
//                                req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime);
//                        //Span.current().setAttribute("ab查询服务圈信息", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//                    })
//            );
//
//            ListenableFuture<ReturnEntity> storePairUniServiceFuture = threadPoolManager.addCoreExecuteTask(
//                    AsynTask.newTask("Qinglu_api_get_storeService").registExecute(() -> {
//                        // 查询门店服务项内容 并且 营业时间check，如 提车时间是否在营业时间
//                        return storeService.selectStoreService(merchantId, req.getChannelId(),
//                                req.getStorePairList(), req.getPickUpPoint(), req.getReturnPoint(), pickUpTime, returnTime);
//                        //Span.current().setAttribute("ac查询门店服务项", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//                    })
//            );
//
//            ListenableFuture<List<ReturnEntity>> allFutures = Futures.successfulAsList(serviceCirclesFuture, storePairUniServiceFuture);
//            try {
//                FuturesUtil.handleWithAllReturn(allFutures, 1000 * 2, TimeUnit.MILLISECONDS);
//            } catch (Exception e) {
//                log.error("ab-ac查询服务圈、门店服务超时:" + e.getMessage());
//                return new StoreVehicleModelPriceResp();
//            }
//
//            List<StorePairServiceCircleDTO> storePairUniServiceCircles =
//                    (List<StorePairServiceCircleDTO>) serviceCirclesFuture.get().getResult();
//            Span.current().setAttribute("ab-ac查询服务圈、门店服务", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            sw.reset().start();
//            if (null == storePairUniServiceCircles || storePairUniServiceCircles.isEmpty()) {
//                // 如果为空的情况没有任何符合的内容
//                return new StoreVehicleModelPriceResp();
//            }
//            List<StorePairUniDTO<List<ServiceItemDTO>>> storePairUniServiceItems =
//                    (List<StorePairUniDTO<List<ServiceItemDTO>>>) storePairUniServiceFuture.get().getResult();
//            if (null == storePairUniServiceItems || storePairUniServiceItems.isEmpty()) {
//                // 如果为空的情况没有任何符合的内容
//                return new StoreVehicleModelPriceResp();
//            }
//
//            // 特殊增加门店是否24小时营业字段
//            Map<Long, StorePairUniDTO<List<ServiceItemDTO>>> storeServiceItemMap = storePairUniServiceItems.stream()
//                    .collect(Collectors.toMap(StorePairUniDTO::getId, o -> o));
//            Map<Long, StorePairServiceCircleDTO> storeServiceCircleMap = storePairUniServiceCircles.stream()
//                    // 过滤无效门店
//                    .filter(o -> storeServiceItemMap.containsKey(o.getId()))
//                    .collect(Collectors.toMap(StorePairServiceCircleDTO::getId, o -> o));
//            // map 对象转换 (门店类最终过滤是用)
//            Map<Long, List<VehicleModelAbbrDTO>> storeVehicleModelMap = storePairUniVehicleModels.stream()
//                    // 过滤无效门店
//                    .filter(o -> storeServiceCircleMap.containsKey(o.getId()))
//                    .collect(Collectors.toMap(StorePairUniDTO::getId, StorePairUniDTO::getData));
//
//            // 车型id
//            Set<String> uniDistinctSet = new HashSet<>();
//            List<VehicleModelUniDTO> vehicleModelIds = storePairUniVehicleModels.stream()
//                    // 过滤无效门店
//                    .filter(o -> storeVehicleModelMap.containsKey(o.getId()))
//                    .flatMap(storePairUniDTO -> {
//                        final long storeId = storePairUniDTO.getPickUpStore().getId();
//                        return storePairUniDTO.getData().stream()
//                                .map(vehicleModelAbbrDTO -> new VehicleModelUniDTO<>(storeId, vehicleModelAbbrDTO.getVehicleModelId(), null));
//                    })
//                    .filter(uni -> uniDistinctSet.add(uni.getStoreId() + "_" + uni.getVehicleModelId()))
//                    .collect(Collectors.toList());
//            Span.current().setAttribute("ad其他处理1", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            sw.reset().start();
//            if (null == vehicleModelIds || vehicleModelIds.isEmpty()) {
//                // 如果为空的情况没有任何符合的内容
//                return new StoreVehicleModelPriceResp();
//            }
//
//            ListenableFuture<ReturnEntity> modelPriceFuture = threadPoolManager.addCoreExecuteTask(
//                    AsynTask.newTask("Qinglu_api_get_modelPrice").registExecute(() -> {
//                        // 价格日历 并且 价格上的最短租期及最小提前时间check
//                        // 是否检查最小提前预定时间
//                        boolean checkMinBook = true;
//                        return vehicleService.selectModelCalendarPrice(merchantId, req.getChannelId(), vehicleModelIds, pickUpTime, returnTime, checkMinBook, req.getDebugInfo());
//                        //Span.current().setAttribute("ae查询价格日历", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//                    })
//            );
//
//            ListenableFuture<ReturnEntity> modelServiceFuture = threadPoolManager.addCoreExecuteTask(
//                    AsynTask.newTask("Qinglu_api_get_modelService").registExecute(() -> {
//                        // 车型服务项
//                        return vehicleService.selectModelService(merchantId, vehicleModelIds, req.getChannelId(), pickUpTime, returnTime, false, null);
//                        //Span.current().setAttribute("af查询车型服务项", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//                    })
//            );
//
//            ListenableFuture<ReturnEntity> modelOtherFuture = threadPoolManager.addCoreExecuteTask(
//                    AsynTask.newTask("Qinglu_api_get_modelOther").registExecute(() -> {
//                        // 车辆缩略信息
//                        return vehicleService.selectModelOther(vehicleModelIds, req.getChannelId(), rentMainList);
//                        //Span.current().setAttribute("ag查询车辆其他信息", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//                    })
//            );
//
//            allFutures = Futures.successfulAsList(modelPriceFuture, modelServiceFuture, modelOtherFuture);
//            try {
//                FuturesUtil.handleWithAllReturn(allFutures, 1000 * 2, TimeUnit.MILLISECONDS);
//            } catch (Exception e) {
//                log.error("ae-ag查询价格日历、车型服务、车型其他超时:" + e.getMessage());
//                return new StoreVehicleModelPriceResp();
//            }
//
//            Span.current().setAttribute("ae-ag查询价格日历、车型服务、车型其他", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            sw.reset().start();
//            List<VehicleModelUniDTO<List<DailyPriceDTO>>> vehicleModelUniDailyPrices =
//                    (List<VehicleModelUniDTO<List<DailyPriceDTO>>>) modelPriceFuture.get().getResult();
//            List<VehicleModelUniDTO<List<ServiceItemDTO>>> vehicleModelUniServiceItems =
//                    (List<VehicleModelUniDTO<List<ServiceItemDTO>>>) modelServiceFuture.get().getResult();
//            List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>> vehicleModelUniDTOS =
//                    (List<VehicleModelUniDTO<VehicleModelSubAbbrDTO>>) modelOtherFuture.get().getResult();
//            sw.reset().start();
//
//            // 门店 -》 车型 -》 对象
//            Map<Long, Map<Long, List<DailyPriceDTO>>> storeVehicleModelDailyPriceMap = vehicleModelUniDailyPrices.stream()
//                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getStoreId() && null != dto.getData() && !dto.getData().isEmpty())
//                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));
//            Map<Long, Map<Long, List<ServiceItemDTO>>> storeVehicleModelServiceItemMap = vehicleModelUniServiceItems.stream()
//                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getData() && null != dto.getStoreId())
//                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));
//            Map<Long, Map<Long, VehicleModelSubAbbrDTO>> storeVehicleModelUniMap = vehicleModelUniDTOS.stream()
//                    .filter(dto -> null != dto.getVehicleModelId() && null != dto.getData() && null != dto.getStoreId())
//                    .collect(Collectors.groupingBy(VehicleModelUniDTO::getStoreId, Collectors.toMap(VehicleModelUniDTO::getVehicleModelId, VehicleModelUniDTO::getData)));
//
//            List<VehicleModelPriceCalDTO> vehicleModelPriceCals = storePairUniVehicleModels.stream()
//                    // 过滤无效门店 如果这里想要优化可以让上面的门店相关检索进行倒序检索并且同时过滤无效门店
//                    .filter(o -> storeVehicleModelMap.containsKey(o.getId()))
//                    // 根据价格日历过滤无效车型(门店)
//                    .filter(o -> storeVehicleModelDailyPriceMap.containsKey(o.getPickUpStore().getId()))
//                    .flatMap(storePairUni -> {
//                        final long storeId = storePairUni.getPickUpStore().getId();
//                        final Map<Long, List<DailyPriceDTO>> vehicleModelDailyPriceMap = storeVehicleModelDailyPriceMap.get(storeId);
//                        final Map<Long, List<ServiceItemDTO>> vehicleModelServiceItemMap = storeVehicleModelServiceItemMap.get(storeId);
//
//                        List<ServiceItemDTO> storeServiceItems = Optional.ofNullable(storeServiceItemMap.get(storePairUni.getId()))
//                                .map(StorePairUniDTO::getData).orElseGet(ArrayList::new);
//                        return storePairUni.getData().stream()
//                                // 根据价格日历过滤无效车型(门店)
//                                .filter(o -> vehicleModelDailyPriceMap.containsKey(o.getVehicleModelId()))
//                                .map(vehicleModelAbbrDTO -> {
//                                    VehicleModelPriceCalDTO dto = new VehicleModelPriceCalDTO();
//                                    dto.setId(storePairUni.getId());
//                                    dto.setMerchantId(merchantId);
//                                    dto.setStoreId(storeId);
//                                    dto.setVehicleModelId(vehicleModelAbbrDTO.getVehicleModelId());
//                                    List<ServiceItemDTO> serviceItems = vehicleModelServiceItemMap.getOrDefault(dto.getVehicleModelId(), new ArrayList<>());
//                                    serviceItems.addAll(storeServiceItems);
//                                    dto.setServiceItemList(serviceItems);
//                                    dto.setPriceDailyList(vehicleModelDailyPriceMap.get(dto.getVehicleModelId()));
//                                    return dto;
//                                });
//                    })
//                    .collect(Collectors.toList());
//            Span.current().setAttribute("ah其他处理2", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            if (null == vehicleModelPriceCals || vehicleModelPriceCals.isEmpty()) {
//                log.error("不存在有效车型 models: {}, storeVehicleModelDailyPrice: {}, ",
//                        objectMapper.writeValueAsString(storePairUniVehicleModels),
//                        objectMapper.writeValueAsString(storeVehicleModelDailyPriceMap));
//                return new StoreVehicleModelPriceResp();
//            }
//
//            // 算价
//            sw.reset().start();
//            Result<List<VehicleModelPriceAbbrDTO>> vehicleModelPricesRes = orderService
//                    .priceCalForThird(pickUpTime.getTime(), returnTime.getTime(), vehicleModelPriceCals, true);
//            if (!vehicleModelPricesRes.isSuccess()) {
//                throw new RuntimeException(vehicleModelPricesRes.getMessage());
//            }
//            List<VehicleModelPriceAbbrDTO> vehicleModelPrices = vehicleModelPricesRes.getModel();
//            Span.current().setAttribute("ai算价", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//
//            // 车型类最终过滤使用
//            sw.reset().start();
//            Map<Long, Map<Long, VehicleModelPriceAbbrDTO>> storePairVehicleModelPriceMap = vehicleModelPrices.stream()
//                    .collect(Collectors.groupingBy(VehicleModelPriceAbbrDTO::getId, Collectors.toMap(VehicleModelPriceAbbrDTO::getVehicleModelId, o -> o)));
//
//            StoreVehicleModelPriceResp resp = new StoreVehicleModelPriceResp();
//            List<StorePairVehicleModelDTO> storePairVehicleModels = req.getStorePairList().stream()
//                    // 过滤无车辆价格信息信息店铺
//                    .filter(storePairDTO -> storePairVehicleModelPriceMap.containsKey(storePairDTO.getId()))
//                    .map(storePairDTO -> {
//                        StorePairVehicleModelDTO storePairVehicleModelDTO = new StorePairVehicleModelDTO();
//                        if (null != storeServiceCircleMap && storeServiceCircleMap.containsKey(storePairDTO.getId())) {
//                            StorePairServiceCircleDTO storePairServiceCircleDTO = storeServiceCircleMap.get(storePairDTO.getId());
//                            storePairVehicleModelDTO.setPickUpStore(storePairServiceCircleDTO.getPickUpStore());
//                            storePairVehicleModelDTO.setReturnStore(storePairServiceCircleDTO.getReturnStore());
//                        } else {
//                            storePairVehicleModelDTO.setPickUpStore(new StoreCircleDTO(storePairDTO.getPickUpStore(), null));
//                            storePairVehicleModelDTO.setReturnStore(new StoreCircleDTO(storePairDTO.getReturnStore(), null));
//                        }
//                        // 24小时营业
//                        Optional.ofNullable(storeServiceItemMap.get(storePairDTO.getId())).ifPresent(uni -> {
//                            storePairDTO.getPickUpStore().setAllDay(uni.getPickUpStore().getAllDay());
//                            storePairDTO.getReturnStore().setAllDay(uni.getReturnStore().getAllDay());
//                        });
//                        Map<Long, VehicleModelPriceAbbrDTO> vehicleModelPriceMap = storePairVehicleModelPriceMap.get(storePairDTO.getId());
//                        Map<Long, VehicleModelSubAbbrDTO> vehicleModelSubAbbrMap = storeVehicleModelUniMap.get(storePairDTO.getPickUpStore().getId());
//                        // 填充车辆信息
//                        List<VehicleModelPriceDetailDTO> vehicleModelPriceDetails = storeVehicleModelMap.get(storePairDTO.getId()).stream()
//                                .filter(vehicleModelAbbrDTO -> vehicleModelPriceMap.containsKey(vehicleModelAbbrDTO.getVehicleModelId()))
//                                .map(vehicleModelAbbrDTO -> {
//                                    VehicleModelPriceDetailDTO priceDetailDTO = new VehicleModelPriceDetailDTO();
//                                    priceDetailDTO.setVehicleModelId(vehicleModelAbbrDTO.getVehicleModelId());
//                                    Optional.ofNullable(vehicleModelSubAbbrMap.get(vehicleModelAbbrDTO.getVehicleModelId()))
//                                            .ifPresent(vehicleModelSubAbbrDTO -> {
//                                                priceDetailDTO.setLicenseType(vehicleModelSubAbbrDTO.getLicenseType());
//                                                priceDetailDTO.setFreeDepositType(vehicleModelSubAbbrDTO.getFreeDepositType());
//                                                priceDetailDTO.setPreAuthIllegalDepositPrice(vehicleModelSubAbbrDTO.getPreAuthIllegalDepositPrice());
//                                                priceDetailDTO.setPreAuthRentDepositPrice(vehicleModelSubAbbrDTO.getPreAuthRentDepositPrice());
//                                                priceDetailDTO.setMileage(vehicleModelSubAbbrDTO.getMileage().intValue());
//                                            });
//                                    Optional.ofNullable(vehicleModelPriceMap.get(vehicleModelAbbrDTO.getVehicleModelId()))
//                                            .ifPresent(vehicleModelPriceAbbrDTO -> {
//                                                priceDetailDTO.setTotalAmount(vehicleModelPriceAbbrDTO.getTotalAmount());
//                                                priceDetailDTO.setDiscountAmount(vehicleModelPriceAbbrDTO.getDiscountAmount());
//                                                priceDetailDTO.setRentalPrice(vehicleModelPriceAbbrDTO.getRentalPrice());
//                                                priceDetailDTO.setServiceItemAmountList(vehicleModelPriceAbbrDTO.getServiceItemAmountList());
//                                            });
//                                    Optional.ofNullable(storeVehicleModelDailyPriceMap.get(storePairDTO.getPickUpStore().getId()))
//                                            .ifPresent(map -> Optional.ofNullable(map.get(vehicleModelAbbrDTO.getVehicleModelId()))
//                                                    .ifPresent(dailyPriceDTOS -> priceDetailDTO.setDailyPriceList(dailyPriceDTOS)));
//                                    return priceDetailDTO;
//                                }).collect(Collectors.toList());
//                        storePairVehicleModelDTO.setVehicleModelPriceList(vehicleModelPriceDetails);
//                        return storePairVehicleModelDTO;
//                    }).collect(Collectors.toList());
//            resp.setStorePairVehicleModelList(storePairVehicleModels);
//            Span.current().setAttribute("aj其他处理3", sw.stop().elapsed(TimeUnit.MILLISECONDS));
//            return resp;
//        } catch (Throwable ex) {
//            logger.error("storeVehicleModelPriceSearch exception!", ex);
//            return null;
//        }
//    }

    public VehicleModelPriceDetailResp vehicleModelPriceDetail(Long merchantId, Long vehicleModelId, VehicleModelPriceDetailReq req) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date pickUpTime = sdf.parse(req.getPickUpDate());
            Date returnTime = sdf.parse(req.getReturnDate());
            PointDTO pickUpPoint = req.getPickUpPoint();
            PointDTO returnPoint = req.getReturnPoint();

            StorePairServiceCircleDTO storePairUniServiceCircle = storeService.selectServiceCirclePrice(req.getChannelId(), req.getStorePair(), pickUpPoint, returnPoint, pickUpTime, returnTime);

            // 自助取还标志
            Byte selfServiceReturn = YesOrNoEnum.NO.getValue();
            if (req.getChannelId().longValue() == OrderSourceEnum.CTRIP.getSource()) {
                VehicleModelVO modelVo = vehicleService.selectModelBase(vehicleModelId);
                selfServiceReturn = modelVo.getSelfServiceReturn();
            }
            VehicleModelUniDTO vehicleModelUniDTO = new VehicleModelUniDTO(req.getStorePair().getPickUpStore().getId(), vehicleModelId, selfServiceReturn, null);
            // 价格日历
            // 是否检查最小提前预定时间
            boolean checkMinBook = true;
            List<DailyPriceDTO> vehicleModelDailyPrice = vehicleService.selectModelCalendarPrice(merchantId, req.getChannelId(), vehicleModelUniDTO, pickUpTime, returnTime, checkMinBook, null, false);
            // 算价
            int childrenSeat = 0;
            VehicleModelPriceAbbrDTO vehicleModelPrice = vehicleDomainService.getModelPrice(merchantId, req.getChannelId(), req.getStorePair(),
                    vehicleModelUniDTO, vehicleModelDailyPrice, pickUpPoint, returnPoint, pickUpTime, returnTime,
                    new ArrayList<>(), req.getCouponCodeList(), new ArrayList<>(), childrenSeat, false);

            // 车辆缩略信息
            VehicleModelSubAbbrDTO vehicleModelSubAbbrDTO = vehicleService.selectModelOther(vehicleModelUniDTO, req.getChannelId(), null, merchantId).getData();
            Result<VehicleModelVO> vehicleModelResult = vehicleModelService.getVehicleModelBaseById(vehicleModelId);
            // 取消规则
            CancelPolicyDTO cancelPolicyDTO = storeService.getCancelRule(merchantId, pickUpTime);
            // 保险规则
            List<InsuranceServicePolicyDTO> insuranceServicePolicyDTOS = vehicleService.getModelInsuranceServicePolicy(merchantId);
            // 服务政策暂时为
            StorePairDTO storePair = req.getStorePair();
            Long storeId = storePair.getPickUpStore().getId();
            ServicePolicyVO servicePolicyVO = storeService.getServicePolicy(req.getChannelId(), merchantId, storeId);

            VehicleModelPriceDetailResp resp = new VehicleModelPriceDetailResp();
            resp.setVehicleModelId(vehicleModelId);
            resp.setPartDailyPriceList(vehicleModelPrice.getPartDailyPriceList());
            if (ResultUtil.isModelNotNull(vehicleModelResult)) {
                resp.setLicenseType(vehicleModelResult.getModel().getLicenseType());
            }

            resp.setHasStock(true);
            // 是否开始校验，直连对接后；再启用
            boolean checkDetailStock = false;
            String checkDetailStockStr = (String) redisService.get("checkDetailStock"); //  ",1,"  or "all"  格式
            if (StringUtils.isEmpty(checkDetailStockStr)) {
                checkDetailStockStr = "";
            }
            if (checkDetailStockStr.indexOf("all") > -1 || checkDetailStockStr.indexOf("," + merchantId + ",") > -1) {
                checkDetailStock = true;
            }
            if (checkDetailStock && req.getChannelId() != null &&
                    (req.getChannelId().intValue() == OrderSourceEnum.CTRIP.getSource().intValue()
                            || req.getChannelId().intValue() == OrderSourceEnum.CTRIP_RESALE.getSource().intValue())) {
                ValidateCreateOrderReq checkOrderReq = new ValidateCreateOrderReq();
                checkOrderReq.setChannelId(req.getChannelId());
                PickUpStoreDTO pickUpStoreDTO = new PickUpStoreDTO();
                pickUpStoreDTO.setStoreId(req.getStorePair().getPickUpStore().getId());
                pickUpStoreDTO.setDatetime(req.getPickUpDate());
                checkOrderReq.setPickUpStore(pickUpStoreDTO);

                DropOffStoreDTO dropOffStoreDTO = new DropOffStoreDTO();
                dropOffStoreDTO.setStoreId(req.getStorePair().getReturnStore().getId());
                dropOffStoreDTO.setDatetime(req.getReturnDate());
                checkOrderReq.setDropOffStore(dropOffStoreDTO);

                checkOrderReq.setVehicleModelId(vehicleModelId);
                checkOrderReq.setSelfPickupReturnOrder(selfServiceReturn == YesOrNoEnum.YES.getValue() ? true : false);
                // todo 没有取还车类型, 后面可能会有空指针
                log.info("询价详细校验库存，req={}", JSON.toJSONString(checkOrderReq));
                String license = orderService.checkOrderStock(req.getChannelId(), merchantId, checkOrderReq, VehicleBusyEnum.DETAIL.getValue());
                log.info("询价详细校验库存，license={}, req={}", license, JSON.toJSONString(checkOrderReq));
                if (StringUtils.isBlank(license)) {
                    resp.setHasStock(false);
                    log.info("询价详细无库存, req={}", JSON.toJSONString(req));
                    return resp;
                }
            }

            //校验 儿童座椅库存 无库存不反回 儿童座椅 服务项
            Boolean childrenResult = accessoryInventoryService.childrenSeatInventoryCheck(pickUpTime, returnTime, vehicleModelUniDTO.getStoreId(), 1);
            if(!childrenResult&&CollectionUtils.isNotEmpty(vehicleModelPrice.getServiceItemAmountList())){
                vehicleModelPrice.setServiceItemAmountList(vehicleModelPrice.getServiceItemAmountList().stream().filter(f-> !ServiceFeeTypeEnum.ADD_CHILESEAT_SERVICE.getServiceCode().equals(f.getCode())).collect(Collectors.toList()));
            }
            resp.copyFromDTO(vehicleModelPrice);
            resp.setMarketCost(vehicleModelPrice.getMarketCost());
            resp.setDailyPriceList(vehicleModelDailyPrice);
            // 如果存在无忧租
            if (null != vehicleModelDailyPrice && !vehicleModelDailyPrice.isEmpty()) {
                DailyPriceDTO dailyPriceDTO = vehicleModelDailyPrice.get(0);
                if (null != dailyPriceDTO.getWorryFreePrice()) {
                    VehicleModelPriceAbsDTO vehicleModelPriceAbsDTO = VehicleModelPriceAbsDTO
                            .instanceFromDTO(vehicleModelPrice.getNoWorriedPriceAbbrDTO());
                    // 处理价格日历
                    vehicleModelPriceAbsDTO.setDailyPriceList(dailyPriceNoWorriedHandler(vehicleModelDailyPrice));
                    resp.setNoWorriedPrice(vehicleModelPriceAbsDTO);
                }
            }
            resp.setFreeDepositType(vehicleModelSubAbbrDTO.getFreeDepositType());
            resp.setRentDepositPrice(vehicleModelSubAbbrDTO.getPreAuthRentDepositPrice());
            resp.setIllegalDepositPrice(vehicleModelSubAbbrDTO.getPreAuthIllegalDepositPrice());
            resp.setUnlimitedMileage(vehicleModelSubAbbrDTO.getUnlimitedMileage());
            resp.setMileage(vehicleModelSubAbbrDTO.getMileage().intValue());
            resp.setOverMileagePrice(vehicleModelSubAbbrDTO.getOverMileagePrice());
            resp.setVehicleTags(vehicleModelSubAbbrDTO.getVehicleTags());
            Optional.ofNullable(storePairUniServiceCircle).ifPresent(storePairServiceCircleDTO -> {
                resp.setPickUpServiceCircleList(storePairServiceCircleDTO.getPickUpStore().getServiceCircleDTOList());
                resp.setReturnServiceCircleList(storePairServiceCircleDTO.getReturnStore().getServiceCircleDTOList());
            });
            resp.setCancelPolicy(cancelPolicyDTO);
            resp.setInsuranceServicePolicyList(insuranceServicePolicyDTOS);
            resp.setServicePolicy(servicePolicyVO);

            String xjOpenLog = (String) redisService.get("xjOpenLogRule"); //  ",1,"  or "all"  格式
            Long channelId = req.getChannelId();
            if (StringUtils.isNotEmpty(xjOpenLog) && xjOpenLog.indexOf("," + merchantId + "-" + channelId + ",") > -1) {
                String title = "商家" + merchantId + "渠道" + channelId + ",详情页,vehicleModelPriceDetail";
                if (req.getPickUpOnDoorAddr() != null && req.getPickUpOnDoorAddr().getAddress() != null) {
                    title = title + "," + req.getPickUpOnDoorAddr().getAddress();
                }
                title = title + "," + req.getPickUpDate() + "-" + req.getReturnDate();
                log.info(title + ",response=" + JSON.toJSONString(resp));
            }

            return resp;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 取得价格日历
     *
     * @param merchantId
     * @param vehicleModelId
     * @param req
     * @return
     */
    public List<DailyPriceDTO> vehicleModelDailyPrice(Long merchantId, Long vehicleModelId, VehicleModelPriceDetailReq req, Long orderId) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date pickUpTime = sdf.parse(req.getPickUpDate());
            Date returnTime = sdf.parse(req.getReturnDate());
            VehicleModelUniDTO vehicleModelUniDTO = new VehicleModelUniDTO(req.getStorePair().getPickUpStore().getId(), vehicleModelId, null,  null);
            // 价格日历
            // 是否检查最小提前预定时间
            boolean checkMinBook = true;
            return vehicleService.selectModelCalendarPrice(merchantId, req.getChannelId(), vehicleModelUniDTO, pickUpTime, returnTime, checkMinBook, orderId, false);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 携程费用标准化项目
     * 1. 返回日历价格
     * 2. 返回零散价格
     * @param merchantId
     * @param vehicleModelId
     * @param req
     * @param orderId
     * @return
     */
    public DailyAndHourlyPriceDTO vehicleModelHourlyPrice(Long merchantId, Long vehicleModelId, VehicleModelPriceDetailReq req, Long orderId) {
        try {
            DailyAndHourlyPriceDTO result = new DailyAndHourlyPriceDTO();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date pickUpTime = sdf.parse(req.getPickUpDate());
            Date returnTime = sdf.parse(req.getReturnDate());
            VehicleModelUniDTO vehicleModelUniDTO = new VehicleModelUniDTO(req.getStorePair().getPickUpStore().getId(), vehicleModelId, null,  null);
            // 价格日历
            // 是否检查最小提前预定时间
            boolean checkMinBook = true;
            List<DailyPriceDTO> daysList = vehicleService.selectModelCalendarPrice(merchantId, req.getChannelId(), vehicleModelUniDTO, pickUpTime, returnTime, checkMinBook, orderId, false);
            result.setDailyPriceList(daysList);
            List<ServiceItemDTO> serviceItemList =vehicleService.selectModelService(merchantId, vehicleModelUniDTO, req.getChannelId(), pickUpTime, returnTime, false);
            List<DailyPriceDTO> hourlyPriceList = new ArrayList<>();
            for (DailyPriceDTO dailyPrice : daysList) {
                if (dailyPrice.getHour() == 24) {
                    continue;
                }
                if (dailyPrice.getPer() == 0) {
                    //continue;
                }
                // 租车费
                DailyPriceDTO hourlyServicePrice = new DailyPriceDTO();
                BeanUtils.copyProperties(hourlyServicePrice, dailyPrice);
                hourlyServicePrice.setFeeCode(RENT_FEE_SERVICE.getServiceCode());
                hourlyServicePrice.setWholeDailyPrice(dailyPrice.getPrice());
                hourlyPriceList.add(hourlyServicePrice);
                // 其他服务
                for (ServiceItemDTO serviceItem : serviceItemList) {
                    hourlyServicePrice = new DailyPriceDTO();
                    hourlyServicePrice.setDate(dailyPrice.getDate());
                    hourlyServicePrice.setHour(dailyPrice.getHour());
                    hourlyServicePrice.setPer(dailyPrice.getPer());
                    hourlyServicePrice.setFeeCode(serviceItem.getCode());
                    hourlyServicePrice.setPartDailyPrice(getIntFeeYuan((int) (serviceItem.getPrice() * dailyPrice.getPer())));
                    hourlyServicePrice.setWholeDailyPrice(serviceItem.getPrice());
                    hourlyServicePrice.setPrice(serviceItem.getPrice());
                    hourlyServicePrice.setAllDay(false);
                    hourlyPriceList.add(hourlyServicePrice);
                }
                result.setPartDailyPriceList(hourlyPriceList);
            }
            return result;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    private Integer getIntFeeYuan(Integer fee) {
        if (fee == null) {
            return null;
        }
        return fee % 100 == 0 ? fee : ((int) (fee / 100) + 1) * 100;
    }

    public List<VehicleModelUniDTO<List<DailyPriceDTO>>> vehicleModelMultiDailyPrice(Long merchatId, VehicleModelsDailyPriceReq req) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date pickUpTime = sdf.parse(req.getPickUpDate());
            Date returnTime = sdf.parse(req.getReturnDate());
            List<VehicleModelUniDTO> vehicleModelUniDTOS = req.getVehicleModelIds().stream()
                    .map(vehicleModelId -> new VehicleModelUniDTO(req.getStorePair().getPickUpStore().getId(), vehicleModelId, null,  null))
                    .collect(Collectors.toList());
            // 价格日历
            // 是否检查最小提前预定时间
            boolean checkMinBook = true;
            return vehicleService.selectModelCalendarPrice(merchatId, req.getChannelId(), vehicleModelUniDTOS, pickUpTime, returnTime, checkMinBook, null,null, null, false);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 取得续租价格详细
     *
     * @param merchantId 商户id
     * @param req
     * @return
     */
    public RenewalPriceDetailResp renewalPriceDetail(Long merchantId, RenewalPriceDetailReq req) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date returnTime = sdf.parse(req.getReturnDate());
            Result<VehicleModelPriceAbbrDTO> result = orderService.rerentPriceCal(req.getOrderId(), returnTime.getTime(), req.getChannelId().byteValue());
            log.info("renewalPriceDetail orderService.rerentPriceCal result={}", JSON.toJSONString(result));
            RenewalPriceDetailResp resp = new RenewalPriceDetailResp();
            if (result.isSuccess() && result.getModel() != null) {
                VehicleModelPriceAbbrDTO vehicleModelPriceAbbrDTO = result.getModel();
                resp.setTotalAmount(vehicleModelPriceAbbrDTO.getTotalAmount());
                resp.setDiscountAmount(vehicleModelPriceAbbrDTO.getDiscountAmount());
                resp.setRentalPrice(vehicleModelPriceAbbrDTO.getRentalPrice());
                resp.setPriceDailyList(vehicleModelPriceAbbrDTO.getPriceDailyList());
                resp.setServiceItemAmountList(vehicleModelPriceAbbrDTO.getServiceItemAmountList());
                resp.setInsuranceServicePolicyList(vehicleModelPriceAbbrDTO.getInsuranceServicePolicyList());
                resp.setPartDailyPriceList(vehicleModelPriceAbbrDTO.getPartDailyPriceList());
            }
            log.info("renewalPriceDetail renewalPriceDetailResp={}", JSON.toJSONString(resp));
            return resp;
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public List<DailyPriceDTO> dailyPriceNoWorriedHandler(List<DailyPriceDTO> dailyPriceDTOS) {
        // 这边需要做价格处理
        return dailyPriceDTOS.stream()
                .map(dailyPriceDTO -> dailyPriceDTO.toBuilder()
                        .price(dailyPriceDTO.getWorryFreePrice())
                        .partDailyPrice(dailyPriceDTO.getWorryFreeDailyPrice())
                        .build()).collect(Collectors.toList());
    }
}
