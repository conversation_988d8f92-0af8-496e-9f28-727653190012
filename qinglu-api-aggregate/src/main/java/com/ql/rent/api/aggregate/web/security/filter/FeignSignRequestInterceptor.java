package com.ql.rent.api.aggregate.web.security.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ql.dto.common.ApiConnSecurity;
import com.ql.rent.api.aggregate.web.security.bean.HttpHeadersConstant;
import com.ql.rent.api.aggregate.web.security.bean.SignConstant;
import com.ql.rent.service.common.IApiConnServiceV2;
import com.ql.rent.share.result.Result;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/10/27, Thursday
 **/
//@Configuration
@Slf4j
@AllArgsConstructor
public class FeignSignRequestInterceptor implements RequestInterceptor {

    private final IApiConnServiceV2 iApiConnService;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        byte[] responseBody = requestTemplate.request().body();
        String bodyParamStr = new String(responseBody);
        log.debug("签名处理 jsonObject : {}", bodyParamStr);
        JSONObject jsonObject = JSON.parseObject(bodyParamStr);
        if (null != jsonObject) {
            Long channelId = jsonObject.getLong(SignConstant.CHANNEL_ID);
            Long merchantId = jsonObject.getLong(SignConstant.MERCHANT_ID);
            Result<ApiConnSecurity> apiConnVoResult = iApiConnService.getApiConnMoreByMerchantIdAndChannelId(merchantId, channelId);
            if (apiConnVoResult.isSuccess()) {
                String appKey = apiConnVoResult.getModel().getAppkey();
                String appSecret = apiConnVoResult.getModel().getAppsecret();
                String timeStamp = String.valueOf(System.currentTimeMillis());
                String channelVendorCode = apiConnVoResult.getModel().getChannelVendorCode();
                String sign = DigestUtils.md5Hex(appKey + appSecret + timeStamp);
                requestTemplate.header(HttpHeadersConstant.SIGN, sign);
                requestTemplate.header(HttpHeadersConstant.TIME_STAMP, timeStamp);
                requestTemplate.header(HttpHeadersConstant.APP_KEY, appKey);
                requestTemplate.header(HttpHeadersConstant.CHANNEL_VENDOR_CODE, channelVendorCode);
//                log.info("签名成功 channelId {} , merchantId {}, appKey {}, sign {}, timeStamp {}", channelId, merchantId, appKey, sign, timeStamp);
            } else {
                //日志太多，先注释了
                //log.info("未找到有效的商户签名 channelId {} , merchantId {}", channelId, merchantId);
            }
        } else {
            log.warn("API请求的数据为空");
        }
//        requestTemplate.body(responseBody, Charset.defaultCharset());
    }
}
