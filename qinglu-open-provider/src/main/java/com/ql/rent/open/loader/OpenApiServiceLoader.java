package com.ql.rent.open.loader;

import com.ql.rent.annotation.OepnApiServiceMethod;
import com.ql.rent.open.service.AbstractOpenApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @auther musi
 * @date 2023/9/15 17:02
 */
@Component
@Slf4j
public class OpenApiServiceLoader {

    private static Map<String, OpenApiInvoke> openApiServiceMap = new HashMap<>();

    private static final Map<String, Class<?>> CLASS_CACHE = new ConcurrentHashMap<>();

    public OpenApiServiceLoader(ObjectProvider<List<AbstractOpenApiService>> skillServiceProvider) {
        List<AbstractOpenApiService> skillServiceList = skillServiceProvider.getIfAvailable();
        for (AbstractOpenApiService openApiService : skillServiceList) {
            // 如果是代理类
            if (AopUtils.isAopProxy(openApiService)) {
                // 获取真实类属性
                Class<?> targetClass = AopProxyUtils.ultimateTargetClass(openApiService);
                Class<?>[] targetInterfaces = targetClass.getInterfaces();
                Method[] targetMethods = targetClass.getMethods();
                // 过滤object方法
                List<Method> methodsWithNoObject = Arrays.stream(targetMethods)
                        .filter(method -> !method.getDeclaringClass().equals(Object.class))
                        .collect(Collectors.toList());
                // 获取interface
                Class<?> targetInterface = targetInterfaces[0];

                Method[] methods = openApiService.getClass().getMethods();
                List<Method> proxyMethodsWithNoObject = Arrays.stream(methods)
                        .filter(method -> !method.getDeclaringClass().equals(Object.class))
                        .collect(Collectors.toList());
                // 设置代理类的map
                Map<String, Method> proxyMethodByName = proxyMethodsWithNoObject.stream().collect(Collectors.toMap(
                        Method::getName,
                        item -> item,
                        (existing, replacement) -> existing));
                // 添加方法
                methodsWithNoObject.forEach(item -> {
                    try {
                        Method interfaceMethod = targetInterface.getMethod(item.getName(), item.getParameterTypes());
                        if (interfaceMethod.isAnnotationPresent(OepnApiServiceMethod.class)) {
                            OepnApiServiceMethod annotation = interfaceMethod.getAnnotation(OepnApiServiceMethod.class);
                            OpenApiInvoke invoke = new OpenApiInvoke();
                            invoke.setOpenApiService(openApiService);
                            invoke.setMethod(proxyMethodByName.get(item.getName()));
                            if (invoke.getParameterType() == null) {
                                log.error("open, 严重问题, 启动时未设置方法参数类型, method:{}", annotation.name());
                            }
                            openApiServiceMap.put(annotation.name(), invoke);
                        }
                    } catch (Throwable e) {
                        log.error("OpenApiServiceMethod Invalid Proxy, item:{}", item.getName(), e);
                    }
                });
                // 非代理类
            } else {
                Method[] methods = openApiService.getClass().getMethods();
                for (Method method : methods) {
                    // 检查方法是否来自于Object类
                    if (method.getDeclaringClass().equals(Object.class)) {
                        continue;
                    }
                    try {
                        Method interfaceMethod = openApiService.getClass().getInterfaces()[0]
                                .getMethod(method.getName(), method.getParameterTypes());
                        if (interfaceMethod.isAnnotationPresent(OepnApiServiceMethod.class)) {
                            OepnApiServiceMethod annotation = interfaceMethod.getAnnotation(OepnApiServiceMethod.class);
                            OpenApiInvoke invoke = new OpenApiInvoke();
                            invoke.setOpenApiService(openApiService);
                            invoke.setMethod(method);
                            if (invoke.getParameterType() == null) {
                                log.error("open, 严重问题, 启动时未设置方法参数类型, method:{}", annotation.name());
                            }
                            openApiServiceMap.put(annotation.name(), invoke);
                        }
                    } catch (Exception e) {
                        log.error("OpenApiServiceMethod Invalid, item:{}, e:{}", method.getName(), e.getMessage());
                    }
                }
            }
        }
    }

    public OpenApiInvoke getOpenApiInvokeByMethod(String method) {
        return openApiServiceMap.get(method);
    }

    /**
     * 获取或缓存类对象，避免重复使用Class.forName导致内存泄漏
     * 
     * @param className 类名
     * @return 类对象
     */
    public Class<?> getOrCacheClass(String className) {
        return CLASS_CACHE.computeIfAbsent(className, name -> {
            try {
                log.error("open, 严重问题, 启动时未设置方法参数类型");
                return Class.forName(name);
            } catch (ClassNotFoundException e) {
                log.error("open, 严重问题, 方法参数类型错误 clsName:{}, e:{}", className, e.getMessage());
                throw new RuntimeException("找不到类: " + name, e);
            }
        });
    }
}
