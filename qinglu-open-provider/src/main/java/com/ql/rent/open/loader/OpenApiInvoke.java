package com.ql.rent.open.loader;

import com.ql.rent.open.service.AbstractOpenApiService;

import java.lang.reflect.Method;

/**
 * @auther musi
 * @date 2023/9/15 17:33
 */
public class OpenApiInvoke {

    private AbstractOpenApiService openApiService;

    private Method method;

    private Class<?> parameterType;

    public Method getMethod() {
        return method;
    }

    public void setMethod(Method method) {
        this.method = method;
        if (method != null && method.getParameterTypes().length > 0) {
            this.parameterType = method.getParameterTypes()[0];
        }
    }

    public Class<?> getParameterType() {
        return parameterType;
    }

    public AbstractOpenApiService getOpenApiService() {
        return openApiService;
    }

    public void setOpenApiService(AbstractOpenApiService openApiService) {
        this.openApiService = openApiService;
    }
}
